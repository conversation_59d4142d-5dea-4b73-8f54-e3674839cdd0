<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.UserRelationWorkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.UserRelationWork">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="work_id" property="workId" />
        <result column="user_code" property="userCode" />
        <result column="industry_type" property="industryType" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, work_id, user_code, industry_type, source, sort, creator_code, creation_time, revise_code, revise_time, remark, is_valid
    </sql>

</mapper>