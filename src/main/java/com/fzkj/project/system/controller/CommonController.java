package com.fzkj.project.system.controller;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.file.FileUtils;
import com.fzkj.common.utils.oss.OSSClientUtil;
import com.fzkj.common.utils.oss.OSSConst;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.aspectj.lang.enums.BusinessType;
import com.fzkj.framework.config.AppConfig;
import com.fzkj.framework.config.ServerConfig;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.concurrent.TimeUnit;

@RestController
@Api(tags = "通用请求管理")
@RequiredArgsConstructor
@ThirdPart
@Slf4j
public class CommonController {

    @Autowired
    private ServerConfig serverConfig;
    @Autowired
    private RedisCache redisCache;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        delete = delete != null && delete;
        try {
            if (!FileUtils.isValidFilename(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = "";
            if (fileName.indexOf("_") >= 0) {
                realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            } else {
                realFileName = fileName;
            }

            String filePath = AppConfig.getDownloadPath() + fileName;

            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, realFileName));
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    @GetMapping("/oss/download")
    public void fileDownloadOss(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.isValidFilename(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, fileName));

            OSS ossClient = new OSSClientBuilder().build(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
            BufferedInputStream input = null;
            OSSObject ossObject = ossClient.getObject(OSSConst.BUCKET_NAME, fileName);
            // 读取文件内容。
            input = new BufferedInputStream(ossObject.getObjectContent());
            byte[] buffBytes = new byte[1024];
            OutputStream outputStream = response.getOutputStream();
            int read = 0;
            while ((read = input.read(buffBytes)) != -1) {
                outputStream.write(buffBytes, 0, read);
            }
            outputStream.flush();
            ossObject.close();
            if (delete != null && delete) {
                OSSClientUtil.deleteFile(OSSConst.BUCKET_NAME, fileName);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }


    /**
     * 通用上传请求
     */
    @PostMapping("/common/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = AppConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * oss上传请求
     */
    @PostMapping("/oss/upload")
    @ApiOperation(value = "oss上传请求")
    public AjaxResult uploadFileOss(MultipartFile file, String group) {
        try {
            UploadFile uploadFile = FileUploadUtils.uploadOss(file, group);
            return AjaxResult.success(uploadFile);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取视频播放凭证
     */
    @PostMapping("GetPlayAuth")
    @ApiOperation(value = "获取视频播放凭证")
    public AjaxResult GetPlayAuth(@RequestBody CommonRequest commonRequest) {
        String videoId = commonRequest.getData();
        try {
            return AjaxResult.success(OSSClientUtil.getPlayAuth(videoId));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取视频播放地址
     */
    @PostMapping("GetPlayUrl")
    @ApiOperation(value = "获取视频播放地址")
    public AjaxResult GetPlayUrl(@RequestBody CommonRequest commonRequest) {
        String videoId = commonRequest.getData();
        try {
            return AjaxResult.success(OSSClientUtil.getPlayUrl(videoId));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取文档预览地址
     */
    @PostMapping("GetPreviewUrl")
    @ApiOperation(value = "获取文档预览地址")
    public AjaxResult GetPreviewUrl(@RequestBody CommonRequest commonRequest) throws Exception {
        String objectName = commonRequest.getData();
        if (objectName.startsWith("https://") || objectName.startsWith("http://")) {
            URL url = new URL(objectName);
            objectName = url.getPath();
        }
        if (objectName.startsWith("/")) {
            objectName = objectName.substring(1);
        }
        try {
            return AjaxResult.success(OSSClientUtil.getPreviewUrl(objectName));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取文档总页数
     */
    @PostMapping("GetTotalPage")
    @ApiOperation(value = "获取文档总页数")
    public AjaxResult GetTotalPage(@RequestBody CommonRequest commonRequest) throws Exception {
        String objectName = commonRequest.getData();
        if (objectName.startsWith("https://") || objectName.startsWith("http://")) {
            URL url = new URL(objectName);
            objectName = url.getPath();
        }
        if (objectName.startsWith("/")) {
            objectName = objectName.substring(1);
        }
        //从缓存中获取
        Integer page = redisCache.getCacheObject(objectName);
        if (null == page) {
            page = OSSClientUtil.GetTotalPage(objectName);
            redisCache.setCacheObject(objectName, page, 24, TimeUnit.HOURS);
        }
        try {
            return AjaxResult.success(page);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取视频上传凭证和路径
     */
    @PostMapping("oss/GetUploadAuth")
    @ApiOperation(value = "获取视频上传凭证和路径")
    public AjaxResult GetUploadAuth(@RequestBody CommonRequest commonRequest) throws Exception {
        String filename = commonRequest.getData();
        try {
            return AjaxResult.success(OSSClientUtil.createUploadVideo(filename));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 刷新视频上传凭证和路径
     */
    @PostMapping("oss/RefreshUploadAuth")
    @ApiOperation(value = "获取视频上传凭证和路径")
    public AjaxResult RefreshUploadAuth(@RequestBody CommonRequest commonRequest) throws Exception {
        String videoId = commonRequest.getData();
        try {
            return AjaxResult.success(OSSClientUtil.refreshCreateUploadVideo(videoId));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
