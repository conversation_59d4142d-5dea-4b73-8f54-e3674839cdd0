package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户批量离职excel实体VO
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户批量离职excel实体VO")
public class UserBatchOutExcelVO implements Serializable {

    @Excel(name = "序号")
    private String order;

    @Excel(name = "身份证号")
    private String idCard;
}