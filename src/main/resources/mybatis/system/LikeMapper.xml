<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.LikeMapper">

    <select id="getMyLikeList" resultType="com.fzkj.project.system.vo.LikeVO">
        SELECT
            sub_stance_title targetName,
            pictures imgUrl,
            creation_time releaseTime,
            sub_stance_type_name categoryName,
            id targetId,
            11 funType
        FROM
            t_d_sub_stance_manage
        WHERE
                id IN (
                SELECT
                    target_id
                FROM
                    t_d_like
                WHERE
                    fun_type = 11
                  AND user_code = #{userCode}
                  AND is_valid = 1
                ORDER BY
                    creation_time DESC
            )
    </select>
    <select id="getMyCollectionList" resultType="com.fzkj.project.system.vo.LikeVO">
        SELECT
            sub_stance_title targetName,
            pictures imgUrl,
            creation_time releaseTime,
            sub_stance_type_name categoryName,
            id targetId,
            11 funType
        FROM
            t_d_sub_stance_manage
        WHERE
                id IN (
                SELECT
                    target_id
                FROM
                    t_d_collection
                WHERE
                    fun_type = 11
                  AND user_code = #{userCode}
                  AND is_valid = 1
                ORDER BY
                    creation_time DESC
            )
    </select>
</mapper>