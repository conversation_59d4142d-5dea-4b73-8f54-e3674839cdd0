<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.monitor.mapper.SysOperLogMapper">

	<resultMap type="SysOperLog" id="SysOperLogResult">
		<id     property="operId"         column="oper_id"        />
		<result property="title"          column="title"          />
		<result property="businessType"   column="business_type"  />
		<result property="method"         column="method"         />
		<result property="requestMethod"  column="request_method" />
		<result property="operatorType"   column="operator_type"  />
		<result property="operatedUserCode"   column="operated_user_code"  />
		<result property="operName"       column="oper_name"      />
		<result property="deptName"       column="dept_name"      />
		<result property="operUrl"        column="oper_url"       />
		<result property="operIp"         column="oper_ip"        />
		<result property="operLocation"   column="oper_location"  />
		<result property="operParam"      column="oper_param"     />
		<result property="jsonResult"     column="json_result"    />
		<result property="status"         column="status"         />
		<result property="errorMsg"       column="error_msg"      />
		<result property="operTime"       column="oper_time"      />
		<result property="platform"       column="platform"      />
	</resultMap>

	<sql id="selectOperLogVo">
        select oper_id, title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, zsol.status, error_msg, oper_time, platform
        from t_d_user_oper_log zsol
    </sql>

	<insert id="insertOperlog" parameterType="SysOperLog">
		insert into t_d_user_oper_log(title, business_type, method, request_method, operator_type,operated_user_code, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time,platform)
        values (#{title}, #{businessType}, #{method}, #{requestMethod}, #{operatorType}, #{operatedUserCode}, #{operName}, #{deptName}, #{operUrl}, #{operIp}, #{operLocation}, #{operParam}, #{jsonResult}, #{status}, #{errorMsg}, sysdate(),#{platform})
	</insert>

	<select id="selectOperLogList" parameterType="SysOperLog" resultMap="SysOperLogResult">
		<include refid="selectOperLogVo"/>
		<where>
			<if test="title != null and title != ''">
				AND title like concat('%', #{title}, '%')
			</if>
			<if test="businessType != null">
				AND business_type = #{businessType}
			</if>
			<if test="businessTypes != null and businessTypes.length > 0">
			    AND business_type in
			    <foreach collection="businessTypes" item="businessType" open="(" separator="," close=")">
		 			#{businessType}
		        </foreach>
			</if>
			<if test="status != null">
				AND status = #{status}
			</if>
			<if test="operName != null and operName != ''">
				AND oper_name like concat('%', #{operName}, '%')
			</if>
			<if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
				and date_format(oper_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
			</if>
			<if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
				and date_format(oper_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
			</if>
			<if test="platform != null">
				AND platform = #{platform}
			</if>
			<if test="operatedUserCode != null and operatedUserCode != ''">
				AND operated_user_code = #{operatedUserCode}
			</if>
		</where>
		<!-- 数据范围过滤 -->
		${dataScope}
		order by oper_id desc
	</select>

	<delete id="deleteOperLogByIds" parameterType="Long">
 		delete from t_d_user_oper_log where oper_id in
 		<foreach collection="array" item="operId" open="(" separator="," close=")">
 			#{operId}
        </foreach>
 	</delete>

 	<select id="selectOperLogById" parameterType="Long" resultMap="SysOperLogResult">
		<include refid="selectOperLogVo"/>
		where oper_id = #{operId}
	</select>

	<update id="cleanOperLog">
        truncate table t_d_user_oper_log
    </update>
	
	<select id="selectUserOperLogPage" resultType="com.fzkj.project.system.vo.UserOperLogVO">
		SELECT
			dc.company_name,
			uc.user_name,
			ui.id_card,
			ui.phone,
			uol.oper_time,
			uol.platform,
			uol.oper_name,
			uol.oper_param
		FROM
			t_d_user_oper_log uol
			LEFT JOIN t_d_user_company uc ON uol.operated_user_code = uc.user_code
			LEFT JOIN t_d_company dc ON uc.company_id = dc.id
			LEFT JOIN t_d_user_info ui ON uol.operated_user_code = ui.user_code
		WHERE
		uol.operated_user_code IS NOT NULL
		<if test="platform != null and platform != -999"> AND uol.platform= #{platform} </if>
		<if test="operName != null and operName != ''"> AND uol.oper_name LIKE CONCAT('%', #{operName}, '%') </if>
		<if test="companyId != null"> AND dc.id= #{companyId} </if>
		<if test="keyWord != null and keyWord != ''"> AND (uc.user_name LIKE CONCAT('%', #{keyWord}, '%') OR ui.phone LIKE CONCAT('%', #{keyWord}, '%')) </if>
		<if test="startTime != null and startTime != '' and endTime != null and endTime != ''"> AND oper_time &lt; #{endTime} AND oper_time &gt; #{startTime} </if>
	</select>

</mapper>
