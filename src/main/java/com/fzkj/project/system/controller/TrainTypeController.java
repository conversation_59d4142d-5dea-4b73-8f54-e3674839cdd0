package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.TrainTypeService;
import com.fzkj.project.system.vo.TrainTypeVO;
import com.fzkj.project.system.request.TrainTypeRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 安全培训课程分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
public class TrainTypeController extends BaseEntity {

    private final TrainTypeService TrainTypeServiceImpl;

    @PostMapping("Get_SafeTrainType")
    @ApiOperation(value = "安全培训课程分类表列表查询")
    public AjaxResult Get_SafeTrainType(@RequestBody CommonRequest commonRequest) {
        TrainTypeRequest request = (TrainTypeRequest) commonRequest.getRequest(TrainTypeRequest.class);
        return AjaxResult.success(TrainTypeServiceImpl.listTrainType(request));
    }

}
