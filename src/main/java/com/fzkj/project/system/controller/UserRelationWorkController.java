package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.UserRelationWorkService;
import com.fzkj.project.system.vo.UserRelationWorkVO;
import com.fzkj.project.system.request.UserRelationWorkRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 用户关联岗位 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "userRelationWork")
public class UserRelationWorkController extends BaseEntity {

    private final UserRelationWorkService UserRelationWorkServiceImpl;

    @PostMapping("list")
    @ApiOperation(value = "用户关联岗位列表查询")
    public AjaxResult listUserRelationWork(@RequestBody CommonRequest commonRequest) {
        UserRelationWorkRequest request = (UserRelationWorkRequest) commonRequest.getRequest(UserRelationWorkRequest.class);
        return AjaxResult.success(UserRelationWorkServiceImpl.listUserRelationWork(request));
    }

    @PostMapping("listByPage")
    @ApiOperation(value = "用户关联岗位分页查询")
    public AjaxResult listUserRelationWorkByPage(@RequestBody CommonRequest commonRequest) {
        UserRelationWorkRequest request = (UserRelationWorkRequest) commonRequest.getRequest(UserRelationWorkRequest.class);
        return AjaxResult.success(UserRelationWorkServiceImpl.listByPage(request, commonRequest.getPageEntity()));
    }

    @GetMapping("{id}")
    @ApiOperation(value = "用户关联岗位查询详情")
    public AjaxResult findUserRelationWork(@RequestBody CommonRequest commonRequest) {
        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
        return AjaxResult.success(UserRelationWorkServiceImpl.findUserRelationWork(request.getId()));
    }

    @PostMapping
    @ApiOperation(value = "用户关联岗位新增数据")
    public AjaxResult saveUserRelationWork(@RequestBody CommonRequest commonRequest) {
        UserRelationWorkVO vo = (UserRelationWorkVO) commonRequest.getRequest(UserRelationWorkVO.class);
        return AjaxResult.success(UserRelationWorkServiceImpl.saveUserRelationWork(vo));
    }

    @PostMapping("update")
    @ApiOperation(value = "用户关联岗位修改数据")
    public AjaxResult updateUserRelationWork(@RequestBody CommonRequest commonRequest) {
        UserRelationWorkVO vo = (UserRelationWorkVO) commonRequest.getRequest(UserRelationWorkVO.class);
        return AjaxResult.success(UserRelationWorkServiceImpl.updateUserRelationWork(vo));
    }

    @PostMapping("delete")
    @ApiOperation(value = "用户关联岗位删除数据")
    public AjaxResult removeUserRelationWork(@RequestBody CommonRequest commonRequest) {
        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
        return AjaxResult.success(UserRelationWorkServiceImpl.removeUserRelationWork(request.getId()));
    }

}
