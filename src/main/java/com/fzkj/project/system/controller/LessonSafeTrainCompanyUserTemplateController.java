package com.fzkj.project.system.controller;

import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.LessonSafeTrainCompanyUserTemplateService;
import com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO;
import com.fzkj.project.system.vo.LessonSafeTrainCompanyUserTemplateQueryVO;
import com.fzkj.project.system.vo.LessonSafeTrainCompanyUserTemplateVO;
import com.fzkj.project.system.request.LessonSafeTrainCompanyUserTemplateRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <p>
 * 企业下的用户安全培训课程用户模板 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
@RequestMapping(value = "lessonSafeTrainCompanyUserTemplate")
@Log
public class LessonSafeTrainCompanyUserTemplateController extends BaseEntity {

    private final LessonSafeTrainCompanyUserTemplateService LessonSafeTrainCompanyUserTemplateServiceImpl;

    @PostMapping("Get_LessonSafeTrainUserTemplateList")
    @ApiOperation(value = "培训设置-查询参培学员列表")
    public AjaxResult Get_LessonSafeTrainUserTemplateList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainCompanyUserTemplateRequest request = (LessonSafeTrainCompanyUserTemplateRequest) commonRequest.getRequest(LessonSafeTrainCompanyUserTemplateRequest.class);
        return AjaxResult.success(LessonSafeTrainCompanyUserTemplateServiceImpl.listLessonSafeTrainCompanyUserTemplate(request,commonRequest.getPageEntity()));
    }

    @PostMapping("Edit_LessonSafeTrainCompanyUserTemplate")
    @ApiOperation(value = "培训设置-参培学员编辑")
    public AjaxResult Edit_LessonSafeTrainCompanyUserTemplate(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainCompanyUserTemplateVO request = (LessonSafeTrainCompanyUserTemplateVO) commonRequest.getRequest(LessonSafeTrainCompanyUserTemplateVO.class);
        return AjaxResult.success(LessonSafeTrainCompanyUserTemplateServiceImpl.editLessonSafeTrainCompanyUserTemplate(request));
    }

    @PostMapping("Export_LessonSafeTrainUserTemplate")
    @ApiOperation(value = "培训设置-导出参培学员列表")
    public AjaxResult Export_LessonSafeTrainUserTemplate(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainCompanyUserTemplateRequest request = (LessonSafeTrainCompanyUserTemplateRequest) commonRequest.getRequest(LessonSafeTrainCompanyUserTemplateRequest.class);
        PageUtils pageUtils = LessonSafeTrainCompanyUserTemplateServiceImpl.listLessonSafeTrainCompanyUserTemplate(request,null);
        List list = pageUtils.getList();
        ExcelUtil<LessonSafeTrainCompanyUserTemplateQueryVO> util = new ExcelUtil<>(LessonSafeTrainCompanyUserTemplateQueryVO.class);
        return util.exportExcel(list, "企业课程安全培训设置-参培学员", "参培学员", true);
    }


}
