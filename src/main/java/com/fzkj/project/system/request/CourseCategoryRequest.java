package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 平台课件分类
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "平台课件分类Request")
public class CourseCategoryRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "分类名称(平台课件)")
    private String keywords;

    @ApiModelProperty(value = "是否显示（1：显示，0：未显示）")
    private Integer isShow = -999;

    @ApiModelProperty(value = "来源（1：行业版）")
    private String source="1";

    @ApiModelProperty(value = "是否有效 1是 0否 默认有效")
    private Integer isValid = 1;

    @ApiModelProperty(value = "平台")
    private Integer platform = 1;
}