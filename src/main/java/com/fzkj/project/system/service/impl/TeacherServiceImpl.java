package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.ServletUtils;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.framework.security.service.TokenService;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.Teacher;
import com.fzkj.project.system.entity.UserCompany;
import com.fzkj.project.system.mapper.TeacherMapper;
import com.fzkj.project.system.request.TeacherQueryRequest;
import com.fzkj.project.system.request.TeacherRequest;
import com.fzkj.project.system.service.TeacherService;
import com.fzkj.project.system.service.UserCompanyService;
import com.fzkj.project.system.vo.TeacherVO;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 讲师 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class TeacherServiceImpl extends ServiceImpl<TeacherMapper, Teacher> implements TeacherService {


    @Resource
    TokenService tokenService;

    @Resource
    UserCompanyService userCompanyService;

    @Override
    public List<Teacher> listTeacher(TeacherRequest request) {
        Teacher entity = (Teacher) DataTransfer.transfer(request, Teacher.class);
        LambdaQueryWrapper<Teacher> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listTeacher(TeacherQueryRequest request, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<Teacher> teachers  = getBaseMapper().selectTeacher(request);
        return new PageUtils(pageEntity,teachers);

    }

    @Override
    public String exportTeacher(TeacherQueryRequest request) {
        PageEntity pageEntity = new PageEntity();
        pageEntity.setPageSize(*********);
        pageEntity.setPageIndex(1);
        PageUtils pageUtils = listTeacher(request,pageEntity);
        List teachers = pageUtils.getList();
        try {
            ExcelUtil<Teacher> util = new ExcelUtil<Teacher>(Teacher.class);
            String fileName = util.encodingDateFilename("讲师信息导出");
            InputStream inputStream = util.exportExcelFile(teachers, fileName);
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "user", fileName);
            return fileName + ".xlsx," + uploadFile.getUrl();
        } catch (Exception e) {
            log.error("讲师信息导出上传异常");
            return "讲师信息导出上传异常";
        }
    }

    @Override
    public PageUtils listByPage(TeacherRequest request, PageEntity pageEntity) {
        Teacher entity = (Teacher) DataTransfer.transfer(request, Teacher.class);
        LambdaQueryWrapper<Teacher> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<Teacher> page = this.page(
                new Query<Teacher>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public TeacherVO findTeacher(Long id) {
        Teacher entity = this.getById(id);
        TeacherVO vo = (TeacherVO) DataTransfer.transfer(entity, TeacherVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveTeacher(TeacherVO vo) {
        Teacher entity = (Teacher) DataTransfer.transfer(vo, Teacher.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        entity.setCreatorCode(userCompany.getUserCode());
        entity.setCreationTime(LocalDateTime.now());
        entity.setIsValid(1);
        entity.setRecycleBin(0);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTeacher(TeacherVO vo) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        Teacher entity = (Teacher) DataTransfer.transfer(vo, Teacher.class);
        entity.setReviseCode(userCompany.getUserCode());
        entity.setReviseTime(LocalDateTime.now());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTeacher(Long id) {
        Teacher byId = this.getById(id);
        if (byId.getRecycleBin().equals(0)){
            byId.setRecycleBin(1);
        }else {
            byId.setRecycleBin(0);
        }
        return this.saveOrUpdate(byId);
    }
}