package com.fzkj.project.system.queue;

import com.alibaba.fastjson2.JSON;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.project.system.entity.WxPushTab;
import com.fzkj.project.system.service.WxService;
import com.fzkj.project.system.vo.WxMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

/**
 * 消息推送队列
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WxPushTabTrainHandler implements MessageListener {
    private final RedisCache redisCache;
    private final WxService wxService;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        System.out.println("Received message for wxPushTabTrain: " + message.toString());
        WxPushTab wxPushTab = JSON.parseObject(new String(message.getBody()), WxPushTab.class);
        if (null == wxPushTab) {
            return;
        }
        if (wxPushTab.getIsWxSend() < 1) {//微信发送失败或未发送时执行
            if (StringUtils.isNotEmpty(wxPushTab.getOpenId())) {
                if (wxPushTab.getType() == 0) {//课程分发
                    try {
                        WxMessage wxMessage = new WxMessage();
                        wxMessage.setCode("5");
                        wxMessage.setOpenId(wxPushTab.getOpenId());
                        wxMessage.setData(wxPushTab.getOpenId() + "|温馨提示：" + wxPushTab.getUserName() + "，您的《" + wxPushTab.getLessonName() + "》已分发，请尽快完成学习和考试|" + wxPushTab.getLessonName()
                                + "|" + wxPushTab.getLessonId() + "|" + com.fzkj.common.utils.StringUtils.getStringInTwenty(wxPushTab.getLessonCategoryName())
                                + "|点击查看|小程序Appid|src/pages/learn/learn?id=" + wxPushTab.getLessonId()
                                + "&type=" + wxPushTab.getUrlType() + "&PlanID=" + wxPushTab.getPlanId() + "|");
                        boolean sent = wxService.sendWxMessage(wxMessage);
                        if (sent) {
                            wxPushTab.setIsWxSend(1);//微信发送成功
                        } else {
                            wxPushTab.setIsWxSend(-1);//微信发送失败
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        wxPushTab.setIsWxSend(-1);//微信发送失败
                    }
                    redisCache.sendMessage("wxPushTabState", wxPushTab);
                }
            } else {
                wxPushTab.setIsWxSend(2);//没有微信标识，则默认为不推送
                redisCache.sendMessage("wxPushTabState", wxPushTab);
            }
        }
    }
}
