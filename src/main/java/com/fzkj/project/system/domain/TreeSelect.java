package com.fzkj.project.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.project.system.entity.Company;
import com.fzkj.project.system.vo.CompanyVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
@Data
public class TreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    @JsonProperty("ID")
    private Long id;

    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("ParentID")
    private Long parentId;

    @JsonProperty("CreatorTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creatorTime;

    @JsonProperty("UserNumber")
    private Integer userNumber;

    @JsonProperty("WorkNumber")
    private Integer workNumber;

    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("CompanyID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long companyId;

    @JsonProperty("DepartID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long departId;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("Children")
    private List<TreeSelect> children;

    public TreeSelect()
    {

    }

    public TreeSelect(CompanyVO dept)
    {
        this.id = dept.getId();
        this.companyName = dept.getCompanyName();
        this.parentId = dept.getParentId();
        this.creatorTime = dept.getCreatorTime();
        this.userNumber = dept.getUserNumber();
        this.workNumber = dept.getWorkNumber();
        this.isValid = dept.getIsValid();
        this.departId = dept.getDepartId();
        this.companyId = dept.getCompanyId();
        this.children = dept.getChildren()!=null && dept.getChildren().size()>0?dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList()):null;
    }
}
