package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 视频文件资源信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "视频文件资源信息表Request")
public class CoursewareResourcesRequest implements Serializable {

    private Long id;
    private Long resID;
    @ApiModelProperty(value = "转码状态（1：转码中，0：正常，-999：全部）")
    private Integer encodeState;
    private String flag;
    private String keywords;

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private String fileMd5;

    @ApiModelProperty(value = "文件ID")
    private String fileId;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    @ApiModelProperty(value = "文件转码地址")
    private String fileUrlHls;

    @ApiModelProperty(value = "文件时长")
    private Integer fileTime;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型")
    private Integer fileType;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @ApiModelProperty(value = "创建人")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @ApiModelProperty(value = "修改人")
    private String reviseName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @ApiModelProperty(value = "来源")
    private String source;

}