package com.fzkj.project.system.controller;

import com.fzkj.common.constant.Constants;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.request.TeacherQueryRequest;
import com.fzkj.project.system.service.TeacherService;
import com.fzkj.project.system.vo.TeacherVO;
import com.fzkj.project.system.request.TeacherRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 讲师 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
@RequestMapping("lessonApi")
public class TeacherController extends BaseEntity {

    private final TeacherService TeacherServiceImpl;

    @PostMapping("Get_Teacher")
    @ApiOperation(value = "讲师分页查询")
    public AjaxResult Get_Teacher(@RequestBody CommonRequest commonRequest) {
        TeacherQueryRequest request = (TeacherQueryRequest) commonRequest.getRequest(TeacherQueryRequest.class);
        return AjaxResult.success(TeacherServiceImpl.listTeacher(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Teacher_Export")
    @ApiOperation(value = "讲师查询导出")
    public AjaxResult Teacher_Export(@RequestBody CommonRequest commonRequest) {
        TeacherQueryRequest request = (TeacherQueryRequest) commonRequest.getRequest(TeacherQueryRequest.class);
        return AjaxResult.success(TeacherServiceImpl.exportTeacher(request));
    }

    @PostMapping("Edit_Teacher")
    @ApiOperation(value = "讲师新增数据")
    public AjaxResult saveTeacher(@RequestBody CommonRequest commonRequest) {
        TeacherVO request = (TeacherVO) commonRequest.getRequest(TeacherVO.class);
        switch (request.getFlag()) {
            case Constants.ADD_DATA:
                return AjaxResult.success(TeacherServiceImpl.saveTeacher(request));
            case Constants.EDIT_DATA:
                return AjaxResult.success(TeacherServiceImpl.updateTeacher(request));
            case Constants.DEL_DATA:
                return AjaxResult.success(TeacherServiceImpl.removeTeacher(request.getId()));
            default:
                return AjaxResult.error("操作异常！");
        }
    }

    @PostMapping("Get_TeacherAPP")
    @ApiOperation(value = "讲师分页查询")
    public AjaxResult Get_TeacherAPP(@RequestBody CommonRequest commonRequest) {
        TeacherQueryRequest request = (TeacherQueryRequest) commonRequest.getRequest(TeacherQueryRequest.class);
        return AjaxResult.success(TeacherServiceImpl.listTeacher(request, commonRequest.getPageEntity()));
    }
    /*@PostMapping("list")
    @ApiOperation(value = "讲师列表查询")
    public AjaxResult listTeacher(@RequestBody CommonRequest commonRequest) {
        TeacherRequest request = (TeacherRequest) commonRequest.getRequest(TeacherRequest.class);
        return AjaxResult.success(TeacherServiceImpl.listTeacher(request));
    }

    @GetMapping("{id}")
    @ApiOperation(value = "讲师查询详情")
    public AjaxResult findTeacher(@RequestBody CommonRequest commonRequest) {
        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
        return AjaxResult.success(TeacherServiceImpl.findTeacher(request.getId()));
    }

    @PostMapping
    @ApiOperation(value = "讲师新增数据")
    public AjaxResult saveTeacher(@RequestBody CommonRequest commonRequest) {
        TeacherVO vo = (TeacherVO) commonRequest.getRequest(TeacherVO.class);
        return AjaxResult.success(TeacherServiceImpl.saveTeacher(vo));
    }

    @PostMapping("update")
    @ApiOperation(value = "讲师修改数据")
    public AjaxResult updateTeacher(@RequestBody CommonRequest commonRequest) {
        TeacherVO vo = (TeacherVO) commonRequest.getRequest(TeacherVO.class);
        return AjaxResult.success(TeacherServiceImpl.updateTeacher(vo));
    }

    @PostMapping("delete")
    @ApiOperation(value = "讲师删除数据")
    public AjaxResult removeTeacher(@RequestBody CommonRequest commonRequest) {
        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
        return AjaxResult.success(TeacherServiceImpl.removeTeacher(request.getId()));
    }*/

}
