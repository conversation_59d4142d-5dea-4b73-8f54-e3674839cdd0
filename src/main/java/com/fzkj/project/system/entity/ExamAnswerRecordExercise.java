package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 用户练习记录
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@TableName("t_d_exam_answer_record_exercise")
@ApiModel(value = "用户练习记录实体")
public class ExamAnswerRecordExercise implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "组织ID")
    @JsonProperty("OrganizationID")
    private String organizationId;

    @ApiModelProperty(value = "用户ID")
    @JsonProperty("UserID")
    private String userId;

    @ApiModelProperty(value = "考试ID")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "题目ID")
    @JsonProperty("QuestionID")
    private Long questionId;

    @ApiModelProperty(value = "用户选择的答案")
    @JsonProperty("SubOption")
    private String subOption;

    @ApiModelProperty(value = "正确答案")
    @JsonProperty("RightOption")
    private String rightOption;

    @ApiModelProperty(value = "得分")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @JsonProperty("CreatorID")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @JsonProperty("ReviseID")
    private String reviseId;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效：0：无效，1：有效")
    @JsonProperty("IsValid")
    private Integer isValid;


}