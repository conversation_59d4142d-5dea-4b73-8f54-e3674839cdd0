package com.fzkj.project.basicdata.controller;

import com.fzkj.common.constant.Constants;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.basicdata.entity.SubStanceLabel;
import com.fzkj.project.basicdata.service.SubStanceLabelService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-14 14:26
 * @Version: 1.0
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
//@RequestMapping("/basicdata")
public class SubStanceLabelController {

    private final SubStanceLabelService subStanceLabelService;

    @PostMapping("Get_SubstanceLabel")
    @ApiOperation(value = "内容分类分页查询")
    public AjaxResult listTrainLabelByPage(@RequestBody CommonRequest commonRequest) {
        SubStanceLabel request = (SubStanceLabel) commonRequest.getRequest(SubStanceLabel.class);
        return AjaxResult.success(subStanceLabelService.listByPage(request, commonRequest.getPageEntity()));
    }

    /**
     * 新增保存常见问题
     */
    @PostMapping("/Edit_SubstanceLabel")
    @ApiOperation(value = "新增标签")
    @ResponseBody
    public AjaxResult addSave(@RequestBody CommonRequest commonRequest) {
        SubStanceLabel request = (SubStanceLabel) commonRequest.getRequest(SubStanceLabel.class);
        switch (request.getFlag()) {
            case Constants.ADD_DATA:
                return AjaxResult.success(subStanceLabelService.insertSubStanceLabel(request));
            case Constants.EDIT_DATA:
                return AjaxResult.success(subStanceLabelService.updateSubStanceLabel(request));
            case Constants.DEL_DATA:
                return AjaxResult.success(subStanceLabelService.deleteSubStanceLabel(request.getId()));
            default:
                return AjaxResult.error("操作异常！");
        }
    }

    /**
     * 导出标签
     */
    @PostMapping("/Export_SubstanceLabel")
    @ApiOperation(value = "导出标签")
    @ResponseBody
    public AjaxResult exportSubstanceLabel(@RequestBody CommonRequest commonRequest) {
        SubStanceLabel request = (SubStanceLabel) commonRequest.getRequest(SubStanceLabel.class);
        return AjaxResult.success(subStanceLabelService.exportSubstanceLabel(request, commonRequest.getPageEntity()));
    }
}
