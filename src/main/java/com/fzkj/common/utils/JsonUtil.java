package com.fzkj.common.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JsonUtil {

    public static ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Long.class, new JsonDeserializer<Long>() {
            @Override
            public Long deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                String rawValue = p.getValueAsString();
                if ("-999".equals(rawValue)) {
                    return null;
                }
                try {
                    return Long.valueOf(rawValue);
                } catch (Exception e) {
                    return null;
                }
            }
        });
        module.addDeserializer(Integer.class, new JsonDeserializer<Integer>() {
            @Override
            public Integer deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                String rawValue = p.getValueAsString();
                if ("-999".equals(rawValue)) {
                    return null;
                }
                try {
                    return Integer.valueOf(rawValue);
                } catch (Exception e) {
                    return null;
                }
            }
        });
        module.addDeserializer(String.class, new JsonDeserializer<String>() {
            @Override
            public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                String rawValue = p.getValueAsString();
                if ("-999".equals(rawValue)) {
                    return null;
                }
                try {
                    return rawValue;
                } catch (Exception e) {
                    return null;
                }
            }
        });
        module.addDeserializer(Map.class, new JsonDeserializer<Map>() {
            @Override
            public Map<String, Object> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                HashMap map = new HashMap<String, Object>();
                while(!p.isClosed()){
                    JsonToken jsonToken = p.nextToken();
                    if (JsonToken.FIELD_NAME.equals(jsonToken)) {
                        String fieldName = p.getCurrentName().toUpperCase();
                        jsonToken = p.nextToken();
                        if (JsonToken.START_OBJECT.equals(jsonToken)) {
                            map.put(fieldName, getObject(p));
                        } else if (JsonToken.START_ARRAY.equals(jsonToken)) {
                            map.put(fieldName, getArray(p));
                        } else {
                            map.put(fieldName, getScalar(jsonToken, p));
                        }
                    }
                }
                return map;
            }
        });
        objectMapper.registerModule(module);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
        return objectMapper;
    }

    private static Map<String, Object> getObject(JsonParser p) throws IOException {
        HashMap map = new HashMap<String, Object>();
        JsonToken jt = p.nextToken();
        while (!JsonToken.END_OBJECT.equals(jt)) {
            if (JsonToken.FIELD_NAME.equals(jt)) {
                String fieldName = p.getCurrentName().toUpperCase();
                jt = p.nextToken();
                if (JsonToken.START_OBJECT.equals(jt)) {
                    map.put(fieldName, getObject(p));
                } else if (JsonToken.START_ARRAY.equals(jt)) {
                    map.put(fieldName, getArray(p));
                } else {
                    map.put(fieldName, getScalar(jt, p));
                }
            }
            jt = p.nextToken();
        }
        return map;
    }
    public static List<Object> getArray(JsonParser p) throws IOException {
        ArrayList list = new ArrayList<>();
        JsonToken jt = p.nextToken();
        while (!JsonToken.END_ARRAY.equals(jt)) {
            if (JsonToken.START_OBJECT.equals(jt)) {
                list.add(getObject(p));
            } else if (JsonToken.START_ARRAY.equals(jt)) {
                list.add(getArray(p));
            } else {
                list.add(getScalar(jt, p));
            }
            jt = p.nextToken();
        }
        return list;
    }

    public static Object getScalar(JsonToken jsonToken, JsonParser p) throws IOException {
        if(null != p.getValueAsString() && p.getValueAsString().equals("-999")){
            return null;
        }
        if (JsonToken.VALUE_NUMBER_INT.equals(jsonToken) || JsonToken.VALUE_NUMBER_FLOAT.equals(jsonToken)) {
            return p.getNumberValue();
        } else if (JsonToken.VALUE_FALSE.equals(jsonToken)) {
            return false;
        } else if (JsonToken.VALUE_TRUE.equals(jsonToken)) {
            return true;
        } else if (JsonToken.VALUE_STRING.equals(jsonToken)) {
            return p.getValueAsString();
        } else if (JsonToken.VALUE_NULL.equals(jsonToken)) {
            return null;
        }
        throw new RuntimeException("did not find a scalar for JsonToken = " + jsonToken);
    }
}
