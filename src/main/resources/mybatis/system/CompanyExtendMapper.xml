<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CompanyExtendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.CompanyExtend">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="taxpayer_number" property="taxpayerNumber" />
        <result column="license_key" property="licenseKey" />
        <result column="liaison" property="liaison" />
        <result column="liaison_phone" property="liaisonPhone" />
        <result column="industry_type" property="industryType" />
        <result column="nature" property="nature" />
        <result column="scale" property="scale" />
        <result column="business_scope" property="businessScope" />
        <result column="register_source" property="registerSource" />
        <result column="is_enable" property="isEnable" />
        <result column="enable_summary" property="enableSummary" />
        <result column="oper_platform" property="operPlatform" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creator_time" property="creatorTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="open_sign" property="openSign" />
        <result column="company_label" property="companyLabel" />
        <result column="merchant_name" property="merchantName" />
        <result column="merchant_code" property="merchantCode" />
        <result column="taxpayer_url" property="taxpayerUrl" />
        <result column="taxpayer_file_name" property="taxpayerFileName" />
        <result column="register_time" property="registerTime" />
        <result column="site_area" property="siteArea" />
        <result column="register_capital" property="registerCapital" />
        <result column="economic_type" property="economicType" />
        <result column="is_open_album" property="isOpenAlbum" />
        <result column="is_safe_status" property="isSafeStatus" />
        <result column="uplus_time" property="uplusTime" />
        <result column="comp_range" property="compRange" />
        <result column="is_address" property="isAddress" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, taxpayer_number, license_key, liaison, liaison_phone, industry_type, nature, scale, business_scope, register_source, is_enable, enable_summary, oper_platform, source, sort, creator_code, creator_time, revise_code, revise_time, remark, is_valid, open_sign, company_label, merchant_name, merchant_code, taxpayer_url, taxpayer_file_name, register_time, site_area, register_capital, economic_type, is_open_album, is_safe_status, uplus_time, comp_range, is_address
    </sql>

</mapper>