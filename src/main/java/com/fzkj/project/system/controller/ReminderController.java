package com.fzkj.project.system.controller;

import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.request.ReminderRequest;
import com.fzkj.project.system.service.ReminderService;
import com.fzkj.project.system.vo.ReminderVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 提醒管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-04
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
@RequestMapping(value = "toolmanage")
@Log
public class ReminderController extends BaseEntity {

    private final ReminderService ReminderServiceImpl;
    @PostMapping("Get_ReminderSetList")
    @ApiOperation(value = "提醒管理表分页查询")
    public AjaxResult listReminderByPage(@RequestBody CommonRequest commonRequest) {
        ReminderRequest request = (ReminderRequest) commonRequest.getRequest(ReminderRequest.class);
        return AjaxResult.success(ReminderServiceImpl.listByPage(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Add_ReminderSet")
    @ApiOperation(value = "提醒管理表新增数据")
    public AjaxResult saveReminder(@RequestBody CommonRequest commonRequest) {
        ReminderVO vo = (ReminderVO) commonRequest.getRequest(ReminderVO.class);
        return AjaxResult.success(ReminderServiceImpl.saveReminder(vo));
    }

    @PostMapping("Edit_ReminderSet")
    @ApiOperation(value = "提醒管理表修改数据")
    public AjaxResult updateReminder(@RequestBody CommonRequest commonRequest) {
        ReminderVO vo = (ReminderVO) commonRequest.getRequest(ReminderVO.class);
        return AjaxResult.success(ReminderServiceImpl.updateReminder(vo));
    }

    @PostMapping("Delete_ReminderSet")
    @ApiOperation(value = "提醒管理表删除数据")
    public AjaxResult removeReminder(@RequestBody CommonRequest commonRequest) {
        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
        return AjaxResult.success(ReminderServiceImpl.removeReminder(request.getId()));
    }

}
