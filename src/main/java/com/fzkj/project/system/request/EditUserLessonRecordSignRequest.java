package com.fzkj.project.system.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EditUserLessonRecordSignRequest {

    @ApiModelProperty(value = "培训类型")
    private String trainType;

    @ApiModelProperty(value = "签到图片")
    private String signImg;

    @ApiModelProperty(value = "是否是考试")
    private Integer isExam;

    @ApiModelProperty(value = "计划ID")
    private String planId;

    @ApiModelProperty(value = "用户编号")
    private String userCode;

    @ApiModelProperty(value = "课程ID")
    private Long lessonId;
}
