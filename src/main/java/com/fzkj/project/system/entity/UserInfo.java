package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 用户登录信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_user_info")
@ApiModel(value = "用户信息表实体")
public class UserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("UserCode")
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @JsonProperty("IDCard")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @JsonProperty("Phone")
    @ApiModelProperty(value = "手机号")
    private String phone;

    @JsonProperty("PassWord")
    @ApiModelProperty(value = "密码")
    private String passWord;

    @JsonProperty("UnionID")
    @ApiModelProperty(value = "unionid")
    private String unionId;

    @JsonProperty("OpenID")
    @ApiModelProperty(value = "openid")
    private String openId;

    @JsonProperty("PublicOpenID")
    @ApiModelProperty(value = "publicOpenId")
    private String publicOpenId;

    @JsonProperty("Source")
    @ApiModelProperty(value = "来源")
    private String source;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @JsonProperty("JobNo")
    @ApiModelProperty(value = "工号")
    private String jobNo;

    @JsonProperty("ReviseTimePwd")
    @ApiModelProperty(value = "修改密码时间")
    private LocalDateTime reviseTimePwd;

    @ApiModelProperty(value = "权限")
    @TableField(exist = false)
    private int permission;

    @ApiModelProperty(value = "公司ID")
    @TableField(exist = false)
    private Long companyId;

    @ApiModelProperty(value = "部门名称")
    @TableField(exist = false)
    private String companyName;

    @ApiModelProperty(value = "公司ID")
    @TableField(exist = false)
    @JsonIgnore
    private int platform;

    @ApiModelProperty(value = "姓名")
    @TableField(exist = false)
    private String userName;

    @ApiModelProperty(value = "地区代码")
    @TableField(exist = false)
    private String areaCode;

    @ApiModelProperty(value = "自增ID")
    @TableField(exist = false)
    private Integer identity;
}