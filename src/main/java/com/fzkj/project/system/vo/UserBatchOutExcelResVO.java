package com.fzkj.project.system.vo;

import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户批量离职excel返回实体VO
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户批量离职excel返回实体VO")
public class UserBatchOutExcelResVO implements Serializable {

    @Excel(name = "序号")
    private String order;

    @Excel(name = "身份证号")
    private String idCard;

    @Excel(name = "备注")
    private String remark;
}