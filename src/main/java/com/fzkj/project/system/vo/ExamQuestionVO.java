package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.alibaba.fastjson2.JSON;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 考试试题表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value="考试试题表VO")
public class ExamQuestionVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "考试ID")
    @NotNull(message = "考试ID不能为空")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "试题类型")
    @NotNull(message = "试题类型不能为空")
    @JsonProperty("SubjectType")
    private Integer subjectType;

    @ApiModelProperty(value = "试题名称")
    @NotNull(message = "试题名称不能为空")
    @JsonProperty("SubjectName")
    @Excel(name = "试题题目")
    private String subjectName;

    @ApiModelProperty(value = "试题类型ID")
    @NotNull(message = "试题类型ID不能为空")
    @JsonProperty("QuestionTypeId")
    private Integer questionTypeId;

    @ApiModelProperty(value = "试题类型名称")
    @NotNull(message = "试题类型名称不能为空")
    @JsonProperty("QuestionTypeName")
    @Excel(name = "试题类型")
    private String questionTypeName;

    @ApiModelProperty(value = "分数")
    @NotNull(message = "分数不能为空")
    @JsonProperty("Score")
    @Excel(name = "试题分数")
    private Integer score;

    @ApiModelProperty(value = "试题选项")
    @NotNull(message = "试题选项不能为空")
    @JsonProperty("QuestionOption")
    private String questionOption;

    @ApiModelProperty(value = "正确答案")
    @NotNull(message = "正确答案不能为空")
    @JsonProperty("RightOption")
    @Excel(name = "正确答案")
    private String rightOption;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    @Excel(name = "排序编号")
    private Integer sort;

    @ApiModelProperty(value = "创建者ID")
    @NotNull(message = "创建者ID不能为空")
    @JsonProperty("CreatorID")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改者ID")
    @NotNull(message = "修改者ID不能为空")
    @JsonProperty("ReviseID")
    private String reviseId;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    @NotNull(message = "是否有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "图片")
    @NotNull(message = "图片不能为空")
    @JsonProperty("Img")
    private String img;

    @ApiModelProperty(value = "试题解析")
    @NotNull(message = "试题解析不能为空")
    @JsonProperty("ProblemAnalysis")
    @Excel(name = "题目解析")
    private String problemAnalysis;

    @JsonProperty("Flag")
    private String flag;

    @JsonProperty("QuestionXML")
    private String questionXML;

    @JsonProperty("QuestionXMLNumber")
    private int questionXMLNumber;

    @Excel(name = "选项A")
    private String choiceA;

    @Excel(name = "选项B")
    private String choiceB;

    @Excel(name = "选项C")
    private String choiceC;

    @Excel(name = "选项D")
    private String choiceD;

    @Excel(name = "选项E")
    private String choiceE;

    @Excel(name = "选项F")
    private String choiceF;

    private char answer;

    private List<QuestionOption> options;

    public List<QuestionOption> getOptions(){
        if(this.questionOption != null && this.questionOption.startsWith("[") && this.questionOption.endsWith("]")){
            return JSON.parseArray(this.questionOption, QuestionOption.class);
        }
        return null;
    }
}