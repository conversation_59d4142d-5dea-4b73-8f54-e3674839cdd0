package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公交培训计划
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "公交培训计划数据统计VO")
public class BusSafeTrainOrgVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private Long id;
    @ApiModelProperty(value = "总学习人数")
    @JsonProperty("SumNumber")
    private Integer sumNumber;

    @ApiModelProperty(value = "学习人数")
    @JsonProperty("LearnNumber")
    private Integer learnNumber;

    @ApiModelProperty(value = "完成人数")
    @JsonProperty("CompleteNumber")
    private Integer completeNumber;

    @ApiModelProperty(value = "学习率")
    @JsonProperty("LearnRate")
    private String learnRate;

    @ApiModelProperty(value = "完成率")
    @JsonProperty("CompleteRate")
    private String completeRate;

    @ApiModelProperty(value = "是否下一级")
    @JsonProperty("IsNext")
    private String isNext;

    @ApiModelProperty(value = "父级ID")
    @JsonProperty("FID")
    private Long fid;

    @ApiModelProperty(value = "名称")
    @JsonProperty("Name")
    private String name;


}