package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LessonRecordVO {

    @ApiModelProperty("企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty("企业名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty("部门ID")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty("部门名称")
    @JsonProperty("DepartName")
    private String departName;

    @ApiModelProperty("工作ID")
    @JsonProperty("WorkID")
    private Long workId;

    @ApiModelProperty("工作名称")
    @JsonProperty("WorkName")
    private String workName;

    @ApiModelProperty("课程类别ID")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty("课程类别名称")
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty("课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty("课程名称")
    @JsonProperty("LessonName")
    private String lessonName;

    @ApiModelProperty("课程日期")
    @JsonProperty("LessonDate")
    private String lessonDate;

    @ApiModelProperty("课程课件数")
    @JsonProperty("LessonCourseCount")
    private Integer lessonCourseCount;

    @ApiModelProperty("用户ID")
    @JsonProperty("UserID")
    private Long userId;

    @ApiModelProperty("用户名称")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty("用户编码")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty("用户手机号")
    @JsonProperty("Phone")
    private String phone;

    @ApiModelProperty("用户头像")
    @JsonProperty("UserPhoto")
    private String userPhoto;

    @ApiModelProperty("用户身份证号")
    @JsonProperty("IDCard")
    private String idCard;

    @ApiModelProperty("用户支付类型")
    @JsonProperty("IsPayType")
    private Integer isPayType;

    @ApiModelProperty("用户开始学习时间")
    @JsonProperty("STime")
    private String sTime;

    @ApiModelProperty("用户结束学习时间")
    @JsonProperty("ETime")
    private String eTime;

    @ApiModelProperty("用户学习总时长")
    @JsonProperty("TotalTimeCount")
    private Integer totalTimeCount;

    @ApiModelProperty("用户来源")
    @JsonProperty("DisSource")
    private Integer disSource;

    @ApiModelProperty("用户学习模式")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty("用户员工价格")
    @JsonProperty("StaffPrice")
    private double staffPrice;

    @ApiModelProperty("用户地区编码")
    @JsonProperty("AreaCode")
    private String areaCode;

    @ApiModelProperty("用户地区名称")
    @JsonProperty("AreaName")
    private String areaName;

    @ApiModelProperty("用户考试ID")
    @JsonProperty("ExamID")
    private Integer examId;

    @ApiModelProperty("用户考试状态")
    @JsonProperty("ExamStateStr")
    private String examStateStr;

    @ApiModelProperty("用户考试完成时间")
    @JsonProperty("ExamCompleteTime")
    private String examCompleteTime;

    @ApiModelProperty("用户考试签名")
    @JsonProperty("ExamSign")
    private String examSign;

    @ApiModelProperty("用户成绩")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty("用户是否完成")
    @JsonProperty("IsComplete")
    private Integer isComplete;

    @ApiModelProperty("用户签名图片")
    @JsonProperty("SignImg")
    private String signImg;

    @ApiModelProperty("用户完成时间")
    @JsonProperty("CompleteTime")
    private String completeTime;

    @ApiModelProperty("用户课程图片")
    @JsonProperty("LessonPic")
    private String lessonPic;

    @ApiModelProperty("用户补考次数")
    @JsonProperty("ResitNumber")
    private int resitNumber;

    @ApiModelProperty("用户是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty("用户培训状态")
    @JsonProperty("TrainStatusStr")
    private String trainStatusStr;

    @ApiModelProperty("用户创建时间")
    @JsonProperty("CreationTime")
    private String creationTime;

    @ApiModelProperty("用户静态状态")
    @JsonProperty("StaticStatus")
    private Integer staticStatus;

    @ApiModelProperty("用户OpenID")
    @JsonProperty("OpenID")
    private String openId;

    @ApiModelProperty("课程形式")
    @JsonProperty("Shape")
    private Integer shape;

    @ApiModelProperty("统计分组")
    @JsonProperty("StatisticalGroup")
    private Integer statisticalGroup;

    @ApiModelProperty("计划ID")
    @JsonProperty("PlanID")
    private Long planId;

    @ApiModelProperty("计划名称")
    @JsonProperty("PlanName")
    private String planName;

    @ApiModelProperty("计划级别")
    @JsonProperty("PlanLevel")
    private Integer planLevel;

}
