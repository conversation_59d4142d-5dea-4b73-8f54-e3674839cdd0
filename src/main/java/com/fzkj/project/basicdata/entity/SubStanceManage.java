package com.fzkj.project.basicdata.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 有效内容对象 t_d_sub_stance_manage
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Data
@TableName("t_d_sub_stance_manage")
@ApiModel(value = "内容标签实体")
public class SubStanceManage {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    /**
     * 内容分类id
     */
    @ApiModelProperty(name = "内容分类id")
    @JsonProperty("SubStanceTypeID")
    private Long subStanceTypeId;

    /**
     * 内容分类id
     */
    @ApiModelProperty(name = "内容分类")
    @JsonProperty("SubStanceTypeName")
    private String subStanceTypeName;
    /**
     * 内容标签ids
     */
    @ApiModelProperty(name = "内容标签ids")
    @JsonProperty("SubStanceLabelIDs")
    private String subStanceLabelIds;
    /**
     * 内容标签ids
     */
    @ApiModelProperty(name = "内容标签查询")
    @JsonProperty("SubStanceLabelQuery")
    private String subStanceLabelQuery;

    /**
     * 内容标签
     */
    @ApiModelProperty(name = "内容标签")
    @JsonProperty("SubStanceLabelNames")
    private String subStanceLabelNames;

    /**
     * 内容
     */
    @ApiModelProperty(name = "内容")
    @JsonProperty("SubStanceTitle")
    private String subStanceTitle;

    /**
     *
     */
    @ApiModelProperty(name = "")
    @JsonProperty("Summary")
    private String summary;

    /**
     * 图片
     */
    @ApiModelProperty(name = "图片")
    @JsonProperty("Pictures")
    private String pictures;

    /**
     * 文件类型
     */
    @ApiModelProperty(name = "文件类型")
    @JsonProperty("FileType")
    private Integer fileType;

    /**
     * 文件url
     */
    @ApiModelProperty(name = "文件url")
    @JsonProperty("FileUrl")
    private String fileUrl;

    /**
     * 链接地址
     */
    @ApiModelProperty(name = "链接地址")
    @JsonProperty("LinkUrl")
    private String linkUrl;

    /**
     * 来源
     */
    @ApiModelProperty(name = "来源")
    @JsonProperty("Source")
    private String source;

    /**
     * 释放时间
     */
    @ApiModelProperty(name = "释放时间")
    @JsonProperty("ReleaseTime")
    private String releaseTime;

    /**
     * 是否登录
     */
    @ApiModelProperty(name = "是否登录")
    @JsonProperty("IsLogin")
    private Integer isLogin;

    /**
     * 阅读人数
     */
    @ApiModelProperty(name = "阅读人数")
    @JsonProperty("ReadNum")
    private Integer readNum;

    /**
     * 排序
     */
    @ApiModelProperty(name = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    /**
     * 新建人code
     */
    @ApiModelProperty(name = "新建人code")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    /**
     * 新建时间
     */
    @ApiModelProperty(name = "新建时间")
    @JsonProperty("CreationTime")
    private String creationTime;

    /**
     * 修改人code
     */
    @ApiModelProperty(name = "修改人code")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "修改时间")
    @JsonProperty("ReviseTime")
    private String reviseTime;

    /**
     * 是否有效
     */
    @ApiModelProperty(name = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    /**
     * 文件id
     */
    @ApiModelProperty(name = "文件id")
    @JsonProperty("FileID")
    private String fileId;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    @JsonProperty("CreatorName")
    private String creatorName;

    /**
     * 修改人
     */
    @ApiModelProperty(name = "修改人")
    @JsonProperty("ReviseName")
    private String reviseName;

    /**
     *
     */
    @ApiModelProperty(name = "")
    @JsonProperty("Author")
    private String author;

    /**
     * 地区id
     */
    @ApiModelProperty(name = "地区id")
    @JsonProperty("OrganizationID")
    private Long organizationId;

    /**
     * 所属平台
     */
    @ApiModelProperty(name = "所属平台")
    @JsonProperty("PutinPlatform")
    private Integer putinPlatform;

    /**
     * 所属平台名称
     */
    @ApiModelProperty(name = "所属平台名称")
    @JsonProperty("PutinPlatformStr")
    private String putinPlatformStr;

    /**
     * 阅读系数
     */
    @ApiModelProperty(name = "阅读系数")
    @JsonProperty("ReadCoefficient")
    private BigDecimal readCoefficient;

    /**
     *
     */
    @ApiModelProperty(name = "")
    @JsonProperty("IsOriginal")
    private Integer isOriginal;

    @ApiModelProperty(name = "")
    @JsonProperty("Flag")
    @TableField(exist = false)
    private String flag;

    @ApiModelProperty(name = "")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(name = "")
    @JsonProperty("Duration")
    private String duration;

    @ApiModelProperty(name = "")
    @JsonProperty("CommentNum")
    @TableField(exist = false)
    private Integer commentNum;
}
