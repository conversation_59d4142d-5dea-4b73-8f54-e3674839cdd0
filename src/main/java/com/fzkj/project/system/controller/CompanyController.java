package com.fzkj.project.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.CompanyService;
import com.fzkj.project.system.vo.CompanyVO;
import com.fzkj.project.system.request.CompanyRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 企业表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userPort")
public class CompanyController extends BaseEntity {

    private final CompanyService CompanyServiceImpl;

    @PostMapping(value = "GetSelectCompanyArray")
    @ApiOperation(value = "企业表列表查询")
    public AjaxResult listCompany(@RequestBody CommonRequest commonRequest) {
        CompanyRequest request = (CompanyRequest) commonRequest.getRequest(CompanyRequest.class);
        return AjaxResult.success(JSONObject.toJSONString(CompanyServiceImpl.listCompany(request)));
    }

    @PostMapping(value = "GetCompanyTree")
    @ApiOperation(value = "企业-部门列表查询")
    public AjaxResult getCompanyTree(@RequestBody CommonRequest commonRequest) {
        CompanyRequest request = (CompanyRequest) commonRequest.getRequest(CompanyRequest.class);
        return AjaxResult.success(JSONObject.toJSONString(CompanyServiceImpl.getCompanyTree(request)));
    }

    @PostMapping(value = "GetCompany")
    @ApiOperation(value = "企业表列表查询（树结构）,企业详情")
    public AjaxResult getCompany(@RequestBody CommonRequest commonRequest) {
        CompanyRequest request = (CompanyRequest) commonRequest.getRequest(CompanyRequest.class);
        if(request.getFlag() != null && request.getFlag().equals("detail")){
            return AjaxResult.success(CompanyServiceImpl.getCompanyDetail(request));
        }else{
            return AjaxResult.success(JSONObject.toJSONString(CompanyServiceImpl.getCompany(request), SerializerFeature.WriteMapNullValue));
        }

    }

//    @PostMapping("listByPage")
//    @ApiOperation(value = "企业表分页查询")
//    public AjaxResult listCompanyByPage(@RequestBody CommonRequest commonRequest) {
//        CompanyRequest request = (CompanyRequest) commonRequest.getRequest(CompanyRequest.class);
//        return AjaxResult.success(CompanyServiceImpl.listByPage(request, commonRequest.getPageEntity()));
//    }

//    @GetMapping("{id}")
//    @ApiOperation(value = "企业表查询详情")
//    public AjaxResult findCompany(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(CompanyServiceImpl.findCompany(request.getId()));
//    }

//    @PostMapping
//    @ApiOperation(value = "企业表新增数据")
//    public AjaxResult saveCompany(@RequestBody CommonRequest commonRequest) {
//        CompanyVO vo = (CompanyVO) commonRequest.getRequest(CompanyVO.class);
//        return AjaxResult.success(CompanyServiceImpl.saveCompany(vo));
//    }

    @PostMapping("EditCompany")
    @ApiOperation(value = "企业表修改数据")
    public AjaxResult updateCompany(@RequestBody CommonRequest commonRequest) {
        CompanyVO vo = (CompanyVO) commonRequest.getRequest(CompanyVO.class);
        if(vo.getFlag().equals("update")){
            return AjaxResult.success(CompanyServiceImpl.updateCompanyVo(vo));
        }else{
            Integer count= CompanyServiceImpl.updateCompany(vo);
            if(count == -1){
                return AjaxResult.error("上级部门为禁用状态，不可启用当级","上级部门为禁用状态，不可启用当级");
            }else{
                return AjaxResult.success(count);
            }
        }

    }

//    @PostMapping("delete")
//    @ApiOperation(value = "企业表删除数据")
//    public AjaxResult removeCompany(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(CompanyServiceImpl.removeCompany(request.getId()));
//    }

}
