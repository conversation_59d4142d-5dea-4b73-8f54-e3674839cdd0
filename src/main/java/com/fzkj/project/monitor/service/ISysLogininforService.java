package com.fzkj.project.monitor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.monitor.domain.SysLogininfor;

import java.util.List;
import java.util.Map;

/**
 * 系统访问日志情况信息 服务层
 *
 * <AUTHOR>
 */
public interface ISysLogininforService extends IService<SysLogininfor>
{
    List selectLogininforList(SysLogininfor logininfor);
}
