<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.LessonSafeTrainCompanyUserTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.LessonSafeTrainCompanyUserTemplate">
        <id column="id" property="id" />
        <result column="company_lesson_pay_id" property="companyLessonPayId" />
        <result column="is_pay_type" property="isPayType" />
        <result column="pay_type_name" property="payTypeName" />
        <result column="company_id" property="companyId" />
        <result column="company_name" property="companyName" />
        <result column="user_code" property="userCode" />
        <result column="user_name" property="userName" />
        <result column="lesson_category_fid" property="lessonCategoryFid" />
        <result column="lesson_category_id" property="lessonCategoryId" />
        <result column="lesson_category_name" property="lessonCategoryName" />
        <result column="hand_mode" property="handMode" />
        <result column="is_trusteeship" property="isTrusteeship" />
        <result column="is_free" property="isFree" />
        <result column="statistical_group" property="statisticalGroup" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_lesson_pay_id, is_pay_type, pay_type_name, company_id, company_name, user_code, user_name, lesson_category_fid, lesson_category_id, lesson_category_name, hand_mode, is_trusteeship, is_free, statistical_group, source, sort, creator_code, creation_time, revise_code, revise_time, remark, is_valid
    </sql>
    <select id="selectLessonSafeTrainCompanyUserTemplate1" resultType="com.fzkj.project.system.vo.LessonSafeTrainCompanyUserTemplateQueryVO">
        SELECT u.user_name, u.user_code, ui.id_card, c1.company_name AS depart_name, w1.work_name, u.work_id, u.depart_id
        FROM t_d_user_company u
        LEFT JOIN t_d_user_info ui ON ui.user_code = u.user_code
        LEFT JOIN t_d_company c1 ON c1.id = u.depart_id
        LEFT JOIN t_d_company_work w1 ON w1.id = u.work_id
        WHERE u.is_valid = 1
          and u.is_out=1
        <if test="departIds != null and departIds.size > 0">
            AND u.depart_id IN
            <foreach item="id" collection="departIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="companyId != null">
            AND u.company_id = #{companyId}
        </if>
        <if test="workId != null">
            AND u.work_id = #{workId}
        </if>
        <if test="userName != null and userName != ''">
            AND (ui.id_card LIKE CONCAT('%', #{userName}, '%') OR u.user_name LIKE CONCAT('%', #{userName}, '%') OR ui.phone LIKE CONCAT('%', #{userName}, '%') OR ui.job_no LIKE CONCAT('%', #{userName}, '%') OR u.plate_number LIKE CONCAT('%', #{userName}, '%') OR '' = #{userName})
        </if>
        AND u.user_code NOT IN (SELECT user_code FROM t_d_lesson_safe_train_company_user_template WHERE is_valid = 1 AND company_id = #{companyId} AND lesson_category_id = #{lessonCategoryId})
    </select>
    <select id="selectLessonSafeTrainCompanyUserTemplate3" resultType="com.fzkj.project.system.vo.LessonSafeTrainCompanyUserTemplateQueryVO">
        SELECT t1.id, t1.user_code, t1.is_free, t1.statistical_group,
        u.user_name, ui.id_card, c.company_name, c1.company_name AS depart_name, w1.work_name, c.area_name, t1.lesson_category_name, s.total_time_count, s.is_pay_type, s.staff_price, s.hand_mode, s.is_trusteeship, ui.phone
        FROM t_d_lesson_safe_train_company_user_template t1
        LEFT JOIN t_d_company c ON c.id = t1.company_id
        LEFT JOIN t_d_user_company u ON t1.user_code = u.user_code
        LEFT JOIN t_d_user_info ui ON ui.user_code = u.user_code
        LEFT JOIN t_d_company c1 ON c1.id = u.depart_id
        LEFT JOIN t_d_company_work w1 ON w1.id = u.work_id
        LEFT JOIN t_d_company_lesson_safe_train_set s ON s.company_id = t1.company_id AND t1.lesson_category_id = s.lesson_category_id AND s.is_valid = 1
        WHERE t1.is_valid = 1 and u.is_out = 1
        <if test="userName != null">
            AND (ui.id_card LIKE CONCAT('%', #{userName}, '%') OR u.user_name LIKE CONCAT('%', #{userName}, '%') OR ui.phone LIKE CONCAT('%', #{userName}, '%') OR ui.job_no LIKE CONCAT('%', #{userName}, '%') OR u.plate_number LIKE CONCAT('%', #{userName}, '%') OR '' = #{userName})
        </if>
        <if test="companyId != null">
            AND t1.company_id = #{companyId}
        </if>
        <if test="workId != null">
            AND u.work_id = #{workId}
        </if>
        <if test="departIds != null and departIds.size > 0">
            AND u.depart_id IN
            <foreach item="id" collection="departIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="lessonCategoryId != null">
            AND t1.lesson_category_id = #{lessonCategoryId}
        </if>
        AND s.total_time_count IS NOT NULL
        ORDER BY t1.creation_time DESC
    </select>

    <select id="selectUsers" resultType="com.fzkj.project.system.vo.LessonSafeTrainQueryUserVO">
        SELECT uc.user_code, uc.user_name,ui.id_card,c.company_name as departName,cw.work_name
        FROM t_d_lesson_safe_train_company_user_template ls
        left join t_d_user_info ui on ui.user_code = ls.user_code
        left join t_d_user_company uc on uc.user_code = ls.user_code
        left join t_d_company c on c.id = uc.depart_id
        left join t_d_company_work cw on cw.id = uc.work_id
        WHERE ls.is_valid=1 AND uc.is_out = 1 and ls.company_lesson_pay_id=#{companyLessonPayId}
        <if test="departIds != null and departIds.size > 0">
            AND (uc.depart_id IN
            <foreach item="id" collection="departIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        <if test="workId != null">
            and uc.work_id = #{workId}
        </if>
        <if test="keyWords != null and keyWords != ''">
            and (uc.user_name like CONCAT('%', #{keyWords}, '%') or ui.id_card like CONCAT('%', #{keyWords}, '%') or
            ui.phone like CONCAT('%', #{keyWords}, '%') or ui.job_no like CONCAT('%', #{keyWords}, '%'))
        </if>
    </select>

    <select id="selectLessonSafeTrainUnDisUser" resultType="com.fzkj.project.system.mongo.UserLessonRecordSafeTrain">
        select ui.id user_id,ui.job_no,u.company_id,t.company_name,c.area_code,c.area_name,1 is_static,u.depart_id,c1.company_name
        depart_name, u.work_id,w.work_name, t.lesson_category_id,u.is_enable,
        t.user_code,t.user_name,ui.phone,u.user_photo,ui.id_card,
        2 is_pay_type,s.total_time_count as trainTimeCount,1 dis_source
        ,s.staff_price,ui.open_id,ui.public_open_id,t.statistical_group
        from t_d_lesson_safe_train_company_user_template t
        left join t_d_user_company u on t.user_code=u.user_code
        left join t_d_user_info ui on ui.user_code=u.user_code
        left join t_d_company_extend ce on ce.company_id=u.company_id
        left join t_d_company_lesson_safe_train_set s on s.id=t.company_lesson_pay_id
        left join t_d_company c on c.id=u.company_id
        left join t_d_company c1 on c1.id=u.depart_id
        left join t_d_company_work w on w.id=u.work_id
        where t.is_valid=1 and t.lesson_category_id=#{lessonCategoryId}
        and s.is_valid=1
        and c.is_valid = 1
        and u.is_out = 1
        and t.company_id in
        <foreach item="companyId" index="index" collection="companyIds.split(',')" open="(" separator="," close=")">
            #{companyId}
        </foreach>
        <if test="userCodes != null and userCodes != ''">
            and t.user_code in
            <foreach item="userCode" index="index" collection="userCodes.split(',')" open="(" separator="," close=")">
                #{userCode}
            </foreach>
        </if>
    </select>

    <select id="selectLessonNoDisUserNum" resultType="com.fzkj.project.system.mongo.UserLessonRecordSafeTrain">
        select
        user_code
        from
        t_d_lesson_safe_train_company_user_template t1
        where
        t1.is_valid = 1
        and company_lesson_pay_id = (
        select id from t_d_company_lesson_safe_train_set where
        company_id = #{companyId}
        and lesson_category_id = #{lessonCategoryId}
        and t1.is_valid = 1)
    </select>
</mapper>