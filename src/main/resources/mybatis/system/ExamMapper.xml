<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.ExamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.Exam">
        <id column="id" property="id"/>
        <result column="exam_name" property="examName"/>
        <result column="question_count" property="questionCount"/>
        <result column="time_count" property="timeCount"/>
        <result column="score" property="score"/>
        <result column="pass_mark" property="passMark"/>
        <result column="resit_number" property="resitNumber"/>
        <result column="group_roll_type" property="groupRollType"/>
        <result column="sort" property="sort"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creation_time" property="creationTime"/>
        <result column="revise_id" property="reviseId"/>
        <result column="revise_time" property="reviseTime"/>
        <result column="remark" property="remark"/>
        <result column="is_valid" property="isValid"/>
        <result column="org_id" property="orgId"/>
        <result column="source" property="source"/>
        <result column="exam_sign_state" property="examSignState"/>
        <result column="exam_face_state" property="examFaceState"/>
        <result column="exam_count_down_time" property="examCountDownTime"/>
        <result column="exam_fail_repeat_num" property="examFailRepeatNum"/>
        <result column="is_prompt_answer" property="isPromptAnswer"/>
        <result column="is_mock_exam" property="isMockExam"/>
        <result column="is_practice" property="isPractice"/>
        <result column="is_mock_exam_face" property="isMockExamFace"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, exam_name, question_count, time_count, score, pass_mark, resit_number, group_roll_type, sort, creator_id,
        creation_time, revise_id, revise_time, remark, is_valid, org_id, source, exam_sign_state, exam_face_state,
        exam_count_down_time, exam_fail_repeat_num, is_prompt_answer, is_mock_exam, is_practice, is_mock_exam_face
    </sql>
    <select id="selectExamInfo" resultType="com.fzkj.project.system.vo.ExamVO">
        SELECT tde.*,IFNULL(tdel.lesson_id,-1) as lessonId
        from t_d_exam tde
        left join t_d_exam_lesson tdel on tde.id = tdel.exam_id
        <where>
            <if test="null != examName and examName != ''">
                and tde.exam_name like concat('%',#{examName},'%'
            </if>
            <if test="null != orgId">
                and tde.org_id = #{orgId}
            </if>
            <if test="null != isValid">
                and tde.is_valid = #{isValid}
            </if>
            <if test="null != issueFlag and issueFlag != ''">
                and tde.remark = #{issueFlag}
            </if>
            <if test="null != lessonId">
                and tdel.lesson_id = #{lessonId}
            </if>
        </where>
        ORDER BY tde.revise_time desc
    </select>
    <select id="selectExamPaperRule" resultType="com.fzkj.project.system.vo.ExaminationPaperRuleVO">
        select * from t_d_examination_paper_rule where exam_id = #{examId}
    </select>
    <select id="selectExamQuestion" resultType="com.fzkj.project.system.vo.ExamQuestionVO">
        SELECT
        tdeq.*
        FROM t_d_exam_question AS tdeq
        WHERE tdeq.exam_id=#{examId} and tdeq.is_valid=1
        <if test="examQuestionId != null and examQuestionId!=''">
            AND tdeq.id=#{examQuestionId}
        </if>
        <if test="questionTypeId != null and questionTypeId!=''">
            AND tdeq.question_type_id=#{questionTypeId}
        </if>
        ORDER BY tdeq.sort,tdeq.id
    </select>
    <select id="getExamByLessonId" resultType="com.fzkj.project.system.vo.ExamVO">
        select e.* from t_d_exam e left join t_d_exam_lesson el on e.id = el.exam_id
        where  el.lesson_id = #{lessonId}
    </select>
    <select id="getUserExamInfo" resultType="com.fzkj.project.system.vo.UserExamInfoVo">
        SELECT tde.id AS exam_id,
        tde.question_count,
        tde.score AS total_score,
        tde.time_count,
        tde.resit_number,
        tde.pass_mark,
        tde.exam_sign_state, tde.exam_face_state, tde.exam_count_down_time, tde.exam_fail_repeat_num, tde.is_mock_exam_face, tde.is_mock_exam, tde.is_practice,
        t_d_exam_lesson.lesson_id,
        (SELECT COUNT(1) FROM t_d_exam_user_record AS tdeur WHERE tdeur.user_id = #{userId}  AND tdeur.lesson_id = t_d_exam_lesson.lesson_id AND tdeur.is_valid = 1) AS exam_count,
        IFNULL(temp.score, -1) AS score,
        IFNULL(temp.resit_number, -1) AS sy_resit_number,
        temp.photo_url, temp.sign_url, temp.remark, IFNULL(temp.creation_time, NOW()) AS creation_time, IFNULL(temp.error_count, 0),
        IFNULL(temp.user_exam_count, 0) AS user_exam_count, IFNULL(temp.accuracy, 0) AS accuracy, IFNULL(temp.use_time_count, 0) AS use_time_count, temp.id_card, temp.user_name, temp.user_phone,
        temp.user_id, IFNULL(temp.organization_id, 0) AS organization_id
        FROM t_d_exam tde
        LEFT JOIN (
        <include refid="tempTable" />
        ) AS temp ON tde.id = temp.exam_id
        LEFT JOIN t_d_exam_lesson ON tde.id = t_d_exam_lesson.exam_id
        WHERE tde.id IN (
        SELECT tdel.exam_id
        FROM t_d_exam_lesson tdel
        WHERE tdel.lesson_id IN
        <foreach item="lessonId" index="index" collection="lessonIds" open="(" separator="," close=")">
            #{lessonId}
        </foreach>
        )
        AND tde.is_valid = 1
    </select>
    <select id="selectUserExamInfo" resultType="com.fzkj.project.system.vo.GetUserExamInfoVO">
        SELECT
        tde.id AS exam_id,
        tde.question_count,
        tde.score AS total_score,
        tde.time_count,
        tde.resit_number,
        tde.pass_mark,
        tde.exam_sign_state,
        tde.exam_face_state,
        tde.exam_count_down_time,
        tde.exam_fail_repeat_num,
        tde.is_mock_exam_face,
        tde.is_mock_exam,
        tde.is_practice,
        tdel.lesson_id,
        (
        SELECT COUNT(1)
        FROM t_d_exam_user_record AS tdeur
        WHERE tdeur.user_id = #{userId} AND tdeur.is_valid = 1 AND tdeur.lesson_id = tdel.lesson_id
        ) AS exam_count,
        IFNULL(tt.score, -1) AS score,
        IFNULL(tt.resit_number, -1) AS sy_resit_number,
        tt.photo_url,
        tt.sign_url,
        tt.remark,
        IFNULL(tt.creation_time, NOW()) AS creation_time,
        IFNULL(tt.error_count, 0) error_count,
        IFNULL(tt.user_exam_count, 0) AS user_exam_count,
        IFNULL(tt.accuracy, 0)  accuracy,
        IFNULL(tt.use_time_count, 0) use_time_count,
        tt.id_card,
        tt.user_name,
        tt.user_phone,
        tt.user_id,
        IFNULL(tt.organization_id, 0) AS organization_id
        FROM  t_d_exam tde
        LEFT JOIN (
        SELECT tdeur.*
        FROM   t_d_exam_user_record  AS tdeur
        WHERE  tdeur.user_id = #{userId}
        and tdeur.lesson_id in
        <foreach item="lessonId" index="index" collection="lessonIds.split(',')" open="(" separator="," close=")">
            #{lessonId}
        </foreach>
        AND tdeur.is_valid = 1
        AND tdeur.id IN (SELECT IFNULL(MAX(t.id), 0) AS ids
        FROM   t_d_exam_user_record t
        WHERE  t.user_id = #{userId} AND t.is_valid = 1
        GROUP BY t.Lesson_id)) tt
        ON tde.id = tt.exam_id
        LEFT JOIN t_d_exam_lesson tdel ON tde.id = tdel.exam_id AND tde.is_valid = 1
        WHERE
        tde.id IN (
        SELECT tdel.exam_id
        FROM t_d_exam_lesson tdel
        WHERE
        tdel.lesson_id in
        <foreach item="lessonId" index="index" collection="lessonIds.split(',')" open="(" separator="," close=")">
            #{lessonId}
        </foreach>
        )
    </select>
    <select id="selectExamFaceRecord" resultType="com.fzkj.project.system.vo.ExamFaceRecordVO">
        select ex.user_id,photo_url
        from t_d_exam_user_record ex
        where ex.id in
        (select max(t.id) from t_d_exam_user_record t
            where t.is_valid =1 and t.lesson_id = #{lessonId} and t.score>=t.pass_mark
        <if test="departIds != null and departIds.size > 0">
            AND t.depart_id IN
            <foreach item="id" collection="departIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="companyId != null">
            and t.organization_id = #{companyId}
        </if>
        group by t.user_id ,t.lesson_id )
    </select>

    <sql id="tempTable">
        SELECT tdeur.*
        FROM t_d_exam_user_record AS tdeur
        WHERE tdeur.user_id = #{userId}
        AND tdeur.lesson_id IN
        <foreach item="lessonId" index="index" collection="lessonIds" open="(" separator="," close=")">
            #{lessonId}
        </foreach>

        AND tdeur.is_valid = 1
        AND tdeur.id IN (
        SELECT IFNULL(MAX(t.id), 0) AS ids
        FROM t_d_exam_user_record t
        WHERE t.user_id = #{userId} AND t.is_valid = 1
        GROUP BY t.lesson_id
        )
    </sql>

</mapper>