package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class DepartTrainDataStaticsVO {
    @JsonProperty("DepartID")
    private Long departId;

    @JsonProperty("DepartName")
    private String departName;

    @ApiModelProperty(value = "总学员数量")
    @NotNull(message = "总学员数量不能为空")
    @JsonProperty("TotalUserNum")
    private long totalUserNum;

    @ApiModelProperty(value = "完成学员数量")
    @JsonProperty("CXUserNum")
    private long cxUserNum;

    @ApiModelProperty(value = "未完成学员数量")
    @JsonProperty("WCUserNum")
    private long wcUserNum;

    @ApiModelProperty(value = "参学率")
    @JsonProperty("CXL")
    private String cxl = "0%";

    @ApiModelProperty(value = "完成率")
    @JsonProperty("WCL")
    private String wcl = "0%";

    @ApiModelProperty(value = "未完成学员数量")
    @JsonProperty("UserInfoList")
    private List userInfoList;
}
