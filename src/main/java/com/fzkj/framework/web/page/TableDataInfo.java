package com.fzkj.framework.web.page;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.filter.Filter;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

import static com.fzkj.framework.web.domain.AjaxResult.filter;
import static com.fzkj.framework.web.domain.AjaxResult.filter1;


/**
 * 表格分页数据对象
 *
 * <AUTHOR>
 */
@Data
public class TableDataInfo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 消息状态码 */
    private int code = 1000;

    /** 消息内容 */
    private String Message="请求成功";

    /** 返回数据对象 */
    private String ResultJson;

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     */
    public TableDataInfo(List<?> list, long total)
    {
        super();
        ResponseData data = new ResponseData();
        data.setRows(list);
        data.setTotal(total);
        this.ResultJson = JSON.toJSONString(data, new Filter[]{filter, filter1}, JSONWriter.Feature.FieldBased, JSONWriter.Feature.WriteNulls);
    }
}
