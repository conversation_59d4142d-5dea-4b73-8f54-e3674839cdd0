package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import com.fzkj.project.system.entity.CommentReply;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class GetMyCommentVO {

    @JsonProperty("rownum")
    private Integer rownum;

    @JsonProperty("ID")
    private Long id;

    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("Grade")
    private String grade;

    @JsonProperty("UserPhoto")
    private String userPhoto;

    @JsonProperty("Content")
    private String content;

    @JsonProperty("CreationTime")
    private Date creationTime;

    @JsonProperty("FunType")
    private String funType;

    @JsonProperty("IsReply")
    private Integer isReply;

    @JsonProperty("ReplyContent")
    private String replyContent;

    @JsonProperty("TargetName")
    private String targetName;

    @JsonProperty("ContentTitle11")
    private String contentTitle11;

    @JsonProperty("ContentTitle10")
    private String contentTitle10;

    @JsonProperty("TargetID")
    private Long targetId;

    @JsonProperty("ReplyInfoList")
    private List<CommentReply> replyInfoList;

}