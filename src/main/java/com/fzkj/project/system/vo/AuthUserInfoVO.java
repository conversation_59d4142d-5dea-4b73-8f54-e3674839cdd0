package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AuthUserInfoVO {
    @JsonProperty("ID")
    private Long id;

    @JsonProperty("UserCode")
    private String userCode;

    @JsonProperty("UserPhoto")
    private String userPhoto;

    @JsonProperty("IDCard")
    private String idCard;

    @JsonProperty("Phone")
    private String phone;

    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("CompanyID")
    private Long companyId;

    @JsonProperty("OrganizationID")
    private Long organizationId;

    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("DepartID")
    private Long departID;

    @JsonProperty("DepartName")
    private String departName;

    @JsonProperty("JobNo")
    private String jobNo;

    @JsonProperty("WorkID")
    private Long workID;

    @JsonProperty("WorkName")
    private String workName;

    @JsonProperty("SignUrl")
    private String signUrl;

    @JsonProperty("TaxpayerNumber")
    private String taxpayerNumber;

    @JsonProperty("IsOpenAlbum")
    private Integer isOpenAlbum;

    @JsonProperty("IsActivate")
    private Integer isActivate;

    @JsonProperty("CompanyAutoSign")
    private String companyAutoSign;

    @JsonProperty("ReviseTimePwd")
    private String reviseTimePwd;

    @JsonProperty("ValidPwdStatus")
    private Integer validPwdStatus;
}
