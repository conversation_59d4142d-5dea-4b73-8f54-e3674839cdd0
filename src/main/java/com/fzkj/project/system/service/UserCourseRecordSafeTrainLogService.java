package com.fzkj.project.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.UserCourseRecordSafeTrainLog;
import com.fzkj.project.system.request.AddUserCourseLogRequest;
import com.fzkj.project.system.request.GetUserCourseStudyTimeRequest;
import com.fzkj.project.system.request.UserCourseRecordSafeTrainLogRequest;
import com.fzkj.project.system.vo.GetUserCourseStudyTimeVO;
import com.fzkj.project.system.vo.UserCourseRecordSafeTrainLogVO;

import java.util.List;

/**
 * <p>
 * 安全培训课件分发学员学习记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface UserCourseRecordSafeTrainLogService extends IService<UserCourseRecordSafeTrainLog> {

    List<UserCourseRecordSafeTrainLog> listUserCourseRecordSafeTrainLog(UserCourseRecordSafeTrainLogRequest request);

    PageUtils listByPage(UserCourseRecordSafeTrainLogRequest request, PageEntity pageEntity);

    UserCourseRecordSafeTrainLogVO findUserCourseRecordSafeTrainLog(Long id);

    Long saveUserCourseRecordSafeTrainLog(UserCourseRecordSafeTrainLogVO vo);

    boolean updateUserCourseRecordSafeTrainLog(UserCourseRecordSafeTrainLogVO vo);

    boolean removeUserCourseRecordSafeTrainLog(Long id);

    boolean addUserCourseLog(AddUserCourseLogRequest request);

    GetUserCourseStudyTimeVO getUserCourseStudyTime(GetUserCourseStudyTimeRequest request);
}
