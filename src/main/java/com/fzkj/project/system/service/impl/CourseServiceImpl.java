package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.mapper.CourseMapper;
import com.fzkj.project.system.mapper.LessonCourseSafeTrainMapper;
import com.fzkj.project.system.mapper.LessonSafeTrainMapper;
import com.fzkj.project.system.mongo.UserCourseRecordSafeTrain;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.*;
import com.fzkj.project.system.vo.*;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 课件信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements CourseService {
    private final UserCourseRecordSafeTrainMongoService userCourseRecordSafeTrainMongoService;
    private final UserCourseRecordSafeTrainLogService userCourseRecordSafeTrainLogService;
    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;
    private final LessonSafeTrainMapper lessonSafeTrainMapper;
    private final LessonCourseSafeTrainMapper lessonCourseSafeTrainMapper;
    private final UserCompanyService userCompanyService;

    @Override
    public List<Course> listCourse(CourseRequest request) {
        Course entity = (Course) DataTransfer.transfer(request, Course.class);
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(CourseQueryRequest request, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<CourseQueryVO> list = getBaseMapper().selectCourseInfo(request);
        if(CollectionUtils.isNotEmpty(list)){
            for (CourseQueryVO courseQueryVO : list) {
                courseQueryVO.setLearnNum(String.valueOf(userCourseRecordSafeTrainMongoService.getLearnNum(Arrays.asList(courseQueryVO.getId()))));
            }
        }
        return new PageUtils(pageEntity, list);
    }

    @Override
    public CourseVO findCourse(Long id) {
        Course entity = this.getById(id);
        CourseVO vo = (CourseVO) DataTransfer.transfer(entity, CourseVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCourse(CourseVO vo) {
        Course entity = (Course) DataTransfer.transfer(vo, Course.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCourse(CourseVO vo) {
        Course entity = (Course) DataTransfer.transfer(vo, Course.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCourse(Long id) {
        return this.removeById(id);
    }

    @Override
    public CourseStatisticsVO courseStatistics() {
        CourseStatisticsVO courseStatisticsVO = getBaseMapper().courseStatistics();
        LambdaQueryWrapper<Course> courseLambdaQueryWrapper = new LambdaQueryWrapper<>();
        courseLambdaQueryWrapper.eq(Course::getIsValid, 1);
        courseLambdaQueryWrapper.eq(Course::getBelongPlat, 1);
        List<Course> courses = this.list(courseLambdaQueryWrapper);
        List<Long> ids = courses.stream().map(Course::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(ids)){
            courseStatisticsVO.setLearnNum((int) userCourseRecordSafeTrainMongoService.getLearnNum(ids));
        }
        return courseStatisticsVO;
    }

    @Override
    public PageUtils getCourse(CourseQueryRequest request, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<CourseVO> list = getBaseMapper().getCourse(request);
        return new PageUtils(pageEntity, list);
    }

    @Override
    public List<GetCourseListVO> getCourseList(GetCourseListRequest request) {
        List<GetCourseListVO> vos = new ArrayList<>();
        UserCourseRecordSafeTrain queryRequest = new UserCourseRecordSafeTrain();
        queryRequest.setUserCode(request.getUserCode());
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(request.getLessonId());
        List<UserCourseRecordSafeTrain> userCourseRecordSafeTrains = userCourseRecordSafeTrainMongoService.getUserCourse(queryRequest);
        //查询课程信息
        LessonSafeTrain lessonSafeTrain = lessonSafeTrainMapper.selectById(request.getLessonId());
        if (lessonSafeTrain.getShape() != 3) {
            vos = getBaseMapper().selectCourseList(request);
            for (GetCourseListVO vo : vos) {
                List<UserCourseRecordSafeTrain> courseRecordSafeTrains = userCourseRecordSafeTrains.stream().filter(a -> a.getUserCode().equals(request.getUserCode()) && a.getLessonId().equals(request.getLessonId()) && a.getCourseId().equals(vo.getCourseId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(courseRecordSafeTrains)) {
                    vo.setIsComplete(String.valueOf(courseRecordSafeTrains.get(0).getCourseIsComplete()));
                } else {
                    vo.setIsComplete("-3");
                }
            }
        }
        return vos;
    }

    @Override
    public boolean editCompleteCourse(EditCompleteCourseRequest request) {
        UserCourseRecordSafeTrain queryRequest = new UserCourseRecordSafeTrain();
        queryRequest.setUserCode(request.getUserCode());
        queryRequest.setCourseId(request.getCourseId());
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(request.getLessonId());
        List<UserCourseRecordSafeTrain> list = userCourseRecordSafeTrainMongoService.getUserCourse(queryRequest);
        if (CollectionUtils.isNotEmpty(list)) {
            UserCourseRecordSafeTrain record = list.get(0);
            //计算当前时间和学习开始时间的时间间隔
            String startStudyTime = record.getStartStudyTime();
            long seconds = 0;
            try {
                Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startStudyTime);
                seconds = Math.abs(System.currentTimeMillis() - date.getTime() / 1000);
            } catch (Exception e) {
                log.error("学习开始时间转换错误", e);
                throw new CustomException("学习未开始");
            }
            int picNum = 0;
            String[] faceImages = record.getFaceDistinguishImg().split(",");
            for (int i = 0; i < faceImages.length; i++) {
                if (StringUtils.isNotEmpty(faceImages[i])) {
                    picNum++;
                }
            }
            String[] faceString = record.getFaceRecognitionString().split(",");
            //暂时屏蔽人脸识别校验
            if (seconds < record.getTimeCount() || (StringUtils.isNotEmpty(faceString[0]) && picNum <= 0 && record.getFileType() != 2)) {
                throw new CustomException(1002, "请认真学习课件");
            }
            UserLessonRecordSafeTrain queryRequest2 = new UserLessonRecordSafeTrain();
            queryRequest2.setUserCode(request.getUserCode());
            queryRequest2.setIsValid(1);
            queryRequest2.setLessonId(request.getLessonId());
            List<UserLessonRecordSafeTrain> lessonList = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest2);
            if (CollectionUtils.isNotEmpty(lessonList)) {
                UserLessonRecordSafeTrain lessonUser = lessonList.get(0);
                int isComplete = 1;//正常开始正常结束
                String now = DateUtils.getCurrentTime();
                if (record.getCourseIsComplete() == 0 && (lessonUser.getTrainEndTime() + " 23:59:59").compareTo(now) < 0) {
                    isComplete = 2;//正常开始过期结束
                } else if (record.getCourseIsComplete() == 3) {
                    isComplete = 4;
                }
                request.setIsComplete(isComplete);
                int update = userCourseRecordSafeTrainMongoService.completeUserCourse(request);
                if (update > 0) {
                    long completeNum = userCourseRecordSafeTrainMongoService.selectCompleteNum(request);
                    LambdaQueryWrapper<LessonCourseSafeTrain> lambdaQueryWrapper = Wrappers.lambdaQuery(LessonCourseSafeTrain.class)
                            .eq(LessonCourseSafeTrain::getLessonId, request.getLessonId())
                            .eq(LessonCourseSafeTrain::getIsValid, 1);
                    long courseNum = lessonCourseSafeTrainMapper.selectCount(lambdaQueryWrapper);
                    if (!request.getIsSign().equals("1") && courseNum == completeNum) {
                        isComplete = 1;
                        if (lessonUser.getIsComplete() == 0 && (lessonUser.getTrainEndTime() + " 23:59:59").compareTo(now) < 0) {
                            isComplete = 2;
                        } else if (lessonUser.getIsComplete() == 3) {
                            isComplete = 4;
                        }
                        if (null != lessonUser.getExamId() && lessonUser.getExamId() > 0) {
                            lessonUser.setIsComplete(isComplete);
                            lessonUser.setCompleteTime(now);
                            userLessonRecordSafeTrainMongoService.updateUserLessonById(lessonUser);
                        } else {
                            lessonUser.setIsComplete(isComplete);
                            lessonUser.setStaticStatus(isComplete);
                            lessonUser.setCompleteTime(now);
                            userLessonRecordSafeTrainMongoService.updateUserLessonById(lessonUser);
                        }
                    }
                } else {
                    throw new CustomException(1004, "课件完成状态变更失败，请刷新后重试");
                }
            } else {
                throw new CustomException(1003, "课程已回收，请联系管理员");
            }
        } else {
            throw new CustomException(1003, "未查询到需要更改的课件信息，请刷新后重新");
        }
        return true;
    }

    @Override
    public PageUtils getCommentAPP(GetCommentAPPRequest request, PageEntity pageEntity) {
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        List<GetCommentAPPVO> list = getBaseMapper().selectCommentAPP(request);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> commentIds = list.stream().map(GetCommentAPPVO::getId).collect(Collectors.toList());
            List<CommentReply> replyList = getBaseMapper().selectCommentReplayList(commentIds);
            list.forEach(item -> {
                item.setReplyInfoList(replyList.stream().filter(reply -> reply.getTargetId().equals(item.getId())).collect(Collectors.toList()));
            });
        }
        return new PageUtils(pageEntity, list);
    }

    @Override
    public List<GetUserCourseDetailVO> getUserCourseDetail(GetUserCourseDetailRequest request) {
        List<GetUserCourseDetailVO> vos = new ArrayList<>();
        UserCourseRecordSafeTrain queryRequest = new UserCourseRecordSafeTrain();
        queryRequest.setUserCode(request.getUserCode());
        queryRequest.setIsValid(1);
        queryRequest.setCourseId(request.getCourseId());
        queryRequest.setLessonId(request.getLessonId());
        List<UserCourseRecordSafeTrain> userCourseRecordSafeTrainList = userCourseRecordSafeTrainMongoService.getUserCourse(queryRequest);
        //查询学习人数
        int learnNum = userCourseRecordSafeTrainMongoService.selectLearnNum(request.getCourseId());
        //查询当前用户学习记录
        List<GetUserCourseDetailVO> userCourseDetailVOList = getBaseMapper().selectUserCourseDetail(request);
        if (CollectionUtils.isNotEmpty(userCourseDetailVOList)) {
            GetUserCourseDetailVO getUserCourseDetailVO = userCourseDetailVOList.get(0);
            getUserCourseDetailVO.setLearnNum(String.valueOf(learnNum));
            getUserCourseDetailVO.setIsComplete("-3");
            getUserCourseDetailVO.setLogTime(5);
            getUserCourseDetailVO.setFaceDistinguishImg("");
            getUserCourseDetailVO.setFaceRecognitionString("");
            if (CollectionUtils.isNotEmpty(userCourseRecordSafeTrainList)) {
                UserCourseRecordSafeTrain userCourseRecordSafeTrain = userCourseRecordSafeTrainList.get(0);
                getUserCourseDetailVO.setIsComplete(String.valueOf(userCourseRecordSafeTrain.getCourseIsComplete()));
                getUserCourseDetailVO.setFaceDistinguishImg(userCourseRecordSafeTrain.getFaceDistinguishImg());
                getUserCourseDetailVO.setFaceRecognitionString(userCourseRecordSafeTrain.getFaceRecognitionString());
            }
            return userCourseDetailVOList;
        }
        return vos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String editUserCourseFirst(EditUserCourseFirstRequest request) {
        request.setUserCode(SecurityUtils.getLoginUser().getUser().getUserCode());
        LocalDateTime reviseTime = LocalDateTime.now();
        String now = DateUtils.getCurrentTime();
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCode(request.getUserCode());
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(request.getLessonId());
        List<UserLessonRecordSafeTrain> lessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        int isComplete = 0;//正常学习中
        UserLessonRecordSafeTrain lesson = null;
        if (CollectionUtils.isNotEmpty(lessonRecordSafeTrains)) {
            lesson = lessonRecordSafeTrains.get(0);
            if ((lesson.getTrainEndTime() + " 23:59:59").compareTo(now) < 0) {
                isComplete = 3;//过期学习中
            }
            if (lesson.getIsComplete() == -3) {
                lesson.setReviseTime(reviseTime);
                lesson.setIsComplete(isComplete);
                lesson.setStaticStatus(isComplete);
                lesson.setStartStudyTime(now);
                userLessonRecordSafeTrainMongoService.updateUserLessonById(lesson);
            }
        } else {
            throw new CustomException(1002, "课程已回收，请联系管理员");
        }
        UserCourseRecordSafeTrain queryRequest2 = new UserCourseRecordSafeTrain();
        queryRequest2.setUserCode(request.getUserCode());
        queryRequest2.setCourseId(request.getCourseId());
        queryRequest2.setLessonId(request.getLessonId());
        List<UserCourseRecordSafeTrain> courseRecordSafeTrains = userCourseRecordSafeTrainMongoService.getUserCourse(queryRequest2);
        //获取用户头像
        UserInfo userInfo = new UserInfo();
        userInfo.setUserCode(request.getUserCode());
        UserCompany userCompany = userCompanyService.selectUserCompany(userInfo);
        String userPhoto = null != userCompany ? userCompany.getUserPhoto() : "";
        if (CollectionUtils.isEmpty(courseRecordSafeTrains)) {
            List<LessonCourseSafeTrainInfoVO> courseSafeTrainInfoVOS = getBaseMapper().selectLessonCourseSafeTrainInfo(request);
            if (CollectionUtils.isNotEmpty(courseSafeTrainInfoVOS)) {
                LessonCourseSafeTrainInfoVO course = courseSafeTrainInfoVOS.get(0);
                UserCourseRecordSafeTrain courseRecordSafeTrain = new UserCourseRecordSafeTrain();
                courseRecordSafeTrain.setCourseIsComplete(isComplete);
                courseRecordSafeTrain.setFaceDistinguishImg("");
                courseRecordSafeTrain.setReviseTime(reviseTime);
                courseRecordSafeTrain.setCreationTime(reviseTime);
                courseRecordSafeTrain.setCreatorCode(request.getUserCode());
                courseRecordSafeTrain.setStudyTimeCount(0L);
                courseRecordSafeTrain.setStartStudyTime(now);
                courseRecordSafeTrain.setIsValid(1);
                courseRecordSafeTrain.setUserCode(request.getUserCode());
                courseRecordSafeTrain.setCompleteTime("");
                String targetTables = StringUtils.EMPTY;
                int verifyNumber = course.getCoursFaceNum();
                int courseTimeCount = course.getTimeCount();
                if (course.getStudyFaceState() == 1 && verifyNumber > 0 && courseTimeCount > 0) {
                    int avgNum = courseTimeCount / verifyNumber;
                    int recCount = 1;
                    if (avgNum > 0) {
                        int targetTable = 0;
                        Random random = new Random();
                        while (recCount <= verifyNumber) {
                            targetTable = random.nextInt(avgNum) + avgNum * (recCount - 1);
                            recCount++;
                            targetTables += targetTable + ",";
                        }
                    }
                }
                if (targetTables.endsWith(",")) {
                    targetTables = targetTables.substring(0, targetTables.length() - 1);
                }
                request.setFaceRecognitionString(targetTables);
                courseRecordSafeTrain.setFaceRecognitionString(targetTables);
                courseRecordSafeTrain.setCompanyId(lesson.getCompanyId());
                courseRecordSafeTrain.setCompanyName(lesson.getCompanyName());
                courseRecordSafeTrain.setDepartId(lesson.getDepartId());
                courseRecordSafeTrain.setWorkId(lesson.getWorkId());
                courseRecordSafeTrain.setHandMode(lesson.getHandMode());
                courseRecordSafeTrain.setDisSource(lesson.getDisSource());
                courseRecordSafeTrain.setLessonCategoryId(lesson.getLessonCategoryId());
                courseRecordSafeTrain.setLessonId(lesson.getLessonId());
                courseRecordSafeTrain.setIsStatic(lesson.getIsStatic());
                courseRecordSafeTrain.setFileUrl(course.getFileUrl());
                courseRecordSafeTrain.setFileType(course.getFileType());
                courseRecordSafeTrain.setCourseName(course.getCourseName());
                courseRecordSafeTrain.setCourseId(request.getCourseId());
                courseRecordSafeTrain.setTimeCount(Long.valueOf(course.getTimeCount()));
                if (course.getStudyFaceState() == 1 && verifyNumber > 0 && StringUtils.isEmpty(request.getFaceImgUrl())) {
                    request.setFaceImgUrl(userPhoto);
                }
                courseRecordSafeTrain.setFaceDistinguishImg(request.getFaceImgUrl());
                userCourseRecordSafeTrainMongoService.save(courseRecordSafeTrain);
            } else {
                throw new CustomException(1003, "没有找到相关的课件信息，请刷新后重试");
            }
        } else {
            UserCourseRecordSafeTrain userCourseRecordSafeTrain = courseRecordSafeTrains.get(0);
            if (userCourseRecordSafeTrain.getIsValid() == 0) {
                throw new CustomException(1003, "课件已回收");
            } else if (userCourseRecordSafeTrain.getCourseIsComplete() == -3) {
                String[] faceString = userCourseRecordSafeTrain.getFaceRecognitionString().split(",");
                if (StringUtils.isNotEmpty(faceString[0]) && StringUtils.isEmpty(request.getFaceImgUrl())) {
                    request.setFaceImgUrl(userPhoto);
                }
                userCourseRecordSafeTrain.setFaceDistinguishImg(request.getFaceImgUrl());
                userCourseRecordSafeTrain.setCourseIsComplete(isComplete);
                userCourseRecordSafeTrain.setReviseTime(reviseTime);
                userCourseRecordSafeTrain.setStartStudyTime(now);
                userCourseRecordSafeTrainMongoService.updateUserCourse(userCourseRecordSafeTrain);
                return userCourseRecordSafeTrain.getFaceRecognitionString();
            }
        }
        return request.getFaceRecognitionString();
    }

    @Override
    public int textModeration(String text) {
        return getBaseMapper().selectTextModerationCount(text);
    }

    @Override
    public boolean editUserCourseFaceImg(EditUserCourseFaceImgRequest request) {
        UserCourseRecordSafeTrain queryRequest = new UserCourseRecordSafeTrain();
        queryRequest.setLessonId(request.getLessonId());
        queryRequest.setCourseId(request.getCourseId());
        queryRequest.setUserCode(request.getUserCode());
        List<UserCourseRecordSafeTrain> userCourses = userCourseRecordSafeTrainMongoService.getUserCourse(queryRequest);
        if (CollectionUtils.isNotEmpty(userCourses)) {
            UserCourseRecordSafeTrain userCourse = userCourses.get(0);
            if (userCourse.getIsValid() == 0) {
                throw new CustomException(1003, "课件已回收");
            } else {
                String faceUrl = StringUtils.isBlank(userCourse.getFaceDistinguishImg()) ? request.getFaceImgUrl() : userCourse.getFaceDistinguishImg() + "," + request.getFaceImgUrl();
                userCourse.setFaceDistinguishImg(faceUrl);
                userCourse.setReviseTime(LocalDateTime.now());
                return userCourseRecordSafeTrainMongoService.updateUserCourse(userCourse);
            }
        }
        return true;
    }

    @Override
    public boolean CheckCourseCode(EditCompleteCourseRequest request) {
        List<String> list = lessonCourseSafeTrainMapper.checkCourseCode(request.getCourseCode());
        if (ObjectUtils.isEmpty(list)){
            return true;
        }
        return false;
    }
}