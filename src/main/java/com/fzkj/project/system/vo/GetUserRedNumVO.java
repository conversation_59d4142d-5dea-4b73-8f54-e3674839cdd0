package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetUserRedNumVO {
    public GetUserRedNumVO() {
    }

    public GetUserRedNumVO(Integer id, int column1) {
        this.id = id;
        this.column1 = column1;
    }

    @ApiModelProperty(value = "ID", example = "3190846")
    @JsonProperty("ID")
    private Integer id;

    @ApiModelProperty(value = "Column1", example = "0")
    @JsonProperty("Column1")
    private int column1;
}
