package com.fzkj.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fzkj.framework.web.domain.PageEntity;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 分页工具类
 */
public class PageUtils implements Serializable {
    private static final long serialVersionUID = -792244239909064922L;
    /**
     * 总记录数
     */
    private int totalCount;
    /**
     * 每页记录数
     */
    private int pageSize;
    /**
     * 总页数
     */
    private int totalPage;
    /**
     * 当前页数
     */
    private int currPage;
    /**
     * 列表数据
     */
    private List<?> list;

    /**
     * 分页
     *
     * @param list       列表数据
     * @param totalCount 总记录数
     * @param pageSize   每页记录数
     * @param currPage   当前页数
     */
    public PageUtils(List<?> list, int totalCount, int pageSize, int currPage) {
        this.list = list;
        this.totalCount = totalCount;
        this.pageSize = pageSize;
        this.currPage = currPage;
        this.totalPage = (int) Math.ceil((double) totalCount / pageSize);
    }

    /**
     * 分页
     */
    public PageUtils(IPage<?> page) {
        this.list = page.getRecords();
        this.totalCount = (int) page.getTotal();
        this.pageSize = (int) page.getSize();
        this.currPage = (int) page.getCurrent();
        this.totalPage = (int) page.getPages();
    }

    public PageUtils(List list) {
        this.list = list;
        if (CollectionUtils.isNotEmpty(list)) {
            this.totalCount = list.size();
        }
    }

    public PageUtils(PageEntity pageEntity, List list) {
        if (null == pageEntity) {
            pageEntity = new PageEntity();
        }
        PageInfo pageInfo = new PageInfo(list);
        this.list = list;
        this.totalCount = (int) pageInfo.getTotal();
        this.pageSize = pageEntity.getPageSize();
        this.currPage = pageEntity.getPageIndex();
        this.totalPage = pageInfo.getPages();
    }

    public PageUtils(PageEntity pageEntity, List list, List data) {
        if (null == pageEntity) {
            pageEntity = new PageEntity();
        }
        PageInfo pageInfo = new PageInfo(list);
        this.list = data;
        this.totalCount = (int) pageInfo.getTotal();
        this.pageSize = pageEntity.getPageSize();
        this.currPage = pageEntity.getPageIndex();
        this.totalPage = pageInfo.getPages();
    }

    public static PageUtils page(PageEntity pageEntity, List list) {
        PageUtils pageUtils = new PageUtils(list);
        if (null != pageEntity) {
            int size = list.size();
            int startIndex = (pageEntity.getPageIndex() - 1) * pageEntity.getPageSize();
            int endIndex = (pageEntity.getPageIndex()) * pageEntity.getPageSize();
            if (size > 0 && startIndex < size) {
                if (endIndex > size) {
                    endIndex = size;
                }
                list = list.subList(startIndex, endIndex);
            }
            pageUtils = new PageUtils(pageEntity, list);
            pageUtils.setTotalCount(size);
            return pageUtils;
        }
        return pageUtils;
    }

    public Page getPage() {
        Page page = new Page();
        page.addAll(list);
        page.setPageNum(this.currPage);
        page.setPageSize(this.pageSize);
        page.setPages(this.totalPage);
        page.setTotal(this.totalCount);
        return page;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getCurrPage() {
        return currPage;
    }

    public void setCurrPage(int currPage) {
        this.currPage = currPage;
    }

    public List<?> getList() {
        return list;
    }

    public void setList(List<?> list) {
        this.list = list;
    }

}
