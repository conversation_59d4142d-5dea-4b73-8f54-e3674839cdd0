package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 公交培训计划
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="公交培训计划VO")
public class LessonSafeTrainPlanVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "培训计划名称")
    @NotNull(message = "培训计划名称不能为空")
    @JsonProperty("PlanName")
    @Excel(name = "培训名称",order = 1)
    private String planName;

    @ApiModelProperty(value = "培训月份")
    @NotNull(message = "培训月份不能为空")
    @JsonProperty("MonthStr")
    @Excel(name = "月份",order = 2)
    private String monthStr;

    @ApiModelProperty(value = "来源")
    @NotNull(message = "来源不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @NotNull(message = "创建人ID不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @NotNull(message = "修改人ID不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    @NotNull(message = "备注信息不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否 默认有效")
    @NotNull(message = "是否有效 1：是， 0：否 默认有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1")
    @NotNull(message = "预留字段1不能为空")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    @NotNull(message = "预留字段2不能为空")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    @NotNull(message = "预留字段3不能为空")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @NotNull(message = "预留字段4不能为空")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @NotNull(message = "预留字段5不能为空")
    @JsonProperty("ReservedField5")
    private String reservedField5;

    @ApiModelProperty(value = "操作标识")
    @NotNull(message = "操作标识不能为空")
    @JsonProperty("Flag")
    private String flag;

    @ApiModelProperty(value = "课程数量")
    @NotNull(message = "课程数量不能为空")
    @JsonProperty("LessonNum")
    @Excel(name = "课程数量",order = 3)
    private Integer lessonNum;

    @ApiModelProperty(value = "总学员数量")
    @NotNull(message = "总学员数量不能为空")
    @JsonProperty("TotalUserNum")
    @Excel(name = "总数量",order = 4)
    private int totalUserNum;

    @ApiModelProperty(value = "参学学员数量")
    @JsonProperty("CXUserNum")
    @Excel(name = "参学人数",order = 5)
    private int cxUserNum;

    @ApiModelProperty(value = "完成学员数量")
    @JsonProperty("WCUserNum")
    @Excel(name = "完成人数",order = 6)
    private int wcUserNum;

    @ApiModelProperty(value = "参学率")
    @JsonProperty("CXL")
    @Excel(name = "参学率",order = 7)
    private String cxl = "0%";

    @ApiModelProperty(value = "完成率")
    @JsonProperty("WCL")
    @Excel(name = "完成率",order = 8)
    private String wcl = "0%";

}