package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AuthFaceRecognitionRequest {
    
    @ApiModelProperty(value = "用户编码", example = "b135339037ae410a87ad42c872c9353f")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "课程ID", example = "3829")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "部门ID", example = "50")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "课程ID", example = "4958")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty(value = "人脸类型", example = "1")
    @JsonProperty("FaceType")
    private int faceType;

    @ApiModelProperty(value = "原始照片URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/mini/avatar/1665667965183.jpg")
    @JsonProperty("OriginalPhotos")
    private String originalPhotos;

    @ApiModelProperty(value = "对比照片URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2024-4/image/171195797661178201.jpg")
    @JsonProperty("ComparisonPhotos")
    private String comparisonPhotos;

    @ApiModelProperty(value = "培训类型", example = "aqpx")
    @JsonProperty("TrainType")
    private String trainType;

    @ApiModelProperty(value = "计划ID", example = "-999")
    @JsonProperty("PlanID")
    private String planId;

}
