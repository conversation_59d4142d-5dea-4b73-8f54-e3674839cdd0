package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 评论表
 *
 */
@Data
@TableName("t_d_comment_reply")
@ApiModel(value = "评论回复表实体")
public class CommentReply implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @JsonProperty("FunType")
    @ApiModelProperty(value = "功能模块（10：课件，11：内容）")
    private String funType;

    @JsonProperty("TargetID")
    @ApiModelProperty(value = "目标id(如课件ID、内容ID)")
    private Long targetId;

    @ApiModelProperty(value = "用户姓名")
    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("ReplyContent")
    @ApiModelProperty(value = "回复内容")
    private String replyContent;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注信息")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效 1是 0否 默认有效")
    private Integer isValid;

    @JsonProperty("Type")
    @ApiModelProperty(value = "回复类型（1：客服回复，2：讲师回复）")
    private Integer type;

}