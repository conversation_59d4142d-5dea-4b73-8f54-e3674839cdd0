package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.entity.PlatformRoleMenu;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.request.PlatformMenuRequest;
import com.fzkj.project.system.service.PlatformMenuService;
import com.fzkj.project.system.service.PlatformRoleMenuService;
import com.fzkj.project.system.vo.PlatformMenuVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 角色菜单关联
 * </p>
 *
 */
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userPort")
public class PlatformRoleMenuController extends BaseEntity {

    private final PlatformRoleMenuService platformRoleMenuService;

//    @PostMapping("GetRole")
//    @ApiOperation(value = "列表查询")
//    public AjaxResult listPlatformMenu(@RequestBody CommonRequest commonRequest) {
//        PlatformRoleMenu request = (PlatformRoleMenu) commonRequest.getRequest(PlatformRoleMenu.class);
//        return AjaxResult.success(platformRoleMenuService.selectPlatformRoleMenuList(request, commonRequest.getPageEntity()));
//    }

//    @GetMapping("{id}")
//    @ApiOperation(value = "详情")
//    public AjaxResult findPlatformMenu(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(platformRoleMenuService.selectPlatformRoleMenuById(request.getId()));
//    }

    @PostMapping("EditRoleMenu")
    @ApiOperation(value = "修改权限")
    public AjaxResult EditRoleMenu(@RequestBody CommonRequest commonRequest) {
        PlatformRoleMenu vo = (PlatformRoleMenu) commonRequest.getRequest(PlatformRoleMenu.class);
        return AjaxResult.success(platformRoleMenuService.updateRoleMenu(vo));
    }

//    @PostMapping("delete")
//    @ApiOperation(value = "平台菜单删除数据")
//    public AjaxResult removePlatformMenu(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(platformRoleMenuService.deletePlatformRoleMenuById(request.getId()));
//    }

}
