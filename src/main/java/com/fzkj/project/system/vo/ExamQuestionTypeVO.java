package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 试题类型表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value="试题类型表VO")
public class ExamQuestionTypeVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "试题类型ID")
    @JsonProperty("QuestionTypeId")
    private Long questionTypeId;

    @ApiModelProperty(value = "类型名称")
    @NotNull(message = "类型名称不能为空")
    @JsonProperty("TypeName")
    private String typeName;

    @ApiModelProperty(value = "创建者ID")
    @NotNull(message = "创建者ID不能为空")
    @JsonProperty("CreatorID")
    private String creatorId;

    @ApiModelProperty(value = "创建者姓名")
    @NotNull(message = "创建者姓名不能为空")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "创建日期")
    @NotNull(message = "创建日期不能为空")
    @JsonProperty("CreateDate")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新日期")
    @NotNull(message = "更新日期不能为空")
    @JsonProperty("UpdateDate")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "是否有效")
    @NotNull(message = "是否有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("Flag")
    private String flag;
}