package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.LessonCourseSafeTrain;
import com.fzkj.project.system.entity.LessonSafeTrain;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.mapper.LessonCourseSafeTrainMapper;
import com.fzkj.project.system.mapper.LessonSafeTrainMapper;
import com.fzkj.project.system.request.LessonCourseSafeTrainRequest;
import com.fzkj.project.system.service.LessonCourseSafeTrainService;
import com.fzkj.project.system.vo.CourseVO;
import com.fzkj.project.system.vo.LessonCourseSafeTrainVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 安全培训课程课件关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class LessonCourseSafeTrainServiceImpl extends ServiceImpl<LessonCourseSafeTrainMapper, LessonCourseSafeTrain> implements LessonCourseSafeTrainService {

    private final LessonSafeTrainMapper lessonSafeTrainMapper;

    @Override
    public List<LessonCourseSafeTrain> listLessonCourseSafeTrain(LessonCourseSafeTrainRequest request) {
        LessonCourseSafeTrain entity = (LessonCourseSafeTrain) DataTransfer.transfer(request, LessonCourseSafeTrain.class);
        LambdaQueryWrapper<LessonCourseSafeTrain> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(LessonCourseSafeTrainRequest request, PageEntity pageEntity) {
        LessonCourseSafeTrain entity = (LessonCourseSafeTrain) DataTransfer.transfer(request, LessonCourseSafeTrain.class);
        LambdaQueryWrapper<LessonCourseSafeTrain> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<LessonCourseSafeTrain> page = this.page(
                new Query<LessonCourseSafeTrain>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public LessonCourseSafeTrainVO findLessonCourseSafeTrain(Long id) {
        LessonCourseSafeTrain entity = this.getById(id);
        LessonCourseSafeTrainVO vo = (LessonCourseSafeTrainVO) DataTransfer.transfer(entity, LessonCourseSafeTrainVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveLessonCourseSafeTrain(LessonCourseSafeTrainVO vo) {
        LessonCourseSafeTrain entity = (LessonCourseSafeTrain) DataTransfer.transfer(vo, LessonCourseSafeTrain.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLessonCourseSafeTrain(LessonCourseSafeTrainVO vo) {
        LessonCourseSafeTrain entity = (LessonCourseSafeTrain) DataTransfer.transfer(vo, LessonCourseSafeTrain.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    public List<CourseVO> getCourseListByLessonId(Long lessonId) {
        return getBaseMapper().selectCourseListByLessonId(lessonId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeLessonCourseSafeTrain(Long id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editLessonCourseSafeTrain(LessonCourseSafeTrainVO request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LessonCourseSafeTrain entity = (LessonCourseSafeTrain) DataTransfer.transfer(request, LessonCourseSafeTrain.class);
        switch (request.getFlag()) {
            case "edit":
                String courseIDs = request.getCourseIDs();
                if (StringUtils.isEmpty(courseIDs)) {
                    throw new CustomException("请添加课件");
                }
                String[] courseIds = courseIDs.split(",");
                Integer sort = request.getSort();
                if(null == sort){
                    sort = 0;
                }
                for (int i = 0; i < courseIds.length; i++) {
                    String courseIdStr = courseIds[i];
                    if (StringUtils.isEmpty(courseIdStr)) {
                        continue;
                    }
                    Long courseId = Long.valueOf(courseIdStr);
                    LambdaQueryWrapper<LessonCourseSafeTrain> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(LessonCourseSafeTrain::getLessonId, request.getLessonId());
                    queryWrapper.eq(LessonCourseSafeTrain::getCourseId, courseId);
                    int count = this.count(queryWrapper);
                    if (count > 0) {
                        LambdaUpdateWrapper<LessonCourseSafeTrain> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(LessonCourseSafeTrain::getSort, sort);
                        updateWrapper.set(LessonCourseSafeTrain::getIsValid, 1);
                        updateWrapper.set(LessonCourseSafeTrain::getReviseTime, LocalDateTime.now());
                        updateWrapper.eq(LessonCourseSafeTrain::getLessonId, request.getLessonId());
                        updateWrapper.eq(LessonCourseSafeTrain::getCourseId, courseId);
                        this.update(updateWrapper);
                    } else {
                        entity.setId(null);
                        entity.setCourseId(courseId);
                        entity.setSort(sort);
                        entity.setIsValid(1);
                        entity.setCreatorCode(user.getUserCode());
                        entity.setCreationTime(LocalDateTime.now());
                        entity.setReviseCode(user.getUserCode());
                        entity.setReviseTime(LocalDateTime.now());
                        this.save(entity);
                    }
                    sort++;
                }
                LambdaUpdateWrapper<LessonSafeTrain> lessonSafeTrainLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lessonSafeTrainLambdaUpdateWrapper.eq(LessonSafeTrain::getId, request.getLessonId());
                lessonSafeTrainLambdaUpdateWrapper.set(LessonSafeTrain::getIsValid, -1);
                lessonSafeTrainMapper.update(null,lessonSafeTrainLambdaUpdateWrapper);
                return true;
            case "del":
                LambdaUpdateWrapper<LessonCourseSafeTrain> delWrapper = new LambdaUpdateWrapper<>();
                delWrapper.set(LessonCourseSafeTrain::getIsValid, 0);
                delWrapper.set(LessonCourseSafeTrain::getReviseCode, user.getUserCode());
                delWrapper.set(LessonCourseSafeTrain::getReviseTime, LocalDateTime.now());
                delWrapper.eq(LessonCourseSafeTrain::getLessonId, request.getLessonId());
                delWrapper.in(LessonCourseSafeTrain::getCourseId, request.getCourseIDs().split(","));
                return this.update(delWrapper);
        }
        throw new CustomException("不支持的操作标识"+request.getFlag());
    }
}