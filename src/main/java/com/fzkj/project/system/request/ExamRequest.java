package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试信息表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value = "考试信息表Request")
public class ExamRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "考试名称")
    private String examName;

    @ApiModelProperty(value = "课程ID")
    private Long lessonId;

    @ApiModelProperty(value = "企业ID")
    private Long companyId;

    @ApiModelProperty(value = "发布标志")
    private Integer issueFlag;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "有效标志")
    private Integer isValid;

    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @ApiModelProperty(value = "试题ID")
    private Long examQuestionId;

    @ApiModelProperty(value = "试卷类型ID")
    private Long questionTypeId;

    @ApiModelProperty(value = "试卷ID")
    private Long examId;

}