package com.fzkj.project.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.Teacher;
import com.fzkj.project.system.request.TeacherQueryRequest;
import com.fzkj.project.system.request.TeacherRequest;
import com.fzkj.project.system.vo.TeacherVO;

import java.util.List;

/**
 * <p>
 * 讲师 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface TeacherService extends IService<Teacher> {

    List<Teacher> listTeacher(TeacherRequest request);

    PageUtils listTeacher(TeacherQueryRequest request, PageEntity pageEntity);

    String exportTeacher(TeacherQueryRequest request);

    PageUtils listByPage(TeacherRequest request, PageEntity pageEntity);

    TeacherVO findTeacher(Long id);

    Long saveTeacher(TeacherVO vo);

    boolean updateTeacher(TeacherVO vo);

    boolean removeTeacher(Long id);
}
