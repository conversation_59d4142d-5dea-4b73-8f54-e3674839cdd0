package com.fzkj.project.system.controller;

import com.fzkj.common.utils.DateUtils;
import com.fzkj.framework.annotation.ListNoRows;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.aspectj.lang.enums.BusinessType;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.ExamService;
import com.fzkj.project.system.vo.ExamQuestionImportVO;
import com.fzkj.project.system.vo.ExamQuestionVO;
import com.fzkj.project.system.vo.ExamVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <p>
 * 考试信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
@Log
public class ExamController extends BaseEntity {

    private final ExamService ExamServiceImpl;

    @PostMapping("Get_ExamInfo")
    @ApiOperation(value = "课程管理-试卷信息查询")
    public AjaxResult Get_ExamInfo(@RequestBody CommonRequest commonRequest) {
        ExamRequest request = (ExamRequest) commonRequest.getRequest(ExamRequest.class);
        return AjaxResult.success(ExamServiceImpl.getExamInfo(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_ExamInfoQuestion")
    @ApiOperation(value = "课程管理-试卷信息查询（含试题）")
    public AjaxResult Get_ExamInfoQuestion(@RequestBody CommonRequest commonRequest) {
        ExamRequest request = (ExamRequest) commonRequest.getRequest(ExamRequest.class);
        return AjaxResult.success(ExamServiceImpl.getExamInfoQuestion(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_ExamInfoQuestionByPage")
    @ApiOperation(value = "课程管理-试题分页查询")
    public AjaxResult Get_ExamInfoQuestionByPage(@RequestBody CommonRequest commonRequest) {
        ExamRequest request = (ExamRequest) commonRequest.getRequest(ExamRequest.class);
        return AjaxResult.success(ExamServiceImpl.getExamInfoQuestionByPage(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Edit_ExamInfo")
    @ApiOperation(value = "课程管理-试卷编辑")
    public AjaxResult Edit_ExamInfo(@RequestBody CommonRequest commonRequest) {
        ExamVO request = (ExamVO) commonRequest.getRequest(ExamVO.class);
        return AjaxResult.success(ExamServiceImpl.editExamInfo(request));
    }

    @PostMapping("Edit_ExamLesson")
    @ApiOperation(value = "课程管理-课程关联试卷编辑")
    public AjaxResult Edit_ExamLesson(@RequestBody CommonRequest commonRequest) {
        ExamVO request = (ExamVO) commonRequest.getRequest(ExamVO.class);
        return AjaxResult.success(ExamServiceImpl.editExamLesson(request));
    }

    @PostMapping("Edit_ExamQuestion")
    @ApiOperation(value = "课程管理-编辑试题")
    public AjaxResult Edit_ExamQuestion(@RequestBody CommonRequest commonRequest) {
        ExamQuestionVO request = (ExamQuestionVO) commonRequest.getRequest(ExamQuestionVO.class);
        return AjaxResult.success(ExamServiceImpl.editExamQuestion(request));
    }

    @PostMapping("Import_ExamQuestion")
    @ApiOperation(value = "课程管理-批量导入试题")
    public AjaxResult Import_ExamQuestion(@RequestBody CommonRequest commonRequest) {
        ExamQuestionImportVO request = (ExamQuestionImportVO) commonRequest.getRequest(ExamQuestionImportVO.class);
        int question = ExamServiceImpl.importExamQuestion(request);
        return AjaxResult.success(1000, "导入成功 " + question + " 题");
    }

    @PostMapping("Edit_ExamInfoIssueExam")
    @ApiOperation(value = "课程管理-发布试卷")
    @Log(title = "课程管理-发布试卷", businessType = BusinessType.UPDATE)
    public AjaxResult Edit_ExamInfoIssueExam(@RequestBody CommonRequest commonRequest) {
        ExamVO request = (ExamVO) commonRequest.getRequest(ExamVO.class);
        String result = ExamServiceImpl.editExamInfoIssueExam(request);
        if (StringUtils.isEmpty(result)) {
            return AjaxResult.success(1000, "发布成功", -1);
        }
        return AjaxResult.error(1001, result);
    }

    @PostMapping("Get_UserExamInfo")
    @ApiOperation(value = "小程序-获取用户考试信息")
    @ListNoRows
    public AjaxResult Get_UserExamInfo(@RequestBody CommonRequest commonRequest) {
        GetUserExamInfoRequest request = (GetUserExamInfoRequest) commonRequest.getRequest(GetUserExamInfoRequest.class);
        return AjaxResult.success(ExamServiceImpl.getUserExamInfo(request));
    }

    @PostMapping("Get_UserExamInfoRecord")
    @ApiOperation(value = "小程序-获取用户考试信息")
    public AjaxResult Get_UserExamInfoRecord(@RequestBody CommonRequest commonRequest) {
        GetUserExamInfoRecordRequest request = (GetUserExamInfoRecordRequest) commonRequest.getRequest(GetUserExamInfoRecordRequest.class);
        return AjaxResult.success(ExamServiceImpl.getUserExamInfoRecord(request));
    }

    @PostMapping("Edit_ExamSubMitAnswer1")
    @ApiOperation(value = "小程序-提交考试")
    public AjaxResult Edit_ExamSubMitAnswer1(@RequestBody CommonRequest commonRequest) {
        EditExamSubMitAnswer1Request request = (EditExamSubMitAnswer1Request) commonRequest.getRequest(EditExamSubMitAnswer1Request.class);
        return AjaxResult.success(ExamServiceImpl.editExamSubMitAnswer1(request));
    }

    @PostMapping("Auth_PortraitVerification")
    @ApiOperation(value = "小程序-考试完成人脸识别")
    public AjaxResult Auth_PortraitVerification(@RequestBody CommonRequest commonRequest) {
        AuthPortraitVerificationRequest request = (AuthPortraitVerificationRequest) commonRequest.getRequest(AuthPortraitVerificationRequest.class);
        return AjaxResult.success(ExamServiceImpl.authPortraitVerification(request));
    }

    @PostMapping("Get_UserExamDetailRecord")
    @ApiOperation(value = "小程序-获取考试答题记录")
    @ListNoRows
    public AjaxResult Get_UserExamDetailRecord(@RequestBody CommonRequest commonRequest) {
        GetUserExamDetailRecordRequest request = (GetUserExamDetailRecordRequest) commonRequest.getRequest(GetUserExamDetailRecordRequest.class);
        return AjaxResult.success(ExamServiceImpl.getUserExamDetailRecord(request));
    }

    @PostMapping("GetCurrentTime")
    @ApiOperation(value = "小程序-获取当前时间")
    public AjaxResult GetCurrentTime(@RequestBody CommonRequest commonRequest) {
        return AjaxResult.success(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
    }


    @PostMapping("Get_GroupRoll")
    @ApiOperation(value = "小程序-组卷")
    public AjaxResult Get_GroupRoll(@RequestBody CommonRequest commonRequest) {
        GroupRollRequest request = (GroupRollRequest) commonRequest.getRequest(GroupRollRequest.class);
        return AjaxResult.success("组卷成功！", ExamServiceImpl.getGroupRoll(request));
    }

}
