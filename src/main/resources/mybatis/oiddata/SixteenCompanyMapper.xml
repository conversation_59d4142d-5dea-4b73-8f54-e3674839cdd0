<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.olddata.mapper.SixteenCompanyMapper">

    <select id="getSixteenLessonUser" resultType="com.fzkj.project.olddata.dto.request.SixteenLessonResp">
        SELECT
            CompanyName as companyName,
            LessonName as  lessonName,
            CourseName as  courseName,
            CONCAT(DATE_FORMAT(StartTime,'%Y-%m-%d'),'至',DATE_FORMAT(EndTime,'%Y-%m-%d')) studyTime,
            UserName as  userName,
            FaceDistinguishImg as  faceDistinguishImg,
            CompleteTime as  completeTime,
            IsComplete as  isComplete
        FROM
            `teach`.`t_f_user_lesson_record`
        WHERE
            `LessonID` = #{lessonId}
    </select>
</mapper>