package com.fzkj.project.system.service.impl;

import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.project.system.entity.Reminder;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.enums.TrainTypeEnum;
import com.fzkj.project.system.vo.ReminderVO;
import com.fzkj.project.system.request.ReminderRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ReminderMapper;
import com.fzkj.project.system.service.ReminderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 提醒管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-04
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ReminderServiceImpl extends ServiceImpl<ReminderMapper, Reminder> implements ReminderService {

    @Override
    public List<Reminder> listReminder(ReminderRequest request) {
        Reminder entity = (Reminder) DataTransfer.transfer(request, Reminder.class);
        LambdaQueryWrapper<Reminder> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ReminderRequest request, PageEntity pageEntity) {
        Reminder entity = (Reminder) DataTransfer.transfer(request, Reminder.class);
        LambdaQueryWrapper<Reminder> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<Reminder> page = this.page(
                new Query<Reminder>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ReminderVO findReminder(Long id) {
        Reminder entity = this.getById(id);
        ReminderVO vo = (ReminderVO) DataTransfer.transfer(entity, ReminderVO.class);
        return vo;
    }

    @Override
    public List<Reminder> findReminderByTrainType(Integer trainType) {
        LambdaQueryWrapper<Reminder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Reminder::getTrainType, trainType);
        return getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public Reminder findReminderByTrainTypAndRemindType(Integer trainType, Integer remindType) {
        LambdaQueryWrapper<Reminder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Reminder::getTrainType, trainType);
        queryWrapper.eq(Reminder::getRemindType, remindType);
        return getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveReminder(ReminderVO vo) {
        Reminder entity = (Reminder) DataTransfer.transfer(vo, Reminder.class);
        if(null == entity.getTrainType()){
            throw new CustomException("培训类型不能为空");
        }
        if(null == entity.getRemindType()){
            throw new CustomException("提醒类型不能为空");
        }
        Reminder reminder = findReminderByTrainTypAndRemindType(entity.getTrainType(), entity.getRemindType());
        if(null != reminder){
            throw new CustomException("同类型提醒已存在");
        }
        if (null == entity.getTitle()){
            entity.setTitle(TrainTypeEnum.SAFETY_TRAIN.getNameByType(entity.getTrainType()));
        }
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        entity.setCreatorCode(user.getUserCode());
        entity.setCreatorTime(LocalDateTime.now());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateReminder(ReminderVO vo) {
        Reminder entity = (Reminder) DataTransfer.transfer(vo, Reminder.class);
        if(null == entity.getTrainType()){
            throw new CustomException("培训类型不能为空");
        }
        if(null == entity.getRemindType()){
            throw new CustomException("提醒类型不能为空");
        }
        Reminder reminder = findReminderByTrainTypAndRemindType(entity.getTrainType(), entity.getRemindType());
        if(null != reminder && !reminder.getId().equals(entity.getId())){
            throw new CustomException("同类型提醒已存在");
        }
        if (null == entity.getTitle()){
            entity.setTitle(TrainTypeEnum.SAFETY_TRAIN.getNameByType(entity.getTrainType()));
        }
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        entity.setReviseCode(user.getUserCode());
        entity.setReviseTime(LocalDateTime.now());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeReminder(Long id) {
        return this.removeById(id);
    }
}