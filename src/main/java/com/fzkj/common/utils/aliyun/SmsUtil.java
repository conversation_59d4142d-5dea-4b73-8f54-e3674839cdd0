package com.fzkj.common.utils.aliyun;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.common.utils.oss.OSSConst;

public class SmsUtil {
    private static final String ACCESS_KEY_ID = OSSConst.ACCESS_KEY_ID;
    private static final String ACCESS_KEY_SECRET = OSSConst.ACCESS_KEY_SECRET;
    private static final String SIGN_NAME = "驿满新能源";
    private static final String TEMPLATE_CODE = "SMS_483990136";
    public static boolean sendSMS(String mobile, String verificationCode) {
        // 创建一个SendSmsRequest实例
        SendSmsRequest request = new SendSmsRequest();
        request.setPhoneNumbers(mobile);
        request.setSignName(SIGN_NAME);
        request.setTemplateCode(TEMPLATE_CODE);
        request.setTemplateParam("{\"code\":\"" + verificationCode + "\"}");
        // 发送短信
        try {
            SendSmsResponse sendSmsResponse = createClient().sendSmsWithOptions(request, new RuntimeOptions());
            String code = sendSmsResponse.getBody().getCode();
            if (!StringUtils.equals(code, "OK")) {
                throw new CustomException("短信发送失败:" + sendSmsResponse.getBody().getMessage());
            }
            return true;
        } catch (Exception e) {
            throw new CustomException("短信发送失败:" + e.getMessage());
        }
    }

    /**
     * 使用AK&SK初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    private static Client createClient() throws Exception {
        Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(ACCESS_KEY_ID)
                .setAccessKeySecret(ACCESS_KEY_SECRET);
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

}
