package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.LessonSafeTrainCompanyUserTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import com.fzkj.project.system.request.LessonNoDisUserNumRequest;
import com.fzkj.project.system.request.LessonSafeTrainCompanyUserTemplateRequest;
import com.fzkj.project.system.request.LessonSafeTrainQueryUserRequest;
import com.fzkj.project.system.vo.LessonSafeTrainCompanyUserTemplateQueryVO;
import com.fzkj.project.system.vo.LessonSafeTrainDisVO;
import com.fzkj.project.system.vo.LessonSafeTrainQueryUserVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 企业下的用户安全培训课程用户模板 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface LessonSafeTrainCompanyUserTemplateMapper extends BaseMapper<LessonSafeTrainCompanyUserTemplate> {

    List<LessonSafeTrainCompanyUserTemplateQueryVO> selectLessonSafeTrainCompanyUserTemplate1(LessonSafeTrainCompanyUserTemplateRequest request);

    List<LessonSafeTrainCompanyUserTemplateQueryVO> selectLessonSafeTrainCompanyUserTemplate3(LessonSafeTrainCompanyUserTemplateRequest request);

    List<LessonSafeTrainQueryUserVO> selectUsers(LessonSafeTrainQueryUserRequest request);

    List<UserLessonRecordSafeTrain> selectLessonSafeTrainUnDisUser(LessonSafeTrainDisVO request);

    List<UserLessonRecordSafeTrain> selectLessonNoDisUserNum(LessonNoDisUserNumRequest request);
}