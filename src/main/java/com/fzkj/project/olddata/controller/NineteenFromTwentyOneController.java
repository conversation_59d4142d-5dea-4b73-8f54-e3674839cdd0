package com.fzkj.project.olddata.controller;

import com.fzkj.common.constant.Constants;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.basicdata.entity.TFeedBack;
import com.fzkj.project.basicdata.request.TFeedBackRequest;
import com.fzkj.project.basicdata.service.FeedBackService;
import com.fzkj.project.olddata.dto.request.NineteenFromTwentyOneRequest;
import com.fzkj.project.olddata.service.NineteenFromTwentyOneService;
import com.fzkj.project.system.request.LessonSafeTrainPlanRequest;
import com.fzkj.project.system.request.UserSafeTrainFilesDetailRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;


/**
 * 常见问题Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Controller
@Api(tags = "2019-2021历史数据")
public class NineteenFromTwentyOneController {

    @Resource
    private NineteenFromTwentyOneService nineteenFromTwentyOneService;

    /**
     * 查询常见问题列表
     */
    @PostMapping("/Get_orgList")
    @ApiOperation(value = "查询常见问题列表")
    @ResponseBody
    public AjaxResult orgList(@RequestBody CommonRequest commonRequest) {
        NineteenFromTwentyOneRequest request = (NineteenFromTwentyOneRequest) commonRequest.getRequest(NineteenFromTwentyOneRequest.class);
        return AjaxResult.success(nineteenFromTwentyOneService.getOrgList(request, commonRequest.getPageEntity()));
    }


    /**
     * 查询常见问题列表
     */
    @PostMapping("/Get_TwentyOneUserList")
    @ApiOperation(value = "查询常见问题列表")
    @ResponseBody
    public AjaxResult getTwentyOneSafeTrainUserList(@RequestBody CommonRequest commonRequest) {
        NineteenFromTwentyOneRequest request = (NineteenFromTwentyOneRequest) commonRequest.getRequest(NineteenFromTwentyOneRequest.class);
        return AjaxResult.success(nineteenFromTwentyOneService.getTwentyOneSafeTrainUserList(request, commonRequest.getPageEntity()));
    }

    /**
     * 查询常见问题列表
     */
    @PostMapping("/Get_TwentyOneSafeTrainFilesDetail")
    @ApiOperation(value = "查询常见问题列表")
    @ResponseBody
    public AjaxResult getTwentyOneSafeTrainFilesDetail(@RequestBody CommonRequest commonRequest) {
        UserSafeTrainFilesDetailRequest request = (UserSafeTrainFilesDetailRequest) commonRequest.getRequest(UserSafeTrainFilesDetailRequest.class);
        return AjaxResult.success(nineteenFromTwentyOneService.getTwentyOneSafeTrainFilesDetail(request));
    }
}
