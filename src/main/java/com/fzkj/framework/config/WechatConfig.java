package com.fzkj.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
@Data
@Component
@ConfigurationProperties(prefix = "wechat.config")
public class WechatConfig {

    /**
     * Enable.
     */
    private boolean enable;
    /**
     * App id.
     */
    private String appId;
    /**
     * secret
     */
    private String secret;
    /**
     * App id.
     */
    private String publicAppId;
    /**
     * secret
     */
    private String publicSecret;
    /**
     * access-token-url
     */
    private String accessTokenUrl;
    /**
     * public-access-token-url
     */
    private String publicAccessTokenUrl;
    /**
     * subscribe-send-url
     */
    private String subscribeSendUrl;

    /**
     * public-subscribe-send-url
     */
    private String publicSubscribeSendUrl;
    /**
     * session-get-url
     */
    private String sessionGetUrl;
    /**
     * openid-get-url
     */
    private String openidGetUrl;
    /**
     * fansi-get-url
     */
    private String fansiGetUrl;
    /**
     * wxacodeunlimit-get-url
     */
    private String wxacodeunlimitGetUrl;
}
