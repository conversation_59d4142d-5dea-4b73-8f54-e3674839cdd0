package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.UserCompanyService;
import com.fzkj.project.system.vo.UserCompanyVO;
import com.fzkj.project.system.request.UserCompanyRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 用户信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userCompany")
public class UserCompanyController extends BaseEntity {

    private final UserCompanyService UserCompanyServiceImpl;

//    @PostMapping("list")
//    @ApiOperation(value = "用户信息列表查询")
//    public AjaxResult listUserCompany(@RequestBody CommonRequest commonRequest) {
//        UserCompanyRequest request = (UserCompanyRequest) commonRequest.getRequest(UserCompanyRequest.class);
//        return AjaxResult.success(UserCompanyServiceImpl.listUserCompany(request));
//    }

//    @PostMapping("listByPage")
//    @ApiOperation(value = "用户信息分页查询")
//    public AjaxResult listUserCompanyByPage(@RequestBody CommonRequest commonRequest) {
//        UserCompanyRequest request = (UserCompanyRequest) commonRequest.getRequest(UserCompanyRequest.class);
//        return AjaxResult.success(UserCompanyServiceImpl.listByPage(request, commonRequest.getPageEntity()));
//    }

//    @GetMapping("{id}")
//    @ApiOperation(value = "用户信息查询详情")
//    public AjaxResult findUserCompany(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(UserCompanyServiceImpl.findUserCompany(request.getId()));
//    }

//    @PostMapping
//    @ApiOperation(value = "用户信息新增数据")
//    public AjaxResult saveUserCompany(@RequestBody CommonRequest commonRequest) {
//        UserCompanyVO vo = (UserCompanyVO) commonRequest.getRequest(UserCompanyVO.class);
//        return AjaxResult.success(UserCompanyServiceImpl.saveUserCompany(vo));
//    }

//    @PostMapping("update")
//    @ApiOperation(value = "用户信息修改数据")
//    public AjaxResult updateUserCompany(@RequestBody CommonRequest commonRequest) {
//        UserCompanyVO vo = (UserCompanyVO) commonRequest.getRequest(UserCompanyVO.class);
//        return AjaxResult.success(UserCompanyServiceImpl.updateUserCompany(vo));
//    }
//
//    @PostMapping("delete")
//    @ApiOperation(value = "用户信息删除数据")
//    public AjaxResult removeUserCompany(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(UserCompanyServiceImpl.removeUserCompany(request.getId()));
//    }

}
