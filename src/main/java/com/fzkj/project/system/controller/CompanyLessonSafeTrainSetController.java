package com.fzkj.project.system.controller;

import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetQueryRequest;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetRequest;
import com.fzkj.project.system.service.CompanyLessonSafeTrainSetService;
import com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 企业安全培训课程设置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
@Log
public class CompanyLessonSafeTrainSetController extends BaseEntity {

    private final CompanyLessonSafeTrainSetService CompanyLessonSafeTrainSetServiceImpl;

    @PostMapping("Get_CompanyLessonSafeTrainSetList")
    @ApiOperation(value = "查询公司课程安全培训设置列表")
    public AjaxResult getCompanyLessonSafeTrainSetList(@RequestBody CommonRequest commonRequest) {
        CompanyLessonSafeTrainSetQueryRequest request = (CompanyLessonSafeTrainSetQueryRequest) commonRequest.getRequest(CompanyLessonSafeTrainSetQueryRequest.class);
        return AjaxResult.success(CompanyLessonSafeTrainSetServiceImpl.listCompanyLessonSafeTrainSet(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Edit_CompanyLessonSafeTrainSet")
    @ApiOperation(value = "编辑企业课程安全培训设置")
    public AjaxResult Edit_CompanyLessonSafeTrainSet(@RequestBody CommonRequest commonRequest) {
        CompanyLessonSafeTrainSetRequest request = (CompanyLessonSafeTrainSetRequest) commonRequest.getRequest(CompanyLessonSafeTrainSetRequest.class);
        return AjaxResult.success(CompanyLessonSafeTrainSetServiceImpl.editCompanyLessonSafeTrainSet(request));
    }

    @PostMapping("Export_CompanyLessonSafeTrainSet")
    @ApiOperation(value = "下载企业课程安全培训设置")
    public AjaxResult Export_CompanyLessonSafeTrainSet(@RequestBody CommonRequest commonRequest) {
        CompanyLessonSafeTrainSetQueryRequest request = (CompanyLessonSafeTrainSetQueryRequest) commonRequest.getRequest(CompanyLessonSafeTrainSetQueryRequest.class);
        PageUtils pageUtils = CompanyLessonSafeTrainSetServiceImpl.listCompanyLessonSafeTrainSet(request, null);
        List list = pageUtils.getList();
        ExcelUtil<CompanyLessonSafeTrainSetVO> util = new ExcelUtil<>(CompanyLessonSafeTrainSetVO.class);
        return util.exportExcel(list, "企业课程安全培训设置-参培企业", "参培企业", true);
    }


}
