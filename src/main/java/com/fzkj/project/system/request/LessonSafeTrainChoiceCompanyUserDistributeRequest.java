package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户管理添加用户分发
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户管理添加用户分发Request")
public class LessonSafeTrainChoiceCompanyUserDistributeRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("LessonId")
    private Long lessonId;

    @JsonProperty("CompanyIDs")
    private Long companyID;

    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("UserCodes")
    private String userCodes;
}

