package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 安全培训课程课件关系
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="安全培训课程课件关系VO")
public class LessonCourseSafeTrainVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CourseIDs")
    private String CourseIDs;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("BelongPlat")
    private Integer belongPlat;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "操作标识")
    @NotNull(message = "操作标识不能为空")
    @JsonProperty("Flag")
    private String flag;

}