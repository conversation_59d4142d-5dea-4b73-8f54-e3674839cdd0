<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CompanyLicenceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.CompanyLicence">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="licence_type" property="licenceType" />
        <result column="licence_url" property="licenceUrl" />
        <result column="file_type" property="fileType" />
        <result column="is_long_term" property="isLongTerm" />
        <result column="expir_time" property="expirTime" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creator_time" property="creatorTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="licence_name" property="licenceName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, licence_type, licence_url, file_type, is_long_term, expir_time, source, sort, creator_code, creator_time, revise_code, revise_time, remark, is_valid, licence_name
    </sql>

</mapper>