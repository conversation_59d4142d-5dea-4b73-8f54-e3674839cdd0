package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 考试试题表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@TableName("t_d_exam_question")
@ApiModel(value = "考试试题表实体")
public class ExamQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "考试ID")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "试题类型")
    @JsonProperty("SubjectType")
    private Integer subjectType;

    @ApiModelProperty(value = "试题名称")
    @JsonProperty("SubjectName")
    private String subjectName;

    @ApiModelProperty(value = "试题类型ID")
    @JsonProperty("QuestionTypeId")
    private Integer questionTypeId;

    @ApiModelProperty(value = "试题类型ID")
    @JsonProperty("QuestionTypeId")
    @TableField(exist = false)
    private Integer questionTypeId1;

    @ApiModelProperty(value = "试题类型名称")
    @JsonProperty("QuestionTypeName")
    private String questionTypeName;

    @ApiModelProperty(value = "分数")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty(value = "试题选项")
    @JsonProperty("QuestionOption")
    private String questionOption;

    @ApiModelProperty(value = "正确答案")
    @JsonProperty("RightOption")
    private String rightOption;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建者ID")
    @JsonProperty("CreatorID")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改者ID")
    @JsonProperty("ReviseID")
    private String reviseId;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "图片")
    @JsonProperty("Img")
    private String img;

    @ApiModelProperty(value = "试题解析")
    @JsonProperty("ProblemAnalysis")
    private String problemAnalysis;

    public void setQuestionTypeId(Integer questionTypeId) {
        this.questionTypeId = questionTypeId;
        this.questionTypeId1 = questionTypeId;
    }

}