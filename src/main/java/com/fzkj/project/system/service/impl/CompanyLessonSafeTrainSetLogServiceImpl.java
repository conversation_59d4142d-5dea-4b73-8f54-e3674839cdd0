package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.CompanyLessonSafeTrainSetLog;
import com.fzkj.project.system.vo.CompanyLessonSafeTrainSetLogVO;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetLogRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.CompanyLessonSafeTrainSetLogMapper;
import com.fzkj.project.system.service.CompanyLessonSafeTrainSetLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 企业安全培训课程设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CompanyLessonSafeTrainSetLogServiceImpl extends ServiceImpl<CompanyLessonSafeTrainSetLogMapper, CompanyLessonSafeTrainSetLog> implements CompanyLessonSafeTrainSetLogService {

    @Override
    public List<CompanyLessonSafeTrainSetLog> listCompanyLessonSafeTrainSetLog(CompanyLessonSafeTrainSetLogRequest request) {
        CompanyLessonSafeTrainSetLog entity = (CompanyLessonSafeTrainSetLog) DataTransfer.transfer(request, CompanyLessonSafeTrainSetLog.class);
        LambdaQueryWrapper<CompanyLessonSafeTrainSetLog> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(CompanyLessonSafeTrainSetLogRequest request, PageEntity pageEntity) {
        CompanyLessonSafeTrainSetLog entity = (CompanyLessonSafeTrainSetLog) DataTransfer.transfer(request, CompanyLessonSafeTrainSetLog.class);
        LambdaQueryWrapper<CompanyLessonSafeTrainSetLog> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<CompanyLessonSafeTrainSetLog> page = this.page(
                new Query<CompanyLessonSafeTrainSetLog>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public CompanyLessonSafeTrainSetLogVO findCompanyLessonSafeTrainSetLog(Long id) {
        CompanyLessonSafeTrainSetLog entity = this.getById(id);
        CompanyLessonSafeTrainSetLogVO vo = (CompanyLessonSafeTrainSetLogVO) DataTransfer.transfer(entity, CompanyLessonSafeTrainSetLogVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCompanyLessonSafeTrainSetLog(CompanyLessonSafeTrainSetLogVO vo) {
        CompanyLessonSafeTrainSetLog entity = (CompanyLessonSafeTrainSetLog) DataTransfer.transfer(vo, CompanyLessonSafeTrainSetLog.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCompanyLessonSafeTrainSetLog(CompanyLessonSafeTrainSetLogVO vo) {
        CompanyLessonSafeTrainSetLog entity = (CompanyLessonSafeTrainSetLog) DataTransfer.transfer(vo, CompanyLessonSafeTrainSetLog.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCompanyLessonSafeTrainSetLog(Long id) {
        return this.removeById(id);
    }
}