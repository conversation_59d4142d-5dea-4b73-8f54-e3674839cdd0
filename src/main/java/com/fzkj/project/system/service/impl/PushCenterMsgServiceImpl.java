package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.project.system.entity.PushCenterMsg;
import com.fzkj.project.system.mapper.PushCenterMsgMapper;
import com.fzkj.project.system.service.PushCenterMsgService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 消息推送中心 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class PushCenterMsgServiceImpl  extends ServiceImpl<PushCenterMsgMapper, PushCenterMsg> implements PushCenterMsgService {
}
