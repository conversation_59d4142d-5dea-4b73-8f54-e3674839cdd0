package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.PlatformMenu;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.mapper.PlatformMenuMapper;
import com.fzkj.project.system.request.PlatformMenuRequest;
import com.fzkj.project.system.service.PlatformMenuService;
import com.fzkj.project.system.vo.AuthMenuMetaVO;
import com.fzkj.project.system.vo.AuthMenuVO;
import com.fzkj.project.system.vo.PlatformMenuVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <p>
 * 平台菜单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class PlatformMenuServiceImpl extends ServiceImpl<PlatformMenuMapper, PlatformMenu> implements PlatformMenuService {

    private final PlatformMenuMapper platformMenuMapper;
    @Override
    public List<PlatformMenu> listPlatformMenuAll(Integer request) {
        LambdaQueryWrapper<PlatformMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformMenu::getOperPlatform,request);
        queryWrapper.orderByAsc(PlatformMenu::getSort);
        List<PlatformMenu> platformMenus = this.list(queryWrapper);
        List<PlatformMenu> treeResponse = buildMenuTree(platformMenus);
        return treeResponse;
    }

    @Override
    public List<PlatformMenu> listPlatformMenu(Integer request) {
        UserInfo loginUser = SecurityUtils.getLoginUser().getUser();
        List<String> resMenuIds =  platformMenuMapper.platformMenuList(loginUser.getUserCode(),request,null);
        String menuIdAll = "";
        for(String menuId:resMenuIds){
            menuIdAll += (menuIdAll == ""?menuId:(","+menuId));
        }
        String[] strArr = menuIdAll.split(",");
        LambdaQueryWrapper<PlatformMenu> queryWrapper = new LambdaQueryWrapper<>();
        //判断当前是否为admin账号
        if(!loginUser.getUserName().equals("admin")){
            queryWrapper.in(PlatformMenu::getId,strArr);
        }
        queryWrapper.eq(PlatformMenu::getOperPlatform,request);
        queryWrapper.eq(PlatformMenu::getIsValid,1);
        List<PlatformMenu> res = this.list(queryWrapper);
        List<PlatformMenu> treeResponse = buildMenuTree(res);
        return treeResponse;
    }

    @Override
    public List<?> loginPlatformMenu(String request,String targetId) {
        targetId = targetId!=null && targetId.equals("0")?null:targetId;
        UserInfo loginUser = SecurityUtils.getLoginUser().getUser();
        List<String> resMenuIds =  platformMenuMapper.platformMenuList(loginUser.getUserCode(),Integer.parseInt(request),targetId);
        String menuIdAll = "";
        for(String menuId:resMenuIds){
            menuIdAll += (menuIdAll == ""?menuId:(","+menuId));
        }
        String[] strArr = menuIdAll.split(",");
        LambdaQueryWrapper<PlatformMenu> queryWrapper = new LambdaQueryWrapper<>();
        //判断当前是否为admin账号
        if(!loginUser.getId().equals(1L)){
            queryWrapper.in(PlatformMenu::getId,strArr);
        }
        queryWrapper.eq(PlatformMenu::getOperPlatform,request);
        if(request.equals("256")){
            queryWrapper.eq(PlatformMenu::getIsShow,1);
            queryWrapper.eq(PlatformMenu::getIsValid,1);
            return this.list(queryWrapper);
        }else {
            //首先赋值button
            List<AuthMenuVO> authMenuVOS = buildLoginMenuButton(this.list(queryWrapper));
            List<AuthMenuVO> treeResponse = buildLoginMenuTree(authMenuVOS);
            return treeResponse;
        }
    }

    @Override
    public PageUtils listByPage(PlatformMenuRequest request, PageEntity pageEntity) {
        PlatformMenu entity = (PlatformMenu) DataTransfer.transfer(request, PlatformMenu.class);
        LambdaQueryWrapper<PlatformMenu> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<PlatformMenu> page = this.page(
                new Query<PlatformMenu>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public PlatformMenuVO findPlatformMenu(Long id) {
        PlatformMenu entity = this.getById(id);
        PlatformMenuVO vo = (PlatformMenuVO) DataTransfer.transfer(entity, PlatformMenuVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePlatformMenu(PlatformMenuVO vo) {
        String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
        PlatformMenu entity = (PlatformMenu) DataTransfer.transfer(vo, PlatformMenu.class);
        entity.setIsValid(1);
        entity.setCreationTime(LocalDateTime.now());
        entity.setCreatorCode(userCode);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlatformMenu(PlatformMenuVO vo) {
        PlatformMenu entity = (PlatformMenu) DataTransfer.transfer(vo, PlatformMenu.class);
        String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
        entity.setReviseTime(LocalDateTime.now());
        entity.setReviseCode(userCode);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removePlatformMenu(Long id) {
        PlatformMenu platformMenu = this.getById(id);
        LambdaQueryWrapper<PlatformMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformMenu::getFid,id);
        queryWrapper.eq(PlatformMenu::getOperPlatform,platformMenu.getOperPlatform());
        Integer count = this.count(queryWrapper);
        if(count >0){
            return null;
        }else{
            return this.removeById(id);
        }
    }


    public List<AuthMenuVO> buildLoginMenuButton(List<PlatformMenu> menus){
        List<AuthMenuVO> authMenuVOS = new ArrayList<>();
        //复制menu
        for(PlatformMenu platformMenu:menus){
            if(platformMenu.getMenuType().equals("menu")){
                AuthMenuVO authMenuVO = new AuthMenuVO();
                authMenuVO.setId(platformMenu.getId());
                authMenuVO.setFid(platformMenu.getFid());
                authMenuVO.setHidden(platformMenu.getIsShow() == 0);
                authMenuVO.setName(platformMenu.getUrl());
                AuthMenuMetaVO authMenuMetaVO = new AuthMenuMetaVO();
                authMenuMetaVO.setIcon(platformMenu.getIcon());
                authMenuMetaVO.setTitle(platformMenu.getName());
                authMenuMetaVO.setKeepAlive(true);
                List<String> button = new ArrayList<>();
                //寻找当前目录下是否存在button
                for(PlatformMenu platformButton:menus){
                    if(platformButton.getMenuType().equals("button") && Long.compare(platformButton.getFid(), platformMenu.getId()) == 0){
                        button.add(platformButton.getAuthority());
                    }
                }
                authMenuMetaVO.setButton(button);
                authMenuVO.setMeta(authMenuMetaVO);
                authMenuVOS.add(authMenuVO);
            }
        }
        return authMenuVOS;
    }

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    @Override
    public List<PlatformMenu> buildMenuTree(List<PlatformMenu> menus)
    {
        List<PlatformMenu> returnList = new ArrayList<PlatformMenu>();
        for (Iterator<PlatformMenu> iterator = menus.iterator(); iterator.hasNext();)
        {
            PlatformMenu t = (PlatformMenu) iterator.next();
            // 根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getFid() == 0)
            {
                recursionFn(menus, t);
                returnList.add(t);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = menus;
        }
        return returnList;
    }

    public List<AuthMenuVO> buildLoginMenuTree(List<AuthMenuVO> menus)
    {
        List<AuthMenuVO> returnList = new ArrayList<AuthMenuVO>();
        for (Iterator<AuthMenuVO> iterator = menus.iterator(); iterator.hasNext();)
        {
            AuthMenuVO t = (AuthMenuVO) iterator.next();
            // 根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getFid() == 0)
            {
                recursionLoginFn(menus, t);
                returnList.add(t);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private void recursionLoginFn(List<AuthMenuVO> list, AuthMenuVO t)
    {
        // 得到子节点列表
        List<AuthMenuVO> childList = getLoginChildList(list, t);
        t.setChildren(childList);
        for (AuthMenuVO tChild : childList)
        {
            if (hasLoginChild(list, tChild))
            {
                // 判断是否有子节点
                Iterator<AuthMenuVO> it = childList.iterator();
                while (it.hasNext())
                {
                    AuthMenuVO n = (AuthMenuVO) it.next();
                    recursionLoginFn(list, n);
                }
            }
        }
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private void recursionFn(List<PlatformMenu> list, PlatformMenu t)
    {
        // 得到子节点列表
        List<PlatformMenu> childList = getChildList(list, t);
        t.setMenuChild(childList);
        for (PlatformMenu tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                // 判断是否有子节点
                Iterator<PlatformMenu> it = childList.iterator();
                while (it.hasNext())
                {
                    PlatformMenu n = (PlatformMenu) it.next();
                    recursionFn(list, n);
                }
            }else{
                tChild.setMenuChild(null);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<AuthMenuVO> getLoginChildList(List<AuthMenuVO> list, AuthMenuVO t)
    {
        List<AuthMenuVO> tlist = new ArrayList<AuthMenuVO>();
        Iterator<AuthMenuVO> it = list.iterator();
        while (it.hasNext())
        {
            AuthMenuVO n = (AuthMenuVO) it.next();
            if (n.getFid().longValue() == t.getId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 得到子节点列表
     */
    private List<PlatformMenu> getChildList(List<PlatformMenu> list, PlatformMenu t)
    {
        List<PlatformMenu> tlist = new ArrayList<PlatformMenu>();
        Iterator<PlatformMenu> it = list.iterator();
        while (it.hasNext())
        {
            PlatformMenu n = (PlatformMenu) it.next();
            if (n.getFid().longValue() == t.getId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<PlatformMenu> list, PlatformMenu t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasLoginChild(List<AuthMenuVO> list, AuthMenuVO t)
    {
        return getLoginChildList(list, t).size() > 0 ? true : false;
    }

}