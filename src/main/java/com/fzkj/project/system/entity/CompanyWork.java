package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 岗位表
 *
 */
@Data
@TableName("t_d_company_work")
@ApiModel(value = "岗位表")
public class CompanyWork implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("CompanyID")
    @ApiModelProperty(value = "公司Id")
    private Long companyId;

    @JsonProperty("DepartID")
    @ApiModelProperty(value = "部门id")
    private String departId;

    @JsonProperty("WorkName")
    @ApiModelProperty(value = "岗位名称")
    private String workName;

    @JsonProperty("Source")
    @ApiModelProperty(value = "")
    private Long source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "")
    private String sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @JsonProperty("DutyBookID")
    @ApiModelProperty(value = "岗位责任书")
    private Long dutyBookId;

    @JsonProperty("WorkDuty")
    @ApiModelProperty(value = "岗位职责")
    private String workDuty;

    @JsonProperty("WorkTasks")
    @ApiModelProperty(value = "工作任务")
    private String workTasks;

    @JsonProperty("IndustryType")
    @ApiModelProperty(value = "行业类型")
    private String industryType;
}