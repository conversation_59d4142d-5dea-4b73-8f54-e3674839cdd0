package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户登录信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户登录信息表Request")
public class UserInfoRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonProperty("UserCode")
    private String userCode;

    @JsonProperty("IdCard")
    private String idCard;

    @JsonProperty("Phone")
    private String phone;

    @JsonProperty("PassWord")
    private String passWord;

    @JsonProperty("UnionId")
    private String unionId;

    @JsonProperty("OpenId")
    private String openId;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("CreatorCode")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "")
    private Integer isValid;

    @JsonProperty("JobNo")
    @ApiModelProperty(value = "")
    private String jobNo;

    @JsonProperty("ReviseTimePwd")
    private LocalDateTime reviseTimePwd;

    @JsonProperty("IsBind")
    private Integer isBind;

    @JsonProperty("KeyWord")
    private String keyWord;

    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("IsOut")
    private Integer isOut;

    @JsonProperty("CompanyID")
    private Long companyId;

    @JsonProperty("DepartID")
    private Long departId;

    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("ExcelFilePath")
    private String excelFilePath;

    @JsonProperty("WorkID")
    private Long workId;

    @JsonProperty("TrainTypeIDStr")
    private String trainTypeIDStr;

    @JsonProperty("StatisticalGroup")
    private Integer statisticalGroup;

}