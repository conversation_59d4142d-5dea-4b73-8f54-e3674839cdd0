package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 平台菜单
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_platform_menu")
@ApiModel(value = "平台菜单实体")
public class PlatformMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("FID")
    @ApiModelProperty(value = "父级菜单")
    private Long fid;

    @JsonProperty("Name")
    @ApiModelProperty(value = "菜单名称")
    private String name;

    @JsonProperty("Url")
    @ApiModelProperty(value = "菜单地址")
    private String url;

    @JsonProperty("Authority")
    @ApiModelProperty(value = "权限标识")
    private String authority;

    @JsonProperty("Icon")
    @ApiModelProperty(value = "菜单图标")
    private String icon;

    @JsonProperty("MenuClass")
    @ApiModelProperty(value = "菜单分类")
    private String menuClass;

    @JsonProperty("MenuType")
    @ApiModelProperty(value = "菜单类型")
    private String menuType;

    @JsonProperty("OperPlatform")
    @ApiModelProperty(value = "操作平台")
    private String operPlatform;

    @JsonProperty("IsShow")
    @ApiModelProperty(value = "是否显示")
    private Integer isShow;

    @JsonProperty("Source")
    @ApiModelProperty(value = "来源")
    private String source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private String sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人")
    private String creatorCode;

    @JsonProperty("CreationTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @TableField(exist = false)
    @JsonProperty("MenuChild")
    List<PlatformMenu> menuChild;
}