package com.fzkj.project.system.service;

import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.lessonSafeTrainTd;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.vo.*;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * <p>
 * 安全培训课程分发学员学习记录 服务实现类-Mongo
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface UserLessonRecordSafeTrainMongoService {

    List<UserLessonRecordLearnNumVO> getLearnNum(LessonSafeTrainPlanRequest request);
    List<UserLessonRecordLearnNumVO> getLessonLearnNumByCompanyDepartId(LessonSafeTrainPlanRequest request);
    List<UserLessonRecordSafeTrainUserVO> getUserLessonRecordSafeTrainBus(LessonSafeTrainPlanRequest request);

    List<SafetyTrainingLedgerRequest> getUserLessonRecordSafeTrain1(String lessonName,Long companyId);

    List<UserLessonRecordLearnNumVO> getSafeTrainCompanyStatisticsUser(LessonSafeTrainCompanyStatisticRequest request);
    List<SafeTrainCompanyDetialUserVO> getSafeTrainCompanyDetialUser(SafeTrainCompanyDetialRequest request);
    List<SafeTrainUserRecordDetailVO> getSafeTrainUserRecordDetail(SafeTrainUserRecordDetailRequest request);
    LessonRecordVO getUserSafeTrainFilesDetail(UserSafeTrainFilesDetailRequest request);
    List<UserLessonRecordSafeTrain> getListLearnNumByRequest(GetSafeTrainCompanyStatisticsListRequest request);
    List<UserLessonRecordSafeTrain> getTotalLearnNumByRequest(GetSafeTrainCompanyStatisticsTotalRequest request);
    List<UserLessonRecordSafeTrain> getCompanyLearnNumByRequest(GetSafeTrainCompanyStatisticsPlanRequest request);
    List<UserLessonRecordLearnNumVO> getLearnNumByLessonId(LessonSafeTrainPlanRequest lessonSafeTrainPlanRequest);
    List<UserLessonRecordSafeTrainVO> getLessonDisedUserList(LessonSafeTrainQueryUserRequest request);
    List<LessonSafeTrainQueryUserVO> getLessonNoDisUserList(LessonSafeTrainQueryUserRequest request);
    List<UserLessonRecordLearnNumVO> getLessonLearnNumByLessonId(LessonSafeTrainRequest lessonSafeTrainPlanRequest);
    List<UserLessonRecordSafeTrain> getLessonSafeTrainUnDisUser(LessonSafeTrainDisVO request);
    int getLessonNoDisUserNum(LessonNoDisUserNumRequest request);
    List<UserLessonRecordSafeTrain> getUserLesson(UserLessonRecordSafeTrain request);
    boolean removeUserLessonById(List<ObjectId> ids);
    boolean updateUserLessonById(UserLessonRecordSafeTrain request);
    boolean delUserLesson(List<ObjectId> userLessonIds);
    int getLessonLearnNum(GetLessonLearnNumRequest request);
    int getLearnRedNum(GetLearnRedNumRequest request);
    List<UserLessonRecordSafeTrain> getCompleteUserLessonRecord(String userId, Long lessonId);
    List<UserLessonRecordSafeTrain> getSafeTrainCompanyLesson(GetSafeTrainCompanyLessonRequest request);
    PageUtils getSafeTrainStatisticsUserList(GetSafeTrainStatisticsUserListRequest request, PageEntity pageEntity);
    List<UserLessonRecordSafeTrain> getHomeLessonInfo(GetHomeLessonInfoRequest request);
    List<UserLessonRecordSafeTrain> getLearningUserLesson(EditUserLessonRecordSignRequest request);
    List<UserLessonRecordSafeTrain> getMyLessonInfo(GetMyLessonInfoRequest request);
    List<UserLessonRecordSafeTrain> getUserSurvey(GetUserSurveyRequest request);
    PageUtils getUserLessonRecordByUserCode(GetUserSurveyRequest request, PageEntity pageEntity);
    boolean recoveryUserLesson(String userCode, List<ObjectId> ids);
    int getUserLessonCount(Long companyId, Long lessonCategoryId);
    PageUtils getPayUserList(UserLessonRecordSafeTrainVO request, PageEntity pageEntity);
    boolean updateName(String userCode, String userName);
    void saveBatch(List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList);
    void updateIsPayById(int payStatus, String id);
    void updateIsPayById(boolean isHistory, int payStatus, String id);
    void moveToHistory();
    List<UserLessonRecordSafeTrain> getExpireLesson(String expireDays);
    int getUserLessonLearningCount(Long lessonId);
    List<Long> getUserLearningLessonIds(String userCode);
    void updateRecord(Long companyId, Long departId, String departName, String companyName, String userCode);

    List<lessonSafeTrainTd> selectSignUrlById(String lessonName);
}
