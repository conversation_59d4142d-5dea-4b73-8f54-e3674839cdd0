package com.fzkj.project.system.controller;

import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.ExamAnswerRecordExerciseService;
import com.fzkj.project.system.vo.ExamAnswerRecordExerciseVO;
import com.fzkj.project.system.request.ExamAnswerRecordExerciseRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 用户练习记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@RestController
@RequiredArgsConstructor
@Log
public class ExamAnswerRecordExerciseController extends BaseEntity {

    private final ExamAnswerRecordExerciseService ExamAnswerRecordExerciseServiceImpl;
}
