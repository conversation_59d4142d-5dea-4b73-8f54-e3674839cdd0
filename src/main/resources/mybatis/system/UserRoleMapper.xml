<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.UserRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.UserRole">
        <id column="id" property="id"/>
        <result column="target_id" property="targetId"/>
        <result column="role_id" property="roleId"/>
        <result column="user_code" property="userCode"/>
        <result column="oper_platform" property="operPlatform"/>
        <result column="source" property="source"/>
        <result column="sort" property="sort"/>
        <result column="creator_code" property="creatorCode"/>
        <result column="creator_time" property="creatorTime"/>
        <result column="revise_code" property="reviseCode"/>
        <result column="revise_time" property="reviseTime"/>
        <result column="remark" property="remark"/>
        <result column="is_valid" property="isValid"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, target_id, role_id, user_code, oper_platform, source, sort, creator_code, creator_time, revise_code, revise_time, remark, is_valid
    </sql>

    <select id="selectUserRolePage" resultType="com.fzkj.project.system.vo.UserRoleVO">
        SELECT
        ur.* ,
        uc.user_name,
        uc.user_photo,
        ui.job_no,
        ui.phone,
        pr.role_name
        FROM
        t_d_user_role ur
        LEFT JOIN t_d_user_company uc on ur.user_code = uc.user_code
        LEFT JOIN t_d_user_info ui ON ur.user_code = ui.user_code
        LEFT JOIN t_d_platform_role pr ON ur.role_id = pr.id
        WHERE ur.oper_platform = 4
        <if test="keyWord != null and keyWord !=''">
          AND (uc.user_name LIKE concat('%',#{keyWord},'%')
                   OR ui.phone LIKE concat('%',#{keyWord},'%')
                   OR ui.job_no LIKE concat('%',#{keyWord},'%')
                   OR ur.remark LIKE concat('%',#{keyWord},'%'))
        </if>
        <if test="roleId !=null">AND ur.role_id = #{roleId}</if>
        <if test="isValid !=null">AND ur.is_valid = #{isValid}</if>
    </select>
    <select id="selectUserCodeByTargetId" resultType="java.lang.Integer">
        select count(*) from t_d_user_role where user_code=#{userCode} and target_id=#{departID} and oper_platform=#{plat}
    </select>

</mapper>