package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.CompanyExtend;
import com.fzkj.project.system.vo.CompanyExtendVO;
import com.fzkj.project.system.request.CompanyExtendRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 企业扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CompanyExtendService extends IService<CompanyExtend> {

    List<CompanyExtend> listCompanyExtend(CompanyExtendRequest request);

    PageUtils listByPage(CompanyExtendRequest request, PageEntity pageEntity);

    CompanyExtendVO findCompanyExtend(Long id);

    Long saveCompanyExtend(CompanyExtendVO vo);

    boolean updateCompanyExtend(CompanyExtendVO vo);

    boolean removeCompanyExtend(Long id);
}
