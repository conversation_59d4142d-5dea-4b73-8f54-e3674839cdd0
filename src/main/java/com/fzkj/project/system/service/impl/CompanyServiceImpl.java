package com.fzkj.project.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.project.system.domain.TreeSelect;
import com.fzkj.project.system.entity.Company;
import com.fzkj.project.system.mapper.CompanyLessonSafeTrainSetMapper;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetQueryRequest;
import com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO;
import com.fzkj.project.system.vo.CompanyVO;
import com.fzkj.project.system.request.CompanyRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.CompanyMapper;
import com.fzkj.project.system.service.CompanyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 企业表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CompanyServiceImpl extends ServiceImpl<CompanyMapper, Company> implements CompanyService {

    private final CompanyLessonSafeTrainSetMapper companyLessonSafeTrainSetMapper;

    @Override
    public List<Company> listCompany(CompanyRequest request) {
        LambdaQueryWrapper<Company> topQueryWrapper = new LambdaQueryWrapper<>();
        topQueryWrapper.eq(Company::getIsValid,1);
        topQueryWrapper.eq(Company::getParentId,0L);
        Company topCompany = baseMapper.selectOne(topQueryWrapper);
        LambdaQueryWrapper<Company> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Company::getParentId,topCompany.getId());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<CompanyVO> getCompanyTree(CompanyRequest request) {
        LambdaQueryWrapper<Company> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Company::getIsValid,1);
        queryWrapper.like(Company::getAncestors,","+request.getId()+",").or().likeLeft(Company::getAncestors,","+request.getId());
        List<Company> companyList = baseMapper.selectList(queryWrapper);
        if (companyList.size()==0){
            List<CompanyVO> res = new ArrayList<>();
            Company company = baseMapper.selectById(request.getId());
            CompanyVO companyVO = (CompanyVO) DataTransfer.transfer(company, CompanyVO.class);
            companyVO.setName(companyVO.getCompanyName());
            res.add(companyVO);
            return res;
        }
        //所有父id和数据
        Map<Long,Company> parentIdMap = new HashMap<>();
        for(Company company:companyList){
            parentIdMap.put(company.getParentId(),company);
        }
        List<CompanyVO> res = new ArrayList<>();
        if(request.getIsSelf() == 1){
            //包含自己
            LambdaQueryWrapper<Company> companyLambdaQueryWrapper = new LambdaQueryWrapper<>();
            companyLambdaQueryWrapper.eq(Company::getId,request.getId());
            Company company = baseMapper.selectOne(companyLambdaQueryWrapper);
            CompanyVO companyVO = (CompanyVO) DataTransfer.transfer(company, CompanyVO.class);
            companyVO.setName(companyVO.getCompanyName());
            res.add(companyVO);
        }
        for(Company company:companyList){
            if(parentIdMap.get(company.getId()) == null){
                //最下级
                CompanyVO companyVO = (CompanyVO) DataTransfer.transfer(company, CompanyVO.class);
                companyVO.setName(companyVO.getCompanyName());
                res.add(companyVO);
            }
        }
        return res;
    }

    @Override
    public List<TreeSelect> getCompany(CompanyRequest request) {
        List<CompanyVO> cpms = baseMapper.getCompanyList(request.getCompanyName(),request.getIsValid(),request.getTargetId());
        //如果搜索了公司名称，获取他的所有父节点
        if(StringUtils.isNotEmpty(request.getCompanyName())){
            String allParentIds = "";
            for(CompanyVO companyVO:cpms){
                allParentIds += ((allParentIds == ""?"":",")+companyVO.getAncestors()+","+companyVO.getId());
            }
            List<CompanyVO> cpmParents = baseMapper.getCompanyParentList(removeRepeat2(allParentIds));
            cpms = cpmParents;
//            cpms.addAll(cpmParents);
        }
        List<CompanyVO> deptTrees = buildDeptTree(cpms);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    @Override
    public String getCompanyDetail(CompanyRequest request) {
        CompanyVO companyVO = baseMapper.getCompanyListById(request.getId());
        String[] ids = companyVO.getAncestors().split(",");
        if(ids.length == 1){
            companyVO.setCompanyId(null);
            companyVO.setDepartId(null);
        }else if(ids.length == 2){
            companyVO.setCompanyId(companyVO.getId());
            companyVO.setDepartId(null);
        }else if(ids.length >2){
            companyVO.setCompanyId(Long.parseLong(ids[2]));
            companyVO.setDepartId(companyVO.getId());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("CompanyInfo",companyVO);
        CompanyLessonSafeTrainSetQueryRequest queryRequest=new CompanyLessonSafeTrainSetQueryRequest();
        queryRequest.setCompanyId(companyVO.getCompanyId());
        queryRequest.setState(1);
        List<CompanyLessonSafeTrainSetVO> companyLessonSafeTrainSetVOS = companyLessonSafeTrainSetMapper.selectCompanyLessonSafeTrainSetList1(queryRequest);
        jsonObject.put("SafeTrainInfo",companyLessonSafeTrainSetVOS);
        return jsonObject.toString(SerializerFeature.WriteMapNullValue);
    }

//    @Override
//    public List<TreeSelect> listCompany(CompanyRequest request) {
//        Company entity = (Company) DataTransfer.transfer(request, Company.class);
//        LambdaQueryWrapper<Company> queryWrapper = new LambdaQueryWrapper<>();
//        if(StringUtils.isNotEmpty(entity.getCompanyName())){
//            queryWrapper.like(Company::getCompanyName,entity.getCompanyName());
//        }
//        if(entity.getIsValid()!=null){
//            queryWrapper.eq(Company::getIsValid,entity.getIsValid());
//        }
//        List<Company> cpms = this.list(queryWrapper);
//        List<Company> deptTrees = buildDeptTree(cpms);
//        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
//    }

    @Override
    public PageUtils listByPage(CompanyRequest request, PageEntity pageEntity) {
        Company entity = (Company) DataTransfer.transfer(request, Company.class);
        LambdaQueryWrapper<Company> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<Company> page = this.page(
                new Query<Company>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public CompanyVO findCompany(Long id) {
        Company entity = this.getById(id);
        CompanyVO vo = (CompanyVO) DataTransfer.transfer(entity, CompanyVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateCompanyVo(CompanyVO vo) {
        Integer isValid = null;
        Company company = this.getById(vo.getId());
        isValid = company.getIsValid() == 1?0:1;
        //启用判断上级是否禁用状态
        if(company.getParentId() != 0 && isValid == 1){
            Company upCompany = this.getById(company.getParentId());
            if(upCompany.getIsValid() == 0){
                return -1;
            }
        }
        Company entity = (Company) DataTransfer.transfer(vo, Company.class);
        this.updateById(entity);
        return 1;
    }

    @Override
    public List<Long> getSubDeptIds(Long deptId) {
        return getBaseMapper().getSubDeptIds(deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateCompany(CompanyVO vo) {
        if(vo.getFlag().equals("enable")){
            Integer isValid = null;
            Company company = this.getById(vo.getId());
            isValid = company.getIsValid() == 1?0:1;
            //启用判断上级是否禁用状态
            if(company.getParentId() != 0 && isValid == 1){
                Company upCompany = this.getById(company.getParentId());
                if(upCompany.getIsValid() == 0){
                    return -1;
                }
            }
            LambdaUpdateWrapper<Company> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Company::getId,vo.getId()).or().like(Company::getAncestors,vo.getId());
            updateWrapper.set(Company::getIsValid,isValid);
            this.update(updateWrapper);
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCompany(Long id) {
        return this.removeById(id);
    }

    @Override
    public List<CompanyVO> selectChildDepartNoRecursive(Long orgId) {
        return getBaseMapper().selectChildDepartNoRecursive(orgId);
    }

    public List<CompanyVO> buildDeptTree(List<CompanyVO> depts) {
        List<CompanyVO> returnList = new ArrayList<CompanyVO>();
        List<Long> tempList = new ArrayList<Long>();
        for (CompanyVO dept : depts) {
            tempList.add(dept.getId());
        }
        for (Iterator<CompanyVO> iterator = depts.iterator(); iterator.hasNext(); ) {
            CompanyVO dept = (CompanyVO) iterator.next();
            //业务逻辑
            String[] ids = dept.getAncestors().split(",");
            if(ids.length == 1){
                dept.setCompanyId(null);
                dept.setDepartId(null);
            }else if(ids.length == 2){
                dept.setCompanyId(dept.getId());
                dept.setDepartId(null);
            }else if(ids.length >2){
                dept.setCompanyId(Long.parseLong(ids[2]));
                dept.setDepartId(dept.getId());
            }
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    private void recursionFn(List<CompanyVO> list, CompanyVO t) {
        // 得到子节点列表
        List<CompanyVO> childList = getChildList(list, t);
        t.setChildren(childList);
        for (CompanyVO tChild : childList) {
            if (hasChild(list, tChild)) {
                // 判断是否有子节点
                Iterator<CompanyVO> it = childList.iterator();
                while (it.hasNext()) {
                    CompanyVO n = (CompanyVO) it.next();
                    recursionFn(list, n);
                }
            }
        }
    }

    private List<CompanyVO> getChildList(List<CompanyVO> list, CompanyVO t) {
        List<CompanyVO> tlist = new ArrayList<CompanyVO>();
        Iterator<CompanyVO> it = list.iterator();
        while (it.hasNext()) {
            CompanyVO n = (CompanyVO) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<CompanyVO> list, CompanyVO t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    /**
     * 通过HashSet去重（保证顺序）
     */
    public List<String> removeRepeat2(String parents) {
        ArrayList<String> userList = new ArrayList<>();
        Collections.addAll(userList,parents.split(","));
        System.out.println("去重前：" + userList.toString());
        Set set = new HashSet();
        List<String> newList = new ArrayList();
        for (String str : userList) {
            if (set.add(str)) {
                newList.add(str);
            }
        }
        System.out.println("去重后：" + newList.toString());
        return newList;
    }

    @Override
    public List<Company> getSubDeps(CompanyRequest request) {
        LambdaQueryWrapper<Company> companyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyLambdaQueryWrapper.eq(Company::getIsValid,1);
        companyLambdaQueryWrapper.eq(Company::getParentId,request.getId());
        return baseMapper.selectList(companyLambdaQueryWrapper);
    }
}