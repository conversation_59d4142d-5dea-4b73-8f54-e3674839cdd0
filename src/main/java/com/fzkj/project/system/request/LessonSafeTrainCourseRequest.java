package com.fzkj.project.system.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LessonSafeTrainCourseRequest {
    @ApiModelProperty(value = "操作标识")
    private String flag;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "文件类型")
    private Integer fileType;

    @ApiModelProperty(value = "所属平台")
    private Integer belongPlat;

    @ApiModelProperty(value = "是否显示")
    private Integer isShow;

    @ApiModelProperty(value = "课程ID")
    private Integer lessonId;

    @ApiModelProperty(value = "企业ID")
    private Integer companyId;
}
