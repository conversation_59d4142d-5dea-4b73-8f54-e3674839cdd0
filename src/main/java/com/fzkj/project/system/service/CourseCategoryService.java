package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.CourseCategory;
import com.fzkj.project.system.request.CourseCategoryQueryRequest;
import com.fzkj.project.system.vo.CourseCategoryVO;
import com.fzkj.project.system.request.CourseCategoryRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 平台课件分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CourseCategoryService extends IService<CourseCategory> {

    List<CourseCategory> listCourseCategory(CourseCategoryRequest request);

    CourseCategoryVO findCourseCategory(Long id);

    Long saveCourseCategory(CourseCategoryVO vo);

    boolean updateCourseCategory(CourseCategoryVO vo);

    boolean removeCourseCategory(Long id);

    PageUtils listCourseCategory(CourseCategoryQueryRequest request, PageEntity pageEntity);
}
