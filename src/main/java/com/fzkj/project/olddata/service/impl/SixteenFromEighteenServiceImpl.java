package com.fzkj.project.olddata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.olddata.dto.SixTeenCompanyEntity;
import com.fzkj.project.olddata.dto.SixTeenLessonEntity;
import com.fzkj.project.olddata.dto.request.NineteenFromTwentyOneRequest;
import com.fzkj.project.olddata.dto.request.SixteenFromEighteenOneRequest;
import com.fzkj.project.olddata.dto.request.SixteenLessonResp;
import com.fzkj.project.olddata.mapper.SixteenCompanyMapper;
import com.fzkj.project.olddata.mapper.SixteenLessonMapper;
import com.fzkj.project.olddata.service.SixteenFromEighteenService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.EmptyStackException;
import java.util.List;

@Service
public class SixteenFromEighteenServiceImpl implements SixteenFromEighteenService {
    @Resource
    SixteenCompanyMapper sixteenCompanyMapper;
    @Resource
    SixteenLessonMapper sixteenLessonMapper;

    @Override
    public List<SixTeenCompanyEntity> getSixteenOrgList(SixteenFromEighteenOneRequest request) {
        LambdaQueryWrapper<SixTeenCompanyEntity> queryWrapper= new LambdaQueryWrapper<>();
        queryWrapper.eq(SixTeenCompanyEntity::getFId,request.getOrgId());
        queryWrapper.eq(SixTeenCompanyEntity::getIsValid,1);
        return sixteenCompanyMapper.selectList(queryWrapper);
    }
    @Override
    public List<SixTeenLessonEntity> getSixteenLesson(SixteenFromEighteenOneRequest request) {
        String stageName = request.getStageName();
        String[] split = stageName.split("-");
        LambdaQueryWrapper<SixTeenLessonEntity> queryWrapper= new LambdaQueryWrapper<>();
        queryWrapper.eq(SixTeenLessonEntity::getCompanyId,request.getOrgId());
        queryWrapper.eq(SixTeenLessonEntity::getLessonYear,split[0]);
        queryWrapper.eq(SixTeenLessonEntity::getLessonMonth,split[1]);
        queryWrapper.eq(SixTeenLessonEntity::getIsValid,1);
        return sixteenLessonMapper.selectList(queryWrapper);
    }

    @Override
    public PageUtils getSixteenLessonUser(SixteenFromEighteenOneRequest request, PageEntity pageEntity) {
        List<SixteenLessonResp> list= sixteenCompanyMapper.getSixteenLessonUser(request.getLessonId());
        int size = list.size();
        if (null != pageEntity) {
            int startIndex = (pageEntity.getPageIndex() - 1) * pageEntity.getPageSize();
            int endIndex = (pageEntity.getPageIndex()) * pageEntity.getPageSize();
            if (size > 0 && startIndex < size) {
                if (endIndex > size) {
                    endIndex = size;
                }
                list = list.subList(startIndex, endIndex);
            }
        }
        PageUtils pageUtils=new PageUtils(pageEntity,list);
        pageUtils.setTotalCount(size);
        return pageUtils;
    }
}
