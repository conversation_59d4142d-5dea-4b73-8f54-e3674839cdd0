package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 安全培训课程分发学员学习记录
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="安全培训课程分发学员学习记录VO")
public class UserLessonRecordSafeTrainUserVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private String id;

    @ApiModelProperty(value = "企业ID")
    @NotNull(message = "企业ID不能为空")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    @NotNull(message = "企业名称不能为空")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "部门ID")
    @NotNull(message = "部门ID不能为空")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "岗位ID")
    @NotNull(message = "岗位ID不能为空")
    @JsonProperty("WorkID")
    private Long workId;

    @ApiModelProperty(value = "部门名称")
    @NotNull(message = "部门名称不能为空")
    @JsonProperty("DepartName")
    @Excel(name = "部门",order = 6)
    private String departName;

    @ApiModelProperty(value = "岗位名称")
    @NotNull(message = "岗位名称不能为空")
    @JsonProperty("WorkName")
    @Excel(name = "岗位",order = 7)
    private String workName;

    @ApiModelProperty(value = "课程分类ID")
    @NotNull(message = "课程分类ID不能为空")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课程分类名称")
    @NotNull(message = "课程分类名称不能为空")
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty(value = "课程ID")
    @NotNull(message = "课程ID不能为空")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课程月份")
    @NotNull(message = "课程月份不能为空")
    @JsonProperty("LessonDate")
    private String lessonDate;

    @ApiModelProperty(value = "课程名称")
    @NotNull(message = "课程名称不能为空")
    @JsonProperty("LessonName")
    @Excel(name = "课程名称",order = 5)
    private String lessonName;

    @ApiModelProperty(value = "课件数量")
    @NotNull(message = "课件数量不能为空")
    @JsonProperty("LessonCourseCount")
    private Integer lessonCourseCount;

    @ApiModelProperty(value = "试卷ID")
    @NotNull(message = "试卷ID不能为空")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "考试分数")
    @JsonProperty("Score")
    private String score;

    @ApiModelProperty(value = "考试分数")
    @JsonProperty("Score1")
    private double score1;

    @JsonProperty("Shape")
    @ApiModelProperty(value = "课程形式")
    private Integer shape;

    @ApiModelProperty(value = "用户可补考次数，默认1次")
    @NotNull(message = "用户可补考次数，默认1次不能为空")
    @JsonProperty("ResitNumber")
    private Integer resitNumber;

    @ApiModelProperty(value = "学习状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）")
    @NotNull(message = "学习状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）不能为空")
    @JsonProperty("IsComplete")
    private Integer isComplete;

    @ApiModelProperty(value = "课程统计状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）")
    @NotNull(message = "课程统计状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）不能为空")
    @JsonProperty("StaticStatus")
    private Integer staticStatus;

    @ApiModelProperty(value = "开始学习时间")
    @NotNull(message = "开始学习时间不能为空")
    @JsonProperty("StartStudyTime")
    private String startStudyTime;

    @ApiModelProperty(value = "学习完成时间")
    @NotNull(message = "学习完成时间不能为空")
    @JsonProperty("CompleteTime")
    @Excel(name = "完成时间",order = 9)
    private String completeTime;

    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不能为空")
    @JsonProperty("UserID")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    @NotNull(message = "用户名称不能为空")
    @JsonProperty("UserName")
    @Excel(name = "姓名",order = 1)
    private String userName;

    @ApiModelProperty(value = "用户编码")
    @NotNull(message = "用户编码不能为空")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "联系电话")
    @NotNull(message = "联系电话不能为空")
    @JsonProperty("Phone")
    @Excel(name = "手机号",order = 4)
    private String phone;

    @ApiModelProperty(value = "用户头像")
    @NotNull(message = "用户头像不能为空")
    @JsonProperty("UserPhoto")
    private String userPhoto;

    @ApiModelProperty(value = "工号")
    @NotNull(message = "工号不能为空")
    @JsonProperty("JobNo")
    @Excel(name = "工号",order = 2)
    private String jobNo;

    @ApiModelProperty(value = "身份证")
    @NotNull(message = "身份证不能为空")
    @JsonProperty("IDCard")
    @Excel(name = "身份证",order = 3)
    private String idCard;

    @ApiModelProperty(value = "是否作为统计对象")
    @NotNull(message = "是否作为统计对象不能为空")
    @JsonProperty("IsStatic")
    private Integer isStatic;

    @ApiModelProperty(value = "课程支付类型：2：学员免费；3:学员付费")
    @NotNull(message = "课程支付类型：2：学员免费；3:学员付费不能为空")
    @JsonProperty("IsPayType")
    private Integer isPayType;

    @ApiModelProperty(value = "课程支付状态：0：未支付；1：已支付")
    @NotNull(message = "课程支付状态：0：未支付；1：已支付不能为空")
    @JsonProperty("IsPay")
    private Integer isPay;

    @ApiModelProperty(value = "培训开始时间")
    @NotNull(message = "培训开始时间不能为空")
    @JsonProperty("TrainStartTime")
    private String trainStartTime;

    @ApiModelProperty(value = "培训结束时间")
    @NotNull(message = "培训结束时间不能为空")
    @JsonProperty("TrainEndTime")
    private String trainEndTime;

    @ApiModelProperty(value = "培训状态")
    @JsonProperty("TrainStatusStr")
    @Excel(name = "培训状态",order = 8)
    private String trainStatusStr;

    @ApiModelProperty(value = "签名图片")
    @NotNull(message = "签名图片不能为空")
    @JsonProperty("SignImg")
    private String signImg;

    @ApiModelProperty(value = "培训时长")
    @NotNull(message = "培训时长不能为空")
    @JsonProperty("TrainTimeCount")
    private Long trainTimeCount;

    @ApiModelProperty(value = "地区编码")
    @NotNull(message = "地区编码不能为空")
    @JsonProperty("AreaCode")
    private String areaCode;

    @ApiModelProperty(value = "地区名称")
    @NotNull(message = "地区名称不能为空")
    @JsonProperty("AreaName")
    private String areaName;

    @ApiModelProperty(value = "来源")
    @NotNull(message = "来源不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @NotNull(message = "创建人ID不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    @Excel(name = "创建时间",order = 10,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @NotNull(message = "修改人ID不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    @JsonProperty("Comment")
    private String comment;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    @NotNull(message = "备注信息不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效，1：是，0：否，默认有效")
    @NotNull(message = "是否有效，1：是，0：否，默认有效不能为空")
    @JsonProperty("IsValid")
    @Excel(name = "记录状态",order = 11,readConverterExp = "0=已收回,1=正常")
    private Integer isValid;

    @ApiModelProperty(value = "分发来源，1：售后服务端分发的，0：用户自己购买分发的")
    @NotNull(message = "分发来源，1：售后服务端分发的，0：用户自己购买分发的不能为空")
    @JsonProperty("DisSource")
    private Integer disSource;

    @ApiModelProperty(value = "参培课程（1：通用课程，2：定制课程）")
    @NotNull(message = "参培课程（1：通用课程，2：定制课程）不能为空")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "预留字段1")
    @NotNull(message = "预留字段1不能为空")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    @NotNull(message = "预留字段2不能为空")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    @NotNull(message = "预留字段3不能为空")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @NotNull(message = "预留字段4不能为空")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @NotNull(message = "预留字段5不能为空")
    @JsonProperty("ReservedField5")
    private String reservedField5;

    @ApiModelProperty(value = "统计分组")
    @JsonProperty("StatisticalGroup")
    private Integer statisticalGroup;

    @ApiModelProperty(value = "统计分组")
    @JsonProperty("StatisticalGroupStr")
    private String statisticalGroupStr;

    @ApiModelProperty(value = "状态")
    @JsonProperty("IsValidStr")
    private String isValidStr;

    @ApiModelProperty(value = "培训总时长")
    @JsonProperty("TrainTotalTime")
    private String trainTotalTime;
}