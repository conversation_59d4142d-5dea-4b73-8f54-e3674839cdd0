package com.fzkj.project.basicdata.controller;

import com.fzkj.common.constant.Constants;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.basicdata.entity.SubStanceManage;
import com.fzkj.project.basicdata.request.SubStanceManageRequest;
import com.fzkj.project.basicdata.service.SubStanceManageService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-14 14:26
 * @Version: 1.0
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
//@RequestMapping("/basicdata")
public class SubStanceManageController {

    private final SubStanceManageService subStanceManageService;

    @PostMapping("Get_SubStanceManage")
    @ApiOperation(value = "内容分类分页查询")
    public AjaxResult listTrainManageByPage(@RequestBody CommonRequest commonRequest) {
        SubStanceManage request = (SubStanceManage) commonRequest.getRequest(SubStanceManage.class);
        return AjaxResult.success(subStanceManageService.listByPage(request, commonRequest.getPageEntity()));
    }

    /**
     * 新增保存常见问题
     */
    @PostMapping("/Edit_SubStanceManage")
    @ApiOperation(value = "新增内容分类")
    @ResponseBody
    public AjaxResult addSave(@RequestBody CommonRequest commonRequest) {
        SubStanceManage request = (SubStanceManage) commonRequest.getRequest(SubStanceManage.class);
        switch (request.getFlag()) {
            case Constants.ADD_DATA:
                return AjaxResult.success(subStanceManageService.insertSubStanceManage(request));
            case Constants.EDIT_DATA:
                return AjaxResult.success(subStanceManageService.updateSubStanceManage(request));
            case Constants.DEL_DATA:
                return AjaxResult.success(subStanceManageService.deleteSubStanceManage(request.getId()));
            default:
                return AjaxResult.error("操作异常！");
        }
    }

    @PostMapping("Get_HomeContentMobile")
    @ApiOperation(value = "内容分类分页查询（小程序）")
    public AjaxResult getHomeContentMobile(@RequestBody CommonRequest commonRequest) {
        SubStanceManageRequest request = (SubStanceManageRequest) commonRequest.getRequest(SubStanceManageRequest.class);
        return AjaxResult.success(subStanceManageService.getHomeContentMobile(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Add_SubStanceBrowsingRecord")
    @ApiOperation(value = "添加内容浏览记录（小程序）")
    public AjaxResult addSubStanceBrowsingRecord(@RequestBody CommonRequest commonRequest) {
        SubStanceManageRequest request = (SubStanceManageRequest) commonRequest.getRequest(SubStanceManageRequest.class);
        return AjaxResult.success(subStanceManageService.addSubStanceBrowsingRecord(request));
    }

    /**
     * 小程序banner获取接口
     *
     * @param commonRequest
     * @return
     */
    @PostMapping("Get_Banner")
    @ApiOperation(value = "小程序banner获取接口")
    public AjaxResult getBanner(@RequestBody CommonRequest commonRequest) {
        return AjaxResult.success(subStanceManageService.getBanner());
    }

    @PostMapping("Get_SubStanceAPP")
    @ApiOperation(value = "内容详情查询(小程序)")
    public AjaxResult getSubStanceAPP(@RequestBody CommonRequest commonRequest) {
        SubStanceManage request = (SubStanceManage) commonRequest.getRequest(SubStanceManage.class);
        if(request.getFlag().equals("list")){
            SubStanceManageRequest subStanceManageRequest = (SubStanceManageRequest) commonRequest.getRequest(SubStanceManageRequest.class);
            return AjaxResult.success(subStanceManageService.getHomeContentMobile(subStanceManageRequest,commonRequest.getPageEntity()));
        }else if(request.getFlag().equals("byId")){
            return AjaxResult.success(subStanceManageService.getSubStanceAPP(request.getId()));
        }else{
            return AjaxResult.error("请求类型错误","");
        }

    }
}
