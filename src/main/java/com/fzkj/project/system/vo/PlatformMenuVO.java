package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 平台菜单
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="平台菜单VO")
public class    PlatformMenuVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("FID")
    private Long fid;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Name")
    private String name;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Url")
    private String url;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Authority")
    private String authority;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Icon")
    private String icon;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("MenuClass")
    private String menuClass;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("MenuType")
    private String menuType;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("OperPlatform")
    private String operPlatform;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsShow")
    private Integer isShow;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private String sort;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Flag")
    private String flag;

}