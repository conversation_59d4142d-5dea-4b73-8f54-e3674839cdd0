<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CourseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.Course">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="course_name" property="courseName" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="tearcher_code" property="tearcherCode" />
        <result column="tearcher_name" property="tearcherName" />
        <result column="summary" property="summary" />
        <result column="content" property="content" />
        <result column="audio_type" property="audioType" />
        <result column="video_content" property="videoContent" />
        <result column="img_url" property="imgUrl" />
        <result column="file_url" property="fileUrl" />
        <result column="file_size" property="fileSize" />
        <result column="time_count" property="timeCount" />
        <result column="file_type" property="fileType" />
        <result column="file_id" property="fileId" />
        <result column="belong_plat" property="belongPlat" />
        <result column="is_show" property="isShow" />
        <result column="creator_name" property="creatorName" />
        <result column="revise_name" property="reviseName" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="reserved_field1" property="reservedField1" />
        <result column="reserved_field2" property="reservedField2" />
        <result column="reserved_field3" property="reservedField3" />
        <result column="reserved_field4" property="reservedField4" />
        <result column="reserved_field5" property="reservedField5" />
        <result column="second_title" property="secondTitle" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, course_name, category_id, category_name, teacher_code, teacher_name, summary, content, audio_type, video_content, img_url, file_url,
        file_size, time_count, file_type, file_id, belong_plat, is_show, creator_name, revise_name, source, sort, creator_code, creation_time, revise_code,
        revise_time, remark, is_valid, reserved_field1, reserved_field2, reserved_field3, reserved_field4, reserved_field5,course_code
    </sql>
    <select id="selectCourseInfo" resultType="com.fzkj.project.system.vo.CourseQueryVO">
        select * from (
            SELECT c.id, c.course_name, c.time_count, c.category_name, c.file_type, c.file_id, c.is_show, c.creation_time, t.real_name tearcherName, c.creator_name, IFNULL(cr.file_url_hls,c.file_url) file_url_hls, IFNULL(cr.encode_state,0) encode_state,
            (SELECT COUNT(*) FROM t_d_comment co WHERE co.target_id = c.id AND co.fun_type = 10 and  co.is_valid=1 and co.status =1 ) AS commentNum,
            (SELECT ROUND(AVG(COALESCE(co.grade, 0)), 0) FROM t_d_comment co WHERE co.target_id = c.id AND co.fun_type = 10 AND co.is_valid = 1 and co.grade>0) AS grade
            FROM t_d_course c
            LEFT JOIN t_d_courseware_resources cr ON cr.file_id = c.file_id
            LEFT JOIN t_d_teacher t ON c.tearcher_code = t.id
            where c.is_valid=1 and c.belong_plat = 1
            <if test="keywords != null and keywords != ''">
                and c.course_name like concat('%',#{keywords},'%')
            </if>
            <if test="encodeState != null">
                and cr.encode_state = #{encodeState}
            </if>
            <if test="isShow != null">
                and c.is_show = #{isShow}
            </if>
            <if test="fileType != null">
                and c.file_type = #{fileType}
            </if>
            <if test="tearcherCode != null and tearcherCode != ''">
                and c.tearcher_code = #{tearcherCode}
            </if>
            <if test="categoryId != null">
                and c.category_id = #{categoryId}
            </if>
            <if test="companyId != null">
                and c.company_id = #{companyId}
            </if>
        ) t
        <if test="sort != null">
            <if test="sort==2">
                order by learnNum desc
            </if>
            <if test="sort==3">
                order by grade desc
            </if>
            <if test="sort==4">
                order by commentNum desc
            </if>
            <if test="sort==5">
                order by time_count desc
            </if>
            <if test="sort==6">
                order by creation_time asc
            </if>
        </if>
        <if test="sort == null or sort == '' or sort == 1">
            order by creation_time desc
        </if>

    </select>
    <select id="courseStatistics" resultType="com.fzkj.project.system.vo.CourseStatisticsVO">
        SELECT COUNT(1) CourseNum, CAST(SUM(c.time_count) / 60 / 45 AS DECIMAL(18, 1)) TotalTimeCount, COUNT(CASE WHEN c.is_show = 1 THEN 1 ELSE NULL END) CompanyUseNum,
        (SELECT COUNT(1) FROM (SELECT COUNT(1) num FROM t_d_course t WHERE t.is_valid = 1 AND t.belong_plat = 1 AND t.tearcher_code != '' AND t.tearcher_name != '' GROUP BY t.tearcher_code) a) TearcherNum,
        (SELECT COUNT(1) num FROM t_d_comment ct WHERE ct.fun_type = '10' AND ct.target_id IN (SELECT t.ID FROM t_d_course t WHERE t.is_valid = 1 AND t.belong_plat = 1) AND ct.is_valid = 1 AND ct.status = 1 AND LENGTH(ct.content) > 0) CommentNum
        FROM t_d_course c WHERE c.is_valid = 1
        and c.belong_plat = 1
    </select>
    <select id="getCourse" resultType="com.fzkj.project.system.vo.CourseVO">
        SELECT c.*,
        (SELECT company_name FROM t_d_company WHERE id=c.company_id) companyName,
        IFNULL(cr.file_url_hls,c.file_url) fileUrlHLS,
        IFNULL(cr.encode_state,0) encodeState from t_d_course c
        left join  t_d_courseware_resources cr ON c.file_id=cr.file_id
        <where>
            <choose>
                <when test="id != null">
                    and c.id = #{id}
                </when>
                <otherwise>
                    <if test="isValid != null">
                        and c.is_valid = #{isValid}
                    </if>
                    <choose>
                        <when test="belongPlat != null">
                            and c.belong_plat = #{belongPlat}
                        </when>
                        <otherwise>
                            and c.belong_plat = 1
                        </otherwise>
                    </choose>
                    <if test="companyId != null">
                        and c.company_id = #{companyId}
                    </if>
                    <if test="categoryId != null">
                        and c.category_id = #{categoryId}
                    </if>
                    <if test="tearcherCode != null and tearcherCode != ''">
                        and c.tearcher_code = #{tearcherCode}
                    </if>
                    <if test="fileType != null">
                        and c.file_type = #{fileType}
                    </if>
                    <if test="isShow != null">
                        and c.is_show = #{isShow}
                    </if>
                    <if test="encodeState != null">
                        and cr.encode_state = #{encodeState}
                    </if>
                    <if test="keywords != null and keywords != ''">
                        and ((case when #{keywords} REGEXP '^[0-9]+$' THEN CONVERT(c.id, CHAR(50)) else c.course_name end) LIKE CONCAT('%', #{keywords}, '%'))
                    </if>
                </otherwise>
            </choose>
        </where>
        order by c.id desc
    </select>
    <select id="selectCommentAPP" resultType="com.fzkj.project.system.vo.GetCommentAPPVO">
        select
        tdc.id,
        (CASE WHEN length(uc.user_name)>0 THEN uc.user_name ELSE REPLACE(ui.phone,SUBSTRING(ui.phone,4,4), '****') END) userName,
        tdc.grade,
        uc.user_photo,
        tdc.content,
        tdc.creation_time,
        tdc.is_reply,
        (SELECT COUNT(1) FROM t_d_comment_reply WHERE is_valid=1 AND target_id=tdc.id AND Type=2) isTeacherReply
        FROM t_d_comment tdc
        LEFT JOIN t_d_user_company uc ON tdc.user_code=uc.user_code
        left join t_d_user_info ui on ui.user_code = uc.user_code
        WHERE tdc.is_valid = 1
        AND (tdc.status=1 OR tdc.user_code=#{userCode})
        AND tdc.fun_type = #{funType}
        AND tdc.target_id = #{targetId}
        AND tdc.grade&lt;=0
        ORDER BY tdc.ID desc
    </select>
    <select id="selectCommentReplayList" resultType="com.fzkj.project.system.entity.CommentReply">
        SELECT user_name, reply_content,creation_time,type,target_id FROM t_d_comment_reply WHERE is_valid=1 AND target_id in
        <foreach collection="targetIds" item="targetId" open="(" separator="," close=")">
            #{targetId}
        </foreach>
    </select>
    <select id="selectTextModerationCount" resultType="java.lang.Integer">
        select count(1) from t_d_word_filter_sensitive_words where word_type = 'sensitive' and del = 0 and #{text} LIKE CONCAT('%', word, '%')
    </select>
    <select id="selectCourseList" resultType="com.fzkj.project.system.vo.GetCourseListVO">
        select
        lcc.lesson_id,
        lcc.course_id,
        c.course_name,
        c.time_count,
        c.file_type,
        coalesce(cr.file_url_hls, cr.file_url) as file_url,
        lcc.sort
        from
        t_d_lesson_course_safe_train as lcc
        left join t_d_course as c on
        c.id = lcc.course_id
        left join t_d_courseware_resources as cr on
        c.file_id = cr.file_id
        where
        lcc.is_valid = 1
        and lcc.lesson_id =
        #{lessonId}
        order by lcc.sort asc,lcc.course_id asc
    </select>
    <select id="selectLessonCourseSafeTrainInfo"  resultType="com.fzkj.project.system.vo.LessonCourseSafeTrainInfoVO">
        select c.course_name,c.file_type,IFNULL(cr.file_url_hls, c.file_url )fileUrl,c.time_count,lst.cours_face_num,lst.study_face_state
        from t_d_lesson_course_safe_train lcs
        left join t_d_course c on lcs.course_id =c.id and c.is_valid =1
        left join t_d_courseware_resources cr on c.file_id = cr.file_id
        left join t_d_lesson_safe_train lst on lcs.lesson_id = lst.id  where lcs.is_valid =1 and lcs.lesson_id = #{lessonId} and lcs.course_id = #{courseId}
    </select>

    <select id="selectUserCourseDetail" resultType="com.fzkj.project.system.vo.GetUserCourseDetailVO">
        select
        c.id courseId,
        c.course_name,
        c.time_count,
        c.file_type,
        c.file_id,
        c.file_url,
        c.img_url,
        c.content,
        c.audio_type,
        c.video_content,
        IFNULL(cr.file_url_hls, c.file_url) fileUrl,
        c.tearcher_name,
        IFNULL(t.technical_title,'') technicalTitle,
        (SELECT ROUND(AVG(COALESCE(grade, 0)), 0) FROM t_d_comment WHERE target_id = c.id AND fun_type = 10 AND is_valid = 1 and grade>0) AS grade,
        case when(select count(1) from t_d_comment ct where ct.fun_type = 10 and ct.target_id = c.id and ct.is_valid =1 and ct.grade >0  and ct.user_code = #{userCode})>0 then 1 else 0 end isGrade,
        l.study_sign_state,l.study_face_state,l.study_count_down_time,l.study_fail_repeat_num,l.cours_face_num,
        c.reserved_field4
        from t_d_course c
        left join t_d_courseware_resources cr on c.file_id = cr.file_id
        left join t_d_teacher t on t.id = c.tearcher_code
        left join t_d_lesson_safe_train l on  l.id  = #{lessonId}
        where c.is_valid = 1 and c.id = #{courseId}
    </select>

</mapper>