package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetCourseAPPVO {

    @ApiModelProperty(value = "课件id")
    @JsonProperty("ID")
    private String id;


    @ApiModelProperty(value = "课程名称")
    @JsonProperty("CourseName")
    private String courseName;

    @ApiModelProperty(value = "课程内容")
    @JsonProperty("Content")
    private String content;

    @ApiModelProperty(value = "视频内容")
    @JsonProperty("VideoContent")
    private String videoContent;

    @ApiModelProperty(value = "教师姓名")
    @JsonProperty("TearcherName")
    private String tearcherName;

    @ApiModelProperty(value = "时间长度")
    @JsonProperty("TimeCount")
    private Integer timeCount;

    @ApiModelProperty(value = "文件ID")
    @JsonProperty("FileID")
    private String fileId;

    @ApiModelProperty(value = "摘要")
    @JsonProperty("Summary")
    private String summary;

    @ApiModelProperty(value = "文件类型")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(value = "图片URL")
    @JsonProperty("ImgUrl")
    private String imgUrl;

    @ApiModelProperty(value = "音频类型")
    @JsonProperty("AudioType")
    private Integer audioType;

    @ApiModelProperty(value = "保留字段3")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "评分")
    @JsonProperty("Grade")
    private double grade;

    @ApiModelProperty(value = "评论数量")
    @JsonProperty("CommentNum")
    private int commentNum;

    @ApiModelProperty(value = "是否收藏")
    @JsonProperty("IsCollection")
    private int isCollection;

    @ApiModelProperty(value = "是否评分")
    @JsonProperty("IsGrade")
    private int isGrade;

    @ApiModelProperty(value = "文件URL")
    @JsonProperty("FileUrl")
    private String fileUrl;

}
