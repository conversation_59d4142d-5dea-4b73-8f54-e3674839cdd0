package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.UserRelationWork;
import com.fzkj.project.system.vo.UserRelationWorkVO;
import com.fzkj.project.system.request.UserRelationWorkRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户关联岗位 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface UserRelationWorkService extends IService<UserRelationWork> {

    List<UserRelationWork> listUserRelationWork(UserRelationWorkRequest request);

    PageUtils listByPage(UserRelationWorkRequest request, PageEntity pageEntity);

    UserRelationWorkVO findUserRelationWork(Long id);

    Long saveUserRelationWork(UserRelationWorkVO vo);

    boolean updateUserRelationWork(UserRelationWorkVO vo);

    boolean removeUserRelationWork(Long id);
}
