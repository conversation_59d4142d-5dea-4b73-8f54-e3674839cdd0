package com.fzkj.common.utils.file;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

/**
 * 文件处理工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class FileUtils {
    public static final String FILENAME_PATTERN = "[a-zA-Z0-9_()（）\\-\\|\\.\\u4e00-\\u9fa5]+";

    /**
     * 输出指定文件的byte数组
     *
     * @param filePath 文件路径
     * @param os       输出流
     * @return
     */
    public static void writeBytes(String filePath, OutputStream os) throws IOException {
        FileInputStream fis = null;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                throw new FileNotFoundException(filePath);
            }
            fis = new FileInputStream(file);
            byte[] b = new byte[1024];
            int length;
            while ((length = fis.read(b)) > 0) {
                os.write(b, 0, length);
            }
        } catch (IOException e) {
            throw e;
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public static void writeBytes(InputStream is, OutputStream os) throws IOException {
        try {
            byte[] b = new byte[1024];
            int length;
            while ((length = is.read(b)) > 0) {
                os.write(b, 0, length);
            }
        } catch (IOException e) {
            throw e;
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 删除文件
     *
     * @param filePath 文件
     * @return
     */
    public static boolean deleteFile(String filePath) {
        boolean flag = false;
        File file = new File(filePath);
        // 路径为文件且不为空则进行删除
        if (file.exists()) {
            file.delete();
            flag = true;
            log.info("删除文件:{}", filePath);
        }
        return flag;
    }

    /**
     * 文件名称验证
     *
     * @param filename 文件名称
     * @return true 正常 false 非法
     */
    public static boolean isValidFilename(String filename) {
        return filename.matches(FILENAME_PATTERN);
    }

    /**
     * 下载文件名重新编码
     *
     * @param request  请求对象
     * @param fileName 文件名
     * @return 编码后的文件名
     */
    public static String setFileDownloadHeader(HttpServletRequest request, String fileName)
            throws UnsupportedEncodingException {
        final String agent = request.getHeader("USER-AGENT");
        String filename = fileName;
        if (agent.contains("MSIE")) {
            // IE浏览器
            filename = URLEncoder.encode(filename, "utf-8");
            filename = filename.replace("+", " ");
        } else if (agent.contains("Firefox")) {
            // 火狐浏览器
            filename = new String(fileName.getBytes(), "ISO8859-1");
        } else if (agent.contains("Chrome")) {
            // google浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        } else {
            // 其它浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        }
        return filename;
    }

    public static List<String> getFileList(String path, List<String> list) {
        //先将指定路径下的所有文件实例化
        File file = new File(path);
        //判断实例化的对象file是否存在，即指定路径是否存在
        if (file.exists()) {
            //则将所有文件的实例化对象转化为数组形式
            File[] files = file.listFiles();
            //遍历文件数组
            for (File file2 : files) {
                //如果从数组中拿出来的值是File是文件类型，就直接先打印这个文件的路径名称
                list.add(file2.getPath());
                //如果是拿出来的File是文件夹类型，就调用自己，利用递归的思想，即一层一层地打开
                if (file2.isDirectory()) {
                    //调用自己时候传入的参数为上一句判断出来的文件夹路径
                    getFileList(file2.getAbsolutePath(), list);
                }
            }
        }
        return list;
    }

    public static void deleteDir(String pathName) {
        List<String> list = getFileList(pathName, Lists.newArrayList());
        for (int i = list.size() - 1; i >= 0; i--) {
            String filePath = list.get(i);
            deleteFile(filePath);
        }
    }
}
