package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.LessonSafeTrainCompany;
import com.fzkj.project.system.vo.LessonInfoVO;
import com.fzkj.project.system.vo.LessonSafeTrainCompanyVO;
import com.fzkj.project.system.request.LessonSafeTrainCompanyRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.LessonSafeTrainCompanyMapper;
import com.fzkj.project.system.service.LessonSafeTrainCompanyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 给企业分发的安全培训课程关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class LessonSafeTrainCompanyServiceImpl extends ServiceImpl<LessonSafeTrainCompanyMapper, LessonSafeTrainCompany> implements LessonSafeTrainCompanyService {

    @Override
    public List<LessonSafeTrainCompany> listLessonSafeTrainCompany(LessonSafeTrainCompanyRequest request) {
        LessonSafeTrainCompany entity = (LessonSafeTrainCompany) DataTransfer.transfer(request, LessonSafeTrainCompany.class);
        LambdaQueryWrapper<LessonSafeTrainCompany> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(LessonSafeTrainCompanyRequest request, PageEntity pageEntity) {
        LessonSafeTrainCompany entity = (LessonSafeTrainCompany) DataTransfer.transfer(request, LessonSafeTrainCompany.class);
        LambdaQueryWrapper<LessonSafeTrainCompany> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<LessonSafeTrainCompany> page = this.page(
                new Query<LessonSafeTrainCompany>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public LessonSafeTrainCompanyVO findLessonSafeTrainCompany(Long id) {
        LessonSafeTrainCompany entity = this.getById(id);
        LessonSafeTrainCompanyVO vo = (LessonSafeTrainCompanyVO) DataTransfer.transfer(entity, LessonSafeTrainCompanyVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveLessonSafeTrainCompany(LessonSafeTrainCompanyVO vo) {
        LessonSafeTrainCompany entity = (LessonSafeTrainCompany) DataTransfer.transfer(vo, LessonSafeTrainCompany.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLessonSafeTrainCompany(LessonSafeTrainCompanyVO vo) {
        LessonSafeTrainCompany entity = (LessonSafeTrainCompany) DataTransfer.transfer(vo, LessonSafeTrainCompany.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeLessonSafeTrainCompany(Long id) {
        return this.removeById(id);
    }

    @Override
    public List<LessonInfoVO> getLessonInfo(String month) {
        return getBaseMapper().selectLessonInfo(month);
    }
}