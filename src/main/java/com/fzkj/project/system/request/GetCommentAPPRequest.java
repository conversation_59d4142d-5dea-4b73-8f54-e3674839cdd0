package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetCommentAPPRequest {

    @ApiModelProperty(value = "用户编码", example = "b135339037ae410a87ad42c872c9353f")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "目标ID", example = "1")
    @JsonProperty("TargetID")
    private Long targetId;

    @ApiModelProperty(value = "功能模块（10：课件，11：内容）", example = "10")
    @JsonProperty("FunType")
    private String funType;
}
