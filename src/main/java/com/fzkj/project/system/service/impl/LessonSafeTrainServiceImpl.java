package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.common.utils.aliyun.FaceUtil;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.image.ImageGenerator;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.mapper.*;
import com.fzkj.project.system.mongo.UserCourseRecordSafeTrain;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.*;
import com.fzkj.project.system.vo.*;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 安全培训课程 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class LessonSafeTrainServiceImpl extends ServiceImpl<LessonSafeTrainMapper, LessonSafeTrain> implements LessonSafeTrainService {
    private final static List completeStatus = Arrays.asList(1, 2, 4);
    private final static List noBeginStatus = Arrays.asList(-3, 3, 4);
    private final static List noCompleteStatus = Arrays.asList(0, 2);
    private final static List completedStatus = Arrays.asList(2, 4);
    private final LessonCourseSafeTrainService lessonCourseSafeTrainService;
    private final LessonSafeTrainCompanyService lessonSafeTrainCompanyService;
    private final CompanyLessonSafeTrainSetService companyLessonSafeTrainSetService;
    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;
    private final UserCourseRecordSafeTrainMongoService userCourseRecordSafeTrainMongoService;
    private final CourseService courseService;
    private final ExamService examService;
    private final CompanyService companyService;
    private final ExamLessonService examLessonService;
    private final ExaminationPaperRuleService examinationPaperRuleService;
    private final ExamQuestionService examQuestionService;
    private final WxPushTabService wxPushTabService;
    private final PushCenterMsgMapper pushCenterMsgMapper;
    private final UserCompanyMapper userCompanyMapper;
    private final WxService wxService;

    private final LessonSafeTrainMapper lessonSafeTrainMapper;

    private final LessonSafeTrainCompanyMapper lessonSafeTrainCompanyMapper;

    private final CompanyLessonSafeTrainSetMapper companyLessonSafeTrainSetMapper;

    private final static Map<Integer, String> fileTypeMap = new HashMap<>();

    static {
        //1：视频，2：文件，3：图文，4：音频
        fileTypeMap.put(1, "视频");
        fileTypeMap.put(2, "文件");
        fileTypeMap.put(3, "图文");
        fileTypeMap.put(4, "音频");
    }

    @Override
    public PageUtils listLessonSafeTrain(LessonSafeTrainRequest request, PageEntity pageEntity) {
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        switch (request.getFlag()) {
            case "list":
                String sTime = request.getSTime();
                if (StringUtils.isNotEmpty(sTime) && sTime.length() < 10) {
                    request.setSTime(sTime + "-01");
                }
                String eTime = request.getETime();
                if (StringUtils.isNotEmpty(eTime) && eTime.length() < 10) {
                    request.setETime(eTime + "-01");
                }
                PageUtils pageUtils = new PageUtils(pageEntity, getBaseMapper().selectLessonSafeTrain(request));
                if(CollectionUtils.isNotEmpty(pageUtils.getList())){
                    List<LessonSafeTrainVO> results = (List<LessonSafeTrainVO>) pageUtils.getList();
                    results.forEach(lesson->{
                        //查询学习次数
                        lesson.setRecordNum(userLessonRecordSafeTrainMongoService.getUserLessonLearningCount(lesson.getLessonId()));
                    });
                }
                return pageUtils;
            case "byId":
                ArrayList<Object> list = new ArrayList<>();
                LessonSafeTrain lessonSafeTrain = getById(request.getId());
                if (null != lessonSafeTrain) {
                    list.add(lessonSafeTrain);
                }
                return new PageUtils(pageEntity, list);
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    public PageUtils listByPage(LessonSafeTrainRequest request, PageEntity pageEntity) {
        LessonSafeTrain entity = (LessonSafeTrain) DataTransfer.transfer(request, LessonSafeTrain.class);
        LambdaQueryWrapper<LessonSafeTrain> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<LessonSafeTrain> page = this.page(new Query<LessonSafeTrain>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public LessonSafeTrainVO findLessonSafeTrain(Long id) {
        LessonSafeTrain entity = this.getById(id);
        LessonSafeTrainVO vo = (LessonSafeTrainVO) DataTransfer.transfer(entity, LessonSafeTrainVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveLessonSafeTrain(LessonSafeTrainVO vo) {
        LessonSafeTrain entity = (LessonSafeTrain) DataTransfer.transfer(vo, LessonSafeTrain.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLessonSafeTrain(LessonSafeTrainVO vo) {
        LessonSafeTrain entity = (LessonSafeTrain) DataTransfer.transfer(vo, LessonSafeTrain.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeLessonSafeTrain(Long id) {
        return this.removeById(id);
    }

    @Override
    public PageUtils getCourseByLessonSafeTrainID(LessonSafeTrainCourseRequest request, PageEntity pageEntity) {
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        switch (request.getFlag()) {
            case "1":
                return new PageUtils(pageEntity, getBaseMapper().getCourseByLessonSafeTrainId1(request));
            case "2":
                return new PageUtils(pageEntity, getBaseMapper().getCourseByLessonSafeTrainId2(request));
            case "3":
                return new PageUtils(pageEntity, getBaseMapper().getCourseByLessonSafeTrainId3(request));
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long editLessonSafeTrain(LessonSafeTrainVO request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LessonSafeTrain entity = (LessonSafeTrain) DataTransfer.transfer(request, LessonSafeTrain.class);
        String lessonDate = request.getLessonDate();
        if (StringUtils.isNotEmpty(lessonDate) && lessonDate.length() < 10) {
            entity.setLessonDate(lessonDate + "-01");
        }
        switch (request.getFlag()) {
            case "add":
                entity.setId(null);
                entity.setIsValid(-1);
                entity.setCreatorCode(user.getUserCode());
                entity.setCreationTime(LocalDateTime.now());
                entity.setReviseCode(user.getUserCode());
                entity.setReviseTime(LocalDateTime.now());
                this.save(entity);
                return entity.getId();
            case "edit":
                LambdaQueryWrapper<LessonSafeTrain> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(LessonSafeTrain::getId, entity.getId());
                LessonSafeTrain lessonSafeTrain = this.getOne(queryWrapper);
                if (lessonSafeTrain.getShape() != null && !lessonSafeTrain.getShape().equals(entity.getShape())) {
                    entity.setIsValid(-1);
                }
                entity.setReviseCode(user.getUserCode());
                entity.setReviseTime(LocalDateTime.now());
                this.updateById(entity);
                return entity.getId();
            case "del":
                LambdaQueryWrapper<LessonCourseSafeTrain> existWrapper = new LambdaQueryWrapper<>();
                existWrapper.eq(LessonCourseSafeTrain::getLessonId, request.getId());
                existWrapper.eq(LessonCourseSafeTrain::getIsValid, 1);
                int exist = lessonCourseSafeTrainService.count(existWrapper);
                if (exist > 0) {
                    throw new CustomException("删除失败,该课程下有课件");
                }
                LambdaUpdateWrapper<LessonSafeTrain> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(LessonSafeTrain::getId, request.getId());
                updateWrapper.set(LessonSafeTrain::getIsValid, 0);
                updateWrapper.set(LessonSafeTrain::getReviseCode, user.getUserCode());
                updateWrapper.set(LessonSafeTrain::getReviseTime, LocalDateTime.now());
                boolean del = this.update(updateWrapper);
                if (del) {
                    LambdaUpdateWrapper<LessonSafeTrainCompany> lessonSafeTrainCompanyUpdateWrapper = new LambdaUpdateWrapper<>();
                    lessonSafeTrainCompanyUpdateWrapper.eq(LessonSafeTrainCompany::getLessonId, request.getId());
                    lessonSafeTrainCompanyUpdateWrapper.set(LessonSafeTrainCompany::getIsValid, 0);
                    lessonSafeTrainCompanyUpdateWrapper.set(LessonSafeTrainCompany::getReviseCode, user.getUserCode());
                    lessonSafeTrainCompanyUpdateWrapper.set(LessonSafeTrainCompany::getReviseTime, LocalDateTime.now());
                    lessonSafeTrainCompanyService.update(lessonSafeTrainCompanyUpdateWrapper);
                    return request.getId();
                } else {
                    throw new CustomException("删除失败");
                }
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editLessonSafeTrainDel(LessonSafeTrainVO request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        String lessonIDs = request.getLessonIDs();
        switch (request.getFlag()) {
            case "del":
                if (StringUtils.isEmpty(lessonIDs)) {
                    throw new CustomException(1002, "请选择要指定删除的课程");
                }
                //查询该课程是否有分发记录
                UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
                queryRequest.setLessonIds(lessonIDs);
                List<UserLessonRecordSafeTrain> userLesson = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
                if (CollectionUtils.isNotEmpty(userLesson)) {
                    throw new CustomException(1002, "课程已分发，无法删除");
                }
                String[] lessonIds = lessonIDs.split(",");
                for (int i = 0; i < lessonIds.length; i++) {
                    Long lessonId = Long.valueOf(lessonIds[i]);
                    LambdaUpdateWrapper<LessonSafeTrain> lessonSafeTrainUpdateWrapper = new LambdaUpdateWrapper();
                    lessonSafeTrainUpdateWrapper.eq(LessonSafeTrain::getId, lessonId);
                    lessonSafeTrainUpdateWrapper.set(LessonSafeTrain::getIsValid, 0);
                    lessonSafeTrainUpdateWrapper.set(LessonSafeTrain::getReviseCode, user.getUserCode());
                    lessonSafeTrainUpdateWrapper.set(LessonSafeTrain::getReviseTime, LocalDateTime.now());
                    boolean update = this.update(lessonSafeTrainUpdateWrapper);
                    if (update) {
                        LambdaUpdateWrapper<LessonSafeTrainCompany> lessonSafeTrainCompanyLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                        lessonSafeTrainCompanyLambdaUpdateWrapper.eq(LessonSafeTrainCompany::getLessonId, lessonId).eq(LessonSafeTrainCompany::getIsValid, 1).set(LessonSafeTrainCompany::getIsValid, 0).set(LessonSafeTrainCompany::getReviseCode, user.getUserCode()).set(LessonSafeTrainCompany::getReviseTime, LocalDateTime.now());
                        lessonSafeTrainCompanyService.update(lessonSafeTrainCompanyLambdaUpdateWrapper);

                        LambdaUpdateWrapper<LessonCourseSafeTrain> lessonCourseSafeTrainLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                        lessonCourseSafeTrainLambdaUpdateWrapper.eq(LessonCourseSafeTrain::getLessonId, lessonId).eq(LessonCourseSafeTrain::getIsValid, 1).set(LessonCourseSafeTrain::getIsValid, 0).set(LessonCourseSafeTrain::getReviseCode, user.getUserCode()).set(LessonCourseSafeTrain::getReviseTime, LocalDateTime.now());
                        lessonCourseSafeTrainService.update(lessonCourseSafeTrainLambdaUpdateWrapper);
                    }
                }
                return true;
            case "complete":
                if (StringUtils.isEmpty(lessonIDs)) {
                    throw new CustomException(1002, "请选择要指定发布的课程");
                }
                LambdaUpdateWrapper<LessonSafeTrain> lessonSafeTrainLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lessonSafeTrainLambdaUpdateWrapper.in(LessonSafeTrain::getId, lessonIDs.split(",")).set(LessonSafeTrain::getIsValid, 1).set(LessonSafeTrain::getReviseCode, user.getUserCode()).set(LessonSafeTrain::getReviseTime, LocalDateTime.now());
                this.update(lessonSafeTrainLambdaUpdateWrapper);
                return true;
            case "cancel":
                if (StringUtils.isEmpty(lessonIDs)) {
                    throw new CustomException(1002, "请选择要指定取消发布的课程");
                }
                LambdaUpdateWrapper<LessonSafeTrain> lessonSafeTrainLambdaCancelWrapper = new LambdaUpdateWrapper<>();
                lessonSafeTrainLambdaCancelWrapper.in(LessonSafeTrain::getId, lessonIDs.split(",")).set(LessonSafeTrain::getIsValid, -1).set(LessonSafeTrain::getReviseCode, user.getUserCode()).set(LessonSafeTrain::getReviseTime, LocalDateTime.now());
                this.update(lessonSafeTrainLambdaCancelWrapper);
                return true;
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long editLessonSafeTrainCopy(LessonSafeTrainVO request) {
        LessonSafeTrain lessonSafeTrain = getById(request.getLessonId());
        if (null == lessonSafeTrain) {
            throw new CustomException("课程不存在");
        }
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        lessonSafeTrain.setId(null);
        lessonSafeTrain.setLessonName(request.getLessonName());
        lessonSafeTrain.setCompanyId(request.getCompanyId());
        lessonSafeTrain.setDepartId(request.getDepartId());
        if (null == request.getBelongPlat()) {
            lessonSafeTrain.setBelongPlat(1);
        } else {
            lessonSafeTrain.setBelongPlat(request.getBelongPlat());
        }
        lessonSafeTrain.setRemark("复制");
        lessonSafeTrain.setIsValid(-1);
        lessonSafeTrain.setCreatorCode(user.getUserCode());
        lessonSafeTrain.setCreationTime(now);
        lessonSafeTrain.setReviseCode(user.getUserCode());
        lessonSafeTrain.setReviseTime(now);
        this.save(lessonSafeTrain);
        //查询关联的课件
        LambdaQueryWrapper<LessonCourseSafeTrain> lessonCourseSafeTrainLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lessonCourseSafeTrainLambdaQueryWrapper.eq(LessonCourseSafeTrain::getLessonId, request.getLessonId());
        lessonCourseSafeTrainLambdaQueryWrapper.eq(LessonCourseSafeTrain::getIsValid, 1);
        List<LessonCourseSafeTrain> lessonCourseSafeTrainList = lessonCourseSafeTrainService.list(lessonCourseSafeTrainLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(lessonCourseSafeTrainList)) {
            List<LessonCourseSafeTrain> addLessonCourseSafeTrainList = new ArrayList<>();
            for (LessonCourseSafeTrain lessonCourseSafeTrain : lessonCourseSafeTrainList) {
                lessonCourseSafeTrain.setId(null);
                lessonCourseSafeTrain.setLessonId(lessonSafeTrain.getId());
                lessonCourseSafeTrain.setSource(lessonCourseSafeTrain.getSource());
                lessonCourseSafeTrain.setIsValid(1);
                lessonCourseSafeTrain.setRemark("复制");
                lessonCourseSafeTrain.setCreatorCode(user.getUserCode());
                lessonCourseSafeTrain.setCreationTime(now);
                lessonCourseSafeTrain.setReviseCode(user.getUserCode());
                lessonCourseSafeTrain.setReviseTime(now);
                addLessonCourseSafeTrainList.add(lessonCourseSafeTrain);
            }
            lessonCourseSafeTrainService.saveBatch(addLessonCourseSafeTrainList);
        }
        return lessonSafeTrain.getId();
    }

    @Override
    public PageUtils getLessonSafeTrainListBus(LessonSafeTrainRequest request, PageEntity pageEntity) {
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        switch (request.getFlag()) {
            case "list":
                List<LessonSafeTrain> list = getBaseMapper().selectLessonSafeTrainListBus(request);
                return new PageUtils(pageEntity, list);
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    public PageUtils getLessonSafeTrainListByPlanIdBus(LessonSafeTrainRequest request, PageEntity pageEntity) {
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        switch (request.getFlag()) {
            case "list":
                List<LessonSafeTrain> list = getBaseMapper().selectLessonSafeTrainListByPlanIdBus(request);
                return new PageUtils(pageEntity, list);
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editLessonSafeTrainChoiceDistribute(LessonSafeTrainDisVO request) {
        // 获取课程信息
        LessonSafeTrain lessonSafeTrain = getById(request.getLessonId());
        if (null == lessonSafeTrain) {
            throw new CustomException("课程不存在");
        }
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        //获取课程关联课件数量
        LambdaQueryWrapper<LessonCourseSafeTrain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LessonCourseSafeTrain::getLessonId, request.getLessonId());
        queryWrapper.eq(LessonCourseSafeTrain::getIsValid, 1);
        int courseCount = lessonCourseSafeTrainService.count(queryWrapper);
        //查询已经分发的企业
        LambdaQueryWrapper<LessonSafeTrainCompany> companyIdQueryWrapper = new LambdaQueryWrapper<>();
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getIsValid, 1);
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getLessonId, request.getLessonId());
        List<LessonSafeTrainCompany> existCompany = lessonSafeTrainCompanyService.list(companyIdQueryWrapper);
        List<Long> existCompanyIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(existCompany)) {
            existCompanyIdList = existCompany.stream().map(LessonSafeTrainCompany::getCompanyId).collect(Collectors.toList());
        }
        //排除已经分发的企业
        String[] companyIds = request.getCompanyIds().split(",");
        List<Long> distributeCompanyIdList = new ArrayList<>();
        for (int i = 0; i < companyIds.length; i++) {
            Long companyId = Long.valueOf(companyIds[i]);
            if (!existCompanyIdList.contains(companyId)) {
                distributeCompanyIdList.add(companyId);
            }
        }
        if (!distributeCompanyIdList.isEmpty()) {
            //获取企业设置内容
            LambdaQueryWrapper<CompanyLessonSafeTrainSet> contentQueryWrapper = new LambdaQueryWrapper<>();
            contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getIsValid, 1);
            contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getLessonCategoryId, lessonSafeTrain.getLessonCategoryId());
            contentQueryWrapper.in(CompanyLessonSafeTrainSet::getCompanyId, distributeCompanyIdList);
            List<CompanyLessonSafeTrainSet> list = companyLessonSafeTrainSetService.list(contentQueryWrapper);
            int sort = 0;
            List<LessonSafeTrainCompany> addList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                CompanyLessonSafeTrainSet companyLessonSafeTrainSet = list.get(i);
                LessonSafeTrainCompany company = new LessonSafeTrainCompany();
                company.setCompanyId(companyLessonSafeTrainSet.getCompanyId());
                company.setCompanyName(companyLessonSafeTrainSet.getCompanyName());
                company.setLessonId(request.getLessonId());
                company.setLessonCategoryId(companyLessonSafeTrainSet.getLessonCategoryId());
                company.setLessonCategoryName(companyLessonSafeTrainSet.getLessonCategoryName());
                company.setHandMode(request.getHandMode());
                company.setSort(sort);
                company.setIsValid(1);
                company.setCreatorCode(user.getUserCode());
                company.setCreationTime(now);
                company.setReviseCode(user.getUserCode());
                company.setReviseTime(now);
                addList.add(company);
                sort++;
            }
            lessonSafeTrainCompanyService.saveBatch(addList);
        }
        request.setLessonCategoryId(lessonSafeTrain.getLessonCategoryId());
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList = userLessonRecordSafeTrainMongoService.getLessonSafeTrainUnDisUser(request);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrainList)) {
            int shape = lessonSafeTrain.getShape();
            int issue = 1;
            Long examId = 0L;
            int resitNumber = 0;
            if (shape != 2) {
                //查询试卷信息
                ExamRequest examRequest = new ExamRequest();
                examRequest.setLessonId(request.getLessonId());
                PageUtils examInfo = examService.getExamInfo(examRequest, null);
                List examInfoList = examInfo.getList();
                if (CollectionUtils.isNotEmpty(examInfoList)) {
                    ExamVO examVO = (ExamVO) examInfoList.get(0);
                    examId = examVO.getId();
                    resitNumber = examVO.getResitNumber();
                    issue = Integer.valueOf(examVO.getRemark());
                }
            }
            if (issue != 1) {
                throw new CustomException("试卷未发布！");
            }
            for (int i = 0; i < userLessonRecordSafeTrainList.size(); i++) {
                UserLessonRecordSafeTrain userLessonRecordSafeTrain = userLessonRecordSafeTrainList.get(i);
                userLessonRecordSafeTrain.setLessonName(lessonSafeTrain.getLessonName());
                userLessonRecordSafeTrain.setLessonDate(lessonSafeTrain.getLessonDate());
                userLessonRecordSafeTrain.setLessonCategoryId(lessonSafeTrain.getLessonCategoryId());
                userLessonRecordSafeTrain.setLessonCategoryName(lessonSafeTrain.getLessonCategoryName());
                userLessonRecordSafeTrain.setLessonId(request.getLessonId());
                userLessonRecordSafeTrain.setTrainStartTime(lessonSafeTrain.getSTime());
                userLessonRecordSafeTrain.setTrainEndTime(lessonSafeTrain.getETime());
                userLessonRecordSafeTrain.setShape(shape);
                userLessonRecordSafeTrain.setHandMode(1);
                userLessonRecordSafeTrain.setLessonCourseCount(courseCount);
                userLessonRecordSafeTrain.setReservedField1(lessonSafeTrain.getLessonPic());
                userLessonRecordSafeTrain.setExamId(examId);
                userLessonRecordSafeTrain.setResitNumber(resitNumber);
                userLessonRecordSafeTrain.setCreationTime(now);
                userLessonRecordSafeTrain.setCreatorCode(user.getUserCode());
                userLessonRecordSafeTrain.setIsValid(1);
                userLessonRecordSafeTrain.setStaticStatus(-3);
                userLessonRecordSafeTrain.setIsComplete(-3);
                userLessonRecordSafeTrain.setIsPay(1);
                userLessonRecordSafeTrain.setScore(0);
                userLessonRecordSafeTrain.setSort(1);
                userLessonRecordSafeTrain.setReviseTime(now);
                userLessonRecordSafeTrain.setIsStatic(1);
            }
            userLessonRecordSafeTrainMongoService.saveBatch(userLessonRecordSafeTrainList);
            //推送分发消息
            this.pushMsg(userLessonRecordSafeTrainList);
        }
        return true;
    }

    private void pushMsg(List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList) {
        List<WxPushTab> wxPushTabs = new ArrayList<>();
        userLessonRecordSafeTrainList.forEach(item -> {
            WxPushTab wxPushTab = new WxPushTab();
            wxPushTab.setUserName(item.getUserName());
            wxPushTab.setUserCode(item.getUserCode());
            wxPushTab.setOpenId(item.getPublicOpenId());
            wxPushTab.setPhone(item.getPhone());
            wxPushTab.setRegId("");
            wxPushTab.setMonthStr(item.getLessonDate());
            wxPushTab.setLessonId(item.getLessonId());
            wxPushTab.setLessonName(item.getLessonName());
            wxPushTab.setLessonCategoryId(item.getLessonCategoryId());
            wxPushTab.setLessonCategoryName(item.getLessonCategoryName());
            wxPushTab.setTrainSTime(item.getTrainStartTime());
            wxPushTab.setTrainETime(item.getTrainEndTime());
            wxPushTab.setTemplateCode(5);
            wxPushTab.setIsWxSend(0);
            wxPushTab.setIsMobile(1);
            wxPushTab.setIsSms(1);
            wxPushTab.setIsDel(0);
            wxPushTab.setType(0);
            wxPushTab.setPlanId(0L);
            wxPushTab.setUrlType("");
            wxPushTabs.add(wxPushTab);
        });
        wxPushTabService.saveBatch(wxPushTabs);
    }

    @Override
    public PageUtils getLessonSafeTrainDisCompanyList(LessonSafeTrainQueryCompanyRequest request, PageEntity pageEntity) {
        CompanyLessonSafeTrainSetQueryRequest request1 = (CompanyLessonSafeTrainSetQueryRequest) DataTransfer.transfer(request, CompanyLessonSafeTrainSetQueryRequest.class);
        request1.setFlag("list2");
        PageUtils pageUtils = companyLessonSafeTrainSetService.listCompanyLessonSafeTrainSet(request1, pageEntity);
        List list = pageUtils.getList();
        LessonSafeTrainPlanRequest lessonSafeTrainPlanRequest = new LessonSafeTrainPlanRequest();
        lessonSafeTrainPlanRequest.setLessonIds(Arrays.asList(request.getLessonId()));
        List<UserLessonRecordLearnNumVO> userLessonRecordLearnNumVOS = userLessonRecordSafeTrainMongoService.getLearnNumByLessonId(lessonSafeTrainPlanRequest);
        for (int i = 0; i < list.size(); i++) {
            CompanyLessonSafeTrainSetVO temp = (CompanyLessonSafeTrainSetVO) list.get(i);
            List<UserLessonRecordLearnNumVO> collect = userLessonRecordLearnNumVOS.stream().filter(user -> user.getCompanyId().equals(temp.getCompanyId())).collect(Collectors.toList());
            temp.setDisUserNum(collect.stream().mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
            temp.setCxUserNum(collect.stream().filter(item -> item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
            temp.setWcUserNum(collect.stream().filter(item -> completeStatus.contains(item.getStaticStatus())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
        }
        return pageUtils;
    }

    @Override
    public PageUtils getLessonDisedUserList(LessonSafeTrainQueryUserRequest request, PageEntity pageEntity) {
        //根据条件查询已分发学员
        List<UserLessonRecordSafeTrainVO> list = userLessonRecordSafeTrainMongoService.getLessonDisedUserList(request);
        list.forEach(item -> {
            int staticStatus = item.getStaticStatus();
            item.setReservedField5(completeStatus.contains(staticStatus) ? "1" : staticStatus == 0 || staticStatus == 3 ? "0" : "-3");
            item.setCompleteTime(completeStatus.contains(staticStatus) && null != item.getExamId() && item.getExamId() > 0 ? item.getReservedField4() : completeStatus.contains(staticStatus) ? item.getCompleteTime() : "");
            item.setReservedField3(completeStatus.contains(staticStatus) && null != item.getExamId() && item.getExamId() > 0 ? "已完成(" + item.getScore() + "分)" : completeStatus.contains(staticStatus) ? "已完成" : staticStatus == -3 ? "未开始" : "未完成");
        });
        list.sort(Comparator.comparing(UserLessonRecordSafeTrainVO::getReservedField5));
        return PageUtils.page(pageEntity,list);
    }

    @Override
    public PageUtils getLessonNoDisUserList(LessonSafeTrainQueryUserRequest request, PageEntity pageEntity) {
        if (null != request.getDepartId()) {
            request.setDepartIds(companyService.getSubDeptIds(request.getDepartId()));
        }
        List<LessonSafeTrainQueryUserVO> list = userLessonRecordSafeTrainMongoService.getLessonNoDisUserList(request);
        return PageUtils.page(pageEntity,list);
    }

    @Override
    public PageUtils getLessonNoDisUserListC(LessonSafeTrainQueryUserRequest request, PageEntity pageEntity) {
        LessonSafeTrain lessonSafeTrain = getById(request.getLessonId());
        //查询企业设置
        LambdaQueryWrapper<CompanyLessonSafeTrainSet> companyLessonSafeTrainSetLambdaQueryWrapper = Wrappers.lambdaQuery(CompanyLessonSafeTrainSet.class)
                .eq(CompanyLessonSafeTrainSet::getCompanyId, request.getCompanyId())
                .eq(CompanyLessonSafeTrainSet::getLessonCategoryId, lessonSafeTrain.getLessonCategoryId())
                .eq(CompanyLessonSafeTrainSet::getIsValid, 1);
        List<CompanyLessonSafeTrainSet> companyLessonSafeTrainSets = companyLessonSafeTrainSetMapper.selectList(companyLessonSafeTrainSetLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(companyLessonSafeTrainSets)) {
            return new PageUtils(pageEntity, new ArrayList());
        } else {
            CompanyLessonSafeTrainSet companyLessonSafeTrainSet = companyLessonSafeTrainSets.get(0);
            request.setCompanyLessonPayId(companyLessonSafeTrainSet.getId());
        }
        return getLessonNoDisUserList(request, pageEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editLessonSafeTrainChoiceUserDistribute(LessonSafeTrainDisVO request) {
        LessonSafeTrain lessonSafeTrain = getById(request.getLessonId());
        if (null == lessonSafeTrain) {
            throw new CustomException("课程不存在");
        }
        //获取课程关联课件数量
        LambdaQueryWrapper<LessonCourseSafeTrain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LessonCourseSafeTrain::getLessonId, request.getLessonId());
        queryWrapper.eq(LessonCourseSafeTrain::getIsValid, 1);
        int courseCount = lessonCourseSafeTrainService.count(queryWrapper);
        Long companyId = Long.valueOf(request.getCompanyIds());
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<LessonSafeTrainCompany> companyIdQueryWrapper = new LambdaQueryWrapper<>();
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getIsValid, 1);
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getLessonId, request.getLessonId());
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getCompanyId, companyId);
        int existCompanyCount = lessonSafeTrainCompanyService.count(companyIdQueryWrapper);
        if (existCompanyCount == 0) {
            //获取企业设置内容
            LambdaQueryWrapper<CompanyLessonSafeTrainSet> contentQueryWrapper = new LambdaQueryWrapper<>();
            contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getIsValid, 1);
            contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getLessonCategoryId, lessonSafeTrain.getLessonCategoryId());
            contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getCompanyId, companyId);
            List<CompanyLessonSafeTrainSet> list = companyLessonSafeTrainSetService.list(contentQueryWrapper);
            int sort = 0;
            List<LessonSafeTrainCompany> addList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                CompanyLessonSafeTrainSet companyLessonSafeTrainSet = list.get(i);
                LessonSafeTrainCompany company = new LessonSafeTrainCompany();
                company.setCompanyId(companyLessonSafeTrainSet.getCompanyId());
                company.setCompanyName(companyLessonSafeTrainSet.getCompanyName());
                company.setLessonId(request.getLessonId());
                company.setLessonCategoryId(companyLessonSafeTrainSet.getLessonCategoryId());
                company.setLessonCategoryName(companyLessonSafeTrainSet.getLessonCategoryName());
                company.setHandMode(request.getHandMode());
                company.setSort(sort);
                company.setIsValid(1);
                company.setCreatorCode(user.getUserCode());
                company.setCreationTime(now);
                company.setReviseCode(user.getUserCode());
                company.setReviseTime(now);
                company.setRemark("");
                addList.add(company);
                sort++;
            }
            lessonSafeTrainCompanyService.saveBatch(addList);
        }
        request.setLessonCategoryId(lessonSafeTrain.getLessonCategoryId());
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList = userLessonRecordSafeTrainMongoService.getLessonSafeTrainUnDisUser(request);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrainList)) {
            int shape = lessonSafeTrain.getShape();
            int issue = 1;
            Long examId = 0L;
            int resitNumber = 0;
            if (shape != 2) {
                //查询试卷信息
                ExamRequest examRequest = new ExamRequest();
                examRequest.setLessonId(request.getLessonId());
                PageUtils examInfo = examService.getExamInfo(examRequest, null);
                List examInfoList = examInfo.getList();
                if (CollectionUtils.isNotEmpty(examInfoList)) {
                    ExamVO examVO = (ExamVO) examInfoList.get(0);
                    examId = examVO.getId();
                    resitNumber = examVO.getResitNumber();
                    issue = Integer.valueOf(examVO.getRemark());
                }
            }
            if (issue != 1) {
                throw new CustomException("试卷未发布！");
            }
            for (int i = 0; i < userLessonRecordSafeTrainList.size(); i++) {
                UserLessonRecordSafeTrain userLessonRecordSafeTrain = userLessonRecordSafeTrainList.get(i);
                userLessonRecordSafeTrain.setLessonName(lessonSafeTrain.getLessonName());
                userLessonRecordSafeTrain.setLessonDate(lessonSafeTrain.getLessonDate());
                userLessonRecordSafeTrain.setLessonCategoryId(lessonSafeTrain.getLessonCategoryId());
                userLessonRecordSafeTrain.setLessonCategoryName(lessonSafeTrain.getLessonCategoryName());
                userLessonRecordSafeTrain.setLessonId(request.getLessonId());
                userLessonRecordSafeTrain.setTrainStartTime(lessonSafeTrain.getSTime());
                userLessonRecordSafeTrain.setTrainEndTime(lessonSafeTrain.getETime());
                userLessonRecordSafeTrain.setShape(shape);
                userLessonRecordSafeTrain.setHandMode(1);
                userLessonRecordSafeTrain.setLessonCourseCount(courseCount);
                userLessonRecordSafeTrain.setReservedField1(lessonSafeTrain.getLessonPic());
                userLessonRecordSafeTrain.setExamId(examId);
                userLessonRecordSafeTrain.setResitNumber(resitNumber);
                userLessonRecordSafeTrain.setCreationTime(now);
                userLessonRecordSafeTrain.setCreatorCode(user.getUserCode());
                userLessonRecordSafeTrain.setIsValid(1);
                userLessonRecordSafeTrain.setStaticStatus(-3);
                userLessonRecordSafeTrain.setIsComplete(-3);
                userLessonRecordSafeTrain.setIsPay(1);
                userLessonRecordSafeTrain.setScore(0);
                userLessonRecordSafeTrain.setSort(1);
                userLessonRecordSafeTrain.setReviseTime(now);
                userLessonRecordSafeTrain.setIsStatic(1);
            }
            userLessonRecordSafeTrainMongoService.saveBatch(userLessonRecordSafeTrainList);
            this.pushMsg(userLessonRecordSafeTrainList);
        }
        return true;
    }

    @Override
    public boolean editLessonSafeTrainChoiceUserDistributeC(LessonSafeTrainDisVO request) {
        LessonSafeTrain lessonSafeTrain = getById(request.getLessonId());
        if (null == lessonSafeTrain) {
            throw new CustomException("课程不存在");
        }
        //获取课程关联课件数量
        LambdaQueryWrapper<LessonCourseSafeTrain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LessonCourseSafeTrain::getLessonId, request.getLessonId());
        queryWrapper.eq(LessonCourseSafeTrain::getIsValid, 1);
        int courseCount = lessonCourseSafeTrainService.count(queryWrapper);

        Long companyId = Long.valueOf(request.getCompanyIds());
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<LessonSafeTrainCompany> companyIdQueryWrapper = new LambdaQueryWrapper<>();
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getIsValid, 1);
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getLessonId, request.getLessonId());
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getCompanyId, companyId);
        int existCompanyCount = lessonSafeTrainCompanyService.count(companyIdQueryWrapper);
        request.setLessonCategoryId(lessonSafeTrain.getLessonCategoryId());
        if (lessonSafeTrain.getBelongPlat() == 2) {//企业课程
            if (existCompanyCount == 0) {
                LessonSafeTrainCompany company = new LessonSafeTrainCompany();
                company.setCompanyId(companyId);
                company.setCompanyName(request.getCompanyName());
                company.setLessonId(request.getLessonId());
                company.setLessonCategoryId(lessonSafeTrain.getLessonCategoryId());
                company.setLessonCategoryName(lessonSafeTrain.getLessonCategoryName());
                company.setHandMode(request.getHandMode());
                company.setSort(1);
                company.setIsValid(1);
                company.setCreatorCode(user.getUserCode());
                company.setCreationTime(now);
                company.setReviseCode(user.getUserCode());
                company.setReviseTime(now);
                company.setRemark("企业自建课程");
                lessonSafeTrainCompanyService.save(company);
            }
            //userLessonRecordSafeTrainList = userLessonRecordSafeTrainMongoService.getLessonSafeTrainUnDisUserC(request);
        } else {
            //获取企业设置内容
            if (existCompanyCount == 0) {
                LambdaQueryWrapper<CompanyLessonSafeTrainSet> contentQueryWrapper = new LambdaQueryWrapper<>();
                contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getIsValid, 1);
                contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getLessonCategoryId, lessonSafeTrain.getLessonCategoryId());
                contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getCompanyId, companyId);
                List<CompanyLessonSafeTrainSet> list = companyLessonSafeTrainSetService.list(contentQueryWrapper);
                int sort = 0;
                List<LessonSafeTrainCompany> addList = new ArrayList<>();
                for (int i = 0; i < list.size(); i++) {
                    CompanyLessonSafeTrainSet companyLessonSafeTrainSet = list.get(i);
                    LessonSafeTrainCompany company = new LessonSafeTrainCompany();
                    company.setCompanyId(companyLessonSafeTrainSet.getCompanyId());
                    company.setCompanyName(companyLessonSafeTrainSet.getCompanyName());
                    company.setLessonId(request.getLessonId());
                    company.setLessonCategoryId(companyLessonSafeTrainSet.getLessonCategoryId());
                    company.setLessonCategoryName(companyLessonSafeTrainSet.getLessonCategoryName());
                    company.setHandMode(request.getHandMode());
                    company.setSort(sort);
                    company.setIsValid(1);
                    company.setCreatorCode(user.getUserCode());
                    company.setCreationTime(now);
                    company.setReviseCode(user.getUserCode());
                    company.setReviseTime(now);
                    company.setRemark("");
                    addList.add(company);
                    sort++;
                }
                lessonSafeTrainCompanyService.saveBatch(addList);
            }
        }
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList = userLessonRecordSafeTrainMongoService.getLessonSafeTrainUnDisUser(request);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrainList)) {
            int shape = lessonSafeTrain.getShape();
            int issue = 1;
            Long examId = 0L;
            int resitNumber = 0;
            if (shape != 2) {
                //查询试卷信息
                ExamRequest examRequest = new ExamRequest();
                examRequest.setLessonId(request.getLessonId());
                PageUtils examInfo = examService.getExamInfo(examRequest, null);
                List examInfoList = examInfo.getList();
                if (CollectionUtils.isNotEmpty(examInfoList)) {
                    ExamVO examVO = (ExamVO) examInfoList.get(0);
                    examId = examVO.getId();
                    resitNumber = examVO.getResitNumber();
                    issue = Integer.valueOf(examVO.getRemark());
                }
            }
            if (issue != 1) {
                throw new CustomException("试卷未发布！");
            }
            for (int i = 0; i < userLessonRecordSafeTrainList.size(); i++) {
                UserLessonRecordSafeTrain userLessonRecordSafeTrain = userLessonRecordSafeTrainList.get(i);
                userLessonRecordSafeTrain.setLessonName(lessonSafeTrain.getLessonName());
                userLessonRecordSafeTrain.setLessonDate(lessonSafeTrain.getLessonDate());
                userLessonRecordSafeTrain.setLessonCategoryId(lessonSafeTrain.getLessonCategoryId());
                userLessonRecordSafeTrain.setLessonCategoryName(lessonSafeTrain.getLessonCategoryName());
                userLessonRecordSafeTrain.setLessonId(request.getLessonId());
                userLessonRecordSafeTrain.setTrainStartTime(lessonSafeTrain.getSTime());
                userLessonRecordSafeTrain.setTrainEndTime(lessonSafeTrain.getETime());
                userLessonRecordSafeTrain.setShape(shape);
                userLessonRecordSafeTrain.setHandMode(1);
                userLessonRecordSafeTrain.setLessonCourseCount(courseCount);
                userLessonRecordSafeTrain.setReservedField1(lessonSafeTrain.getLessonPic());
                userLessonRecordSafeTrain.setExamId(examId);
                userLessonRecordSafeTrain.setResitNumber(resitNumber);
                userLessonRecordSafeTrain.setCreationTime(now);
                userLessonRecordSafeTrain.setCreatorCode(user.getUserCode());
                userLessonRecordSafeTrain.setIsValid(1);
                userLessonRecordSafeTrain.setStaticStatus(-3);
                userLessonRecordSafeTrain.setIsComplete(-3);
                userLessonRecordSafeTrain.setIsPay(1);
                userLessonRecordSafeTrain.setScore(0);
                userLessonRecordSafeTrain.setSort(1);
                userLessonRecordSafeTrain.setReviseTime(now);
                userLessonRecordSafeTrain.setIsStatic(1);
            }
            userLessonRecordSafeTrainMongoService.saveBatch(userLessonRecordSafeTrainList);
            this.pushMsg(userLessonRecordSafeTrainList);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delUserLessonRecordSafeTrain(LessonSafeTrainDisVO request) {
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCodes(request.getUserCodes());
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(request.getLessonId());
        List<UserLessonRecordSafeTrain> trainList = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (trainList.size() > 0) {
            List<UserLessonRecordSafeTrain> unCompleteList = trainList.stream().filter(item -> item.getIsComplete() == -3).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(unCompleteList)) {
                userLessonRecordSafeTrainMongoService.removeUserLessonById(unCompleteList.stream().map(UserLessonRecordSafeTrain::getId).collect(Collectors.toList()));
            }
            List<UserLessonRecordSafeTrain> completeList = trainList.stream().filter(item -> item.getIsComplete() != -3).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(completeList)) {
                //修改用户的课程记录状态为删除
                boolean update = userLessonRecordSafeTrainMongoService.delUserLesson(completeList.stream().map(UserLessonRecordSafeTrain::getId).collect(Collectors.toList()));
                if (update) {
                    userCourseRecordSafeTrainMongoService.delUserCourse(request.getLessonId(), Arrays.asList(request.getUserCodes().split(",")));
                }
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delUserLessonRecordSafeTrainAll(String userCode) {
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCodes(userCode);
        queryRequest.setStaticStatus(1);
        queryRequest.setIsValid(1);
        List<UserLessonRecordSafeTrain> trainList = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (trainList.size() > 0) {
            List<UserLessonRecordSafeTrain> unCompleteList = trainList.stream().filter(item -> item.getIsComplete() == -3).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(unCompleteList)) {
                userLessonRecordSafeTrainMongoService.removeUserLessonById(unCompleteList.stream().map(UserLessonRecordSafeTrain::getId).collect(Collectors.toList()));
            }
            List<UserLessonRecordSafeTrain> completeList = trainList.stream().filter(item -> item.getIsComplete() != -3).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(completeList)) {
                //修改用户的课程记录状态为删除
                boolean update = userLessonRecordSafeTrainMongoService.delUserLesson(completeList.stream().map(UserLessonRecordSafeTrain::getId).collect(Collectors.toList()));
                if (update) {
                    userCourseRecordSafeTrainMongoService.delUserCourses(userCode);
                }
            }
        }
        return true;
    }

    @Override
    public boolean editLessonSafeTrainCompanyDel(LessonSafeTrainDisVO request) {
        if ("del".equals(request.getFlag())) {
            if (StringUtils.isEmpty(request.getLessonSafeTrainCompanyIds())) {
                throw new CustomException(1002, "请选择要指定删除的企业课程");
            }
            UserInfo user = SecurityUtils.getLoginUser().getUser();
            LocalDateTime now = LocalDateTime.now();
            LambdaUpdateWrapper<LessonSafeTrainCompany> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(LessonSafeTrainCompany::getIsValid, 0);
            updateWrapper.set(LessonSafeTrainCompany::getReviseCode, user.getUserCode());
            updateWrapper.set(LessonSafeTrainCompany::getReviseTime, now);
            updateWrapper.in(LessonSafeTrainCompany::getId, request.getLessonSafeTrainCompanyIds().split(","));
            return lessonSafeTrainCompanyService.update(updateWrapper);
        }
        throw new CustomException("不支持的操作标识");
    }

    @Override
    public boolean editLessonSafeTrainOnekeyDistribute(LessonSafeTrainDisVO request) {
        LessonSafeTrain lessonSafeTrain = getById(request.getLessonId());
        if (null == lessonSafeTrain) {
            throw new CustomException("课程不存在");
        }
        //获取课程关联课件数量
        LambdaQueryWrapper<LessonCourseSafeTrain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LessonCourseSafeTrain::getLessonId, request.getLessonId());
        queryWrapper.eq(LessonCourseSafeTrain::getIsValid, 1);
        int courseCount = lessonCourseSafeTrainService.count(queryWrapper);
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        //查询可分发企业ID
        request.setLessonCategoryId(lessonSafeTrain.getLessonCategoryId());
        request.setTotalTimeCount(lessonSafeTrain.getTotalTimeCount());
        List<Long> canDistributeCompanyIds = getBaseMapper().selectDistributeCompanyIds(request);
        //查询已经分发的企业
        LambdaQueryWrapper<LessonSafeTrainCompany> companyIdQueryWrapper = new LambdaQueryWrapper<>();
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getIsValid, 1);
        companyIdQueryWrapper.eq(LessonSafeTrainCompany::getLessonId, request.getLessonId());
        List<LessonSafeTrainCompany> existCompany = lessonSafeTrainCompanyService.list(companyIdQueryWrapper);
        List<Long> existCompanyIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(existCompany)) {
            existCompanyIdList = existCompany.stream().map(LessonSafeTrainCompany::getCompanyId).collect(Collectors.toList());
        }
        //排除已经分发的企业
        List<Long> distributeCompanyIdList = new ArrayList<>();
        for (int i = 0; i < canDistributeCompanyIds.size(); i++) {
            Long tempId = canDistributeCompanyIds.get(i);
            if (!existCompanyIdList.contains(tempId)) {
                distributeCompanyIdList.add(tempId);
            }
        }
        if (!distributeCompanyIdList.isEmpty()) {
            //获取企业设置内容
            LambdaQueryWrapper<CompanyLessonSafeTrainSet> contentQueryWrapper = new LambdaQueryWrapper<>();
            contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getIsValid, 1);
            contentQueryWrapper.eq(CompanyLessonSafeTrainSet::getLessonCategoryId, lessonSafeTrain.getLessonCategoryId());
            contentQueryWrapper.in(CompanyLessonSafeTrainSet::getCompanyId, distributeCompanyIdList);
            List<CompanyLessonSafeTrainSet> list = companyLessonSafeTrainSetService.list(contentQueryWrapper);
            int sort = 0;
            List<LessonSafeTrainCompany> addList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                CompanyLessonSafeTrainSet companyLessonSafeTrainSet = list.get(i);
                LessonSafeTrainCompany company = new LessonSafeTrainCompany();
                company.setCompanyId(companyLessonSafeTrainSet.getCompanyId());
                company.setCompanyName(companyLessonSafeTrainSet.getCompanyName());
                company.setLessonId(request.getLessonId());
                company.setLessonCategoryId(companyLessonSafeTrainSet.getLessonCategoryId());
                company.setLessonCategoryName(companyLessonSafeTrainSet.getLessonCategoryName());
                company.setHandMode(request.getHandMode());
                company.setSort(sort);
                company.setIsValid(1);
                company.setCreatorCode(user.getUserCode());
                company.setCreationTime(now);
                company.setReviseCode(user.getUserCode());
                company.setReviseTime(now);
                addList.add(company);
                sort++;
            }
            lessonSafeTrainCompanyService.saveBatch(addList);
        }
        //
        request.setCompanyIds(StringUtils.join(canDistributeCompanyIds, ","));
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList = userLessonRecordSafeTrainMongoService.getLessonSafeTrainUnDisUser(request);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrainList)) {
            int shape = lessonSafeTrain.getShape();
            int issue = 1;
            Long examId = 0L;
            int resitNumber = 0;
            if (shape != 2) {
                //查询试卷信息
                ExamRequest examRequest = new ExamRequest();
                examRequest.setLessonId(request.getLessonId());
                PageUtils examInfo = examService.getExamInfo(examRequest, null);
                List examInfoList = examInfo.getList();
                if (CollectionUtils.isNotEmpty(examInfoList)) {
                    ExamVO examVO = (ExamVO) examInfoList.get(0);
                    examId = examVO.getId();
                    resitNumber = examVO.getResitNumber();
                    issue = Integer.valueOf(examVO.getRemark());
                }
            }
            if (issue != 1) {
                throw new CustomException("试卷未发布！");
            }
            for (int i = 0; i < userLessonRecordSafeTrainList.size(); i++) {
                UserLessonRecordSafeTrain userLessonRecordSafeTrain = userLessonRecordSafeTrainList.get(i);
                userLessonRecordSafeTrain.setLessonName(lessonSafeTrain.getLessonName());
                userLessonRecordSafeTrain.setLessonDate(lessonSafeTrain.getLessonDate());
                userLessonRecordSafeTrain.setLessonCategoryId(lessonSafeTrain.getLessonCategoryId());
                userLessonRecordSafeTrain.setLessonCategoryName(lessonSafeTrain.getLessonCategoryName());
                userLessonRecordSafeTrain.setLessonId(request.getLessonId());
                userLessonRecordSafeTrain.setTrainStartTime(lessonSafeTrain.getSTime());
                userLessonRecordSafeTrain.setTrainEndTime(lessonSafeTrain.getETime());
                userLessonRecordSafeTrain.setShape(shape);
                userLessonRecordSafeTrain.setHandMode(1);
                userLessonRecordSafeTrain.setLessonCourseCount(courseCount);
                userLessonRecordSafeTrain.setReservedField1(lessonSafeTrain.getLessonPic());
                userLessonRecordSafeTrain.setExamId(examId);
                userLessonRecordSafeTrain.setResitNumber(resitNumber);
                userLessonRecordSafeTrain.setCreationTime(now);
                userLessonRecordSafeTrain.setCreatorCode(user.getUserCode());
                userLessonRecordSafeTrain.setIsValid(1);
                userLessonRecordSafeTrain.setStaticStatus(-3);
                userLessonRecordSafeTrain.setIsComplete(-3);
                userLessonRecordSafeTrain.setIsPay(1);
                userLessonRecordSafeTrain.setScore(0);
                userLessonRecordSafeTrain.setSort(1);
                userLessonRecordSafeTrain.setReviseTime(now);
                userLessonRecordSafeTrain.setIsStatic(1);
            }
            userLessonRecordSafeTrainMongoService.saveBatch(userLessonRecordSafeTrainList);
            this.pushMsg(userLessonRecordSafeTrainList);
        }
        return true;
    }

    @Override
    public PageUtils getLessonSafeTrainListC(LessonSafeTrainRequest request, PageEntity pageEntity) {

        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        String sTime = request.getSTime();
        if (StringUtils.isNotEmpty(sTime) && sTime.length() < 10) {
            request.setSTime(sTime + "-01");
        }
        String eTime = request.getETime();
        if (StringUtils.isNotEmpty(eTime) && eTime.length() < 10) {
            request.setETime(DateUtils.getLastDayOfMonthAsString(eTime));
        }
        List<LessonSafeTrainVO> lessonSafeTrainVOS = null;
        switch (request.getFlag()) {
            case "listAll":
                lessonSafeTrainVOS = getBaseMapper().selectLessonSafeTrainListCAll(request);
                //封装学习统计数据
                getLearnNum(lessonSafeTrainVOS, request);
                return new PageUtils(pageEntity, lessonSafeTrainVOS);
            case "list1":
                lessonSafeTrainVOS = getBaseMapper().selectLessonSafeTrainListC1(request);
                //封装学习统计数据
                getLearnNum(lessonSafeTrainVOS, request);
                return new PageUtils(pageEntity, lessonSafeTrainVOS);
            case "list":
                lessonSafeTrainVOS = getBaseMapper().selectLessonSafeTrainListC(request);
                //封装学习统计数据
                getLearnNum(lessonSafeTrainVOS, request);
                return new PageUtils(pageEntity, lessonSafeTrainVOS);
            case "byId":
                ArrayList<Object> list = new ArrayList<>();
                LessonSafeTrain lessonSafeTrain = getById(request.getId());
                if (null != lessonSafeTrain) {
                    list.add(lessonSafeTrain);
                }
                return new PageUtils(pageEntity, list);
            default:
                throw new CustomException("不支持的操作标识" + request.getFlag());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recoveryUserLessonRecordSafeTrain(LessonSafeTrainDisVO request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<UserCompany> userCompanyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userCompanyLambdaQueryWrapper.eq(UserCompany::getUserCode, request.getUserCodes());
        UserCompany userCompany = userCompanyMapper.selectOne(userCompanyLambdaQueryWrapper);
        if (null == userCompany && userCompany.getIsOut() == 0) {
            throw new CustomException("用户已离职，不能恢复课程记录");
        }
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCodes(request.getUserCodes());
        queryRequest.setIsValid(0);
        queryRequest.setLessonId(request.getLessonId());
        List<UserLessonRecordSafeTrain> trainList = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (trainList.size() > 0) {
            List<UserLessonRecordSafeTrain> completeList = trainList.stream().filter(item -> item.getIsComplete() != -3).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(completeList)) {
                //恢复用户的课程记录状态
                boolean update = userLessonRecordSafeTrainMongoService.recoveryUserLesson(user.getUserCode(), completeList.stream().map(UserLessonRecordSafeTrain::getId).collect(Collectors.toList()));
                if (update) {
                    //恢复户的课件记录状态
                    userCourseRecordSafeTrainMongoService.recoveryUserCourse(request.getLessonId(), request.getUserCodes());
                }
            }
        }
        return true;
    }

    @Override
    public List<GetHomeLessonInfoVO> getHomeLessonInfo(GetHomeLessonInfoRequest request) {
        List<GetHomeLessonInfoVO> homeLessonInfoVOS = new ArrayList<>();
        List<UserLessonRecordSafeTrain> list = userLessonRecordSafeTrainMongoService.getHomeLessonInfo(request);
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年MM月dd日");
        for (UserLessonRecordSafeTrain userLessonRecordSafeTrain : list) {
            GetHomeLessonInfoVO vo = new GetHomeLessonInfoVO();
            vo.setId(userLessonRecordSafeTrain.getId());
            vo.setTrainType("aqpx");
            vo.setTrainTypeName("安全培训");
            vo.setLessonId(userLessonRecordSafeTrain.getLessonId());
            vo.setLessonName(userLessonRecordSafeTrain.getLessonName());
            String trainStartTime = userLessonRecordSafeTrain.getTrainStartTime();
            String trainEndTime = userLessonRecordSafeTrain.getTrainEndTime();
            if (StringUtils.isNotEmpty(trainStartTime) && StringUtils.isNotEmpty(trainEndTime)) {
                try {
                    vo.setTrainCycle(outputFormat.format(inputFormat.parse(trainStartTime)) + "-" + outputFormat.format(inputFormat.parse(trainEndTime)));
                } catch (Exception e) {
                    log.error("日期转换异常", e);
                    vo.setTrainCycle(StringUtils.EMPTY);
                }
            } else {
                vo.setTrainCycle(StringUtils.EMPTY);
            }
            vo.setStaticStatus(userLessonRecordSafeTrain.getStaticStatus());
            vo.setIsPay(userLessonRecordSafeTrain.getIsPay());
            vo.setCreationTime(userLessonRecordSafeTrain.getCreationTime());
            vo.setShape(userLessonRecordSafeTrain.getShape());
            homeLessonInfoVOS.add(vo);
        }
        return homeLessonInfoVOS;
    }

    @Override
    public List<GetLessonInfoDetailVO> getLessonInfoDetail(GetLessonInfoDetailRequest request) {
        List<GetLessonInfoDetailVO> vos = new ArrayList<>();
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCode(request.getUserCode());
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(request.getLessonId());
        List<UserLessonRecordSafeTrain> list = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (CollectionUtils.isNotEmpty(list)) {
            String studyFaceState = "0";
            String studySignState = "0";
            LessonSafeTrain lessonSafeTrain = getById(request.getLessonId());
            if (null != lessonSafeTrain) {
                studyFaceState = lessonSafeTrain.getStudyFaceState().toString();
                studySignState = lessonSafeTrain.getStudySignState().toString();
            }
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年MM月dd日");
            for (UserLessonRecordSafeTrain record : list) {
                GetLessonInfoDetailVO vo = new GetLessonInfoDetailVO();
                try {
                    String trainStartTime = outputFormat.format(inputFormat.parse(record.getTrainStartTime()));
                    String trainEndTime = outputFormat.format(inputFormat.parse(record.getTrainEndTime()));
                    vo.setTrainStartTime(trainStartTime);
                    vo.setTrainEndTime(trainEndTime);
                    vo.setTrainCycle(trainStartTime + "-" + trainEndTime);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                vo.setTrainType("aqpx");
                vo.setTrainTypeName("安全培训");
                vo.setPayStatus(record.getPayStatus());
                vo.setLessonId(record.getLessonId().toString());
                vo.setLessonName(record.getLessonName());
                vo.setStudyNum("0");
                vo.setStaticStatus(record.getStaticStatus());
                vo.setId(record.getId());
                vo.setTrainTimeCount(record.getTrainTimeCount() + "学时");
                vo.setIsPay(String.valueOf(record.getIsPay()));
                vo.setLessonPic(StringUtils.isNotEmpty(record.getReservedField1()) ? record.getReservedField1() : "");
                vo.setStudyComplete(getStudyAndExamState(record.getIsComplete(), "study", ""));
                vo.setExamId(String.valueOf(record.getExamId()));
                vo.setExamComplete(getStudyAndExamState(record.getStaticStatus(), "exam", String.valueOf(record.getExamId())));
                vo.setCompanyId(String.valueOf(record.getCompanyId()));
                vo.setDepartId(String.valueOf(record.getDepartId()));
                vo.setStudyFaceState(studyFaceState);
                vo.setStudySignState(studySignState);
                vo.setSignImg(record.getSignImg());
                vo.setResitNumber(String.valueOf(record.getResitNumber()));
                vo.setIsStatic(String.valueOf(record.getIsStatic()));
                vo.setIsComplete(String.valueOf(record.getIsComplete()));
                vo.setShape(record.getShape());
                vos.add(vo);
            }
        }
        return vos;
    }

    private String getStudyAndExamState(int state, String type, String examId) {
        if ("study".equals(type)) {
            if (completeStatus.contains(state)) {
                return "已完成";
            } else if (-3 == state) {
                return "未参加";
            } else {
                return "未完成";
            }
        } else if ("exam".equals(type)) {
            if (state == 1 && StringUtils.isNotEmpty(examId) && Long.valueOf(examId) > 0) {
                return "合格";
            } else if (StringUtils.isEmpty(examId) || Long.parseLong(examId) == 0) {
                return "无考试";
            } else if (state == 0) {
                return "不合格";
            }
            return "未参加";
        }
        return "";
    }

    @Override
    public boolean editUserLessonRecordSign(EditUserLessonRecordSignRequest request) {
        List<UserLessonRecordSafeTrain> list = userLessonRecordSafeTrainMongoService.getLearningUserLesson(request);
        if (CollectionUtils.isNotEmpty(list)) {
            if (StringUtils.isEmpty(request.getSignImg())) {
                throw new CustomException(1002, "签名图片不能为空");
            }
            long courseList = userCourseRecordSafeTrainMongoService.countCourse(request);
            LambdaQueryWrapper<LessonCourseSafeTrain> lambdaQueryWrapper = Wrappers.lambdaQuery(LessonCourseSafeTrain.class)
                    .eq(LessonCourseSafeTrain::getLessonId, request.getLessonId())
                    .eq(LessonCourseSafeTrain::getIsValid, 1);
            long courseNum = lessonCourseSafeTrainService.count(lambdaQueryWrapper);
            if (courseList == courseNum) {
                String now = DateUtils.getCurrentTime();
                UserLessonRecordSafeTrain lessonUser = list.get(0);
                int isComplete = 1;
                if (lessonUser.getIsComplete() == 0 && (lessonUser.getTrainEndTime() + " 23:59:59").compareTo(now) < 0) {
                    isComplete = 2;
                } else if (lessonUser.getIsComplete() == 3) {
                    isComplete = 4;
                }
                lessonUser.setSignImg(request.getSignImg());
                lessonUser.setIsComplete(isComplete);
                lessonUser.setCompleteTime(now);
                if (null == lessonUser.getExamId() || lessonUser.getExamId() <= 0) {
                    lessonUser.setStaticStatus(isComplete);
                }
                return userLessonRecordSafeTrainMongoService.updateUserLessonById(lessonUser);
            }
        } else {
            throw new CustomException(1003, "还有未完成的课件");
        }
        return true;
    }

    @Override
    public List<GetMyLessonInfoVO> getMyLessonInfo(GetMyLessonInfoRequest request) {
        List<GetMyLessonInfoVO> vos = new ArrayList<>();
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getMyLessonInfo(request);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrains)) {
            for (UserLessonRecordSafeTrain item : userLessonRecordSafeTrains) {
                GetMyLessonInfoVO vo = new GetMyLessonInfoVO();
                vo.setId(item.getId());
                vo.setTrainType("aqpx");
                vo.setTrainTypeName("安全培训");
                vo.setLessonId(String.valueOf(item.getLessonId()));
                vo.setLessonName(String.valueOf(item.getLessonName()));
                vo.setTrainCycle(DateUtils.formatDate(item.getTrainStartTime(), "yyyy-MM-dd", "yyyy年MM月dd日") + "-" + DateUtils.formatDate(item.getTrainEndTime(), "yyyy-MM-dd", "yyyy年MM月dd日"));
                vo.setStudyNum("0");
                vo.setStaticStatus(item.getStaticStatus());
                vo.setIsPay(String.valueOf(item.getIsPay()));
                vo.setCreationTime(item.getCreationTime());
                vo.setShape(item.getShape());
                vo.setStage(1);
                vo.setTrainStartTime(item.getTrainStartTime());
                vos.add(vo);
            }
        }
        return vos;
    }

    @Override
    public List<GetUserTrainTypeVO> getUserTrainType(GetUserTrainTypeRequest request) {
        GetUserTrainTypeVO getUserTrainTypeVO = new GetUserTrainTypeVO();
        getUserTrainTypeVO.setTrainType("aqpx");
        getUserTrainTypeVO.setTrainTypeName("安全培训");
        return Arrays.asList(getUserTrainTypeVO);
    }

    @Override
    public boolean authFaceRecognition(AuthFaceRecognitionRequest request) {
        boolean face = FaceUtil.detectLivingFace(request.getComparisonPhotos()) && FaceUtil.compareFace(request.getComparisonPhotos(), request.getOriginalPhotos());
        if (face) {
            if (request.getFaceType() == 1) {
                EditUserCourseFaceImgRequest imgRequest = new EditUserCourseFaceImgRequest();
                imgRequest.setLessonId(request.getLessonId());
                imgRequest.setCourseId(request.getCourseId());
                imgRequest.setUserCode(request.getUserCode());
                imgRequest.setFaceImgUrl(request.getComparisonPhotos());
                return courseService.editUserCourseFaceImg(imgRequest);
            }
        }
        return face;
    }

    @Override
    public List<GetUserRedNumVO> getUserRedNum(GetUserRedNumRequest request) {
        //TODO 暂无需求
        return new ArrayList<>();
    }

    @Override
    public List<GetUserLessonTrainDetailVO> getUserLessonTrainDetail(GetUserLessonTrainDetailRequest request) {
        GetUserLessonTrainDetailVO vo = new GetUserLessonTrainDetailVO();
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCode(request.getUserCode());
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(Long.valueOf(request.getLessonId()));
        List<UserLessonRecordSafeTrain> list = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (CollectionUtils.isNotEmpty(list)) {
            UserLessonRecordSafeTrain record = list.get(0);
            vo.setCompleteTime(record.getCompleteTime());
            vo.setIdCard(record.getIdCard());
            vo.setIsComplete(record.getIsComplete());
            vo.setIsCompleteStr(completeStatus.contains(record.getIsComplete()) && null != record.getExamId() && record.getExamId() > 0 ? "已完成(" + record.getScore() + "分)" : completeStatus.contains(record.getIsComplete()) ? "已完成" : record.getIsComplete() == -3 ? "未开始" : "未完成");
            vo.setLessonId(record.getLessonId());
            vo.setLessonName(record.getLessonName());
            vo.setSignUrl(record.getSignImg());
            vo.setStartStudyTime(record.getStartStudyTime());
            vo.setTrainType("aqpx");
            vo.setTrainTypeName("安全培训");
            vo.setUserId(record.getUserId());
            vo.setUserName(record.getUserName());
            vo.setExamId(record.getExamId());
            vo.setReservedField1(record.getReservedField1());
            vo.setStaticStatus(String.valueOf(record.getStaticStatus()));
            UserCourseRecordSafeTrain queryRequest2 = new UserCourseRecordSafeTrain();
            queryRequest2.setUserCode(request.getUserCode());
            queryRequest2.setIsValid(1);
            queryRequest2.setLessonId(Long.valueOf(request.getLessonId()));
            List<UserCourseRecordSafeTrain> userCourseRecordSafeTrains = userCourseRecordSafeTrainMongoService.getUserCourse(queryRequest2);
            String faceImg = "";
            if (CollectionUtils.isNotEmpty(userCourseRecordSafeTrains)) {
                for (UserCourseRecordSafeTrain userCourseRecordSafeTrain : userCourseRecordSafeTrains) {
                    if (StringUtils.isNotEmpty(userCourseRecordSafeTrain.getFaceDistinguishImg())) {
                        faceImg += "," + userCourseRecordSafeTrain.getFaceDistinguishImg();
                    }
                }
            }
            vo.setFaceImgUrls(faceImg.length() > 0 ? faceImg.substring(1) : faceImg);
            vo.setShape(String.valueOf(record.getShape()));
            vo.setIsCompleteStr(getStudyAndExamState(record.getIsComplete(), "study", "0"));
            vo.setScore(-1);
            vo.setExamStatus(-3);
            vo.setExamStatusStr("-");
            vo.setExamEndTime("-");
            vo.setExamSignUrl("");
            vo.setUseTimeCount(0);
            if (null != vo.getExamId() && vo.getExamId() > 0) {
                GetUserExamInfoRequest getUserExamInfoRequest = new GetUserExamInfoRequest();
                getUserExamInfoRequest.setLessonIds(request.getLessonId());
                getUserExamInfoRequest.setUserId(request.getUserCode());
                List<GetUserExamInfoVO> userExamInfo = examService.getUserExamInfo(getUserExamInfoRequest);
                if (CollectionUtils.isNotEmpty(userExamInfo)) {
                    GetUserExamInfoVO getUserExamInfoVO = userExamInfo.get(0);
                    vo.setScore(getUserExamInfoVO.getScore());
                    vo.setExamSignUrl(getUserExamInfoVO.getSignUrl());
                    vo.setExamEndTime(getUserExamInfoVO.getScore() < 0 ? "-" : DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", getUserExamInfoVO.getCreationTime()));
                    vo.setExamStatus(getUserExamInfoVO.getScore() < 0 ? -3 : getUserExamInfoVO.getScore() >= getUserExamInfoVO.getPassMark() ? 1 : 0);
                    vo.setExamStatusStr(getUserExamInfoVO.getScore() < 0 ? "未考试" : getUserExamInfoVO.getScore() >= getUserExamInfoVO.getPassMark() ? "合格（" + getUserExamInfoVO.getScore() + "分）" : "不合格（" + getUserExamInfoVO.getScore() + "分）");
                    vo.setUseTimeCount(null == getUserExamInfoVO.getUseTimeCount() ? 0 : getUserExamInfoVO.getUseTimeCount());
                    vo.setFaceImgUrls(StringUtils.isNotEmpty(getUserExamInfoVO.getPhotoUrl()) ? vo.getFaceImgUrls() + "," + getUserExamInfoVO.getPhotoUrl() : vo.getFaceImgUrls());
                }
            }
        }
        return Arrays.asList(vo);
    }

    @Override
    public String getLearnCertificatePic(GetLearnCertificatePicRequest request) {
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCode(request.getUserCode());
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(request.getLessonId());
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrains)) {
            UserLessonRecordSafeTrain userLesson = userLessonRecordSafeTrains.get(0);
            LearnCertificatePicVO vo = new LearnCertificatePicVO();
            vo.setCompanyName(userLesson.getCompanyName());
            vo.setAreaName(userLesson.getAreaName());
            vo.setStartStudyTime(DateUtils.formatDate(userLesson.getStartStudyTime(), "yyyy-MM-dd", "yyyy年MM月dd日"));
            vo.setCompleteTime(StringUtils.isNotEmpty(userLesson.getReservedField4()) ? DateUtils.formatDate(userLesson.getReservedField4(), "yyyy-MM-dd", "yyyy年MM月dd日") : DateUtils.formatDate(userLesson.getCompleteTime(), "yyyy-MM-dd", "yyyy年MM月dd日"));
            vo.setLessonName(userLesson.getLessonName());
            vo.setIdCard(userLesson.getIdCard());
            vo.setUserName(userLesson.getUserName());
            vo.setScore(null != userLesson.getExamId() && userLesson.getExamId() > 0 ? "经考试成绩合格（" + userLesson.getScore() + "分)" : "");
            vo.setSignImg(userLesson.getSignImg());
            vo.setTrainTimeCount(userLesson.getTrainTimeCount());
            LambdaQueryWrapper<UserCompany> userCompanyLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userCompanyLambdaQueryWrapper.eq(UserCompany::getUserCode, request.getUserCode());
            userCompanyLambdaQueryWrapper.eq(UserCompany::getIsValid, 1);
            UserCompany userCompany = userCompanyMapper.selectOne(userCompanyLambdaQueryWrapper);
            vo.setUserPhoto(null != userCompany ? userCompany.getUserPhoto() : "");
            vo.setIndustryType(userLesson.getLessonCategoryName());
            vo.setExamId(userLesson.getExamId());
            //生成微信二维码
            HashMap requestMap = new HashMap<>();
            requestMap.put("Url", "src/pages/learn/report");
            requestMap.put("ParameterStr", "{\"UserCode\":\"" + request.getUserCode() + "\",\"LessonID\":\"" + request.getLessonId() + "\",\"TrainType\":\"" + request.getTrainType() + "\",\"PlanID\":\"" + request.getPlanId() + "\"}");
            requestMap.put("LessonID", request.getLessonId());
            requestMap.put("UserCode", request.getUserCode());
            requestMap.put("TrainType", request.getTrainType());
            //长期有效
            requestMap.put("permanent", 1);
            byte[] qrCodeBytes = wxService.generateMiniProgramQRCode(requestMap);
            String base64QRCode = Base64.getEncoder().encodeToString(qrCodeBytes);
            vo.setCode(base64QRCode);
            return ImageGenerator.generateImage(vo);
        } else {
            throw new CustomException("根据课程ID查询用户培训记录异常");
        }
    }

    @Override
    public GetLearnRedNumVO getLearnRedNum(GetLearnRedNumRequest request) {
        GetLearnRedNumVO vo = new GetLearnRedNumVO();
        int learnNum = userLessonRecordSafeTrainMongoService.getLearnRedNum(request);
        vo.setLearnRedNum(learnNum);
        LambdaQueryWrapper<PushCenterMsg> pushCenterMsgLambdaQueryWrapper = Wrappers.lambdaQuery(PushCenterMsg.class)
                .eq(PushCenterMsg::getUserCode, request.getUserCode()).eq(PushCenterMsg::getIsRead, 0);
        vo.setMsgRedNum(pushCenterMsgMapper.selectCount(pushCenterMsgLambdaQueryWrapper));
        return vo;
    }

    @Override
    public GetLessonSafeTrainListAPPVO getLessonSafeTrainListAPP(GetLessonSafeTrainListAPPRequest request, PageEntity pageEntity) {
        GetLessonSafeTrainListAPPVO vo = new GetLessonSafeTrainListAPPVO();
        switch (request.getFlag()) {
            case "listAll":
                PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
                PageUtils pageUtils = new PageUtils(pageEntity, getBaseMapper().selectLessonSafeTrainListAPP(request));
                vo.setTotal(pageUtils.getTotalCount());
                vo.setLessonlist(pageUtils.getList());
                return vo;
            case "byId":
                LessonSafeTrain lessonSafeTrain = getById(request.getId());
                if (null != lessonSafeTrain) {
                    vo.setTotal(1);
                    vo.getLessonlist().add(lessonSafeTrain);
                    vo.setCourselist(getBaseMapper().selectCourseByLessonSafeTrainId(lessonSafeTrain.getId()));
                }
                return vo;
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    public List<GetCompanyLessonTrainTypeListAPPVO> getCompanyLessonTrainTypeListAPP(GetCompanyLessonTrainTypeListAPPRequest request) {
        GetCompanyLessonTrainTypeListAPPVO vo = new GetCompanyLessonTrainTypeListAPPVO();
        vo.setTrainType("aqpx");
        vo.setTrainTypeName("安全培训");
        return Arrays.asList(vo);
    }

    @Override
    public List<GetCourseAPPVO> getCourseAPP(GetCourseAPPRequest request) {
        switch (request.getFlag()) {
            case "list":
                return getBaseMapper().selectCourseAPP(request);
            case "byId":
                return getBaseMapper().selectCourseAPPById(request);
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    public GetCompanySafeTrainFaceRecordVO getCompanySafeTrainFaceRecord(GetCompanySafeTrainFaceRecordRequest request) {
        GetCompanySafeTrainFaceRecordVO vo = new GetCompanySafeTrainFaceRecordVO();
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setCompanyId(request.getCompanyId());
        queryRequest.setIsValid(1);
        queryRequest.setIsStatic(1);
        queryRequest.setLessonId(request.getLessonId());
        List<Long> subDeptIds = companyService.getSubDeptIds(request.getDepartId());
        if (null != request.getDepartId()) {
            request.setSubDeptIds(subDeptIds);
            queryRequest.setDepartIds(subDeptIds);
        }
        List<UserLessonRecordSafeTrain> lessonList = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        vo.setUserLessonList(lessonList);
        if (CollectionUtils.isNotEmpty(lessonList)) {
            List<UserCourseRecordSafeTrain> courseList = userCourseRecordSafeTrainMongoService.getUserCourseFaceMap(request);
            //查询用户考试记录
            if (null != request.getDepartId()) {
                request.setDepartIds(subDeptIds);
            }
            List<ExamFaceRecordVO> examFaceRecordVOS = examService.getExamFaceRecord(request);
            if (null == examFaceRecordVOS) {
                examFaceRecordVOS = new ArrayList<>();
            }
            Map<String, List<String>> faceMap = new HashMap<>();
            for (UserCourseRecordSafeTrain userCourse : courseList) {
                List<String> images =faceMap.get(userCourse.getUserCode());
                if (CollectionUtils.isEmpty(images)) {
                    images = new ArrayList<>();
                }
                String[] tempImages = userCourse.getFaceDistinguishImg().split(",");
                for (int i = 0; i < tempImages.length; i++) {
                    images.add(tempImages[i] + "?x-oss-process=image/resize,h_70/quality,Q_80");
                }
                faceMap.put(userCourse.getUserCode(), images);
            }
            Map<String, String> examFaceRecordMap = examFaceRecordVOS.stream().collect(Collectors.toMap(ExamFaceRecordVO::getUserId, ExamFaceRecordVO::getPhotoUrl));
            int tempLearnFlag = null != request.getLearnFlag() ? request.getLearnFlag() : -1;
            for (UserLessonRecordSafeTrain item : lessonList) {
                List<String> faceImages = faceMap.get(item.getUserCode());
                item.setReservedField5(CollectionUtils.isNotEmpty(faceImages) ? StringUtils.join(faceImages, "&&&").replace("{", "").replace("}", "").replace("[", "").replace("]", "") : "");
                item.setReservedField4("");
                Integer staticStatus = tempLearnFlag == 1 && noBeginStatus.contains(item.getStaticStatus()) ? -3 : tempLearnFlag == 1 && noCompleteStatus.contains(item.getStaticStatus()) ? 0 : tempLearnFlag == 0 && completedStatus.contains(item.getStaticStatus()) ? 1 : item.getStaticStatus();
                item.setReservedField3(completeStatus.contains(staticStatus) ? "已完成" : staticStatus == -3 ? "未开始" : "未完成");
                if (examFaceRecordMap.containsKey(item.getUserCode())) {
                    item.setReservedField4(examFaceRecordMap.get(item.getUserCode()));
                }
                if (StringUtils.isNotEmpty(item.getReservedField4())) {
                    String[] images = item.getReservedField4().split(",");
                    String result = "";
                    for (int i = 0; i < images.length; i++) {
                        result += images[i] + "?x-oss-process=image/resize,h_70/quality,Q_80";
                        if (i != images.length - 1) {
                            result += "&&&";
                        }
                    }
                    item.setReservedField4(result);
                }
            }
        }
        return vo;
    }

    @Override
    public SafeTrainFilesVO safeTrainFiles(SafeTrainFilesRequest request) {
        SafeTrainFilesVO vo = new SafeTrainFilesVO();
        //获取课程信息和获取课件
        List<CourseVO> courseList = lessonCourseSafeTrainService.getCourseListByLessonId(request.getLessonId());
        LessonSafeTrain lesson = getById(request.getLessonId());
        vo.setLessonDate(lesson.getLessonDate().substring(0, 7).replace("-", "年") + "月");
        if (CollectionUtils.isNotEmpty(courseList)) {
            for (CourseVO courseVO : courseList) {
                courseVO.setFileTypeStr(fileTypeMap.get(courseVO.getFileType()));
            }
        }
        vo.setCourseList(courseList);
        vo.setTearcherName(courseList.isEmpty() ? "" : StringUtils.join(courseList.stream().map(CourseVO::getTearcherName).distinct().collect(Collectors.toList()), "、"));
        vo.setTotalTimeCountStr(lesson.getTotalTimeCount() + "学时");
        vo.setLessonName(lesson.getLessonName());
        vo.setSTime(DateUtils.formatDate(lesson.getSTime(), "yyyy-MM-dd", "yyyy年MM月dd日"));
        vo.setETime(DateUtils.formatDate(lesson.getETime(), "yyyy-MM-dd", "yyyy年MM月dd日"));
        ExamRequest examRequest = new ExamRequest();
        examRequest.setLessonId(request.getLessonId());
        List<ExamVO> examList = (List<ExamVO>) examService.getExamInfoQuestion(examRequest, null).getList();
        if (CollectionUtils.isNotEmpty(examList)) {
            List<ExamPdfVO> examPdfVOS = new ArrayList<>();
            vo.setExamList(examPdfVOS);
            ExamVO examVO = examList.get(0);
            List<ExamQuestionVO> examQuestionList = examVO.getExamQuestionList();
            Map<Integer, List<ExamQuestionVO>> userRecordMap = examQuestionList.stream().collect(Collectors.groupingBy(ExamQuestionVO::getQuestionTypeId));
            userRecordMap.forEach((k, v) -> {
                Integer subjectType = v.get(0).getSubjectType();
                ExamPdfVO examPdfVO = new ExamPdfVO();
                examPdfVO.setRowNum(subjectType == 1 ? "一" : subjectType == 2 ? "二" : "三");
                examPdfVO.setSubjectTypeId(subjectType);
                examPdfVO.setSubjectTypeName(subjectType == 1 ? "单选题" : subjectType == 2 ? "多选题" : "判断题");
                examPdfVO.setExamQuestionList(v);
                examPdfVOS.add(examPdfVO);
            });
            examPdfVOS.sort(Comparator.comparingInt(ExamPdfVO::getSubjectTypeId));
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean getCopyExamInfo(GetCopyExamInfoRequest request) {
        //查询原课程管理的试卷ID
        LambdaQueryWrapper<ExamLesson> queryWrapper = Wrappers.lambdaQuery(ExamLesson.class)
                .eq(ExamLesson::getLessonId, request.getLessonId());
        ExamLesson lesson = examLessonService.getOne(queryWrapper);
        if (null == lesson) {
            return true;
        }
        //查询原课程试卷
        LambdaQueryWrapper<Exam> examLambdaQueryWrapper = Wrappers.lambdaQuery(Exam.class)
                .eq(Exam::getId, lesson.getExamId());
        Exam exam = examService.getOne(examLambdaQueryWrapper);
        if (null == exam) {
            return true;
        }
        //查询原课程管理
        exam.setId(null);
        exam.setCreationTime(LocalDateTime.now());
        exam.setReviseTime(LocalDateTime.now());
        exam.setCreatorId(request.getUserCode());
        exam.setReviseId(request.getUserCode());
        examService.save(exam);
        Long newExamId = exam.getId();
        LambdaQueryWrapper<ExaminationPaperRule> paperRuleLambdaQueryWrapper = Wrappers.lambdaQuery(ExaminationPaperRule.class)
                .eq(ExaminationPaperRule::getExamId, lesson.getExamId());
        List<ExaminationPaperRule> examinationPaperRules = examinationPaperRuleService.list(paperRuleLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(examinationPaperRules)) {
            for (ExaminationPaperRule examinationPaperRule : examinationPaperRules) {
                examinationPaperRule.setId(null);
                examinationPaperRule.setExamId(newExamId);
            }
            examinationPaperRuleService.saveBatch(examinationPaperRules);
        }
        LambdaQueryWrapper<ExamQuestion> examQuestionLambdaQueryWrapper = Wrappers.lambdaQuery(ExamQuestion.class)
                .eq(ExamQuestion::getExamId, lesson.getExamId());
        List<ExamQuestion> examQuestions = examQuestionService.list(examQuestionLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(examQuestions)) {
            for (ExamQuestion examQuestion : examQuestions) {
                examQuestion.setId(null);
                examQuestion.setExamId(newExamId);
                examQuestion.setCreationTime(LocalDateTime.now());
                examQuestion.setReviseTime(LocalDateTime.now());
                examQuestion.setCreatorId(request.getUserCode());
                examQuestion.setReviseId(request.getUserCode());
            }
            examQuestionService.saveBatch(examQuestions);
        }
        ExamLesson examLesson = new ExamLesson();
        examLesson.setExamId(newExamId);
        examLesson.setLessonId(request.getNewLessonId());
        return examLessonService.save(examLesson);
    }

    @Override
    public GetUserSurveyVO getUserSurvey(GetUserSurveyRequest request) {
        GetUserSurveyVO vo = new GetUserSurveyVO();
        List<LessonSafeTrainUserTemplateListVO> list = getBaseMapper().selectLessonSafeTrainUserTemplateList(request);
        vo.setSafetraintemplate(list);
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList = userLessonRecordSafeTrainMongoService.getUserSurvey(request);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrainList)) {
            userLessonRecordSafeTrainList.forEach(item -> {
                item.setCompleteTime((completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0) ? item.getReservedField4() : completeStatus.contains(item.getStaticStatus()) ? item.getCompleteTime() : "");
            });
        }
        vo.setSafe(userLessonRecordSafeTrainList);
        return vo;
    }

    @Override
    public PageUtils getUserLessonRecordByUserCode(GetUserSurveyRequest request, PageEntity pageEntity) {
        PageUtils pageUtils = userLessonRecordSafeTrainMongoService.getUserLessonRecordByUserCode(request, pageEntity);
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList = (List<UserLessonRecordSafeTrain>) pageUtils.getList();
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrainList)) {
            userLessonRecordSafeTrainList.forEach(item -> {
                item.setCompleteTime((completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0) ? item.getReservedField4() : completeStatus.contains(item.getStaticStatus()) ? item.getCompleteTime() : "");
            });
        }
        return pageUtils;
    }

    @Override
    public String getUserSafeTrainLessonRecordExport(GetUserSurveyRequest request) {
        PageEntity pageEntity = new PageEntity();
        pageEntity.setPageIndex(1);
        pageEntity.setPageSize(99999999);
        PageUtils pageUtils = getUserLessonRecordByUserCode(request, pageEntity);
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList = pageUtils.getPage().getResult();
        try {
            ExcelUtil<UserLessonRecordSafeTrain> util = new ExcelUtil<UserLessonRecordSafeTrain>(UserLessonRecordSafeTrain.class);
            String fileName = util.encodingDateFilename("安全培训报表导出");
            InputStream inputStream = util.exportExcelFile(userLessonRecordSafeTrainList, fileName);
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "user", fileName);
            return fileName + ".xlsx," + uploadFile.getUrl();
        } catch (Exception e) {
            log.error("用户信息导出上传异常");
            return "用户信息导出上传异常";
        }
    }

    @Override
    public String userToCompanyIsEnable(UserToCompanyIsEnableRequest request) {
        //根据userCode查询管理的company的状态is_valid是否为1
        LambdaQueryWrapper<UserCompany> queryWrapper = Wrappers.lambdaQuery(UserCompany.class)
                .eq(UserCompany::getUserCode, request.getUserCode())
                .eq(UserCompany::getIsValid, 1);
        List<UserCompany> userCompany = userCompanyMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userCompany)) {
            return "false";
        }
        LambdaQueryWrapper<Company> companyLambdaQueryWrapper = Wrappers.lambdaQuery(Company.class)
                .in(Company::getId, userCompany.stream().map(UserCompany::getCompanyId).collect(Collectors.toList()))
                .eq(Company::getIsValid, 1);
        List<Company> companyList = companyService.list(companyLambdaQueryWrapper);
        return CollectionUtils.isNotEmpty(companyList) ? "true" : "false";
    }

    @Override
    public PageUtils getUserNotDisSafeLessonList(LessonSafeTrainRequest lessonSafeTrainRequest, PageEntity pageEntity) {
        List<LessonSafeTrain> lessonSafeTrains = lessonSafeTrainMapper.selectLessonSafeTrainByCompanyIDAndLessonName(lessonSafeTrainRequest.getCompanyId(), lessonSafeTrainRequest.getLessonName(), lessonSafeTrainRequest.getUserCode());
        if (CollectionUtils.isEmpty(lessonSafeTrains)) {
            return new PageUtils(pageEntity, lessonSafeTrains);
        }
        List<Long> lessonIds = userLessonRecordSafeTrainMongoService.getUserLearningLessonIds(lessonSafeTrainRequest.getUserCode());
        List<LessonSafeTrain> result = lessonSafeTrains.stream().filter(item -> !lessonIds.contains(item.getId())).collect(Collectors.toList());
        return PageUtils.page(pageEntity,result);
    }

    private void getLearnNum(List<LessonSafeTrainVO> lessonSafeTrainVOS, LessonSafeTrainRequest request) {
        if (CollectionUtils.isEmpty(lessonSafeTrainVOS)) {
            return;
        }
        List<Long> lessonIds = lessonSafeTrainVOS.stream().map(LessonSafeTrainVO::getId).collect(Collectors.toList());
        request.setLessonIds(lessonIds);
        List<UserLessonRecordLearnNumVO> userLessonRecordLearnNumVOS = userLessonRecordSafeTrainMongoService.getLessonLearnNumByLessonId(request);
        DecimalFormat df = new DecimalFormat("#.##");
        for (int i = 0; i < lessonSafeTrainVOS.size(); i++) {
            LessonSafeTrainVO temp = lessonSafeTrainVOS.get(i);
            List<UserLessonRecordLearnNumVO> collect = userLessonRecordLearnNumVOS.stream().filter(user -> user.getLessonId().equals(temp.getId())).collect(Collectors.toList());
            if (null == request.getState()) {
                temp.setTotalUserNum(collect.stream().mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                temp.setCxUserNum(collect.stream().filter(item -> item.getStaticStatus() == 0 || item.getStaticStatus() == 1 || item.getStaticStatus() == 2).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                temp.setWcUserNum(collect.stream().filter(item -> item.getStaticStatus() == 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                temp.setLedgerFileName(temp.getLessonDate().substring(0, 7).replace("-", "年") + "月" + request.getCompanyName() + temp.getLessonCategoryName() + "安全培训全部数据台账");
            } else if (request.getState() == 0) {
                temp.setTotalUserNum(collect.stream().filter(item -> item.getStaticStatus() != 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                temp.setCxUserNum(collect.stream().filter(item -> item.getStaticStatus() != 1 && item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                temp.setWcUserNum(collect.stream().filter(item -> item.getStaticStatus() == 2 || item.getStaticStatus() == 4).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                temp.setLedgerFileName(temp.getLessonDate().substring(0, 7).replace("-", "年") + "月" + request.getCompanyName() + temp.getLessonCategoryName() + "安全培训补学数据台账");
            } else if (request.getState() == 1) {
                temp.setTotalUserNum(collect.stream().mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                temp.setCxUserNum(collect.stream().filter(item -> item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                temp.setWcUserNum(collect.stream().filter(item -> completeStatus.contains(item.getStaticStatus())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                temp.setLedgerFileName(temp.getLessonDate().substring(0, 7).replace("-", "年") + "月" + request.getCompanyName() + temp.getLessonCategoryName() + "安全培训数据台账");
            }
            int noCompleteUserNum = temp.getTotalUserNum() - temp.getWcUserNum();
            String noCompleteData = (temp.getTotalUserNum() > 0 ? df.format((double) noCompleteUserNum / temp.getTotalUserNum() * 100) : "0") + "%";
            String cxl = (temp.getTotalUserNum() > 0 ? df.format((double) temp.getCxUserNum() / temp.getTotalUserNum() * 100) : "0") + "%";
            String wcl = (temp.getTotalUserNum() > 0 ? df.format((double) temp.getWcUserNum() / temp.getTotalUserNum() * 100) : "0") + "%";
            temp.setCxl(cxl);
            temp.setWcl(wcl);
            temp.setCxUserNumStr(temp.getCxUserNum() + "(" + cxl + ")");
            temp.setWcUserNumStr(temp.getWcUserNum() + "(" + wcl + ")");
            temp.setNoCompleteUserNumStr(noCompleteUserNum + "(" + noCompleteData + ")");
            //下载
            temp.setLedgerUrl("/ShowInfo/Show/Get_CompanySafeTrainFiles?LessonMonth=" + temp.getLessonDate().substring(0, 7) + "&CompanyID=" + request.getCompanyId() + "&DepartID=" + request.getDepartId() + "&LessonID=" + temp.getId() + "&CompanyName=" + request.getCompanyName() + "&LearnFlag=" + request.getState());
            temp.setFileName(temp.getLessonDate().substring(0, 7).replace("-", "年") + "月" + request.getCompanyName() + temp.getLessonCategoryName() + "安全培训课程档案");
            temp.setFilesUrl("/ShowInfo/Show/SafeTrainFiles?lessonid=" + temp.getId() + "&companyname=" + request.getCompanyName());
            temp.setFaceLederUrl("/ShowInfo/Show/Get_CompanySafeTrainFaceRecord?LessonMonth=" + temp.getLessonDate().substring(0, 7) + "&CompanyID=" + request.getCompanyId() + "&DepartID=" + request.getDepartId() + "&LessonID=" + temp.getId() + "&CompanyName=" + request.getCompanyName() + "&LearnFlag=" + request.getState());
            temp.setFaceLederFileName(temp.getLessonDate().substring(0, 7).replace("-", "年") + "月" + request.getCompanyName() + temp.getLessonCategoryName() + "安全培训数据人像照片");
            temp.setLessonDate(temp.getLessonDate().substring(0, 7));
            temp.setTotalTimeCountStr(temp.getTotalTimeCount() + "学时");
            temp.setBelongPlatStr(temp.getBelongPlat() == 1 ? "平台" : "企业");
        }
    }

}