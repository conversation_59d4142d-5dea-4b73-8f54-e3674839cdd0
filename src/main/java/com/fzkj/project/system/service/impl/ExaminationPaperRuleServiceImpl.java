package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.ExaminationPaperRule;
import com.fzkj.project.system.vo.ExaminationPaperRuleVO;
import com.fzkj.project.system.request.ExaminationPaperRuleRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ExaminationPaperRuleMapper;
import com.fzkj.project.system.service.ExaminationPaperRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 随机组卷规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExaminationPaperRuleServiceImpl extends ServiceImpl<ExaminationPaperRuleMapper, ExaminationPaperRule> implements ExaminationPaperRuleService {

    @Override
    public List<ExaminationPaperRule> listExaminationPaperRule(ExaminationPaperRuleRequest request) {
        ExaminationPaperRule entity = (ExaminationPaperRule) DataTransfer.transfer(request, ExaminationPaperRule.class);
        LambdaQueryWrapper<ExaminationPaperRule> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ExaminationPaperRuleRequest request, PageEntity pageEntity) {
        ExaminationPaperRule entity = (ExaminationPaperRule) DataTransfer.transfer(request, ExaminationPaperRule.class);
        LambdaQueryWrapper<ExaminationPaperRule> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<ExaminationPaperRule> page = this.page(
                new Query<ExaminationPaperRule>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ExaminationPaperRuleVO findExaminationPaperRule(Long id) {
        ExaminationPaperRule entity = this.getById(id);
        ExaminationPaperRuleVO vo = (ExaminationPaperRuleVO) DataTransfer.transfer(entity, ExaminationPaperRuleVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExaminationPaperRule(ExaminationPaperRuleVO vo) {
        ExaminationPaperRule entity = (ExaminationPaperRule) DataTransfer.transfer(vo, ExaminationPaperRule.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExaminationPaperRule(ExaminationPaperRuleVO vo) {
        ExaminationPaperRule entity = (ExaminationPaperRule) DataTransfer.transfer(vo, ExaminationPaperRule.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExaminationPaperRule(Long id) {
        return this.removeById(id);
    }
}