package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.ExamUserRecord;
import com.fzkj.project.system.entity.lessonSafeTrainTd;
import com.fzkj.project.system.vo.ExamUserRecordVO;
import com.fzkj.project.system.request.ExamUserRecordRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户考试记录表（正式） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExamUserRecordService extends IService<ExamUserRecord> {

    List<ExamUserRecord> listExamUserRecord(ExamUserRecordRequest request);

    PageUtils listByPage(ExamUserRecordRequest request, PageEntity pageEntity);

    ExamUserRecordVO findExamUserRecord(Long id);

    Long saveExamUserRecord(ExamUserRecordVO vo);

    boolean updateExamUserRecord(ExamUserRecordVO vo);

    boolean removeExamUserRecord(Long id);

}
