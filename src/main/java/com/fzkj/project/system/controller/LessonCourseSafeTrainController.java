package com.fzkj.project.system.controller;

import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.LessonCourseSafeTrainService;
import com.fzkj.project.system.vo.LessonCourseSafeTrainVO;
import com.fzkj.project.system.request.LessonCourseSafeTrainRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 安全培训课程课件关系 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "lessonApi")
@ThirdPart
@Log
public class LessonCourseSafeTrainController extends BaseEntity {

    private final LessonCourseSafeTrainService LessonCourseSafeTrainServiceImpl;

    @PostMapping("Edit_LessonCourseSafeTrain")
    @ApiOperation(value = "编辑安全培训课程课件关系")
    public AjaxResult Edit_LessonCourseSafeTrain(@RequestBody CommonRequest commonRequest) {
        LessonCourseSafeTrainVO request = (LessonCourseSafeTrainVO) commonRequest.getRequest(LessonCourseSafeTrainVO.class);
        return AjaxResult.success(LessonCourseSafeTrainServiceImpl.editLessonCourseSafeTrain(request));
    }
}
