package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.project.system.entity.WxSendMsgTemplate;
import com.fzkj.project.system.mapper.WxSendMsgTemplateMapper;
import com.fzkj.project.system.service.WxSendMsgTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 微信推送模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class WxSendMsgTemplateServiceImpl extends ServiceImpl<WxSendMsgTemplateMapper, WxSendMsgTemplate> implements WxSendMsgTemplateService {
    @Override
    public WxSendMsgTemplate getTemplateByCode(String code) {
        return getBaseMapper().selectTemplateByCode(code);
    }
}
