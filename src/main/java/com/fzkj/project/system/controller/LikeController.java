package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.UserInfoRequest;
import com.fzkj.project.system.service.LikeService;
import com.fzkj.project.system.service.UserLessonRecordSafeTrainMongoService;
import com.fzkj.project.system.vo.LikeVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *  点赞 控制层
 *
 */
@RestController
@RequiredArgsConstructor
public class LikeController extends BaseEntity {

    private final LikeService likeService;

    @RequestMapping(value = "/Edit_LikeShareCollectionAPP", method = RequestMethod.POST)
    @ApiOperation(value = "收藏/点赞（确认/取消）")
    public AjaxResult editLikeShareCollectionAPP(@RequestBody CommonRequest commonRequest) {
        LikeVO request = (LikeVO) commonRequest.getRequest(LikeVO.class);
        return AjaxResult.success(likeService.edit_LikeShareCollectionAPP(request));
    }

    @RequestMapping(value = "/Get_MyIntegral_Total", method = RequestMethod.POST)
    @ApiOperation(value = "我的点赞收藏数量")
    public AjaxResult getMyIntegralTotal(@RequestBody CommonRequest commonRequest) {
        LikeVO request = (LikeVO) commonRequest.getRequest(LikeVO.class);
        return AjaxResult.success(likeService.getMyIntegralTotal());
    }

    @RequestMapping(value = "/Get_MyLikeCollection", method = RequestMethod.POST)
    @ApiOperation(value = "我的收藏/点赞列表")
    public AjaxResult getMyLikeCollection(@RequestBody CommonRequest commonRequest) {
        LikeVO request = (LikeVO) commonRequest.getRequest(LikeVO.class);
        return AjaxResult.success(likeService.getMyLikeCollection(request.getFlag(),commonRequest.getPageEntity()));
    }

}
