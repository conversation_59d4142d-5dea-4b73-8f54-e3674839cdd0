package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 讲师
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_teacher")
@ApiModel(value = "讲师实体")
public class Teacher implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    @Excel(name = "编号", order = 1)
    private Long id;

    @JsonProperty("Photo")
    @ApiModelProperty(value = "头像")
    private String photo;

    @JsonProperty("RealName")
    @ApiModelProperty(value = "姓名")
    @Excel(name = "讲师姓名", order = 2)
    private String realName;

    @JsonProperty("Phone")
    @ApiModelProperty(value = "手机号")
    private String phone;

    @JsonProperty("Education")
    @ApiModelProperty(value = "学历")
    private String education;

    @JsonProperty("GraduationSchool")
    @ApiModelProperty(value = "毕业学校")
    private String graduationSchool;

    @JsonProperty("BeGoodAt")
    @ApiModelProperty(value = "擅长领域")
    private String beGoodAt;

    @JsonProperty("Unit")
    @ApiModelProperty(value = "单位")
    @Excel(name = "所在单位", order = 3)
    private String unit;

    @JsonProperty("WorkPost")
    @ApiModelProperty(value = "职务")
    @Excel(name = "岗位/职称", order = 4)
    private String workPost;

    @JsonProperty("TechnicalTitle")
    @ApiModelProperty(value = "职称")
    private String technicalTitle;

    @JsonProperty("PersonIntroduct")
    @ApiModelProperty(value = "个人介绍")
    private String personIntroduct;

    @JsonProperty("LoginAccount")
    @JsonIgnore
    @ApiModelProperty(value = "登录账号")
    private String loginAccount;

    @JsonProperty("LoginPwd")
    @JsonIgnore
    @ApiModelProperty(value = "登录密码")
    private String loginPwd;

    @JsonProperty("LoginPwdMd5")
    @JsonIgnore
    @ApiModelProperty(value = "登录密码MD5")
    private String loginPwdMd5;

    @JsonProperty("AuditStatus")
    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;

    @JsonProperty("AuditCode")
    @ApiModelProperty(value = "审核编码")
    private String auditCode;

    @JsonProperty("AuditTime")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    @JsonProperty("AuditReason")
    @ApiModelProperty(value = "审核意见")
    private String auditReason;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    @Excel(name = "排序", order = 7)
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreationTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("RecycleBin")
    @ApiModelProperty(value = "回收站")
    @Excel(name = "状态", order = 9, readConverterExp = "0=正常,1=已删除")
    private Integer recycleBin;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @JsonProperty("IsShow")
    @ApiModelProperty(value = "是否显示")
    @Excel(name = "是否展示", order = 8, readConverterExp = "0=不展示,1=展示")
    private Integer isShow;

    @JsonProperty("CourseNum")
    @TableField(exist = false)
    @ApiModelProperty(value = "课件数量")
    @Excel(name = "课件数量", order = 5)
    private int courseNum;

    @JsonProperty("CommentNum")
    @TableField(exist = false)
    @ApiModelProperty(value = "评论数量")
    @Excel(name = "评论数量", order = 6)
    private int commentNum;
}