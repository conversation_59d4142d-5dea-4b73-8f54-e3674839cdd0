package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.ExamAnswerRecordExercise;
import com.fzkj.project.system.vo.ExamAnswerRecordExerciseVO;
import com.fzkj.project.system.request.ExamAnswerRecordExerciseRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户练习记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExamAnswerRecordExerciseService extends IService<ExamAnswerRecordExercise> {

    List<ExamAnswerRecordExercise> listExamAnswerRecordExercise(ExamAnswerRecordExerciseRequest request);

    PageUtils listByPage(ExamAnswerRecordExerciseRequest request, PageEntity pageEntity);

    ExamAnswerRecordExerciseVO findExamAnswerRecordExercise(Long id);

    Long saveExamAnswerRecordExercise(ExamAnswerRecordExerciseVO vo);

    boolean updateExamAnswerRecordExercise(ExamAnswerRecordExerciseVO vo);

    boolean removeExamAnswerRecordExercise(Long id);
}
