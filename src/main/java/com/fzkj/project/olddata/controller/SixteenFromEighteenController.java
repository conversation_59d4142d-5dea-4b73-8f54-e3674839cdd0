package com.fzkj.project.olddata.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.olddata.dto.request.NineteenFromTwentyOneRequest;
import com.fzkj.project.olddata.dto.request.SixteenFromEighteenOneRequest;
import com.fzkj.project.olddata.mapper.SixteenLessonMapper;
import com.fzkj.project.olddata.service.SixteenFromEighteenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;


/**
 * 常见问题Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Controller
@Api(tags = "2016-2018历史数据")
public class SixteenFromEighteenController {

    @Resource
    SixteenFromEighteenService sixteenFromEighteenService;
    /**
     * 企业部门下拉框
     */
    @PostMapping("/Get_SixteenOrgList")
    @ApiOperation(value = "企业部门下拉框")
    @ResponseBody
    public AjaxResult getSixteenOrgList(@RequestBody CommonRequest commonRequest) {
        SixteenFromEighteenOneRequest request = (SixteenFromEighteenOneRequest) commonRequest.getRequest(SixteenFromEighteenOneRequest.class);
        return AjaxResult.success(sixteenFromEighteenService.getSixteenOrgList(request));
    }
    /**
     * 课程下拉框
     */
    @PostMapping("/Get_SixteenLesson")
    @ApiOperation(value = "课程下拉框")
    @ResponseBody
    public AjaxResult getSixteenLesson(@RequestBody CommonRequest commonRequest) {
        SixteenFromEighteenOneRequest request = (SixteenFromEighteenOneRequest) commonRequest.getRequest(SixteenFromEighteenOneRequest.class);
        return AjaxResult.success(sixteenFromEighteenService.getSixteenLesson(request));
    }
    /**
     * 查询常见问题列表
     */
    @PostMapping("/Get_SixteenLessonUser")
    @ApiOperation(value = "查询常见问题列表")
    @ResponseBody
    public AjaxResult Get_SixteenLessonUser(@RequestBody CommonRequest commonRequest) {
        SixteenFromEighteenOneRequest request = (SixteenFromEighteenOneRequest) commonRequest.getRequest(SixteenFromEighteenOneRequest.class);
        return AjaxResult.success(sixteenFromEighteenService.getSixteenLessonUser(request, commonRequest.getPageEntity()));
    }
}
