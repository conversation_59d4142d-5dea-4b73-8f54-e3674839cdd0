package com.fzkj.common.utils;

import net.sourceforge.pinyin4j.PinyinHelper;

public class PinYinHeadUtils {

    /**
     * 提取每个汉字的首字母(大写)
     *
     * @param str
     * @return
     */
    public static String getPinYinHeadChar(String str) {
        if (StringUtils.isEmpty(str)) {
            return "";
        }
        String convert = "";
        for (int j = 0; j < str.length(); j++) {
            char word = str.charAt(j);
            // 提取汉字的首字母
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(word);
            if (pinyinArray != null) {
                convert += pinyinArray[0].charAt(0);
            }
            else {
                convert += word;
            }
            if(j == 0){
                convert = convert.toUpperCase();
            }
        }

        return convert.trim().replace(" ", "");
    }
}
