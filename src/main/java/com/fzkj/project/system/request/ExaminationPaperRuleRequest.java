package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 随机组卷规则表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value = "随机组卷规则表Request")
public class ExaminationPaperRuleRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "考试ID")
    private Long examId;

    @ApiModelProperty(value = "试题类型ID")
    private Integer questionTypeId;

    @ApiModelProperty(value = "试题类型名称")
    private String questionTypeName;

    @ApiModelProperty(value = "题目数")
    private Integer num;

    @ApiModelProperty(value = "分数")
    private Integer sorce;

    @JsonProperty("Flag")
    private String flag;
}