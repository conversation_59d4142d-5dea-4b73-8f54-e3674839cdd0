package com.fzkj.project.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.UserImg;

import java.util.List;

public interface UserImgService extends IService<UserImg> {

    PageUtils getUserImg(UserImg userImg, PageEntity pageEntity);

    AjaxResult editUserImg(UserImg userImg);
}
