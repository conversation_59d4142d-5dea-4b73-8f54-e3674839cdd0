package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.CourseCategoryOneself;
import com.fzkj.project.system.vo.CourseCategoryOneselfVO;
import com.fzkj.project.system.request.CourseCategoryOneselfRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 企业自建课件分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CourseCategoryOneselfService extends IService<CourseCategoryOneself> {

    List<CourseCategoryOneself> listCourseCategoryOneself(CourseCategoryOneselfRequest request);

    PageUtils listByPage(CourseCategoryOneselfRequest request, PageEntity pageEntity);

    CourseCategoryOneselfVO findCourseCategoryOneself(Long id);

    Long saveCourseCategoryOneself(CourseCategoryOneselfVO vo);

    boolean updateCourseCategoryOneself(CourseCategoryOneselfVO vo);

    boolean removeCourseCategoryOneself(Long id);
}
