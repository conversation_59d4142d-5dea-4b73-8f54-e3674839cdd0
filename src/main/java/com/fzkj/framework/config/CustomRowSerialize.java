package com.fzkj.framework.config;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fzkj.framework.web.domain.RowData;

import java.io.IOException;
import java.lang.reflect.Type;

public class CustomRowSerialize implements ObjectSerializer {
    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
        RowData rowData = (RowData) object;
        JSONObject data = JSONObject.parse(JSON.toJSONString(rowData.getData()));
        data.put("Num", rowData.getNum());
        data.put("rownum", rowData.getNum());
        data.put("Number", rowData.getNum());
        // 序列化 JSONObject
        serializer.write(data);
    }
}
