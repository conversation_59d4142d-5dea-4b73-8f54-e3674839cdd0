<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.LessonSafeTrainCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.LessonSafeTrainCompany">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="company_name" property="companyName" />
        <result column="lesson_id" property="lessonId" />
        <result column="lesson_category_id" property="lessonCategoryId" />
        <result column="lesson_category_name" property="lessonCategoryName" />
        <result column="hand_mode" property="handMode" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, company_name, lesson_id, lesson_category_id, lesson_category_name, hand_mode, source, sort, creator_code, creation_time, revise_code, revise_time, remark, is_valid
    </sql>

    <select id="selectLessonInfo" resultType="com.fzkj.project.system.vo.LessonInfoVO">
        select lstc.lesson_id ,lst.lesson_name ,lst.lesson_category_name ,lstc.company_id
        from t_d_lesson_safe_train_company lstc
        left join t_d_lesson_safe_train lst on lst.id = lstc.lesson_id
        where lstc.is_valid =1 and SUBSTR(lst.lesson_date, 1, 7) = SUBSTR(#{month}, 1, 7)
    </select>

</mapper>