package com.fzkj.project.system.service.impl;

import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.entity.UserRole;
import com.fzkj.project.system.mapper.UserInfoMapper;
import com.fzkj.project.system.vo.UserRoleVO;
import com.fzkj.project.system.request.UserRoleRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.UserRoleMapper;
import com.fzkj.project.system.service.UserRoleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户关联角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements UserRoleService {

    @Override
    public List<UserRole> listUserRole(UserRoleRequest request) {
        UserRole entity = (UserRole) DataTransfer.transfer(request, UserRole.class);
        LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(UserRoleVO request, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(),pageEntity.getPageSize());
        List<UserRoleVO> list = baseMapper.selectUserRolePage(request.getKeyWord(),request.getRoleId(),request.getIsValid());
        return new PageUtils(pageEntity,list);
    }

    @Override
    public UserRoleVO findUserRole(Long id) {
        UserRole entity = this.getById(id);
        UserRoleVO vo = (UserRoleVO) DataTransfer.transfer(entity, UserRoleVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveUserRole(UserRoleVO vo) {
        UserRole entity = (UserRole) DataTransfer.transfer(vo, UserRole.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateUserRole(UserRoleVO vo) {
        String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
        if(vo.getFlag().equals("enable")){
            LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserRole::getUserCode,vo.getUserCode());
            queryWrapper.eq(UserRole::getOperPlatform,4);
            UserRole userRole = baseMapper.selectOne(queryWrapper);
            userRole.setReviseTime(LocalDateTime.now());
            userRole.setReviseCode(userCode);
            userRole.setIsValid(userRole.getIsValid() == 1?0:1);
            this.updateById(userRole);
        }else if(vo.getFlag().equals("edit")){
            LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserRole::getUserCode,vo.getUserCode());
            queryWrapper.eq(UserRole::getOperPlatform,4);
            UserRole userRole = baseMapper.selectOne(queryWrapper);
            userRole.setRemark(vo.getRemark());
            userRole.setRoleId(vo.getRoleId());
            userRole.setReviseTime(LocalDateTime.now());
            userRole.setReviseCode(userCode);
            this.updateById(userRole);
        }else if(vo.getFlag().equals("add")){
            LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserRole::getUserCode,vo.getUserCode());
            queryWrapper.eq(UserRole::getOperPlatform,4);
            Integer userRoleCount = baseMapper.selectCount(queryWrapper);
            if(userRoleCount > 0){
                return null;
            }
            UserRole userRole = new UserRole();
            userRole.setRemark(vo.getRemark());
            userRole.setRoleId(vo.getRoleId());
            userRole.setUserCode(vo.getUserCode());
            userRole.setOperPlatform(4);
            userRole.setCreatorCode(userCode);
            userRole.setCreatorTime(LocalDateTime.now());
            userRole.setIsValid(1);
            this.save(userRole);
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserRole(Long id) {
        return this.removeById(id);
    }
}