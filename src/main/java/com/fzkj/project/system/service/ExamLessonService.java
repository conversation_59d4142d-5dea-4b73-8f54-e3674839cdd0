package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.ExamLesson;
import com.fzkj.project.system.vo.ExamLessonVO;
import com.fzkj.project.system.request.ExamLessonRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 考试课程关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExamLessonService extends IService<ExamLesson> {

    List<ExamLesson> listExamLesson(ExamLessonRequest request);

    PageUtils listByPage(ExamLessonRequest request, PageEntity pageEntity);

    ExamLessonVO findExamLesson(Long id);

    Long saveExamLesson(ExamLessonVO vo);

    boolean updateExamLesson(ExamLessonVO vo);

    boolean removeExamLesson(Long id);
}
