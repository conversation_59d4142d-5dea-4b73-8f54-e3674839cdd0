# fkxt-edu
**在线教育平台java后台代码**

### 关于请求

当前所有请求都为POST请求,鉴于请求前缀不一致且请求路径不重复，在url与请求匹配时优先按全路径匹配，匹配不到时会删除第一个路径再次匹配，当controller没有配置RequestMapping时，如/lessonApi/Get_Course和Get_Course都可以匹配到同一个Get_Course方法

### 关于请求参数
```
    @PostMapping("GetUserLogin")
    public AjaxResult GetUserLogin(@RequestBody CommonRequest request){
        LoginRequest loginVO = (LoginRequest) request.getRequest(LoginRequest.class);
        // 生成令牌
        LoginResultVO result = loginService.login(loginVO.getAccount(), loginVO.getPwd(), request.getPlatform());
        if (null != result) {
            return AjaxResult.success(result);
        } else {
            return AjaxResult.error("登录失败,账号或密码错误");
        }
    }
```
当前所有请求参数都用(@RequestBody CommonRequest request)接收，获取请求参数对象使用LoginRequest loginVO = (LoginRequest) request.getRequest(LoginRequest.class);

可以支持用map获取参数，如HashMap data = (HashMap) request.getRequest(HashMap.class);

data参数转为Request对象时，不区分属性大小写，所以不建议对象同时存在相同属性名，大小写不同的属性

data参数转为Request对象时，如果属性值为-999，不管是字符串还是数字类型，Request对象中对应的属性值为null,方便在mapper.xml统一使用null作为查询参数判断，详见JsonUtil

### 关于返回结果

本项目有两种返回结果格式，一种是{code:1000,Message:"操作成功",ResultJson:""},一种是{code:true/false,rtncode:1000,Message:"操作成功",ResultJson:""}

需要返回第二种结果的接口，在controller类或方法上加上@ThirdPart注解即可,如果类上标记了@ThirdPart，但个别方法不需要返回这种格式，可以在方法上加上@ThirdPart(false),
详见AjaxResult类

关于列表查询结果，会自动在查询记录中返回Num和rownum,无需再对应返回实体中添加属性，详见CustomRowSerialize类

关于返回json中属性名称，默认使用JsonField,JsonProperties，如果没有添加对应注解，默认使用属性名，并保留大小写。详见AjaxResult.

关于返回json中属性值，当属性类型为Long,且值为null时，序列化后的值为-1。详见AjaxResult.

### 关于token

当前运行环境profile为dev时，不校验token，可访问所有请求，不需要该功能请自行注释，详见SecurityConfig

