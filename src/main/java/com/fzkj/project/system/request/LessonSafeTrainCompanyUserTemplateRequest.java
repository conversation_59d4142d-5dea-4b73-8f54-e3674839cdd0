package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 企业下的用户安全培训课程用户模板
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "企业下的用户安全培训课程用户模板Request")
public class LessonSafeTrainCompanyUserTemplateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业ID")
    private Long companyId;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "区域编码")

    private String areaCode;

    @ApiModelProperty(value = "部门ID")
    private Long departId;

    @ApiModelProperty(value = "部门ID集合")
    private List<Long> departIds;

    @ApiModelProperty(value = "工作ID")
    private Long workId;

    @ApiModelProperty(value = "参培课程（1：（通用课程）自动分发 2：（定制课程）手动分发）")
    private Integer handMode;

    @ApiModelProperty(value = "是否托管：1：开启0：不开启")
    private Integer isTrusteeship;

    @ApiModelProperty(value = "课程分类ID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "是否是付费课程")
    private Integer isPayType;

    @ApiModelProperty(value = "是否是统计分组")
    private Integer statisticalGroup;

    @ApiModelProperty(value = "操作标识")
    private Integer flag;

    @ApiModelProperty(value = "是否是免费课程")
    private Integer isFree;
}