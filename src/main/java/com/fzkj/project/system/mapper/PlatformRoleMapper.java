package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.PlatformRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.vo.PlatformRoleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 平台角色 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface PlatformRoleMapper extends BaseMapper<PlatformRole> {

    List<PlatformRoleVO> getPlatformRoleList(@Param(value = "roleName") String roleName,
                                             @Param(value = "isValid") Integer isValid,
                                             @Param(value = "operPlatform") Integer operPlatform,
                                             @Param(value = "type") Integer type);

    List<PlatformRoleVO> getPlatformRoleListByCompanyIdAndOper(@Param(value = "operPlatform") Integer operPlatform,
                                                               @Param(value = "companyId") Long companyId
                                                               );

}