package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 提醒管理表
 *
 * <AUTHOR>
 * @since 2024-03-04
 */
@Data
@TableName("t_d_reminder")
@ApiModel(value = "提醒管理表实体")
public class Reminder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @JsonProperty("Title")
    private String title;

    @JsonProperty("Val")
    private String val;

    @JsonProperty("TrainType")
    private Integer trainType;

    @JsonProperty("RemindType")
    private Integer remindType;

    @JsonProperty("CreatorCode")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;
}