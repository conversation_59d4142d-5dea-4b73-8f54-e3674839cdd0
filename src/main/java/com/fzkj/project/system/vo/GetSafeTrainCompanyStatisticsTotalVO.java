package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetSafeTrainCompanyStatisticsTotalVO {

    @ApiModelProperty(value = "培训计划ID")
    @JsonProperty("PlanID")
    private Long planId;

    @ApiModelProperty(value = "培训计划")
    @JsonProperty("PlanName")
    private String planName;

    @ApiModelProperty(value = "培训计划月份")
    @JsonProperty("LessonDate")
    private String lessonDate;

    @ApiModelProperty(value = "企业名称/部门名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "总人数")
    @JsonProperty("TotalUserNum")
    private int totalUserNum;

    @ApiModelProperty(value = "参学学员数量")
    @JsonProperty("CXUserNum")
    private int cxUserNum = 0;

    @ApiModelProperty(value = "已完成人数")
    @JsonProperty("WCUserNum")
    private int wcUserNum = 0;

    @ApiModelProperty(value = "参学率")
    @JsonProperty("CXL")
    private String cxl = "0%";

    @ApiModelProperty(value = "完成率")
    @JsonProperty("WCL")
    private String wcl = "0%";


}
