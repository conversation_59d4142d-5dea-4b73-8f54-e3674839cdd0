package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户答题记录表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value = "用户答题记录表Request")
public class ExamAnswerRecordRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "T_D_ExamUserRecord表ID")
    private Long recordId;

    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "考试ID")
    private Long examId;

    @ApiModelProperty(value = "题目ID")
    private Long questionId;

    @ApiModelProperty(value = "用户选择的答案")
    private String subOption;

    @ApiModelProperty(value = "正确答案")
    private String rightOption;

    @ApiModelProperty(value = "得分")
    private Integer score;

    @ApiModelProperty(value = "题目序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    private String reviseId;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否有效：0：无效，1：有效")
    private Integer isValid;

    @JsonProperty("Flag")
    private String flag;
}