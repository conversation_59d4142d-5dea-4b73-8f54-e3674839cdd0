package com.fzkj.project.system.controller;

import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.CourseCategoryQueryRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.CourseCategoryService;
import com.fzkj.project.system.vo.CourseCategoryVO;
import com.fzkj.project.system.request.CourseCategoryRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 平台课件分类 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
@RequestMapping("lessonApi")
@Log
public class CourseCategoryController extends BaseEntity {

    private final CourseCategoryService CourseCategoryServiceImpl;

    @PostMapping("Get_CourseCategory")
    @ApiOperation(value = "平台课件分类分页查询")
    public AjaxResult Get_CourseCategory(@RequestBody CommonRequest commonRequest) {
        CourseCategoryQueryRequest request = (CourseCategoryQueryRequest) commonRequest.getRequest(CourseCategoryQueryRequest.class);
        return AjaxResult.success(CourseCategoryServiceImpl.listCourseCategory(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Edit_CourseCategory")
    @ApiOperation(value = "编辑课件分类")
    public AjaxResult Edit_CourseCategory(@RequestBody CommonRequest commonRequest) {
        CourseCategoryVO vo = (CourseCategoryVO) commonRequest.getRequest(CourseCategoryVO.class);
        return AjaxResult.success(CourseCategoryServiceImpl.updateCourseCategory(vo));
    }

}
