package com.fzkj.common.constant;

import io.jsonwebtoken.Claims;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants
{
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    public static final int DAY =0;

    public static final int MONTH = 1;

    public static final int YEAR = 2;

    public static  final Integer MAX_EXPORT = 100000;

    public static  final int PLATFORM_MINI = 3;

    public static  final int PLATFORM_MANAGER = 6;

    public static  final int PLATFORM_ENTERPRISE = 7;

    public static final String APPLETS_APPID = "wx53a3c70173b9c223";

    public static final String APPLETS_SECRET = "b1ae7ae5ce6f8344d0678a9b669ccdd4";

    public static final String PUBLIC_APPID = "wx4a3a60c42b1fa616";

    public static final String PUBLIC_SECRET = "ec635be10ff80cdadbadce84255101bf";

    //新增数据
    public static final String ADD_DATA="add";

    //修改数据
    public static final String EDIT_DATA="edit";

    //删除数据
    public static final String DEL_DATA="del";

    //小程序token
    public static final String ACCESS_TOKEN_KEY = "access_token";

    //公众号token
    public static final String PUBLIC_ACCESS_TOKEN_KEY = "public_access_token";
}
