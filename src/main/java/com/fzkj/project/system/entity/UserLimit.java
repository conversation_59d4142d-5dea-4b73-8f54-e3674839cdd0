package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 用户管理机构表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_user_limit")
@ApiModel(value = "用户管理机构表实体")
public class UserLimit implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("UserCode")
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @JsonProperty("TargetID")
    @ApiModelProperty(value = "机构ID")
    private Long targetId;

    @JsonProperty("TargetType")
    @ApiModelProperty(value = "管理类型 company企业 depart部门")
    private String targetType;

    @JsonProperty("PcRoleID")
    @ApiModelProperty(value = "PC端权限")
    private String pcRoleId;

    @JsonProperty("MobileRoleID")
    @ApiModelProperty(value = "移动端权限")
    private String mobileRoleId;

    @JsonProperty("OperPlatform")
    @ApiModelProperty(value = "操作平台")
    private Integer operPlatform;

    @JsonProperty("Source")
    @ApiModelProperty(value = "来源")
    private String source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;
}