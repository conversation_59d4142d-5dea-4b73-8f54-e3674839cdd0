package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户关联角色
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="用户关联角色VO")
public class UserRoleVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("TargetID")
    private Long targetId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("RoleID")
    private Long roleId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("OperPlatform")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsEnable")
    private Integer isValid;

    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("UserPhoto")
    private String userPhoto;

    @JsonProperty("JobNo")
    private String jobNo;

    @JsonProperty("Phone")
    private String phone;

    @JsonProperty("RoleName")
    private String roleName;

    @JsonProperty("KeyWord")
    private String keyWord;

    @JsonProperty("Flag")
    private String flag;

}