package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class CommentVO{

    @JsonProperty("ID")
    private String id;

    @JsonProperty("FunType")
    private String funType;

    @JsonProperty("UserCode")
    private String userCode;

    @JsonProperty("IdCard")
    private String idCard;

    @JsonProperty("UserName")
    @Excel(name = "评论用户",order = 3)
    private String userName;

    @JsonProperty("Phone")
    private String phone;

    @JsonProperty("UserPhoto")
    private String userPhoto;

    @JsonProperty("Content")
    @Excel(name = "评论内容",order = 2)
    private String content;

    @JsonProperty("Grade")
    private String grade;

    @JsonProperty("TargetID")
    private Long targetId;

    @JsonProperty("Status")
    private Integer status;

    @JsonProperty("Sort")
    private String sort;

    @JsonProperty("CreatorCode")
    private String creatorCode;

    @JsonProperty("CreationTime")
    @Excel(name = "评论时间",order = 4, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date creationTime;

    @JsonProperty("ReviseCode")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("IsReply")
    @Excel(name = "回复状态",order = 5,readConverterExp = "0=未回复,1=已回复")
    private Integer isReply;

    @JsonProperty("ReplyContent")
    @Excel(name = "回复内容",order = 6)
    private String replyContent;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("ReplyUserCode")
    private Integer replyUserCode;

    @JsonProperty("ReplyTime")
    @Excel(name = "回复时间",order = 8)
    private String replyTime;

    @JsonProperty("ContentTitle")
    @Excel(name = "课件名称",order = 1)
    private String contentTitle;

    @JsonProperty("Flag")
    private String flag;

    @JsonProperty("ReplyName")
    @Excel(name = "回复人",order = 7)
    private String replyName;

    @JsonProperty("Keywords")
    private String keywords;

    @JsonProperty("Type")
    private Integer type;

    @JsonProperty("TeacherID")
    private Long teacherId;

}