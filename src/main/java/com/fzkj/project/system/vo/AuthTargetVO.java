package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AuthTargetVO {
    @JsonProperty("ID")
    private Long id;

    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("FID")
    private Long fid;

    @JsonProperty("FName")
    private String fName;

    @JsonProperty("PackageName")
    private String packageName;

    @JsonProperty("AreaCode")
    private String areaCode;

    @JsonProperty("AreaName")
    private String areaName;
}
