package com.fzkj.project.system.service;

/**
 * 短信服务
 */
public interface ISmsService {
    /**
     * 发送短信验证码
     * @param mobile
     * @param smsType
     * @return
     */
    String sendVerificationCode(String mobile, String smsType);

    /**
     * 验证短信验证码
     * @param mobile
     * @param smsType
     * @param verificationCode
     * @return
     */
    boolean verify(String mobile, String smsType, String verificationCode);

    /**
     * 清除短信验证码
     *
     * @param mobile
     * @param smsType
     */
    void removeVerificationCode(String mobile, String smsType);
}
