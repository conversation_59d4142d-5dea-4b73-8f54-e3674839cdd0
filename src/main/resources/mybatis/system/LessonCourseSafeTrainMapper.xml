<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.LessonCourseSafeTrainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.LessonCourseSafeTrain">
        <id column="id" property="id"/>
        <result column="lesson_id" property="lessonId"/>
        <result column="course_id" property="courseId"/>
        <result column="belong_plat" property="belongPlat"/>
        <result column="source" property="source"/>
        <result column="sort" property="sort"/>
        <result column="creator_code" property="creatorCode"/>
        <result column="creation_time" property="creationTime"/>
        <result column="revise_code" property="reviseCode"/>
        <result column="revise_time" property="reviseTime"/>
        <result column="remark" property="remark"/>
        <result column="is_valid" property="isValid"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, lesson_id, course_id, belong_plat, source, sort, creator_code, creation_time, revise_code, revise_time,
        remark, is_valid
    </sql>

    <select id="selectCourseListByLessonId" resultType="com.fzkj.project.system.vo.CourseVO">
        select c.* from t_d_course c
        left join t_d_lesson_course_safe_train lc on lc.course_id = c.id
        where lc.lesson_id = #{lessonId} and lc.is_valid = 1 and c.is_valid = 1
    </select>
    <select id="selectCourseRecord" resultType="com.fzkj.project.system.vo.CourseRecordVO">
        select lcc.lesson_id,lcc.course_id,c.course_name,lcc.sort,
        -3 course_is_complete,
        '' face_distinguish_img,
        '' start_study_time
        from t_d_lesson_course_safe_train lcc
        left join t_d_course c on c.id = lcc.course_id
        where lcc.lesson_id = #{lessonId} and lcc.is_valid = 1
        order by lcc.sort asc,lcc.course_id asc
    </select>

    <select id="checkCourseCode" resultType="java.lang.String">
        select course_code from t_d_course where course_code = #{courseCode}
    </select>
</mapper>