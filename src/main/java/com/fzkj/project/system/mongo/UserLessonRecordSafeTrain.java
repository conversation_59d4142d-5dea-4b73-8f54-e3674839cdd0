package com.fzkj.project.system.mongo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全培训课程分发学员学习记录
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@Document(collection = "_tableSafeTrainUserLesson")
@ApiModel(value = "安全培训课程分发学员学习记录实体")
public class UserLessonRecordSafeTrain implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @Id
    private ObjectId id;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "部门ID")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "岗位ID")
    @JsonProperty("WorkID")
    private Long workId;

    @ApiModelProperty(value = "部门名称")
    @JsonProperty("DepartName")
    private String departName;

    @ApiModelProperty(value = "岗位名称")
    @JsonProperty("WorkName")
    private String workName;

    @ApiModelProperty(value = "课程分类ID")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课程分类名称")
    @JsonProperty("LessonCategoryName")
    @Excel(name = "课程分类",order = 3)
    private String lessonCategoryName;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课程月份")
    @JsonProperty("LessonDate")
    @Excel(name = "月份",order = 2)
    private String lessonDate;

    @ApiModelProperty(value = "课程名称")
    @JsonProperty("LessonName")
    @Excel(name = "课程名称",order = 1)
    private String lessonName;

    @ApiModelProperty(value = "课件数量")
    @JsonProperty("LessonCourseCount")
    private Integer lessonCourseCount;

    @ApiModelProperty(value = "试卷ID")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "考试分数")
    @JsonProperty("Score")
    private Integer score;

    @JsonProperty("Shape")
    @ApiModelProperty(value = "课程形式")
    private Integer shape;

    @ApiModelProperty(value = "用户可补考次数，默认1次")
    @JsonProperty("ResitNumber")
    private Integer resitNumber;

    @ApiModelProperty(value = "学习状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）")
    @JsonProperty("IsComplete")
    private Integer isComplete;

    @ApiModelProperty(value = "课程统计状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）")
    @JsonProperty("StaticStatus")
    @Excel(name = "培训状态",order = 8, readConverterExp = "0=未完成,1=已完成,-3=未开始")
    private Integer staticStatus;

    @ApiModelProperty(value = "开始学习时间")
    @JsonProperty("StartStudyTime")
    private String startStudyTime;

    @ApiModelProperty(value = "学习完成时间")
    @JsonProperty("CompleteTime")
    @Excel(name = "完成时间",order = 9)
    private String completeTime;

    @ApiModelProperty(value = "用户ID")
    @JsonProperty("UserID")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "用户编码")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "联系电话")
    @JsonProperty("Phone")
    private String phone;

    @ApiModelProperty(value = "用户头像")
    @JsonProperty("UserPhoto")
    private String userPhoto;

    @ApiModelProperty(value = "身份证")
    @JsonProperty("IDCard")
    private String idCard;

    @ApiModelProperty(value = "是否作为统计对象")
    @JsonProperty("IsStatic")
    private Integer isStatic;

    @ApiModelProperty(value = "课程支付类型：2：学员免费；3:学员付费")
    @JsonProperty("IsPayType")
    private Integer isPayType;

    @ApiModelProperty(value = "课程支付状态：0：未支付；1：已支付")
    @JsonProperty("IsPay")
    private Integer isPay;

    @ApiModelProperty(value = "培训开始时间")
    @JsonProperty("TrainStartTime")
    @Excel(name = "开始日期",order = 6)
    private String trainStartTime;

    @ApiModelProperty(value = "培训结束时间")
    @JsonProperty("TrainEndTime")
    @Excel(name = "结束日期",order = 7)
    private String trainEndTime;

    @ApiModelProperty(value = "签名图片")
    @JsonProperty("SignImg")
    private String signImg;

    @ApiModelProperty(value = "培训时长")
    @JsonProperty("TrainTimeCount")
    @Excel(name = "课程学时",order = 4)
    private Long trainTimeCount;

    @ApiModelProperty(value = "地区编码")
    @JsonProperty("AreaCode")
    private String areaCode;

    @ApiModelProperty(value = "地区名称")
    @JsonProperty("AreaName")
    private String areaName;

    @ApiModelProperty(value = "来源")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    @Excel(name = "创建时间",order = 11, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Comment")
    private String comment;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效，1：是，0：否，默认有效")
    @JsonProperty("IsValid")
    @Excel(name = "记录状态",order = 10, readConverterExp = "0=已回收,1=正常")
    private Integer isValid;

    @ApiModelProperty(value = "分发来源，1：售后服务端分发的，0：用户自己购买分发的")
    @JsonProperty("DisSource")
    private Integer disSource;

    @ApiModelProperty(value = "参培课程（1：通用课程，2：定制课程）")
    @JsonProperty("HandMode")
    @Excel(name = "课程类型",order = 5, readConverterExp = "1=通用课程,2=定制课程")
    private Integer handMode;

    @ApiModelProperty(value = "预留字段1")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @JsonProperty("ReservedField5")
    private String reservedField5;

    @ApiModelProperty(value = "统计分组")
    @JsonProperty("StatisticalGroup")
    private Integer statisticalGroup;

    @ApiModelProperty(value = "工号")
    @JsonProperty("JobNo")
    private String jobNo;

    @TableField(exist = false)
    @ApiModelProperty(value = "学习人数")
    @JsonProperty("LearnNum")
    private int learnNum;

    @TableField(exist = false)
    @JsonProperty("IsEnable")
    @ApiModelProperty(value = "是否启用")
    private Integer isEnable;

    @TableField(exist = false)
    @ApiModelProperty(value = "员工支付价格（企业课程）")
    @JsonProperty("StaffPrice")
    private BigDecimal staffPrice;

    @TableField(exist = false)
    @JsonProperty("OpenID")
    @ApiModelProperty(value = "用户微信标识")
    private String openId;

    @TableField(exist = false)
    @JsonProperty("PublicOpenID")
    @ApiModelProperty(value = "用户微信标识")
    private String publicOpenId;

    @TableField(exist = false)
    @JsonIgnore
    private String LessonIds;
    @TableField(exist = false)
    @JsonIgnore
    private String companyIds;

    @TableField(exist = false)
    @JsonIgnore
    private String userCodes;
    @TableField(exist = false)
    @JsonIgnore
    private List<Long> departIds;

    private Integer payStatus;
}