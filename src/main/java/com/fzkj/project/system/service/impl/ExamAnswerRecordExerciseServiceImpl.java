package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.ExamAnswerRecordExercise;
import com.fzkj.project.system.vo.ExamAnswerRecordExerciseVO;
import com.fzkj.project.system.request.ExamAnswerRecordExerciseRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ExamAnswerRecordExerciseMapper;
import com.fzkj.project.system.service.ExamAnswerRecordExerciseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户练习记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExamAnswerRecordExerciseServiceImpl extends ServiceImpl<ExamAnswerRecordExerciseMapper, ExamAnswerRecordExercise> implements ExamAnswerRecordExerciseService {

    @Override
    public List<ExamAnswerRecordExercise> listExamAnswerRecordExercise(ExamAnswerRecordExerciseRequest request) {
        ExamAnswerRecordExercise entity = (ExamAnswerRecordExercise) DataTransfer.transfer(request, ExamAnswerRecordExercise.class);
        LambdaQueryWrapper<ExamAnswerRecordExercise> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ExamAnswerRecordExerciseRequest request, PageEntity pageEntity) {
        ExamAnswerRecordExercise entity = (ExamAnswerRecordExercise) DataTransfer.transfer(request, ExamAnswerRecordExercise.class);
        LambdaQueryWrapper<ExamAnswerRecordExercise> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<ExamAnswerRecordExercise> page = this.page(
                new Query<ExamAnswerRecordExercise>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ExamAnswerRecordExerciseVO findExamAnswerRecordExercise(Long id) {
        ExamAnswerRecordExercise entity = this.getById(id);
        ExamAnswerRecordExerciseVO vo = (ExamAnswerRecordExerciseVO) DataTransfer.transfer(entity, ExamAnswerRecordExerciseVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExamAnswerRecordExercise(ExamAnswerRecordExerciseVO vo) {
        ExamAnswerRecordExercise entity = (ExamAnswerRecordExercise) DataTransfer.transfer(vo, ExamAnswerRecordExercise.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamAnswerRecordExercise(ExamAnswerRecordExerciseVO vo) {
        ExamAnswerRecordExercise entity = (ExamAnswerRecordExercise) DataTransfer.transfer(vo, ExamAnswerRecordExercise.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExamAnswerRecordExercise(Long id) {
        return this.removeById(id);
    }
}