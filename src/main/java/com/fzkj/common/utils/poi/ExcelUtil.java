package com.fzkj.common.utils.poi;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.fzkj.common.core.text.Convert;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.common.utils.reflect.ReflectUtils;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import com.fzkj.framework.aspectj.lang.annotation.Excel.ColumnType;
import com.fzkj.framework.aspectj.lang.annotation.Excel.Type;
import com.fzkj.framework.aspectj.lang.annotation.Excels;
import com.fzkj.framework.config.AppConfig;
import com.fzkj.framework.web.domain.AjaxResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFPicture;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URL;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel相关处理
 *
 * <AUTHOR>
 */
public class ExcelUtil<T> {
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * Excel sheet最大行数，默认65536
     */
    public static final int sheetSize = 65536;

    /**
     * 工作表名称
     */
    private String sheetName;
    private List<List<ExcelCellData>> others;
    private List<SheetVO> sheetList;
    private LinkedHashSet<String> includeKeys;

    private int startIndex = 0;

    /**
     * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
     */
    private Type type;
    /**
     * 是否导出序号
     */
    private boolean index;

    /**
     * 工作薄对象
     */
    private Workbook wb;

    /**
     * 工作表对象
     */
    private Sheet sheet;

    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;

    /**
     * 导入导出数据列表
     */
    private List<T> list;

    /**
     * 注解列表
     */
    private List<Object[]> fields;

    /**
     * 实体对象
     */
    public Class<T> clazz;

    public ExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }

    public void init(List<T> list, String sheetName, Type type, List others, LinkedHashSet<String> includeKeys, boolean index) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.others = others;
        this.includeKeys = includeKeys;
        if (null != others) {
            startIndex = others.size();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        this.index = index;
        createExcelField();
        createWorkbook();
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     *
     * @param is 输入流
     * @return 转换后集合
     */
    public List<T> importExcel(InputStream is) throws Exception {
        return importExcel(StringUtils.EMPTY, is);
    }

    /**
     * 对excel表单指定表格索引名转换成list
     *
     * @param sheetName 表格索引名
     * @param is        输入流
     * @return 转换后集合
     */
    public List<T> importExcel(String sheetName, InputStream is) throws Exception {
        this.type = Type.IMPORT;
        this.wb = WorkbookFactory.create(is);
        List<T> list = new ArrayList<T>();
        Sheet sheet = null;
        if (StringUtils.isNotEmpty(sheetName)) {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = wb.getSheet(sheetName);
        } else {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = wb.getSheetAt(0);
        }

        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }

        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0) {
            // 定义一个map用于存放excel列的序号和field.
            Map<String, Integer> cellMap = new HashMap<String, Integer>();
            // 获取表头
            Row heard = sheet.getRow(0);
            for (int i = 0; i < heard.getPhysicalNumberOfCells(); i++) {
                Cell cell = heard.getCell(i);
                if (StringUtils.isNotNull(cell != null)) {
                    String value = this.getCellValue(heard, i).toString();
                    cellMap.put(value, i);
                } else {
                    cellMap.put(null, i);
                }
            }
            // 有数据时才处理 得到类的所有field.
            Field[] allFields = clazz.getDeclaredFields();
            // 定义一个map用于存放列的序号和field.
            Map<Integer, Field> fieldsMap = new HashMap<Integer, Field>();
            for (int col = 0; col < allFields.length; col++) {
                Field field = allFields[col];
                Excel attr = field.getAnnotation(Excel.class);
                if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
                    // 设置类的私有字段属性可访问.
                    field.setAccessible(true);
                    Integer column = cellMap.get(attr.name());
                    if (ObjectUtils.isEmpty(column)) {
                        column = col;
                    }
                    fieldsMap.put(column, field);
                }
            }
            for (int i = 1; i < rows; i++) {
                // 从第2行开始取数据,默认第一行是表头.
                Row row = sheet.getRow(i);
                T entity = null;
                for (Map.Entry<Integer, Field> entry : fieldsMap.entrySet()) {
                    Object val = this.getCellValue(row, entry.getKey());

                    // 如果不存在实例则新建.
                    entity = (entity == null ? clazz.newInstance() : entity);
                    // 从map中得到对应列的field.
                    Field field = fieldsMap.get(entry.getKey());
                    // 取得类型,并根据对象类型设置值.
                    Class<?> fieldType = field.getType();
                    if (String.class == fieldType) {
                        String s = Convert.toStr(val);
                        if (StringUtils.endsWith(s, ".0")) {
                            val = StringUtils.substringBefore(s, ".0");
                        } else {
                            val = Convert.toStr(val);
                        }
                    } else if ((Integer.TYPE == fieldType) || (Integer.class == fieldType)) {
                        val = Convert.toInt(val);
                    } else if ((Long.TYPE == fieldType) || (Long.class == fieldType)) {
                        val = Convert.toLong(val);
                    } else if ((Double.TYPE == fieldType) || (Double.class == fieldType)) {
                        val = Convert.toDouble(val);
                    } else if ((Float.TYPE == fieldType) || (Float.class == fieldType)) {
                        val = Convert.toFloat(val);
                    } else if (BigDecimal.class == fieldType) {
                        val = Convert.toBigDecimal(val);
                    } else if (Date.class == fieldType) {
                        if (val instanceof String) {
                            val = DateUtils.parseDate(val);
                        } else if (val instanceof Double) {
                            val = DateUtil.getJavaDate((Double) val);
                        }
                    }
                    if (StringUtils.isNotNull(fieldType)) {
                        Excel attr = field.getAnnotation(Excel.class);
                        String propertyName = field.getName();
                        if (StringUtils.isNotEmpty(attr.targetAttr())) {
                            propertyName = field.getName() + "." + attr.targetAttr();
                        } else if (StringUtils.isNotEmpty(attr.readConverterExp())) {
                            val = reverseByExp(String.valueOf(val), attr.readConverterExp());
                        }
                        ReflectUtils.invokeSetter(entity, propertyName, val);
                    }
                }
                list.add(entity);
            }
        }
        return list;
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public AjaxResult exportExcel(List<T> list, String sheetName) {
        this.init(list, sheetName, Type.EXPORT, null, null, false);
        return exportExcel();
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public InputStream exportExcelFile(List<T> list, String sheetName) {
        this.init(list, sheetName, Type.EXPORT, null, null, false);
        return exportExcelFile();
    }


    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public AjaxResult exportExcel(List<T> list, String sheetName, boolean index) {
        this.init(list, sheetName, Type.EXPORT, null, null, index);
        return exportExcel();
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public AjaxResult exportExcel(List<T> list, String sheetName, List<List<ExcelCellData>> others, boolean index) {
        this.init(list, sheetName, Type.EXPORT, others, null, index);
        return exportExcel();
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public AjaxResult exportExcel(List<T> list, String sheetName, LinkedHashSet<String> includeKeys, boolean index) {
        this.init(list, sheetName, Type.EXPORT, null, includeKeys, index);
        return exportExcel();
    }


    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public AjaxResult importTemplateExcel(String sheetName, boolean index) {
        this.init(null, sheetName, Type.IMPORT, null, null, index);
        return exportExcel();
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @return 结果
     */
    public AjaxResult exportExcel() {
        ByteArrayOutputStream out = null;
        try {
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);
                //导出头部数据
                if (startIndex != 0) {
                    for (int i = 0; i < others.size(); i++) {
                        Row row = sheet.createRow(i);
                        List<ExcelCellData> excelCellDatas = others.get(i);
                        if (null == excelCellDatas || excelCellDatas.isEmpty()) {
                            continue;
                        }
                        int startColumn = 0;
                        for (int j = 0; j < excelCellDatas.size(); j++) {
                            ExcelCellData excelCellData = excelCellDatas.get(j);
                            if (excelCellData.getColumn() > 1) {
                                for (int k = 0; k < excelCellData.getColumn(); k++) {
                                    Cell cell = row.createCell(startColumn + k);
                                    // 写入列信息
                                    cell.setCellValue(excelCellData.getContent());
                                    cell.setCellStyle(styles.get(excelCellData.getStyle()));
                                }
                                sheet.addMergedRegion(new CellRangeAddress(i, i, startColumn, startColumn + excelCellData.getColumn() - 1));
                                startColumn += excelCellData.getColumn();
                            } else {
                                Cell cell = row.createCell(startColumn);
                                // 写入列信息
                                cell.setCellValue(excelCellData.getContent());
                                cell.setCellStyle(styles.get(excelCellData.getStyle()));
                                startColumn++;
                            }
                        }
                    }
                }
                // 产生一行
                Row row = sheet.createRow(startIndex);
                int column = 0;
                if (this.index) {
                    this.createIndexCell(row, column++);
                }
                // 写入各个字段的列头名称
                for (Object[] os : fields) {
                    Excel excel = (Excel) os[1];
                    this.createCell(excel, row, column++);
                }
                if (Type.EXPORT.equals(type)) {
                    if (CollectionUtils.isEmpty(list)) {
                        fillExcelEmpty(index, row);
                    } else {
                        fillExcelData(index, row);
                    }
                }
            }
            String filename = encodingDateFilename(sheetName);
            out = new ByteArrayOutputStream();
            wb.write(out);
            //将字节数组转换成输入流
            InputStream bio = new ByteArrayInputStream(out.toByteArray());
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(bio, "download", filename);
            return AjaxResult.success(filename + "," + uploadFile.getUrl());
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }


    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @return 结果
     */
    public InputStream exportExcelFile() {
        ByteArrayOutputStream out = null;
        try {
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);
                //导出头部数据
                if (startIndex != 0) {
                    for (int i = 0; i < others.size(); i++) {
                        Row row = sheet.createRow(i);
                        List<ExcelCellData> excelCellDatas = others.get(i);
                        if (null == excelCellDatas || excelCellDatas.isEmpty()) {
                            continue;
                        }
                        int startColumn = 0;
                        for (int j = 0; j < excelCellDatas.size(); j++) {
                            ExcelCellData excelCellData = excelCellDatas.get(j);
                            if (excelCellData.getColumn() > 1) {
                                for (int k = 0; k < excelCellData.getColumn(); k++) {
                                    Cell cell = row.createCell(startColumn + k);
                                    // 写入列信息
                                    cell.setCellValue(excelCellData.getContent());
                                    cell.setCellStyle(styles.get(excelCellData.getStyle()));
                                }
                                sheet.addMergedRegion(new CellRangeAddress(i, i, startColumn, startColumn + excelCellData.getColumn() - 1));
                                startColumn += excelCellData.getColumn();
                            } else {
                                Cell cell = row.createCell(startColumn);
                                // 写入列信息
                                cell.setCellValue(excelCellData.getContent());
                                cell.setCellStyle(styles.get(excelCellData.getStyle()));
                                startColumn++;
                            }
                        }
                    }
                }
                // 产生一行
                Row row = sheet.createRow(startIndex);
                int column = 0;
                if (this.index) {
                    this.createIndexCell(row, column++);
                }
                // 写入各个字段的列头名称
                for (Object[] os : fields) {
                    Excel excel = (Excel) os[1];
                    this.createCell(excel, row, column++);
                }
                if (Type.EXPORT.equals(type)) {
                    if (CollectionUtils.isEmpty(list)) {
                        fillExcelEmpty(index, row);
                    } else {
                        fillExcelData(index, row);
                    }
                }
            }
            out = new ByteArrayOutputStream();
            wb.write(out);
            //将字节数组转换成输入流
            InputStream bio = new ByteArrayInputStream(out.toByteArray());
            return bio;
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 填充excel数据
     *
     * @param index 序号
     * @param row   单元格行
     */
    public void fillExcelData(int index, Row row) {
        int startNo = index * sheetSize;
        int endNo = Math.min(startNo + sheetSize, list.size());
        for (int i = startNo; i < endNo; i++) {
            row = sheet.createRow(i + startIndex + 1 - startNo);
            // 得到导出对象.
            T vo = (T) list.get(i);
            int column = 0;
            if (this.index) {
                Cell cell = row.createCell(column);
                cell.setCellValue(i);
                cell.setCellStyle(styles.get("data"));
                column++;
            }
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                // 设置实体类私有属性可访问
                field.setAccessible(true);
                this.addCell(excel, row, vo, field, column++);
            }
        }
    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style.setWrapText(true);
        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFont(dataFont);
        styles.put("wrap_data", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
       /* style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);*/
        Font headerFont = wb.createFont();
        headerFont.setFontName("Arial");
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(headerFont);
        styles.put("header", style);
        styles.put("formatHeader", setCellHeadStyle(wb));
        styles.put("formatLogHeader", setCellLogHeadStyle(wb));
        styles.put("formatTitle", setCellTitleStyle(wb));
        styles.put("formatVal", setCellStyle(wb));
        styles.put("formatLeftVal", setCellLeftStyle(wb));
        styles.put("formatTabulation", setCellTabulationStyle(wb));
        return styles;
    }

    /**
     * 创建单元格
     */
    public Cell createCell(Excel attr, Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(attr.name());
        setDataValidation(attr, row, column);
        cell.setCellStyle(styles.get("header"));
        return cell;
    }

    /**
     * 创建单元格
     */
    public Cell createIndexCell(Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue("序号");
        cell.setCellStyle(styles.get("header"));
        return cell;
    }

    /**
     * 设置单元格信息
     *
     * @param value 单元格值
     * @param attr  注解相关
     * @param cell  单元格信息
     */
    public void setCellVo(Object value, Excel attr, Cell cell) {
        if (attr.wrapText()) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue(new HSSFRichTextString(StringUtils.isNull(value) ? attr.defaultValue() : value + attr.suffix()));
        } else if (ColumnType.STRING == attr.cellType()) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue(StringUtils.isNull(value) ? attr.defaultValue() : value + attr.suffix());
        } else if (ColumnType.NUMERIC == attr.cellType()) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue(Integer.parseInt(value + ""));
        }
    }

    /**
     * 创建表格样式
     */
    public void setDataValidation(Excel attr, Row row, int column) {
        if (attr.name().indexOf("注：") >= 0) {
            sheet.setColumnWidth(column, 6000);
        } else {
            // 设置列宽
            sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
            row.setHeight((short) (attr.height() * 20));
        }
        // 如果设置了提示信息则鼠标放上去提示.
        if (StringUtils.isNotEmpty(attr.prompt())) {
            // 这里默认设了2-101列提示.
            setXSSFPrompt(sheet, "", attr.prompt(), 1, 100, column, column);
        }
        // 如果设置了combo属性则本列只能选择不能输入
        if (attr.combo().length > 0) {
            // 这里默认设了2-101列只能选择不能输入.
            setXSSFValidation(sheet, attr.combo(), 1, 100, column, column);
        }
    }

    /**
     * 添加单元格
     */
    public Cell addCell(Excel attr, Row row, T vo, Field field, int column) {
        Cell cell = null;
        try {
            // 设置行高
            row.setHeight((short) (attr.height() * 20));
            // 根据Excel中设置情况决定是否导出,有些情况需要保持为空,希望用户填写这一列.
            if (attr.isExport()) {
                // 创建cell
                cell = row.createCell(column);
                if (attr.wrapText()) {
                    cell.setCellStyle(styles.get("wrap_data"));
                } else {
                    cell.setCellStyle(styles.get("data"));
                }

                // 用于读取对象中的属性
                Object value = getTargetValue(vo, field, attr);
                String dateFormat = attr.dateFormat();
                String readConverterExp = attr.readConverterExp();
                if (StringUtils.isNotEmpty(dateFormat) && StringUtils.isNotNull(value)) {
                    if (value instanceof LocalDateTime) {
                        cell.setCellValue(DateUtils.parseDateToStr(dateFormat, (LocalDateTime) value));
                    } else if (value instanceof Date) {
                        cell.setCellValue(DateUtils.parseDateToStr(dateFormat, (Date) value));
                    }

                } else if (StringUtils.isNotEmpty(readConverterExp) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(convertByExp(String.valueOf(value), readConverterExp));
                } else {
                    // 设置列类型
                    setCellVo(value, attr, cell);
                }
            }
        } catch (Exception e) {
            log.error("导出Excel失败{}", e);
        }
        return cell;
    }

    /**
     * 设置 POI XSSFSheet 单元格提示
     *
     * @param sheet         表单
     * @param promptTitle   提示标题
     * @param promptContent 提示内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     */
    public void setXSSFPrompt(Sheet sheet, String promptTitle, String promptContent, int firstRow, int endRow,
                              int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createCustomConstraint("DD1");
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        dataValidation.createPromptBox(promptTitle, promptContent);
        dataValidation.setShowPromptBox(true);
        sheet.addValidationData(dataValidation);
    }

    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框.
     *
     * @param sheet    要设置的sheet.
     * @param textlist 下拉框显示的内容
     * @param firstRow 开始行
     * @param endRow   结束行
     * @param firstCol 开始列
     * @param endCol   结束列
     * @return 设置好的sheet.
     */
    public void setXSSFValidation(Sheet sheet, String[] textlist, int firstRow, int endRow, int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textlist);
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
    }

    /**
     * 解析导出值 0=男,1=女,2=未知
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @return 解析后值
     * @throws Exception
     */
    public static String convertByExp(String propertyValue, String converterExp) throws Exception {
        try {
            String[] convertSource = converterExp.split(",");
            for (String item : convertSource) {
                String[] itemArray = item.split("=");
                if (itemArray[0].equals(propertyValue)) {
                    return itemArray[1];
                }
            }
        } catch (Exception e) {
            throw e;
        }
        return propertyValue;
    }

    /**
     * 反向解析值 男=0,女=1,未知=2
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @return 解析后值
     * @throws Exception
     */
    public static String reverseByExp(String propertyValue, String converterExp) throws Exception {
        try {
            String[] convertSource = converterExp.split(",");
            for (String item : convertSource) {
                String[] itemArray = item.split("=");
                if (itemArray[1].equals(propertyValue)) {
                    return itemArray[0];
                }
            }
        } catch (Exception e) {
            throw e;
        }
        return propertyValue;
    }

    /**
     * 编码文件名+时间
     */
    public String encodingDateFilename(String filename) {
        filename = DateUtils.dateTimesNow() + "-" + filename;
        return filename;
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename) {
        String downloadPath = AppConfig.getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }

    /**
     * 获取bean中的属性值
     *
     * @param vo    实体对象
     * @param field 字段
     * @param excel 注解
     * @return 最终的属性值
     * @throws Exception
     */
    private Object getTargetValue(T vo, Field field, Excel excel) throws Exception {
        Object o = field.get(vo);
        if (!excel.dataValue().isEmpty()) {
            if (excel.dataValue().startsWith("#")) {
                // 如果dataValue以$开头，则调用实体类的方法来获取值
                String methodName = excel.dataValue().substring(1, excel.dataValue().length() - 1);
                try {
                    Method method = vo.getClass().getMethod(methodName);
                    o = method.invoke(vo);
                } catch (Exception e) {
                    log.error("调用方法失败: {}", e.getMessage());
                }
            } else {
                o = resolveDataValue(vo, excel.dataValue());
            }
        }
        if (StringUtils.isNotEmpty(excel.targetAttr())) {
            if (excel.self()) {
                return o;
            }
            String target = excel.targetAttr();
            if (excel.alias().length() > 0) {
                target = excel.alias();
                if (target.contains(",")) {
                    String[] targets = target.split(",");
                    for (String name : targets) {
                        Object temp = getValue(o, name);
                        if (null != temp) {
                            return temp;
                        }
                    }
                }
            }
            if (target.contains(".")) {
                String[] targets = target.split("[.]");
                for (String name : targets) {
                    o = getValue(o, name);
                }
            } else {
                o = getValue(o, target);
            }
        }
        return o;
    }

    /**
     * 以类的属性的get方法方法形式获取值
     *
     * @param o
     * @param name
     * @return value
     * @throws Exception
     */
    private Object getValue(Object o, String name) throws Exception {
        if (StringUtils.isNotEmpty(name)) {
            Class<?> clazz = o.getClass();
            if (o instanceof Map) {
                o = (Map) o;
                return ((Map<?, ?>) o).get(name);
            }
            String methodName = "get" + name.substring(0, 1).toUpperCase() + name.substring(1);
            try {
                Method method = clazz.getMethod(methodName);
                o = method.invoke(o);
            } catch (NoSuchMethodException e) {
                log.error("方法不存在" + methodName);
            }

        }
        return o;
    }

    /**
     * 得到所有定义字段
     */
    private void createExcelField() {
        this.fields = new ArrayList<Object[]>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        for (Field field : tempFields) {
            // 单注解
            if (field.isAnnotationPresent(Excel.class)) {
                putToField(field, field.getAnnotation(Excel.class));
            }

            // 多注解
            if (field.isAnnotationPresent(Excels.class)) {
                Excels attrs = field.getAnnotation(Excels.class);
                Excel[] excels = attrs.value();
                for (Excel excel : excels) {
                    putToField(field, excel);
                }
            }
        }
        //按照includeKeys排序
        if (null != includeKeys && !includeKeys.isEmpty()) {
            List<String> list1 = includeKeys.stream().collect(Collectors.toList());
            this.fields.sort((o1, o2) -> {
                Field field1 = (Field) o1[0];
                Excel attr1 = (Excel) o1[1];
                Field field2 = (Field) o2[0];
                Excel attr2 = (Excel) o2[1];
                String key1 = includeKeys.contains(field1.getName()) ? field1.getName() : attr1.targetAttr();
                String key2 = includeKeys.contains(field2.getName()) ? field2.getName() : attr2.targetAttr();
                return list1.indexOf(key1) - list1.indexOf(key2);
            });
        }
    }

    /**
     * 放到字段集合中
     */
    private void putToField(Field field, Excel attr) {
        if (null != includeKeys && !includeKeys.isEmpty()) {
            String key = attr.targetAttr();
            if (StringUtils.isEmpty(key)) {
                key = field.getName();
            }
            if (!includeKeys.contains(key)) {
                return;
            }
        }
        if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
            this.fields.add(new Object[]{field, attr});
        }
        this.fields.sort(new Comparator<Object[]>() {
            @Override
            public int compare(Object[] t0, Object[] t1) {
                Excel excel0 = (Excel) t0[1];
                Excel excel1 = (Excel) t1[1];
                return excel0.order() - excel1.order();
            }
        });
    }

    /**
     * 创建一个工作簿
     */
    public void createWorkbook() {
        this.wb = new SXSSFWorkbook(500);
    }

    /**
     * 创建工作表
     *
     * @param sheetNo sheet数量
     * @param index   序号
     */
    public void createSheet(double sheetNo, int index) {
        this.sheet = wb.createSheet();
        this.styles = createStyles(wb);
        // 设置工作表的名称.
        if (sheetNo == 0) {
            wb.setSheetName(index, sheetName);
        } else {
            wb.setSheetName(index, sheetName + index);
        }
    }

    /**
     * 获取单元格值
     *
     * @param row    获取的行
     * @param column 获取单元格列号
     * @return 单元格值
     */
    public Object getCellValue(Row row, int column) {
        if (row == null) {
            return row;
        }
        Object val = "";
        try {
            Cell cell = row.getCell(column);
            if (cell != null) {
                if (cell.getCellTypeEnum() == CellType.NUMERIC || cell.getCellTypeEnum() == CellType.FORMULA) {
                    val = cell.getNumericCellValue();
                    if (HSSFDateUtil.isCellDateFormatted(cell)) {
                        val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
                    } else {
                        if ((Double) val % 1 > 0) {
                            val = new DecimalFormat("0.00").format(val);
                        } else {
                            val = new DecimalFormat("0").format(val);
                        }
                    }
                } else if (cell.getCellTypeEnum() == CellType.STRING) {
                    val = cell.getStringCellValue();
                } else if (cell.getCellTypeEnum() == CellType.BOOLEAN) {
                    val = cell.getBooleanCellValue();
                } else if (cell.getCellTypeEnum() == CellType.ERROR) {
                    val = cell.getErrorCellValue();
                }

            }
        } catch (Exception e) {
            return val;
        }
        return val;
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     * chenLing
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public AjaxResult exportExcel(List<T> list, String sheetName, String title, boolean index) {
        this.init(list, sheetName, Type.EXPORT, others, includeKeys, index);
        return exportExcel(title);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     * chenLing
     * 第一行有总标题
     *
     * @return 结果
     */
    public AjaxResult exportExcel(String title) {
        ByteArrayOutputStream out = null;
        try {
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);

                // 产生一行
                CellRangeAddress region3 = new CellRangeAddress(0, 0, 0, fields.size() - 1);
                if (this.index) {
                    region3 = new CellRangeAddress(0, 0, 0, fields.size());
                }
                sheet.addMergedRegion(region3);
                Row row = sheet.createRow(startIndex);
                Cell cell = row.createCell(0);
                cell.setCellValue(title);
                cell.setCellStyle(styles.get("data"));

                row = sheet.createRow(startIndex + 1);
                int column = 0;
                // 写入各个字段的列头名称
                if (this.index) {
                    this.createIndexCell(row, column++);
                }
                for (Object[] os : fields) {
                    Excel excel = (Excel) os[1];
                    this.createCell(excel, row, column++);
                }
                if (Type.EXPORT.equals(type)) {
                    if (CollectionUtils.isEmpty(list)) {
                        fillExcelEmpty(index, row);
                    } else {
                        fillExcelData2(index, row);
                    }
                }
            }
            String filename = encodingDateFilename(sheetName);
            out = new ByteArrayOutputStream();
            wb.write(out);
            //将字节数组转换成输入流
            InputStream bio = new ByteArrayInputStream(out.toByteArray());
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(bio, "download", filename);
            return AjaxResult.success(filename + "," + uploadFile.getUrl());
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 填充excel数据
     * chenLing
     *
     * @param index 序号
     * @param row   单元格行
     */
    public void fillExcelData2(int index, Row row) {
        int startNo = index * sheetSize;//增加了一列title
        int endNo = Math.min(startNo + sheetSize, list.size());
        for (int i = startNo + 1; i <= endNo; i++) {
            row = sheet.createRow(i + startIndex + 1 - startNo);
            // 得到导出对象.
            T vo = (T) list.get(i - 1);
            int column = 0;
            if (this.index) {
                Cell cell = row.createCell(column);
                cell.setCellValue(i);
                cell.setCellStyle(styles.get("data"));
                column++;
            }
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                // 设置实体类私有属性可访问
                field.setAccessible(true);
                this.addCell(excel, row, vo, field, column++);
            }

        }
    }

    /**
     * 填充excel数据
     * chenLing
     *
     * @param index 序号
     * @param row   单元格行
     */
    public void fillExcelEmpty(int index, Row row) {
        int startNo = index * sheetSize;//增加了一列title
        row = sheet.createRow(startNo + 1 + startIndex + 1 - startNo);
        int column = 0;
        if (this.index) {
            Cell cell = row.createCell(column);
            cell.setCellValue("");
            cell.setCellStyle(styles.get("data"));
            column++;
        }
        for (Object[] os : fields) {
            Field field = (Field) os[0];
            Excel excel = (Excel) os[1];
            // 设置实体类私有属性可访问
            field.setAccessible(true);
            Cell cell = row.createCell(column);
            if (excel.wrapText()) {
                cell.setCellStyle(styles.get("wrap_data"));
            } else {
                cell.setCellStyle(styles.get("data"));
            }
            cell.setCellValue("");
            column++;
        }
    }

    private String resolveDataValue(T entity, String expression) {
        if (expression.contains("${") && expression.contains("}")) {
            String[] parts = expression.split("\\$\\{");
            StringBuilder sb = new StringBuilder();
            for (String part : parts) {
                if (part.contains("}")) {
                    String[] subParts = part.split("\\}");
                    if (subParts.length == 2) {
                        String fieldName = subParts[0];
                        String fieldValue = getFieldValue(entity, fieldName);
                        sb.append(fieldValue);
                        sb.append(subParts[1]);
                    } else {
                        sb.append(part);
                    }
                } else {
                    sb.append(part);
                }
            }
            return sb.toString();
        }
        return expression;
    }

    private String getFieldValue(T entity, String fieldName) {
        try {
            String[] fieldNames = fieldName.split("\\.");
            Object fieldValue = entity;
            for (String name : fieldNames) {
                Field field = fieldValue.getClass().getDeclaredField(name);
                field.setAccessible(true);
                fieldValue = field.get(fieldValue);
            }
            return String.valueOf(fieldValue);
        } catch (Exception e) {
            log.error("获取字段值失败: {}", e.getMessage());
            return "";
        }
    }

    public List<List<ExcelCellData>> getOthers() {
        return others;
    }

    public void setOthers(List<List<ExcelCellData>> others) {
        this.others = others;
    }


    public AjaxResult exportCustomExcel(List<SheetVO> vos, String fileName) {
        ByteArrayOutputStream out = null;
        try {
            createWorkbook();
            this.styles = createStyles(wb);
            for (int p = 0; p < vos.size(); p++) {
                SheetVO vo = vos.get(p);
                Sheet sheet = wb.createSheet(vo.getSheetName());
                sheet.getPrintSetup().setLandscape(false);
                sheet.getPrintSetup().setPaperSize((short) 9);
                sheet.setRepeatingColumns(new CellRangeAddress(1, 1, 0, 5));
                sheet.getFooter().setCenter("&C第 &P页，共 &N 页");
                sheet.setHorizontallyCenter(true);
                sheet.setMargin(HSSFSheet.RightMargin, (double) 0.6 / 3);
                sheet.setMargin(HSSFSheet.TopMargin, (double) 1.2 / 3);
                sheet.setMargin(HSSFSheet.LeftMargin, (double) 0.6 / 3);
                sheet.setMargin(HSSFSheet.BottomMargin, (double) 1.2 / 3);
                for (int i = 0; i < vo.getExcelCells().size(); i++) {
                    Row row = sheet.createRow(i);
                    List<ExcelCellData> excelCellDatas = vo.getExcelCells().get(i);
                    if (null == excelCellDatas || excelCellDatas.isEmpty()) {
                        continue;
                    }
                    int startColumn = 0;
                    for (int j = 0; j < excelCellDatas.size(); j++) {
                        short rowHeight = 600;
                        if (i == 0) {
                            rowHeight = 800;
                        }
                        ExcelCellData excelCellData = excelCellDatas.get(j);
                        if (j == 0) {
                            if (excelCellData.getHeight() > 0) {
                                row.setHeight(excelCellData.getHeight());
                            } else {
                                row.setHeight(rowHeight);
                            }
                        }
                        if (excelCellData.getWidth() > 0) {
                            sheet.setColumnWidth(startColumn, excelCellData.getWidth() * 256);
                        }
                        if (excelCellData.getColumn() > 1) {
                            for (int k = 0; k < excelCellData.getColumn(); k++) {
                                Cell cell = row.createCell(startColumn + k);
                                cell.setCellValue(excelCellData.getContent());
                                cell.setCellStyle(styles.get(excelCellData.getStyle()));
                            }
                            sheet.addMergedRegion(new CellRangeAddress(i, i, startColumn, startColumn + excelCellData.getColumn() - 1));
                            startColumn += excelCellData.getColumn();
                        } else {
                            Cell cell = row.createCell(startColumn);
                            cell.setCellValue(excelCellData.getContent());
                            cell.setCellStyle(styles.get(excelCellData.getStyle()));
                            startColumn++;
                        }
                    }
                }
            }
            String filename = encodingDateFilename(fileName);
            out = new ByteArrayOutputStream();
            wb.write(out);
            //将字节数组转换成输入流
            InputStream bio = new ByteArrayInputStream(out.toByteArray());
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(bio, "download", filename);
            return AjaxResult.success(filename + "," + uploadFile.getUrl());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出Excel异常{}", e);
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public AjaxResult exportFormatExcel(List<SheetVO> vos, String fileName) {
        ByteArrayOutputStream out = null;
        try {
            createWorkbook();
            this.styles = createStyles(wb);
            for (int p = 0; p < vos.size(); p++) {
                SheetVO vo = vos.get(p);
                Sheet sheet = wb.createSheet(vo.getSheetName());
                sheet.getPrintSetup().setLandscape(false);
                sheet.getPrintSetup().setPaperSize((short) 9);
                sheet.setRepeatingColumns(new CellRangeAddress(1, 1, 0, 5));
                sheet.getFooter().setCenter("&C第 &P页，共 &N 页");
                sheet.setHorizontallyCenter(true);
                sheet.setMargin(HSSFSheet.RightMargin, (double) 0.6 / 3);
                sheet.setMargin(HSSFSheet.TopMargin, (double) 1.2 / 3);
                sheet.setMargin(HSSFSheet.LeftMargin, (double) 0.6 / 3);
                sheet.setMargin(HSSFSheet.BottomMargin, (double) 1.2 / 3);
                for (int i = 0; i < vo.getExcelCells().size(); i++) {
                    Row row = sheet.createRow(i);
                    List<ExcelCellData> excelCellDatas = vo.getExcelCells().get(i);
                    if (null == excelCellDatas || excelCellDatas.isEmpty()) {
                        continue;
                    }
                    int startColumn = 0;
                    for (int j = 0; j < excelCellDatas.size(); j++) {
                        short rowHeight = 600;
                        if (i == 0) {
                            rowHeight = 800;
                        }
                        ExcelCellData excelCellData = excelCellDatas.get(j);
                        if (j == 0) {
                            if (excelCellData.getHeight() > 0) {
                                row.setHeight(excelCellData.getHeight());
                            } else {
                                row.setHeight(rowHeight);
                            }
                        }
                        if (excelCellData.getWidth() > 0) {
                            sheet.setColumnWidth(startColumn, excelCellData.getWidth() * 256);
                        }
                        if (excelCellData.getColumn() > 1) {
                            for (int k = 0; k < excelCellData.getColumn(); k++) {
                                Cell cell = row.createCell(startColumn + k);
                                cell.setCellValue(excelCellData.getContent());
                                cell.setCellStyle(styles.get(excelCellData.getStyle()));
                            }
                            sheet.addMergedRegion(new CellRangeAddress(i, i, startColumn, startColumn + excelCellData.getColumn() - 1));
                            startColumn += excelCellData.getColumn();
                        } else {
                            Cell cell = row.createCell(startColumn);
                            cell.setCellValue(excelCellData.getContent());
                            cell.setCellStyle(styles.get(excelCellData.getStyle()));
                            startColumn++;
                        }
                    }
                }
                //创建制表行
                int lastRowIndex = vo.getExcelCells().size();
                Row lastRow = sheet.createRow(lastRowIndex);
                lastRow.setHeight((short) 600);
                int column = sheet.getRow(sheet.getLastRowNum()-1).getLastCellNum();
                for (int k = 0; k < column; k++) {
                    Cell cell = lastRow.createCell(k);
                    cell.setCellValue("制表：重庆市凤筑科技有限公司");
                    cell.setCellStyle(styles.get("formatTabulation"));
                }
                sheet.addMergedRegion(new CellRangeAddress(lastRowIndex, lastRowIndex, 0, column - 1));
            }
            for (Sheet sheet : wb) {
                int lastColumn = sheet.getRow(sheet.getLastRowNum()-1).getLastCellNum(); // 获取当前工作表的最后一列索引
                int imageColumnIndex = lastColumn - 2; // 图片放在倒数第二列
                byte[] imageBytes = getImageBytes("templates/stamp2.png");
                int rowIndex = 0; // 确保rowIndex不超过工作表的实际行数
                Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
                ClientAnchor anchor = drawingPatriarch.createAnchor(
                        0, 0, 0, 0,
                        imageColumnIndex, rowIndex, // 图片左上角的单元格位置
                        imageColumnIndex + 2, rowIndex + 4); // 图片右下角的单元格位置
                // 创建图片对象
                int pictureIndex = wb.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
                drawingPatriarch.createPicture(anchor, pictureIndex);
            }
            String filename = encodingDateFilename(fileName);
            out = new ByteArrayOutputStream();
            wb.write(out);
            //将字节数组转换成输入流
            InputStream bio = new ByteArrayInputStream(out.toByteArray());
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(bio, "download", filename);
            return AjaxResult.success(filename + "," + uploadFile.getUrl());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出Excel异常{}", e);
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public static byte[] getImageBytes(String imagePath) throws IOException {
        InputStream stampStream = ExcelUtil.class.getClassLoader().getResourceAsStream(imagePath);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = stampStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }
        stampStream.close();
        return outputStream.toByteArray();
    }

    private static CellStyle setCellLogHeadStyle(Workbook wb) {
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.TOP);
        cellStyle.setAlignment(HorizontalAlignment.RIGHT);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        Font dataFont = wb.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 12);
        cellStyle.setFont(dataFont);
        return cellStyle;
    }

    private static CellStyle setCellHeadStyle(Workbook wb) {
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        Font dataFont = wb.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 14);
        dataFont.setBold(true);
        cellStyle.setFont(dataFont);
        return cellStyle;
    }

    private static CellStyle setCellTitleStyle(Workbook wb) {
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setWrapText(true);
        Font dataFont = wb.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        dataFont.setBold(true);
        cellStyle.setFont(dataFont);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        return cellStyle;
    }

    private static CellStyle setCellStyle(Workbook wb) {
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setWrapText(true);
        Font dataFont = wb.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        cellStyle.setFont(dataFont);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        return cellStyle;
    }

    private static CellStyle setCellLeftStyle(Workbook wb) {
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setWrapText(true);
        Font dataFont = wb.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        cellStyle.setFont(dataFont);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        return cellStyle;
    }

    private static CellStyle setCellTabulationStyle(Workbook wb) {
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.RIGHT);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        Font dataFont = wb.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        cellStyle.setFont(dataFont);
        return cellStyle;
    }

    /**
     * 将图片插入到Excel中
     * @param workbook 工作簿
     * @param sheet 工作表
     * @param imageColumn 图片列索引
     * @param imageUrls 图片URL列表
     * @throws IOException IO异常
     */
    public void insertImagesToSheet(XSSFWorkbook workbook, Sheet sheet, int imageColumn, List<String> imageUrls) throws IOException {
        CreationHelper helper = workbook.getCreationHelper();
        Drawing drawing = sheet.createDrawingPatriarch();

        for (int i = 0; i < imageUrls.size(); i++) {
            String imageUrl = imageUrls.get(i);
            if (imageUrl != null && !imageUrl.isEmpty()) {
                BufferedImage image = downloadImageFromUrl(imageUrl);
                if (image != null) {
                    byte[] imageBytes = imageToByteArray(image);
                    int pictureIndex = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);

                    // 设置图片的锚点（位置）
                    ClientAnchor anchor = helper.createClientAnchor();
                    anchor.setCol1(imageColumn);
                    anchor.setRow1(i + 1);  // 行号从1开始，所以i + 1

                    // 创建图片并插入
                    XSSFPicture picture = (XSSFPicture) drawing.createPicture(anchor, pictureIndex);
                    picture.resize();  // 图片自适应单元格大小
                }
            }
        }
    }


    /**
     * 从URL下载图片
     * @param urlString 图片URL
     * @return BufferedImage 图片对象
     */
    private BufferedImage downloadImageFromUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            InputStream inputStream = url.openStream();
            return ImageIO.read(inputStream);
        } catch (IOException e) {
            System.err.println("Error downloading image from URL: " + urlString);
            return null;
        }
    }

    /**
     * 将BufferedImage转换为字节数组
     * @param image BufferedImage图片对象
     * @return 字节数组
     * @throws IOException IO异常
     */
    private byte[] imageToByteArray(BufferedImage image) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", byteArrayOutputStream);  // 将图片写入字节流
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 导出Excel格式
     * @param vos 数据内容
     * @param filename 文件名
     * @return 导出结果
     */
    public AjaxResult exportFormatExcel2(List<SheetVO> vos, String fileName) {
//        XSSFWorkbook workbook = new XSSFWorkbook();
//        for (SheetVO sheetVO : vos) {
//            Sheet sheet = workbook.createSheet(sheetVO.getSheetName());
//            List<List<ExcelCellData>> excelData = sheetVO.getExcelCells();
//
//            for (int rowIndex = 0; rowIndex < excelData.size(); rowIndex++) {
//                Row row = sheet.createRow(rowIndex);
//                List<ExcelCellData> cellDataList = excelData.get(rowIndex);
//                for (int colIndex = 0; colIndex < cellDataList.size(); colIndex++) {
//                    Cell cell = row.createCell(colIndex);
//                    ExcelCellData cellData = cellDataList.get(colIndex);
//
//                    // 判断是否是标题
//                    if ("formatTitle".equals(cellData.getStyle())) {
//                        // 设置标题样式
//                        cell.setCellValue(cellData.getContent());
//                        // 在这里可以添加额外的样式处理，比如设置字体、背景色等
//                    } else {
//                        // 普通单元格
//                        cell.setCellValue(cellData.getContent());
//                    }
//                }
//            }
//        }
//
//        // 根据业务需求，插入图片
//        try {
//            // 假设图片URL已经在某个字段中，如signImg 和 examSign
//            insertImagesToSheet(workbook, workbook.getSheetAt(0), 7, vos.get(0).getImageUrls());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//        return saveWorkbook(workbook, filename);
        ByteArrayOutputStream out = null;
        try {
            createWorkbook();
            this.styles = createStyles(wb);
            for (int p = 0; p < vos.size(); p++) {
                SheetVO vo = vos.get(p);
                Sheet sheet = wb.createSheet(vo.getSheetName());
                sheet.getPrintSetup().setLandscape(false);
                sheet.getPrintSetup().setPaperSize((short) 9);
                sheet.setRepeatingColumns(new CellRangeAddress(1, 1, 0, 5));
                sheet.getFooter().setCenter("&C第 &P页，共 &N 页");
                sheet.setHorizontallyCenter(true);
                sheet.setMargin(HSSFSheet.RightMargin, (double) 0.6 / 3);
                sheet.setMargin(HSSFSheet.TopMargin, (double) 1.2 / 3);
                sheet.setMargin(HSSFSheet.LeftMargin, (double) 0.6 / 3);
                sheet.setMargin(HSSFSheet.BottomMargin, (double) 1.2 / 3);
                for (int i = 0; i < vo.getExcelCells().size(); i++) {
                    Row row = sheet.createRow(i);
                    List<ExcelCellData> excelCellDatas = vo.getExcelCells().get(i);
                    if (null == excelCellDatas || excelCellDatas.isEmpty()) {
                        continue;
                    }
                    int startColumn = 0;
                    for (int j = 0; j < excelCellDatas.size(); j++) {
                        short rowHeight = 600;
                        if (i == 0) {
                            rowHeight = 800;
                        }
                        ExcelCellData excelCellData = excelCellDatas.get(j);
                        if (j == 0) {
                            if (excelCellData.getHeight() > 0) {
                                row.setHeight(excelCellData.getHeight());
                            } else {
                                row.setHeight(rowHeight);
                            }
                        }
                        if (excelCellData.getWidth() > 0) {
                            sheet.setColumnWidth(startColumn, excelCellData.getWidth() * 256);
                        }
                        if (excelCellData.getColumn() > 1) {
                            for (int k = 0; k < excelCellData.getColumn(); k++) {
                                Cell cell = row.createCell(startColumn + k);
                                cell.setCellValue(excelCellData.getContent());
                                cell.setCellStyle(styles.get(excelCellData.getStyle()));
                            }
                            sheet.addMergedRegion(new CellRangeAddress(i, i, startColumn, startColumn + excelCellData.getColumn() - 1));
                            startColumn += excelCellData.getColumn();
                        } else {
                            Cell cell = row.createCell(startColumn);
                            cell.setCellValue(excelCellData.getContent());
                            cell.setCellStyle(styles.get(excelCellData.getStyle()));
                            startColumn++;
                        }
                    }
                }
            }
            String filename = encodingDateFilename(fileName);
            out = new ByteArrayOutputStream();
            wb.write(out);
            //将字节数组转换成输入流
            InputStream bio = new ByteArrayInputStream(out.toByteArray());
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(bio, "download", filename);
            return AjaxResult.success(filename + "," + uploadFile.getUrl());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出Excel异常{}", e);
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }


    /**
     * 保存工作簿到文件
     * @param workbook 工作簿
     * @param filename 文件名
     * @return AjaxResult
     */
    private AjaxResult saveWorkbook(XSSFWorkbook workbook, String filename) {
        try (FileOutputStream out = new FileOutputStream(new File(filename + ".xlsx"))) {
            workbook.write(out);
            return AjaxResult.success("导出成功", filename + ".xlsx");
        } catch (IOException e) {
            e.printStackTrace();
            return AjaxResult.error("导出失败");
        }
    }
}
