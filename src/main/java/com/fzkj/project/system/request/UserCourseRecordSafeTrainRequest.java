package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 安全培训课件分发学员学习记录
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "安全培训课件分发学员学习记录Request")
public class UserCourseRecordSafeTrainRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "课程ID")
    private Long lessonId;

    @ApiModelProperty(value = "课程分类")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课件ID")
    private Long courseId;

    @ApiModelProperty(value = "课件名称")
    private String courseName;

    @ApiModelProperty(value = "文件类型")
    private Integer fileType;

    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    @ApiModelProperty(value = "课件完成状态：（-3,未开始，0学习中，1已完成，2过期学习，默认-3）")
    private Integer courseIsComplete;

    @ApiModelProperty(value = "企业ID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "部门ID")
    private Long departId;

    @ApiModelProperty(value = "岗位ID")
    private Long workId;

    @ApiModelProperty(value = "用户编号")
    private String userCode;

    @ApiModelProperty(value = "是否统计")
    private Integer isStatic;

    @ApiModelProperty(value = "学习时长")
    private Long studyTimeCount;

    @ApiModelProperty(value = "课件时长")
    private Long timeCount;

    @ApiModelProperty(value = "人脸识别弹框的时间（562,2256）")
    private String faceRecognitionString;

    @ApiModelProperty(value = "人脸识别图片地址")
    private String faceDistinguishImg;

    @ApiModelProperty(value = "开始学习时间")
    private String startStudyTime;

    @ApiModelProperty(value = "完成学习时间")
    private String completeTime;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @ApiModelProperty(value = "分发来源 1：售后服务端分发的， 0：用户自己购买分发的")
    private Integer disSource;

    @ApiModelProperty(value = "参培课程（1：（通用课程） 2：（定制课程））")
    private Integer handMode;

    @ApiModelProperty(value = "预留字段1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    private String reservedField5;

}