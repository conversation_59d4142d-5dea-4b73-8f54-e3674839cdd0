package com.fzkj.project.system.vo;

import lombok.Data;

import java.util.List;

@Data
public class SafeTrainDataExportVO {
    private String passMark;
    private int studyNoComplete;
    private String studyNoCompleteName;
    private List<DepartTrainDataStaticsVO> departTrainDataList;
    private List<CompanyPlanTimeVO> trainDates;
    private List<CourseVO> courseList;
    private String totalTimeCount;
    private String trainStartTime;
    private String trainEndTime;
    private String trainCycle;
    private String lessonMonth;
    private String lessonCategoryName;
    private String lessonName;
    private String title;
    private String companyName;
    private String lessonDate;
}
