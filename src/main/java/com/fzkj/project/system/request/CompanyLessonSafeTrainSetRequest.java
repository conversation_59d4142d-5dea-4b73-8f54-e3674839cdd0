package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 企业安全培训课程设置表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "企业课程安全培训设置编辑Request")
public class CompanyLessonSafeTrainSetRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("企业ID")
    private Long companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("课程分类ID")
    private Long lessonCategoryId;

    @ApiModelProperty("课程分类名称")
    private String lessonCategoryName;

    @ApiModelProperty("参培人次")
    private Integer personNum;

    @ApiModelProperty("付费方式")
    private String isPayType;

    @ApiModelProperty("参培课程分发方式")
    private Integer handMode;

    @ApiModelProperty("是否托管")
    private Integer isTrusteeship;

    @ApiModelProperty("有效期开始时间")
    private String sTime;

    @ApiModelProperty("有效期结束时间")
    private String eTime;

    @ApiModelProperty("员工支付价格")
    private BigDecimal staffPrice;

    @ApiModelProperty("课程总时长")
    private Integer totalTimeCount;

    @ApiModelProperty("操作标识")
    private String flag;
}
