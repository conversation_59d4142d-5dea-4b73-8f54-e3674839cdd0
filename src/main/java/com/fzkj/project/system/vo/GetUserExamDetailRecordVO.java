package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetUserExamDetailRecordVO {
    @ApiModelProperty(value = "ID", example = "45965261")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "记录ID", example = "3190670")
    @JsonProperty("RecordID")
    private Long recordId;

    @ApiModelProperty(value = "问题ID", example = "292975")
    @JsonProperty("QuestionID")
    private Long questionId;

    @ApiModelProperty(value = "子选项", example = "A|B|C")
    @JsonProperty("SubOption")
    private String subOption;

    @ApiModelProperty(value = "正确选项", example = "A|B|C")
    @JsonProperty("RightOption")
    private String rightOption;

    @ApiModelProperty(value = "分数", example = "40")
    @JsonProperty("Score")
    private int score;

    @ApiModelProperty(value = "序列号", example = "1")
    @JsonProperty("SerialNumber")
    private int serialNumber;

    @ApiModelProperty(value = "主题类型", example = "2")
    @JsonProperty("SubjectType")
    private Integer subjectType;

    @ApiModelProperty(value = "主题名称", example = "落实三年专项整治要求，要注意三个重点：一是安全生产基本面改善，二是企业安全生产标准化，三要围绕“两重大一突出”实施专项整治。其中“两重大一突出”具体指的是（  ）。")
    @JsonProperty("SubjectName")
    private String subjectName;

    @ApiModelProperty(value = "问题类型ID", example = "2")
    @JsonProperty("QuestionTypeId")
    private Long questionTypeId;

    @ApiModelProperty(value = "问题选项", example = "[{\\\"answer\\\":\\\"A\\\",\\\"title\\\":\\\"重大风险\\\",\\\"imgurl\\\":\\\"\\\"},{\\\"answer\\\":\\\"B\\\",\\\"title\\\":\\\"重大隐患\\\",\\\"imgurl\\\":\\\"\\\"},{\\\"answer\\\":\\\"C\\\",\\\"title\\\":\\\"突出违法行为\\\",\\\"imgurl\\\":\\\"\\\"},{\\\"answer\\\":\\\"D\\\",\\\"title\\\":\\\"重大事故\\\",\\\"imgurl\\\":\\\"\\\"}]")
    @JsonProperty("QuestionOption")
    private String questionOption;

    @ApiModelProperty(value = "问题类型名称", example = "多选题")
    @JsonProperty("QuestionTypeName")
    private String questionTypeName;

    @ApiModelProperty(value = "图片URL", example = "null")
    @JsonProperty("Img")
    private String img;

    @ApiModelProperty(value = "问题分数", example = "40")
    @JsonProperty("QuestionScore")
    private int questionScore;

    @ApiModelProperty(value = "问题分析", example = "")
    @JsonProperty("ProblemAnalysis")
    private String problemAnalysis;

}
