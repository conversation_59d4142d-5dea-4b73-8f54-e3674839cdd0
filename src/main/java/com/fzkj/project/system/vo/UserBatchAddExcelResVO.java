package com.fzkj.project.system.vo;

import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户批量添加excel返回实体VO
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户批量添加excel返回实体VO")
public class UserBatchAddExcelResVO implements Serializable {

    @Excel(name = "编号")
    private String order;

    @Excel(name = "员工姓名")
    private String userName;

    @Excel(name = "身份证号")
    private String idCard;

    @Excel(name = "手机号码")
    private String phone;

    @Excel(name = "入职时间", dateFormat = "yyyy-MM-dd")
    private Date joinTime;

    @Excel(name = "家庭住址")
    private String homeAddress;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "错误")
    private String error;
}