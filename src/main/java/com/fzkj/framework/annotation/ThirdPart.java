package com.fzkj.framework.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记当前controller或controller方法是否是调用系统第三方接口，如果是，则返回code时使用rtncode,否则用code
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ThirdPart {

    /**
     * 第三方接口名称
     * @return
     */
    boolean value() default true;
}