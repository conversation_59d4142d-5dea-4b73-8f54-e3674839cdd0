package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.project.system.entity.LogSendEmailInfo;
import com.fzkj.project.system.mapper.LogSendEmailInfoMapper;
import com.fzkj.project.system.service.LogSendEmailInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 邮件推送日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class LogSendEmailInfoServiceImpl extends ServiceImpl<LogSendEmailInfoMapper, LogSendEmailInfo> implements LogSendEmailInfoService {
}
