package com.fzkj.framework.security.service;

import com.fzkj.project.system.entity.UserInfo;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@Component
public class SysPermissionService
{


    /**
     * 获取菜单数据权限
     *
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(UserInfo user)
    {
        Set<String> roles = new HashSet<String>();
/*        // 管理员拥有所有权限
        if (user.isAdmin())
        {
            roles.add("*:*:*");
        }
        else
        {
            roles.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
        }*/
        return roles;
    }
}
