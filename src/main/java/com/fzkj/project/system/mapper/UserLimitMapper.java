package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.UserLimit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.vo.AuthTargetVO;
import com.fzkj.project.system.vo.UserLimitVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 用户管理机构表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface UserLimitMapper extends BaseMapper<UserLimit> {

    List<UserLimitVO> selectUserLimitListByUserCode(String userCode);

    List<UserLimitVO> selectUserLimitListByCompanyId(Long companyId);

    List<AuthTargetVO> selectLoginAuthTargetVO(String userCode,String targetId);

    Integer selectUserLimitCountByUserCode(String userCode,Long departId);
}