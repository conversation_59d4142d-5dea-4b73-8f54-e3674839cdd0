// 设置具体参数实现微信接口
package com.fzkj.project.wxPay.utils;

import com.fzkj.project.wxPay.config.WxPayConfig;
import com.github.wxpay.sdk.WXPayConfig;
import com.qiniu.util.IOUtils;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.PrivateKey;

public class WXConfigUtil implements WXPayConfig {
    private byte[] certData;
    public static final String APP_ID = WxPayConfig.getAppid();
    public static final String KEY = WxPayConfig.getMchkey();
    public static final String MCH_ID = WxPayConfig.getMchid();

//    public WXConfigUtil() throws Exception {
//        String certPath = ClassUtils.getDefaultClassLoader().getResource("").getPath()+"/weixin/apiclient_cert.p12";//从微信商户平台下载的安全证书存放的路径
//        File file = new File(certPath);
//        InputStream certStream = new FileInputStream(file);
//        this.certData = new byte[(int) file.length()];
//        certStream.read(this.certData);
//        certStream.close();
//    }

    public WXConfigUtil() throws Exception {
        ClassPathResource classPathResource = new ClassPathResource("weixin/apiclient_cert.p12");
        InputStream certStream = classPathResource.getInputStream();
        this.certData = IOUtils.toByteArray(certStream);
        certStream.read(this.certData);
        certStream.close();
    }

    @Override
    public String getAppID() {
        return WxPayConfig.getAppid();
    }

    //parnerid，商户号
    @Override
    public String getMchID() {
        return  WxPayConfig.getMchid();
    }

    @Override
    public String getKey() {
        return WxPayConfig.getMchkey();
    }

    @Override
    public InputStream getCertStream() {
        ByteArrayInputStream certBis = new ByteArrayInputStream(this.certData);
        return certBis;
    }

    @Override
    public int getHttpConnectTimeoutMs() {
        return 8000;
    }

    @Override
    public int getHttpReadTimeoutMs() {
        return 10000;
    }
}

