package com.fzkj.project.basicdata.controller;

import com.fzkj.common.constant.Constants;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.basicdata.entity.SubStanceLabel;
import com.fzkj.project.basicdata.entity.SubStanceType;
import com.fzkj.project.basicdata.service.SubStanceTypeService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-14 14:26
 * @Version: 1.0
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
//@RequestMapping("/basicdata")
public class SubStanceTypeController {

    private final SubStanceTypeService subStanceTypeService;

    @PostMapping("Get_SubStanceType")
    @ApiOperation(value = "内容分类分页查询")
    public AjaxResult listTrainTypeByPage(@RequestBody CommonRequest commonRequest) {
        SubStanceType request = (SubStanceType) commonRequest.getRequest(SubStanceType.class);
        return AjaxResult.success(subStanceTypeService.listByPage(request, commonRequest.getPageEntity()));
    }

    /**
     * 新增保存常见问题
     */
    @PostMapping("/Edit_SubStanceType")
    @ApiOperation(value = "新增内容分类")
    @ResponseBody
    public AjaxResult addSave(@RequestBody CommonRequest commonRequest) {
        SubStanceType request = (SubStanceType) commonRequest.getRequest(SubStanceType.class);
        switch (request.getFlag()) {
            case Constants.ADD_DATA:
                return AjaxResult.success(subStanceTypeService.insertSubStanceType(request));
            case Constants.EDIT_DATA:
                return AjaxResult.success(subStanceTypeService.updateSubStanceType(request));
            case Constants.DEL_DATA:
                return AjaxResult.success(subStanceTypeService.deleteSubStanceType(request.getId()));
            default:
                return AjaxResult.error("操作异常！");
        }
    }
    /**
     * 导出分类
     */
    @PostMapping("/Export_SubStanceType")
    @ApiOperation(value = "导出分类")
    @ResponseBody
    public AjaxResult exportSubStanceType(@RequestBody CommonRequest commonRequest) {
        SubStanceType request = (SubStanceType) commonRequest.getRequest(SubStanceType.class);
        return AjaxResult.success(subStanceTypeService.exportSubStanceType(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_SubStanceType_APP")
    @ApiOperation(value = "内容分类分页查询(小程序)")
    public AjaxResult getSubStanceTypeAPP(@RequestBody CommonRequest commonRequest) {
        SubStanceType request = (SubStanceType) commonRequest.getRequest(SubStanceType.class);
        return AjaxResult.success(subStanceTypeService.getSubStanceTypeAPP(request, commonRequest.getPageEntity()));
    }
}
