<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.Company">
        <id column="id" property="id"/>
        <result column="fid" property="fid"/>
        <result column="parent_id" property="parentId"/>
        <result column="company_name" property="companyName"/>
        <result column="area_code" property="areaCode"/>
        <result column="area_name" property="areaName"/>
        <result column="abbr_name" property="abbrName"/>
        <result column="address" property="address"/>
        <result column="email" property="email"/>
        <result column="company_label" property="companyLabel"/>
        <result column="oper_platform" property="operPlatform"/>
        <result column="source" property="source"/>
        <result column="sort" property="sort"/>
        <result column="creator_code" property="creatorCode"/>
        <result column="creator_time" property="creatorTime"/>
        <result column="revise_code" property="reviseCode"/>
        <result column="revise_time" property="reviseTime"/>
        <result column="remark" property="remark"/>
        <result column="is_valid" property="isValid"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fid, parent_id, company_name, area_code, area_name, abbr_name, address, email, company_label, oper_platform, source, sort, creator_code, creator_time, revise_code, revise_time, remark, is_valid
    </sql>
    <select id="getSubDeptIds" resultType="java.lang.Long">
        SELECT id
        FROM t_d_company where find_in_set(#{id},ancestors) or id = #{id}
    </select>

    <select id="getCompanyList" resultType="com.fzkj.project.system.vo.CompanyVO">
        SELECT
        dc.*
        FROM
        t_d_company dc
        LEFT JOIN t_d_user_company uc ON dc.id = uc.depart_id AND uc.is_valid = 1
        LEFT JOIN t_d_company_work cw ON dc.id = cw.depart_id AND cw.is_valid = 1
        <where>
            <if test="companyName != null and companyName !=''">AND dc.company_name LIKE
                concat('%',#{companyName},'%')
            </if>
            <if test="isValid != null">AND dc.is_valid = #{isValid}</if>
            <if test="targetId != null">AND dc.id in (
                SELECT
                id
                FROM
                t_d_company
                WHERE
                FIND_IN_SET ( #{targetId}, ancestors ) UNION
                SELECT
                #{targetId} AS id
                )
            </if>
        </where>
        GROUP BY
        dc.id
    </select>

    <select id="getCompanyParentList" resultType="com.fzkj.project.system.vo.CompanyVO">
        SELECT
        dc.*
        FROM
        t_d_company dc
        LEFT JOIN t_d_user_company uc ON dc.id = uc.depart_id AND uc.is_valid = 1
        LEFT JOIN t_d_company_work cw ON dc.id = cw.depart_id AND cw.is_valid = 1
        WHERE
        dc.id IN
        <foreach collection="parentIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY
        dc.id
        ORDER BY
        dc.sort
    </select>

    <select id="getCompanyListById" resultType="com.fzkj.project.system.vo.CompanyVO">
        SELECT
        dc.*,
        (
        SELECT
        count(*)
        FROM
        t_d_user_company
        WHERE
        depart_id IN ( SELECT id FROM t_d_company WHERE FIND_IN_SET ( dc.id, ancestors ) UNION ALL SELECT dc.id )
        ) userNumber,
        (
        SELECT
        count(*)
        FROM
        t_d_company_work
        WHERE
        depart_id IN ( SELECT id FROM t_d_company WHERE FIND_IN_SET ( dc.id, ancestors ) UNION ALL SELECT dc.id )
        ) workNumber,
        dct.company_name upCompanyName
        FROM
        t_d_company dc
        LEFT JOIN t_d_company dct ON dc.parent_id = dct.id
        WHERE
        1 = 1
        <if test="id != null">AND dc.id = #{id}</if>
        GROUP BY
        dc.id
    </select>
    <select id="selectChildDepartNoRecursive" resultType="com.fzkj.project.system.vo.CompanyVO">
        select c.*,
               (case
                    when (select count(1) from t_d_company where is_valid = 1 and parent_id = c.id) > 0 then 1
                    else 0 end) isNext
        from t_d_company c
        where c.is_valid = 1
          and parent_id = #{orgId}
    </select>
    <select id="selectUserNumByDeptId" resultType="java.lang.Integer">
        select count(*)
        from t_d_user_company
        where depart_id in (
            SELECT id
            FROM t_d_company
            WHERE FIND_IN_SET(#{companyId}, ancestors)
            UNION ALL
            SELECT #{companyId})
    </select>
    <select id="selectWorkNumByDeptId" resultType="java.lang.Integer">
        select count(*)
        from t_d_company_work
        where depart_id in (
            SELECT id
            FROM t_d_company
            WHERE FIND_IN_SET(#{companyId}, ancestors)
            UNION ALL
            SELECT #{companyId})
    </select>

</mapper>