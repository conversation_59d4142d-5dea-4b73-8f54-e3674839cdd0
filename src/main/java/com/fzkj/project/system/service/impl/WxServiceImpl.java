package com.fzkj.project.system.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fzkj.common.constant.Constants;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.sign.Base64;
import com.fzkj.framework.config.WechatConfig;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.project.system.entity.SmallProgramCode;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.entity.WxSendMsgTemplate;
import com.fzkj.project.system.mapper.SmallProgramCodeMapper;
import com.fzkj.project.system.mapper.UserInfoMapper;
import com.fzkj.project.system.service.WxSendMsgTemplateService;
import com.fzkj.project.system.service.WxService;
import com.fzkj.project.system.vo.FansVO;
import com.fzkj.project.system.vo.WechatAppSubscribeResponse;
import com.fzkj.project.system.vo.WxLoginVO;
import com.fzkj.project.system.vo.WxMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.AlgorithmParameters;
import java.security.Security;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@RequiredArgsConstructor
@Slf4j
public class WxServiceImpl implements WxService {
    private final RestTemplate restTemplate;
    private final RedisCache redisCache;
    private final WechatConfig wechatConfig;
    private final UserInfoMapper userInfoMapper;
    private final SmallProgramCodeMapper smallProgramCodeMapper;
    private final WxSendMsgTemplateService wxSendMsgTemplateService;

    @Override
    public WxLoginVO wxLogin(String code) {
        //1.通过前端给的code获取openid和access_token还有unionid
        String getTokenOpenid = wechatConfig.getSessionGetUrl().concat(code);
        String data = HttpUtil.get(getTokenOpenid);
        WxLoginVO result = JSONObject.parseObject(data, WxLoginVO.class);
        return result;
    }

    @Override
    public JSONObject getUserInfo(String encryptedData, String code, String iv) {
        WxLoginVO wxLoginVO = wxLogin(code);
        try {
            // 被加密的数据
            byte[] dataByte = Base64.decode(encryptedData);
            // 加密秘钥
            byte[] keyByte = Base64.decode(wxLoginVO.getSession_key());
            // 偏移量
            byte[] ivByte = Base64.decode(iv);
            // 如果密钥不足16位，那么就补足
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            // 初始化
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, "UTF-8");
                return (JSONObject) JSON.parse(result);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public byte[] generateMiniProgramQRCode(HashMap request) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("page", null != request.get("url") ? request.get("url") : request.get("Url"));
        requestBody.put("width", 224);
        requestBody.put("auto_color", false);
        //存放至数据库
        String sceneId = UUID.randomUUID().toString().replaceAll("-", "");
        if (request.containsKey("permanent")) {//长期有效
            SmallProgramCode code = new SmallProgramCode();
            code.setParams(JSON.toJSONString(request));
            smallProgramCodeMapper.insert(code);
            sceneId = String.valueOf(code.getId());
        }
        requestBody.put("scene", sceneId);
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<byte[]> response = restTemplate.exchange(wechatConfig.getWxacodeunlimitGetUrl().concat(getMiniAccessToken()), HttpMethod.POST, entity, byte[].class);
        if (!request.containsKey("permanent")) {//永久有效
            redisCache.setCacheObject(sceneId, request, 1, TimeUnit.DAYS);
        }
        return response.getBody();
    }


    @Override
    public Object getParamsBySceneId(String sceneId) {
        try {
            Long aLong = Long.valueOf(sceneId);
            SmallProgramCode smallProgramCode = smallProgramCodeMapper.selectById(aLong);
            return JSON.parseObject(smallProgramCode.getParams(), HashMap.class);
        } catch (Exception e) {
            return redisCache.getCacheObject(sceneId);
        }
    }

    @Override
    public boolean sendWxMessage(WxMessage wxMessage) {
        WxSendMsgTemplate template = wxSendMsgTemplateService.getTemplateByCode(wxMessage.getCode());
        if (null == template || StringUtils.isEmpty(template.getTemplateId())) {
            log.error("模板不存在");
            return false;
        }
        String data = wxMessage.getData();
        data = data.replace("小程序Appid", wechatConfig.getAppId());
        data += "|" + wechatConfig.getAppId();
        String templateContent = template.getTemplateContent();
        String[] split = data.split("\\|");
        //判断openId是否是公众号OpenId
        LambdaQueryWrapper<UserInfo> userInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userInfoLambdaQueryWrapper.eq(UserInfo::getOpenId, split[0]);
        UserInfo userInfo = userInfoMapper.selectOne(userInfoLambdaQueryWrapper);
        if (null != userInfo) {
            split[0] = userInfo.getPublicOpenId();
            if (StringUtils.isEmpty(userInfo.getPublicOpenId())) {
                split[0] = getPublicOpenIdByUnionId(userInfo.getUnionId());
            }
        }
        for (int i = 0; i < split.length; i++) {
            String oldStr = "{" + i + "}";
            templateContent = templateContent.replace(oldStr, split[i]);
        }
        return sendPublicTemplateMessage(getPublicAccessToken(), templateContent);
    }

    @Override
    public String getPublicOpenIdByUnionId(String unionId) {
        //查询当前公众号下所有关注的用户
        String openIdUnionIdMapStr = redisCache.getCacheObject("openIdMap");
        Map<String, String> openIdUnionIdMap = null;
        if (null != openIdUnionIdMapStr) {
            openIdUnionIdMap = JSON.parseObject(openIdUnionIdMapStr, Map.class);
        }
        if (null == openIdUnionIdMap) {
            openIdUnionIdMap = new HashMap<>();
            //从数据库查询已经存在的匹配数据
            LambdaQueryWrapper<UserInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(UserInfo::getUnionId, UserInfo::getPublicOpenId);
            lambdaQueryWrapper.isNotNull(UserInfo::getPublicOpenId);
            lambdaQueryWrapper.isNotNull(UserInfo::getUnionId);
            List<UserInfo> users = userInfoMapper.selectList(lambdaQueryWrapper);
            //转成map
            for (UserInfo user : users) {
                openIdUnionIdMap.put(user.getUnionId(), user.getPublicOpenId());
            }
            redisCache.setCacheObject("openIdMap", JSON.toJSONString(openIdUnionIdMap));
        }

        String retrunOpenId = openIdUnionIdMap.get(unionId);
        if (null != retrunOpenId) {
            syncUser(unionId, retrunOpenId);
            return retrunOpenId;
        }
        List<String> allOpenIds = new ArrayList<>();
        Boolean lock = redisCache.getLock("syncOpenId", "true", 600);
        if (lock) {
            try {

                int total = 0;
                int count = 0;
                String next_openid = null;
                while (allOpenIds.size() != total || count == 0) {
                    String getFansList = wechatConfig.getFansiGetUrl().concat(getPublicAccessToken());
                    if (StringUtils.isNotEmpty(next_openid)) {
                        getFansList.concat("next_openid=").concat(next_openid);
                    }
                    String data = HttpUtil.get(getFansList);
                    count++;
                    FansVO result = JSONObject.parseObject(data, FansVO.class);
                    total = result.getTotal();
                    next_openid = result.getNext_openid();
                    if (total > 0 && result.getCount() > 0) {
                        for (String openId : result.getData().getOpenid()) {
                            allOpenIds.add(openId);
                        }
                    }
                }

                if (allOpenIds.size() > 0) {
                    Map<String, String> finalOpenIdUnionIdMap = openIdUnionIdMap;
                    List<String> openIds = allOpenIds.stream().filter(openId -> !finalOpenIdUnionIdMap.containsValue(openId)).collect(Collectors.toList());
                    List<CompletableFuture<String>> futures = openIds.stream()
                            .map(openId -> CompletableFuture.supplyAsync(() -> {
                                String getUnionId = wechatConfig.getOpenidGetUrl() + getPublicAccessToken() + "&openid=" + openId;
                                String unionIdData = HttpUtil.get(getUnionId);
                                JSONObject jsonObject = JSONObject.parseObject(unionIdData);
                                return jsonObject.getString("unionid");
                            }))
                            .collect(Collectors.toList());

                    List<String> unionIds = futures.stream()
                            .map(future -> {
                                try {
                                    return future.get();
                                } catch (InterruptedException | ExecutionException e) {
                                    throw new RuntimeException(e);
                                }
                            })
                            .filter(Objects::nonNull) // 过滤掉 null 结果
                            .collect(Collectors.toList());

                    openIdUnionIdMap = IntStream.range(0, openIds.size())
                            .boxed()
                            .collect(Collectors.toMap(
                                    i -> unionIds.get(i),
                                    i -> openIds.get(i),
                                    (u1, u2) -> u1,
                                    HashMap::new));
                    openIdUnionIdMap.putAll(finalOpenIdUnionIdMap);
                    redisCache.setCacheObject("openIdMap", JSON.toJSONString(openIdUnionIdMap));
                    if (openIdUnionIdMap.containsKey(unionId)) {
                        //同步更新到数据库
                        retrunOpenId = openIdUnionIdMap.get(unionId);
                        syncUser(unionId, retrunOpenId);
                        return retrunOpenId;
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }finally {
                redisCache.releaseLock("syncOpenId", "true");
            }
        }

        return null;
    }

    /**
     * 获取小程序accessToken
     *
     * @return
     */
    private String getMiniAccessToken() {
        return getAccessToken(wechatConfig.getAccessTokenUrl(), Constants.ACCESS_TOKEN_KEY);
    }

    /**
     * 获取公众号accessToken
     *
     * @return
     */
    private String getPublicAccessToken() {
        return getAccessToken(wechatConfig.getPublicAccessTokenUrl(), Constants.PUBLIC_ACCESS_TOKEN_KEY);
    }

    /**
     * 获取accessToken
     *
     * @return
     */
    private String getAccessToken(String tokenUrl, String tokenKey) {
        String accessToken = redisCache.getCacheObject(tokenKey);
        if (StringUtils.isNotEmpty(accessToken)) {
            return accessToken;
        }

        int maxRetries = 3;
        int retries = 0;

        while (retries < maxRetries) {
            try {
                RestTemplate restTemplate = new RestTemplate();
                HttpHeaders headers = new HttpHeaders();
                headers.set("Content-Type", "application/json");
                HttpEntity<String> entity = new HttpEntity<>(headers);
                ResponseEntity<String> response = restTemplate.exchange(tokenUrl, HttpMethod.GET, entity, String.class);
                String responseBody = response.getBody();
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                accessToken = jsonObject.getString("access_token");
                String errCode = jsonObject.getString("errcode");
                if (StringUtils.isNotEmpty(accessToken)) {
                    redisCache.setCacheObject(tokenKey, accessToken, 600, TimeUnit.SECONDS);
                    return accessToken;
                }
                if (!"-1".equals(errCode)) {
                    break;
                }
            } catch (Exception e) {
                throw new CustomException("获取access_token失败");
            }
            retries++;
            try {
                Thread.sleep(3000L);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new CustomException("获取access_token重试等待时发生异常");
            }
        }

        if (retries == maxRetries) {
            throw new CustomException("获取access_token失败");
        }
        return accessToken;
    }


    /**
     * 发送公众号消息
     */
    private boolean sendPublicTemplateMessage(String accessToken, String templateContent) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> requestEntity = new HttpEntity<>(templateContent, headers);
        String result = restTemplate.postForObject(wechatConfig.getPublicSubscribeSendUrl().concat(accessToken), requestEntity, String.class);
        WechatAppSubscribeResponse wechatAppSubscribeResponse =
                JSONObject.parseObject(result, WechatAppSubscribeResponse.class);
        if (!"0".equals(wechatAppSubscribeResponse.getErrcode())) {
            log.error("微信模板发送错误:" + result);
            return false;
        }
        return true;
    }

    private void syncUser(String unionId, String openId) {
        LambdaUpdateWrapper<UserInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(UserInfo::getUnionId, unionId);
        lambdaUpdateWrapper.set(UserInfo::getPublicOpenId, openId);
        userInfoMapper.update(null, lambdaUpdateWrapper);
    }
}
