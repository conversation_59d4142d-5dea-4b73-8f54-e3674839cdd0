package com.fzkj.framework.web.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONField;
import com.alibaba.fastjson2.filter.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.annotation.ListNoArray;
import com.fzkj.framework.annotation.ListNoRows;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.web.page.ResponseData;
import com.fzkj.framework.web.page.SpecialResponseData;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 操作消息提醒
 *
 * <AUTHOR>
 */
public class AjaxResult extends HashMap<String, Object> {
    private static final long serialVersionUID = 1L;
    public static ContextNameFilter filter = (BeanContext context, Object object, String name, Object value) -> {
        if (context.getField().getAnnotations().length > 0) {
            JsonProperty jsonProperty = context.getAnnotation(JsonProperty.class);
            if (null != jsonProperty) {
                return jsonProperty.value();
            }
            JSONField jsonField = context.getAnnotation(JSONField.class);
            if (null != jsonField) {
                return jsonField.name();
            }
            com.alibaba.fastjson.annotation.JSONField jsonField1 = context.getAnnotation(com.alibaba.fastjson.annotation.JSONField.class);
            if (null != jsonField1) {
                return jsonField1.name();
            }
        }
        return name;
    };
    public static ContextValueFilter filter1 = (BeanContext context, Object object, String name, Object value) -> {
        //判断是否为Long类型
        if (context.getField().getType().equals(Long.class) && null == value) {
            return -1L;
        }
        return value;
    };
    /**
     * 外部状态码
     */
    public static final String RTNCODE = "rtncode";
    /**
     * 状态码
     */
    public static final String CODE_TAG = "code";

    /**
     * 返回内容
     */
    public static final String MSG_TAG = "Message";

    /**
     * 分页数据
     */
    public static final String ROWS_TAG = "rows";

    /**
     * 分页数据总计
     */
    public static final String TOTAL_TAG = "total";

    /**
     * 数据对象
     */
    public static final String DATA_TAG = "ResultJson";

    /**
     * 初始化一个新创建的 AjaxResult 对象，使其表示一个空消息。
     */
    public AjaxResult() {
    }

    public static AjaxResult success() {
        return new AjaxResult(null, null, null, true);
    }

    public AjaxResult(Integer code, String msg, Object data, boolean success) {
        code = null == code ? success ? 1000 : 1001 : code;
        msg = null == msg ? success ? "请求成功" : "请求失败" : msg;
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        HandlerMethod handlerMethod = (HandlerMethod) request.getAttribute("org.springframework.web.servlet.HandlerMapping.bestMatchingHandler");
        ThirdPart thirdPart = null;
        if (null != handlerMethod) {
            thirdPart = handlerMethod.getMethod().getAnnotation(ThirdPart.class);
            if (thirdPart == null) {
                thirdPart = handlerMethod.getBeanType().getAnnotation(ThirdPart.class);
            }
        }
        if (null != thirdPart && thirdPart.value()) {
            put(CODE_TAG, success);
            put(RTNCODE, code);
        } else {
            put(CODE_TAG, code);
        }
        put(MSG_TAG, msg);
        if (null == data) {
            put(DATA_TAG, "");
        } else if (data instanceof String) {
            put(DATA_TAG, data);
        } else if (data instanceof PageUtils) {
            PageUtils page = (PageUtils) data;
            ResponseData responseData = new ResponseData();
            List list = page.getList();
            if (!CollectionUtils.isEmpty(list)) {
                List temp = new ArrayList();
                int startIndex = (page.getCurrPage() - 1) * page.getPageSize();
                for (int i = 0; i < list.size(); i++) {
                    startIndex++;
                    temp.add(new RowData(startIndex, list.get(i)));
                }
                responseData.setRows(temp);
            } else {
                responseData.setRows(list);
            }
            responseData.setTotal(page.getTotalCount());
            //调用JSON.toJSONString的时候data中字段当成RowData中的字段序列号
            put(DATA_TAG, JSON.toJSONString(responseData, new Filter[]{filter, filter1}, JSONWriter.Feature.FieldBased, JSONWriter.Feature.WriteNulls));
        } else if (data instanceof List) {
            ListNoRows listNoRows = null;
            if (null != handlerMethod) {
                listNoRows = handlerMethod.getMethod().getAnnotation(ListNoRows.class);
                if (listNoRows == null) {
                    listNoRows = handlerMethod.getBeanType().getAnnotation(ListNoRows.class);
                }
            }
            ListNoArray listNoArray = null;
            if (null != handlerMethod) {
                listNoArray = handlerMethod.getMethod().getAnnotation(ListNoArray.class);
                if (listNoArray == null) {
                    listNoArray = handlerMethod.getBeanType().getAnnotation(ListNoArray.class);
                }
            }
            if (null != listNoRows && listNoRows.value()) {
                put(DATA_TAG, JSON.toJSONString(data, new Filter[]{filter, filter1}, JSONWriter.Feature.FieldBased, JSONWriter.Feature.WriteNulls));
            } else if (null != listNoArray && listNoArray.value()) {
                List list = (List) data;
                SpecialResponseData responseData = new SpecialResponseData();
                if (!CollectionUtils.isEmpty(list)) {
                    responseData.setRows(list.get(0));
                }
                responseData.setTotal(list.size());
                put(DATA_TAG, JSON.toJSONString(responseData, new Filter[]{filter, filter1}, JSONWriter.Feature.FieldBased, JSONWriter.Feature.WriteNulls));
            } else {
                List list = (List) data;
                ResponseData responseData = new ResponseData();
                if (!CollectionUtils.isEmpty(list)) {
                    List temp = new ArrayList();
                    int startIndex = 0;
                    for (int i = 0; i < list.size(); i++) {
                        startIndex++;
                        temp.add(new RowData(startIndex, list.get(i)));
                    }
                    responseData.setRows(temp);
                } else {
                    responseData.setRows(list);
                }
                responseData.setTotal(list.size());
                put(DATA_TAG, JSON.toJSONString(responseData, new Filter[]{filter, filter1}, JSONWriter.Feature.FieldBased, JSONWriter.Feature.WriteNulls));
            }
        } else {
            put(DATA_TAG, JSON.toJSONString(data, new Filter[]{filter, filter1}, JSONWriter.Feature.FieldBased, JSONWriter.Feature.WriteNulls));
        }
    }

    public static AjaxResult success(Object data) {
        //判断data是否是布尔类型，如果是则转为布尔类型Boolean 或者 boolean
        if (data instanceof Boolean) {
            Boolean data1 = (Boolean) data;
            if (data1) {
                return success();
            } else {
                return error();
            }
        }
        return new AjaxResult(null, null, data, true);
    }

    public static AjaxResult success(int code, String message) {
        return new AjaxResult(code, message, null, true);
    }

    public static AjaxResult success(String message, Object data) {
        return new AjaxResult(null, message, data, true);
    }

    public static AjaxResult success(int code, String message, Object data) {
        return new AjaxResult(code, message, data, true);
    }

    public static AjaxResult error() {
        return AjaxResult.error(null);
    }

    public static AjaxResult error(Object data) {
        if (data instanceof String) {
            return new AjaxResult(null, data.toString(), null, false);
        }
        return new AjaxResult(null, null, data, false);
    }

    public static AjaxResult error(int code, String message) {
        return new AjaxResult(code, message, null, false);
    }

    public static AjaxResult error(String message, Object data) {
        return new AjaxResult(null, message, data, false);
    }

    public static AjaxResult error(int code, String message, Object data) {
        return new AjaxResult(code, message, data, false);
    }

}
