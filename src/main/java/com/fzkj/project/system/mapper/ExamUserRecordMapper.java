package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.ExamUserRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.entity.lessonSafeTrainTd;
import com.fzkj.project.system.request.GetUserExamDetailRecordRequest;
import com.fzkj.project.system.request.GetUserExamInfoRecordRequest;
import com.fzkj.project.system.request.SafeTrainCompanyDetialRequest;
import com.fzkj.project.system.vo.GetUserExamDetailRecordVO;
import com.fzkj.project.system.vo.GetUserExamInfoRecordVO;
import com.fzkj.project.system.vo.SafeTrainCompanyDetialUserExamRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户考试记录表（正式） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Mapper
public interface ExamUserRecordMapper extends BaseMapper<ExamUserRecord> {

    List<SafeTrainCompanyDetialUserExamRecordVO> selectSafeTrainCompanyDetialUserExamRecord(SafeTrainCompanyDetialRequest request);

    List<GetUserExamInfoRecordVO> selectUserExamInfoRecord1(GetUserExamInfoRecordRequest request);

    List<GetUserExamInfoRecordVO> selectUserExamInfoRecord2(GetUserExamInfoRecordRequest request);

    List<GetUserExamDetailRecordVO> selectUserExamDetailRecord1(GetUserExamDetailRecordRequest request);

    List<GetUserExamDetailRecordVO> selectUserExamDetailRecord2(GetUserExamDetailRecordRequest request);

    List<lessonSafeTrainTd> selectSignUrlById(@Param("lessonId")Long lessonId);
}