package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.Company;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.vo.CompanyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 企业表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface CompanyMapper extends BaseMapper<Company> {

    List<Long> getSubDeptIds(Long deptId);

    List<CompanyVO> getCompanyList(@Param(value = "companyName") String companyName,
                                   @Param(value = "isValid") Integer isValid,
                                   @Param(value = "targetId") Long targetId);

    List<CompanyVO> getCompanyParentList(@Param(value = "parentIds") List<String> parentIds);

    CompanyVO getCompanyListById(@Param(value = "id") Long id);

    List<CompanyVO> selectChildDepartNoRecursive(Long orgId);

    Integer selectUserNumByDeptId(Long companyId);

    Integer selectWorkNumByDeptId(Long companyId);
}