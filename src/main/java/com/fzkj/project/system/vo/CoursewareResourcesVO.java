package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 视频文件资源信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="视频文件资源信息表VO")
public class CoursewareResourcesVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("FileMd5")
    private String fileMd5;

    @ApiModelProperty(value = "文件ID")
    @NotNull(message = "文件ID不能为空")
    @JsonProperty("FileID")
    private String fileId;

    @ApiModelProperty(value = "文件名称")
    @NotNull(message = "文件名称不能为空")
    @JsonProperty("FileName")
    private String fileName;

    @ApiModelProperty(value = "文件地址")
    @NotNull(message = "文件地址不能为空")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "文件转码地址")
    @NotNull(message = "文件转码地址不能为空")
    @JsonProperty("FileUrlHls")
    private String fileUrlHls;

    @ApiModelProperty(value = "文件时长")
    @NotNull(message = "文件时长不能为空")
    @JsonProperty("FileTime")
    private Integer fileTime;

    @ApiModelProperty(value = "文件大小")
    @NotNull(message = "文件大小不能为空")
    @JsonProperty("FileSize")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型")
    @NotNull(message = "文件类型不能为空")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(value = "转码状态 0：正常")
    @NotNull(message = "转码状态 0：正常不能为空")
    @JsonProperty("EncodeState")
    private Integer encodeState;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    @NotNull(message = "创建人编码不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建人")
    @NotNull(message = "创建人不能为空")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    @NotNull(message = "修改人编码不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改人")
    @NotNull(message = "修改人不能为空")
    @JsonProperty("ReviseName")
    private String reviseName;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    @NotNull(message = "是否有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "来源")
    @NotNull(message = "来源不能为空")
    @JsonProperty("Source")
    private String source;

}