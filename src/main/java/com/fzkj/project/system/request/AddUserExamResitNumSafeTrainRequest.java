package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AddUserExamResitNumSafeTrainRequest {

    @ApiModelProperty(value = "用户IDs")
    @JsonProperty("UserIDs")
    private String userIds;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "培训计划ID")
    @JsonProperty("PlanID")
    private Long planId;

    @ApiModelProperty(value = "补考次数")
    @JsonProperty("ExamNum")
    private int examNum;

}
