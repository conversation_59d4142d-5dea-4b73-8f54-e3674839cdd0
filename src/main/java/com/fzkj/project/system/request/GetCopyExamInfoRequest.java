package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetCopyExamInfoRequest {

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "新课程ID")
    @JsonProperty("NewLessonID")
    private Long newLessonId;

    @ApiModelProperty(value = "用户代码")
    @JsonProperty("UserCode")
    private String userCode;

}
