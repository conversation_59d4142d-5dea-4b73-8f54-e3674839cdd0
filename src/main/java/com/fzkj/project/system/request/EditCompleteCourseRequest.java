package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EditCompleteCourseRequest {
    @JsonProperty("UserCode")
    private String userCode;

    @JsonProperty("CourseID")
    private Long courseId;

    @JsonProperty("LessonID")
    private Long lessonId;

    @JsonProperty("NowStudyTimeCount")
    private Integer nowStudyTimeCount;

    @JsonProperty("IsExam")
    private Integer isExam;

    @JsonProperty("IsSign")
    private String isSign; // 根据上下文，可能需要转换为Integer类型

    @JsonProperty("TrainType")
    private String trainType;

    @JsonProperty("PlanID")
    private Long planId;

    @JsonProperty("IsComplete")
    private Integer isComplete;

    @ApiModelProperty(value = "编码")
    @JsonProperty("courseCode")
    private String courseCode;
}
