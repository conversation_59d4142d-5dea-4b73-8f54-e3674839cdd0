package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户登录信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户登录信息表VO")
public class UserInfoVO implements Serializable {
    @JsonProperty("CompanyID")
    private Integer companyId;

    @JsonProperty("UserCode")
    private String userCode;

    @JsonProperty("UserName")
    @Excel(name = "用户姓名")
    private String userName;

    @JsonProperty("IDCard")
    @Excel(name = "身份证号")
    private String idCard;

    @JsonProperty("JobNo")
    @Excel(name = "工号")
    private String jobNo;

    @JsonProperty("Phone")
    @Excel(name = "手机号")
    private String phone;

    @JsonProperty("CompanyName")
    @Excel(name = "所属公司")
    private String companyName;

    @JsonProperty("DepartName")
    @Excel(name = "部门")
    private String departName;

    @JsonProperty("WorkName")
    @Excel(name = "岗位")
    private String workName;

    @JsonProperty("IsOut")
    @Excel(name = "岗位情况", readConverterExp = "0=离职,1=在职")
    private Integer isOut;

    @JsonProperty("IsBind")
    @Excel(name = "微信绑定", readConverterExp = "0=未绑定,1=已绑定")
    private Integer isBind;

    @JsonProperty("LoginDate")
    @Excel(name = "上次登录", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date loginDate;

    @JsonProperty("CreatorTime")
    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date creatorTime;

    @JsonProperty("Identity")
    private Integer identity;

    @JsonProperty("IsAuth")
    private Integer isAuth;

    @JsonProperty("IsEnable")
    private Integer isEnable;

    @JsonProperty("IsActivate")
    private Integer isActivate;

    @JsonProperty("Token")
    private String token;

    @JsonProperty("CompanyAutoSign")
    private String companyAutoSign;

    @JsonProperty("IndustryType")
    private String industryType;

    @JsonProperty("AreaName")
    private String areaName;

    @JsonProperty("AreaCode")
    private String areaCode;

    @JsonProperty("WorkRoleID")
    private String workRoleId;

    @JsonProperty("ValidPwdStatus")
    private Integer validPwdStatus;

    @JsonProperty("OpenID")
    private String openId;

    @JsonProperty("PublicOpenID")
    private String publicopenId;

    @JsonProperty("UserPhoto")
    private String userPhoto;

    @JsonProperty("WorkID")
    private Long workId;

    @JsonProperty("UnionId")
    private String unionId;

    @JsonProperty("Flag")
    private String flag;

    @JsonProperty("KeyWord")
    private String keyWord;

    @JsonProperty("OperContent")
    private String operContent;

    @JsonProperty("DepartID")
    private Long departId;

    @JsonProperty("DeptName")
    private String deptName;

    @JsonProperty("JoinTime")
    private String joinTime;

    @JsonProperty("CompanyLabel")
    private String companyLabel;

    @JsonProperty("PhotoUrl")
    private String photoUrl;

    @JsonProperty("OldPwd")
    private String oldPwd;

    @JsonProperty("NewPwd")
    private String newPwd;

    @JsonProperty("PlateNumber")
    private String plateNumber;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("HomeAddress")
    private String homeAddress;

    @JsonProperty("SignUrl")
    private String signUrl;

    @JsonProperty("TrainTypeIDStr")
    private String trainTypeIDStr;

    @JsonProperty("StatisticalGroup")
    private Integer statisticalGroup;
}