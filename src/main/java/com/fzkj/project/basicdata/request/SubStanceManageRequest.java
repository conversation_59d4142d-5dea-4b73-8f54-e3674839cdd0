package com.fzkj.project.basicdata.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 有效内容对象 t_d_sub_stance_manage
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Data
@ApiModel(value = "内容标签实体")
public class SubStanceManageRequest {
    private static final long serialVersionUID = 1L;

    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(name = "内容分类id")
    @JsonProperty("SubStanceTypeID")
    private Long subStanceTypeId;

    @ApiModelProperty(name = "内容分类")
    @JsonProperty("SubStanceTypeName")
    private String subStanceTypeName;


    @ApiModelProperty(name = "内容标签ids")
    @JsonProperty("SubStanceLabelIDs")
    private String subStanceLabelIds;


    @ApiModelProperty(name = "内容标签查询")
    @JsonProperty("SubStanceLabelQuery")
    private String subStanceLabelQuery;

    @ApiModelProperty(name = "内容标签")
    @JsonProperty("SubStanceLabelNames")
    private String subStanceLabelNames;

    @ApiModelProperty(name = "内容")
    @JsonProperty("SubStanceTitle")
    private String subStanceTitle;

    @ApiModelProperty(name = "")
    @JsonProperty("Summary")
    private String summary;

    @ApiModelProperty(name = "图片")
    @JsonProperty("Pictures")
    private String pictures;

    @ApiModelProperty(name = "文件类型")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(name = "文件url")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty(name = "链接地址")
    @JsonProperty("LinkUrl")
    private String linkUrl;

    @ApiModelProperty(name = "来源")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(name = "释放时间")
    @JsonProperty("ReleaseTime")
    private String releaseTime;

    @ApiModelProperty(name = "是否登录")
    @JsonProperty("IsLogin")
    private Integer isLogin;

    @ApiModelProperty(name = "阅读人数")
    @JsonProperty("ReadNum")
    private Integer readNum;

    @ApiModelProperty(name = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(name = "新建人code")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(name = "新建时间")
    @JsonProperty("CreationTime")
    private String creationTime;

    @ApiModelProperty(name = "修改人code")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(name = "修改时间")
    @JsonProperty("ReviseTime")
    private String reviseTime;

    @ApiModelProperty(name = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(name = "文件id")
    @JsonProperty("FileID")
    private String fileId;

    @ApiModelProperty(name = "创建人")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(name = "修改人")
    @JsonProperty("ReviseName")
    private String reviseName;

    @ApiModelProperty(name = "")
    @JsonProperty("Author")
    private String author;

    @ApiModelProperty(name = "地区id")
    @JsonProperty("OrganizationID")
    private Long organizationId;

    @ApiModelProperty(name = "所属平台")
    @JsonProperty("PutinPlatform")
    private Integer putinPlatform;

    @ApiModelProperty(name = "所属平台名称")
    @JsonProperty("PutinPlatformStr")
    private String putinPlatformStr;

    @ApiModelProperty(name = "阅读系数")
    @JsonProperty("ReadCoefficient")
    private BigDecimal readCoefficient;

    @ApiModelProperty(name = "")
    @JsonProperty("IsOriginal")
    private Integer isOriginal;

    @ApiModelProperty(name = "")
    @JsonProperty("Flag")
    private String flag;

    @ApiModelProperty(name = "")
    @JsonProperty("Duration")
    private String duration;

    @ApiModelProperty(name = "")
    @JsonProperty("TypeID")
    private Long typeID;

    @ApiModelProperty(name = "")
    @JsonProperty("Keywords")
    private String keywords;

    @ApiModelProperty(name = "")
    @JsonProperty("SubStanceID")
    private Long subStanceID;
}
