package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业证照
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_company_licence")
@ApiModel(value = "企业证照实体")
public class CompanyLicence implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("CompanyID")
    @ApiModelProperty(value = "企业id")
    private Long companyId;

    @JsonProperty("LicenceType")
    @ApiModelProperty(value = "证照类型")
    private String licenceType;

    @JsonProperty("LicenceUrl")
    @ApiModelProperty(value = "证照图片地址")
    private String licenceUrl;

    @JsonProperty("FileType")
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @JsonProperty("IsLongTerm")
    @ApiModelProperty(value = "是否长期有效")
    private Integer isLongTerm;

    @JsonProperty("ExpirTime")
    @ApiModelProperty(value = "证照到期时间")
    private LocalDate expirTime;

    @JsonProperty("Source")
    @ApiModelProperty(value = "来源")
    private String source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @JsonProperty("LicenceName")
    @ApiModelProperty(value = "证照名称")
    private String licenceName;
}