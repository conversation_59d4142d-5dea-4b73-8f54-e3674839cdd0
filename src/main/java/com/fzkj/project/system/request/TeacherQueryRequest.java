package com.fzkj.project.system.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 讲师
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "讲师查询Request")
public class TeacherQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "关键字")
    private String keyWord;


    @ApiModelProperty(value = "是否删除")
    private String recycleBin;

    @ApiModelProperty("来源")
    private String source;

    @ApiModelProperty("操作标识")
    private String flag;

    @ApiModelProperty("操作标识")
    private Integer isShow;
}