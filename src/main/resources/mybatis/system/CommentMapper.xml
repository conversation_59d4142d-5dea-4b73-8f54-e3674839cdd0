<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CommentMapper">
    
    <select id="getCommentSubStanceList" resultType="com.fzkj.project.system.vo.CommentVO">
        SELECT
            dc.content,
            dc.user_name,
            dc.creation_time,
            dc.id_card,
            dc.id,
            cr.user_name replyName,
            cr.reply_content,
            cr.creation_time replyTime,
            dc.is_reply,
        <if test="funType != null and funType == '11'">
            ssm.sub_stance_title contentTitle
        </if>
        <if test="funType != null and funType == '10'">
            ssm.course_name contentTitle
        </if>
        FROM
            t_d_comment dc
        <if test="funType != null and funType == '11'">
            LEFT JOIN t_d_sub_stance_manage ssm ON dc.target_id = ssm.id
        </if>
        <if test="funType != null and funType == '10'">
            LEFT JOIN t_d_course ssm ON dc.target_id = ssm.id
        </if>
            LEFT JOIN t_d_comment_reply cr ON dc.id = cr.target_id AND cr.type = 1
        WHERE dc.grade &lt;= 0
        <if test="status != null"> AND dc.status = #{status}</if>
        <if test="isValid != null"> AND dc.is_valid = #{isValid}</if>
        <if test="funType != null"> AND dc.fun_type = #{funType}</if>
        <if test="targetId != null"> AND dc.target_id = #{targetId}</if>
        <if test="userName != null and userName != ''"> AND (dc.user_name LIKE concat('%',#{userName},'%') OR dc.phone LIKE concat('%',#{userName},'%') OR dc.id_card LIKE concat('%',#{userName},'%'))</if>
        <if test="keywords != null and keywords != ''"> AND dc.content LIKE concat('%',#{keywords},'%')</if>
        <if test="isReply != null"> AND dc.is_reply = #{isReply}</if>
        <if test="teacherId != null and funType != null and funType == '10'"> AND ssm.tearcher_code = #{teacherId}</if>
        ORDER BY
            dc.creation_time DESC
    </select>


    <select id="getMyCommentSubStanceList" resultType="com.fzkj.project.system.vo.GetMyCommentVO">
        SELECT
            dc.id,
            dc.user_name,
            dc.grade,
            dc.user_photo,
            dc.content,
            dc.creation_time,
            dc.fun_type,
            dc.is_reply,
            cr.reply_content,
            ssm11.sub_stance_title contentTitle11,
            ssm10.course_name contentTitle10,
            dc.target_id
        FROM
            t_d_comment dc
                LEFT JOIN t_d_sub_stance_manage ssm11 ON dc.target_id = ssm11.id
                LEFT JOIN t_d_course ssm10 ON dc.target_id = ssm10.id
                LEFT JOIN t_d_comment_reply cr ON dc.id = cr.target_id
                AND cr.type = 1
        WHERE
            dc.is_valid = 1
          AND dc.user_code = #{userCode}
    </select>

    <select id="getCommentAppList" resultType="com.fzkj.project.system.vo.GetMyCommentVO">
        SELECT
            dc.id,
            dc.user_name,
            dc.grade,
            dc.user_photo,
            dc.content,
            dc.creation_time,
            dc.fun_type,
            dc.is_reply,
            cr.reply_content
        FROM
            t_d_comment dc
            LEFT JOIN t_d_comment_reply cr ON dc.id = cr.target_id
            AND cr.type = 1
        WHERE
            dc.is_valid = 1
          AND dc.target_id = #{targetId}
            AND dc.status = 1
        ORDER BY
            dc.creation_time DESC
    </select>

</mapper>