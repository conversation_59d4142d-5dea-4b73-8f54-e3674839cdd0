package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业下的用户安全培训课程用户模板
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_lesson_safe_train_company_user_template")
@ApiModel(value = "企业下的用户安全培训课程用户模板实体")
public class LessonSafeTrainCompanyUserTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "企业安全培训课程设置表T_D_CompanyLessonSafeTrainSet")
    @JsonProperty("CompanyLessonPayID")
    private Long companyLessonPayId;

    @ApiModelProperty(value = "课程支付类型：1：余额支付；2：包干（学员免费）；3:员工支付（学员付费）")
    @JsonProperty("IsPayType")
    private Integer isPayType;

    @ApiModelProperty(value = "课程支付类型名称：1：余额支付；2：包干；3:员工支付")
    @JsonProperty("PayTypeName")
    private String payTypeName;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "用户编码")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "用户名称")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "课程父级分类ID")
    @JsonProperty("LessonCategoryFID")
    private Long lessonCategoryFid;

    @ApiModelProperty(value = "课程分类")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课程分类名称")
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty(value = "参培课程（1：（通用课程）自动分发 2：（定制课程）手动分发）")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "是否托管：1：开启0：不开启")
    @JsonProperty("IsTrusteeship")
    private Integer isTrusteeship;

    @ApiModelProperty(value = "是否免费：1：免费 0：不免费")
    @JsonProperty("IsFree")
    private Integer isFree;

    @ApiModelProperty(value = "统计分组：1：管理人员 0：普通学员")
    @JsonProperty("StatisticalGroup")
    private Integer statisticalGroup;

    @ApiModelProperty(value = "来源")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否")
    @JsonProperty("IsValid")
    private Integer isValid;
}