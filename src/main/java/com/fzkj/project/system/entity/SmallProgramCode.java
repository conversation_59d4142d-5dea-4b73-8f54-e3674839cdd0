package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 小程序二维码表
 *
 */
@Data
@TableName("t_d_small_program_code")
@ApiModel(value = "小程序二维码实体")
public class SmallProgramCode implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @JsonProperty("Params")
    @ApiModelProperty(value = "参数")
    private String params;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private String isValid;
}