package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.LessonCourseSafeTrain;
import com.fzkj.project.system.vo.CourseVO;
import com.fzkj.project.system.vo.LessonCourseSafeTrainVO;
import com.fzkj.project.system.request.LessonCourseSafeTrainRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 安全培训课程课件关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface LessonCourseSafeTrainService extends IService<LessonCourseSafeTrain> {

    List<LessonCourseSafeTrain> listLessonCourseSafeTrain(LessonCourseSafeTrainRequest request);

    PageUtils listByPage(LessonCourseSafeTrainRequest request, PageEntity pageEntity);

    LessonCourseSafeTrainVO findLessonCourseSafeTrain(Long id);

    Long saveLessonCourseSafeTrain(LessonCourseSafeTrainVO vo);

    boolean updateLessonCourseSafeTrain(LessonCourseSafeTrainVO vo);

    boolean removeLessonCourseSafeTrain(Long id);

    List<CourseVO> getCourseListByLessonId(Long lessonId);

    boolean editLessonCourseSafeTrain(LessonCourseSafeTrainVO request);
}
