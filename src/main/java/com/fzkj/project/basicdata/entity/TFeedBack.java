package com.fzkj.project.basicdata.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 常见问题对象 t_feed_back
 * 
 * <AUTHOR>
 * @date 2024-03-12
 */
@TableName("t_d_feed_back")
@ApiModel(value = "讲师实体")
public class TFeedBack implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    /** 用户名 */
    @ApiModelProperty(name = "用户名")
    @JsonProperty("UserName")
    private String userName;

    /** 用户code */
    @ApiModelProperty(name = "用户code")
    @JsonProperty("UserCode")
    private String userCode;

    /** 1:问题反馈，2：意见箱，3：常见问题 */
    @ApiModelProperty(name = "1:问题反馈，2：意见箱，3：常见问题")
    @JsonProperty("BackType")
    private Integer backType;

    /** 问题内容 */
    @ApiModelProperty(name = "问题内容")
    @JsonProperty("Content")
    private String content;

    /** 是否回复（0：否，1：是） */
    @ApiModelProperty(name = "是否回复 0=：否，1：是")
    @JsonProperty("IsReply")
    private Integer isReply;

    /** 回复时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("ReplyTime")
    @ApiModelProperty(name = "回复时间")
    private Date replyTime;

    /** 回复内容 */
    @ApiModelProperty(name = "回复内容")
    @JsonProperty("ReplyContent")
    private String replyContent;

    /** 新建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("CreationTime")
    @ApiModelProperty(name = "新建时间")
    private Date creationTime;

    /** 是否有效（0：无效，1：有效） */
    @ApiModelProperty(name = "是否有效0：无效，1：有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    /** 排序 */
    @ApiModelProperty(name = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getUserName()
    {
        return userName;
    }
    public void setUserCode(String userCode)
    {
        this.userCode = userCode;
    }

    public String getUserCode()
    {
        return userCode;
    }
    public void setBackType(Integer backType)
    {
        this.backType = backType;
    }

    public Integer getBackType()
    {
        return backType;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setIsReply(Integer isReply)
    {
        this.isReply = isReply;
    }

    public Integer getIsReply()
    {
        return isReply;
    }
    public void setReplyTime(Date replyTime)
    {
        this.replyTime = replyTime;
    }

    public Date getReplyTime()
    {
        return replyTime;
    }
    public void setReplyContent(String replyContent)
    {
        this.replyContent = replyContent;
    }

    public String getReplyContent()
    {
        return replyContent;
    }
    public void setCreationTime(Date creationTime)
    {
        this.creationTime = creationTime;
    }

    public Date getCreationTime()
    {
        return creationTime;
    }
    public void setIsValid(Integer isValid)
    {
        this.isValid = isValid;
    }

    public Integer getIsValid()
    {
        return isValid;
    }
    public void setSort(Integer sort)
    {
        this.sort = sort;
    }

    public Integer getSort()
    {
        return sort;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userName", getUserName())
            .append("userCode", getUserCode())
            .append("backType", getBackType())
            .append("content", getContent())
            .append("isReply", getIsReply())
            .append("replyTime", getReplyTime())
            .append("replyContent", getReplyContent())
            .append("creationTime", getCreationTime())
            .append("isValid", getIsValid())
            .append("sort", getSort())
            .toString();
    }
}
