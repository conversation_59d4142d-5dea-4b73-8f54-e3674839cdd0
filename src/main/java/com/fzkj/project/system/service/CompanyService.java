package com.fzkj.project.system.service;

import com.fzkj.project.system.domain.TreeSelect;
import com.fzkj.project.system.entity.Company;
import com.fzkj.project.system.vo.CompanyVO;
import com.fzkj.project.system.request.CompanyRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 企业表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CompanyService extends IService<Company> {

    List<Company> listCompany(CompanyRequest request);

    List<Company> getSubDeps(CompanyRequest request);

    List<CompanyVO> getCompanyTree(CompanyRequest request);

    List<TreeSelect> getCompany(CompanyRequest request);

    String getCompanyDetail(CompanyRequest request);

    PageUtils listByPage(CompanyRequest request, PageEntity pageEntity);

    CompanyVO findCompany(Long id);

    Integer updateCompanyVo(CompanyVO vo);

    List<Long> getSubDeptIds(Long deptId);

    Integer updateCompany(CompanyVO vo);

    boolean removeCompany(Long id);

    List<CompanyVO> selectChildDepartNoRecursive(Long orgId);
}
