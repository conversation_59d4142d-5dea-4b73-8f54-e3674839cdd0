package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class UserSafeTrainFilesDetailVO {
    @JsonProperty("lessonrecord")
    private LessonRecordVO lessonRecord; // 课程记录
    @JsonProperty("courserecord")
    private List<CourseRecordVO> courseRecord = new ArrayList<>(); // 课件记录

}
