package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.CompanyExtend;
import com.fzkj.project.system.vo.CompanyExtendVO;
import com.fzkj.project.system.request.CompanyExtendRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.CompanyExtendMapper;
import com.fzkj.project.system.service.CompanyExtendService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 企业扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CompanyExtendServiceImpl extends ServiceImpl<CompanyExtendMapper, CompanyExtend> implements CompanyExtendService {

    @Override
    public List<CompanyExtend> listCompanyExtend(CompanyExtendRequest request) {
        CompanyExtend entity = (CompanyExtend) DataTransfer.transfer(request, CompanyExtend.class);
        LambdaQueryWrapper<CompanyExtend> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(CompanyExtendRequest request, PageEntity pageEntity) {
        CompanyExtend entity = (CompanyExtend) DataTransfer.transfer(request, CompanyExtend.class);
        LambdaQueryWrapper<CompanyExtend> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<CompanyExtend> page = this.page(
                new Query<CompanyExtend>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public CompanyExtendVO findCompanyExtend(Long id) {
        CompanyExtend entity = this.getById(id);
        CompanyExtendVO vo = (CompanyExtendVO) DataTransfer.transfer(entity, CompanyExtendVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCompanyExtend(CompanyExtendVO vo) {
        CompanyExtend entity = (CompanyExtend) DataTransfer.transfer(vo, CompanyExtend.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCompanyExtend(CompanyExtendVO vo) {
        CompanyExtend entity = (CompanyExtend) DataTransfer.transfer(vo, CompanyExtend.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCompanyExtend(Long id) {
        return this.removeById(id);
    }
}