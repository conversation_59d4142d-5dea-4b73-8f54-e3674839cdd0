package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.ExamAnswerRecord;
import com.fzkj.project.system.vo.ExamAnswerRecordVO;
import com.fzkj.project.system.request.ExamAnswerRecordRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户答题记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExamAnswerRecordService extends IService<ExamAnswerRecord> {

    List<ExamAnswerRecord> listExamAnswerRecord(ExamAnswerRecordRequest request);

    PageUtils listByPage(ExamAnswerRecordRequest request, PageEntity pageEntity);

    ExamAnswerRecordVO findExamAnswerRecord(Long id);

    Long saveExamAnswerRecord(ExamAnswerRecordVO vo);

    boolean updateExamAnswerRecord(ExamAnswerRecordVO vo);

    boolean removeExamAnswerRecord(Long id);
}
