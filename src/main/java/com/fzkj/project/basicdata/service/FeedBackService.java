package com.fzkj.project.basicdata.service;

import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.basicdata.entity.TFeedBack;
import com.fzkj.project.basicdata.request.TFeedBackRequest;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2024-03-12 9:37
 * @Version: 1.0
 */
public interface FeedBackService {
    /**
     * 查询常见问题
     *
     * @param id 常见问题主键
     * @return 常见问题
     */
    public TFeedBack selectTFeedBackById(Long id);

    /**
     * 查询常见问题列表
     *
     * @param tFeedBack 常见问题
     * @return 常见问题集合
     */
    public PageUtils selectTFeedBackList(TFeedBackRequest tFeedBack, PageEntity pageEntity);

    /**
     * 新增常见问题
     *
     * @param tFeedBack 常见问题
     * @return 结果
     */
    public Long insertTFeedBack(TFeedBackRequest tFeedBack);

    /**
     * 修改常见问题
     *
     * @param tFeedBack 常见问题
     * @return 结果
     */
    public Boolean updateTFeedBack(TFeedBackRequest tFeedBack);

    /**
     * 删除常见问题信息
     *
     * @param id 常见问题主键
     * @return 结果
     */
    public Boolean deleteTFeedBackById(Long id);

    PageUtils getFeedBackAPP(TFeedBackRequest tFeedBack, PageEntity pageEntity);
}
