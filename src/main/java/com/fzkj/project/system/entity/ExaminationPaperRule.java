package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 随机组卷规则表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@TableName("t_d_examination_paper_rule")
@ApiModel(value = "随机组卷规则表实体")
public class ExaminationPaperRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "考试ID")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "试题类型ID")
    @JsonProperty("QuestionTypeId")
    private Integer questionTypeId;

    @ApiModelProperty(value = "试题类型名称")
    @JsonProperty("QuestionTypeName")
    private String questionTypeName;

    @ApiModelProperty(value = "题目数")
    @JsonProperty("Num")
    private Integer num;

    @ApiModelProperty(value = "分数")
    @JsonProperty("Sorce")
    private Integer sorce;


}