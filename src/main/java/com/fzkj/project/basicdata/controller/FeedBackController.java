package com.fzkj.project.basicdata.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.fzkj.common.constant.Constants;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.basicdata.entity.TFeedBack;
import com.fzkj.project.basicdata.request.TFeedBackRequest;
import com.fzkj.project.basicdata.service.FeedBackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


/**
 * 常见问题Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Controller
@Api(tags = "常见问题")
//@RequestMapping("/basicdata")
public class FeedBackController {
    @Autowired
    private FeedBackService tFeedBackService;

    /**
     * 查询常见问题列表
     */
    @PostMapping("/Get_FeedBack")
    @ApiOperation(value = "查询常见问题列表")
    @ResponseBody
    public AjaxResult list(@RequestBody CommonRequest commonRequest) {
        TFeedBackRequest request = (TFeedBackRequest) commonRequest.getRequest(TFeedBackRequest.class);
        return AjaxResult.success(tFeedBackService.selectTFeedBackList(request, commonRequest.getPageEntity()));
    }

    /**
     * 新增保存常见问题
     */
    @PostMapping("/Edit_Feedback")
    @ApiOperation(value = "新增保存常见问题")
    @ResponseBody
    public AjaxResult addSave(@RequestBody CommonRequest commonRequest) {
        TFeedBackRequest request = (TFeedBackRequest) commonRequest.getRequest(TFeedBackRequest.class);
        switch (request.getFlag()) {
            case Constants.ADD_DATA:
                return AjaxResult.success(tFeedBackService.insertTFeedBack(request));
            case Constants.EDIT_DATA:
                return AjaxResult.success(tFeedBackService.updateTFeedBack(request));
            case Constants.DEL_DATA:
                return AjaxResult.success(tFeedBackService.deleteTFeedBackById(request.getId()));
            default:
                return AjaxResult.error("操作异常！");
        }
    }

    @PostMapping("/Get_FeedBackAPP")
    @ApiOperation(value = "查询常见问题列表")
    @ResponseBody
    public AjaxResult getFeedBackAPP(@RequestBody CommonRequest commonRequest) {
        TFeedBackRequest request = (TFeedBackRequest) commonRequest.getRequest(TFeedBackRequest.class);
        if(request.getFlag().equals("list")){
            return AjaxResult.success(tFeedBackService.selectTFeedBackList(request, commonRequest.getPageEntity()));
        }else if(request.getFlag().equals("byId")){
            TFeedBack tFeedBack = tFeedBackService.selectTFeedBackById(request.getId());
            return AjaxResult.success(new ArrayList<>(Arrays.asList(tFeedBack)));
        }else{
            return  AjaxResult.error("请求类型错误","");
        }
    }
}
