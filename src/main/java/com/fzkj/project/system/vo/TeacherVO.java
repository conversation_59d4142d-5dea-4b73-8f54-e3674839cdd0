package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 讲师
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="讲师VO")
public class TeacherVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Photo")
    private String photo;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("RealName")
    private String realName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Phone")
    private String phone;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Education")
    private String education;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("GraduationSchool")
    private String graduationSchool;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("BeGoodAt")
    private String beGoodAt;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Unit")
    private String unit;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("WorkPost")
    private String workPost;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("TechnicalTitle")
    private String technicalTitle;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("PersonIntroduct")
    private String personIntroduct;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LoginAccount")
    private String loginAccount;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LoginPwd")
    private String loginPwd;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LoginPwdMd5")
    private String loginPwdMd5;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("AuditStatus")
    private Integer auditStatus;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("AuditCode")
    private String auditCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("AuditTime")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("AuditReason")
    private String auditReason;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("RecycleBin")
    private Integer recycleBin;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsShow")
    private Integer isShow;
    @JsonProperty("Flag")
    private String flag;
}