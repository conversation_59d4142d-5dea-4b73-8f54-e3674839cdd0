package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.CompanyLicenceService;
import com.fzkj.project.system.vo.CompanyLicenceVO;
import com.fzkj.project.system.request.CompanyLicenceRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 企业证照 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "companyLicence")
public class CompanyLicenceController extends BaseEntity {

    private final CompanyLicenceService CompanyLicenceServiceImpl;

    @PostMapping("list")
    @ApiOperation(value = "企业证照列表查询")
    public AjaxResult listCompanyLicence(@RequestBody CommonRequest commonRequest) {
        CompanyLicenceRequest request = (CompanyLicenceRequest) commonRequest.getRequest(CompanyLicenceRequest.class);
        return AjaxResult.success(CompanyLicenceServiceImpl.listCompanyLicence(request));
    }

    @PostMapping("listByPage")
    @ApiOperation(value = "企业证照分页查询")
    public AjaxResult listCompanyLicenceByPage(@RequestBody CommonRequest commonRequest) {
        CompanyLicenceRequest request = (CompanyLicenceRequest) commonRequest.getRequest(CompanyLicenceRequest.class);
        return AjaxResult.success(CompanyLicenceServiceImpl.listByPage(request, commonRequest.getPageEntity()));
    }

    @GetMapping("{id}")
    @ApiOperation(value = "企业证照查询详情")
    public AjaxResult findCompanyLicence(@RequestBody CommonRequest commonRequest) {
        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
        return AjaxResult.success(CompanyLicenceServiceImpl.findCompanyLicence(request.getId()));
    }

    @PostMapping
    @ApiOperation(value = "企业证照新增数据")
    public AjaxResult saveCompanyLicence(@RequestBody CommonRequest commonRequest) {
        CompanyLicenceVO vo = (CompanyLicenceVO) commonRequest.getRequest(CompanyLicenceVO.class);
        return AjaxResult.success(CompanyLicenceServiceImpl.saveCompanyLicence(vo));
    }

    @PostMapping("update")
    @ApiOperation(value = "企业证照修改数据")
    public AjaxResult updateCompanyLicence(@RequestBody CommonRequest commonRequest) {
        CompanyLicenceVO vo = (CompanyLicenceVO) commonRequest.getRequest(CompanyLicenceVO.class);
        return AjaxResult.success(CompanyLicenceServiceImpl.updateCompanyLicence(vo));
    }

    @PostMapping("delete")
    @ApiOperation(value = "企业证照删除数据")
    public AjaxResult removeCompanyLicence(@RequestBody CommonRequest commonRequest) {
        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
        return AjaxResult.success(CompanyLicenceServiceImpl.removeCompanyLicence(request.getId()));
    }

}
