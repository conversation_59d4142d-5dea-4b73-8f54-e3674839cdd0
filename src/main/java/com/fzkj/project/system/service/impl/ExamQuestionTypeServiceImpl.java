package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.ExamQuestionType;
import com.fzkj.project.system.vo.ExamQuestionTypeVO;
import com.fzkj.project.system.request.ExamQuestionTypeRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ExamQuestionTypeMapper;
import com.fzkj.project.system.service.ExamQuestionTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 试题类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExamQuestionTypeServiceImpl extends ServiceImpl<ExamQuestionTypeMapper, ExamQuestionType> implements ExamQuestionTypeService {

    @Override
    public List<ExamQuestionType> listExamQuestionType(ExamQuestionTypeRequest request) {
        ExamQuestionType entity = (ExamQuestionType) DataTransfer.transfer(request, ExamQuestionType.class);
        LambdaQueryWrapper<ExamQuestionType> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ExamQuestionTypeRequest request, PageEntity pageEntity) {
        ExamQuestionType entity = (ExamQuestionType) DataTransfer.transfer(request, ExamQuestionType.class);
        LambdaQueryWrapper<ExamQuestionType> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<ExamQuestionType> page = this.page(
                new Query<ExamQuestionType>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ExamQuestionTypeVO findExamQuestionType(Long id) {
        ExamQuestionType entity = this.getById(id);
        ExamQuestionTypeVO vo = (ExamQuestionTypeVO) DataTransfer.transfer(entity, ExamQuestionTypeVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExamQuestionType(ExamQuestionTypeVO vo) {
        ExamQuestionType entity = (ExamQuestionType) DataTransfer.transfer(vo, ExamQuestionType.class);
        this.save(entity);
        return entity.getQuestionTypeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamQuestionType(ExamQuestionTypeVO vo) {
        ExamQuestionType entity = (ExamQuestionType) DataTransfer.transfer(vo, ExamQuestionType.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExamQuestionType(Long id) {
        return this.removeById(id);
    }
}