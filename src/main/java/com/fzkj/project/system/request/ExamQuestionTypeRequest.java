package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 试题类型表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value = "试题类型表Request")
public class ExamQuestionTypeRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    @ApiModelProperty(value = "创建者姓名")
    private String creatorName;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新日期")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @JsonProperty("Flag")
    private String flag;
}