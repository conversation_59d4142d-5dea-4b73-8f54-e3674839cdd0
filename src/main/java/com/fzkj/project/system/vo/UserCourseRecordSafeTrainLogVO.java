package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 安全培训课件分发学员学习记录
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="安全培训课件分发学员学习记录日志VO")
public class UserCourseRecordSafeTrainLogVO implements Serializable {

    private static final long serialVersionUID=1L;
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private String id;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课件ID")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("OrganizationID")
    private Long organizationId;

    @ApiModelProperty(value = "用户编号")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "学习时长")
    @JsonProperty("StudyTimeCount")
    private Long studyTimeCount;

    @JsonProperty("RecordType")
    private Integer recordType;

    @JsonProperty("RecordTypeStr")
    private String recordTypeStr;

    @JsonProperty("StartLearningTime")
    private String startLearningTime;

    @JsonProperty("TrainType")
    private Integer trainType;

    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "创建人ID")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @JsonProperty("ReservedField5")
    private String reservedField5;

    @ApiModelProperty(value = "操作标识")
    @JsonIgnore
    private String flag;

}