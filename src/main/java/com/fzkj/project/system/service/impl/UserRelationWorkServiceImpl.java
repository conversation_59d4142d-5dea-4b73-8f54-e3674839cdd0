package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.UserRelationWork;
import com.fzkj.project.system.vo.UserRelationWorkVO;
import com.fzkj.project.system.request.UserRelationWorkRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.UserRelationWorkMapper;
import com.fzkj.project.system.service.UserRelationWorkService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户关联岗位 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserRelationWorkServiceImpl extends ServiceImpl<UserRelationWorkMapper, UserRelationWork> implements UserRelationWorkService {

    @Override
    public List<UserRelationWork> listUserRelationWork(UserRelationWorkRequest request) {
        UserRelationWork entity = (UserRelationWork) DataTransfer.transfer(request, UserRelationWork.class);
        LambdaQueryWrapper<UserRelationWork> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(UserRelationWorkRequest request, PageEntity pageEntity) {
        UserRelationWork entity = (UserRelationWork) DataTransfer.transfer(request, UserRelationWork.class);
        LambdaQueryWrapper<UserRelationWork> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<UserRelationWork> page = this.page(
                new Query<UserRelationWork>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public UserRelationWorkVO findUserRelationWork(Long id) {
        UserRelationWork entity = this.getById(id);
        UserRelationWorkVO vo = (UserRelationWorkVO) DataTransfer.transfer(entity, UserRelationWorkVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveUserRelationWork(UserRelationWorkVO vo) {
        UserRelationWork entity = (UserRelationWork) DataTransfer.transfer(vo, UserRelationWork.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserRelationWork(UserRelationWorkVO vo) {
        UserRelationWork entity = (UserRelationWork) DataTransfer.transfer(vo, UserRelationWork.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserRelationWork(Long id) {
        return this.removeById(id);
    }
}