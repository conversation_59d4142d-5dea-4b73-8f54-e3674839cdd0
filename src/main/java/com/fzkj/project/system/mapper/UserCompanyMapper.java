package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.UserCompany;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.vo.UserCompanyVO;
import com.fzkj.project.system.vo.UserInfoVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 用户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface UserCompanyMapper extends BaseMapper<UserCompany> {

    Integer userOut(String userCode);

    List<UserCompanyVO> selectPlateNumberByCompanyId(Long companyId);

    List<UserInfoVO> selectPayUserList();



    UserCompanyVO selectUserInfoByPhoneOrIdCard(String phone,String idCard);

    void updateCompany(String userCode, Long companyId, Long departId);

}