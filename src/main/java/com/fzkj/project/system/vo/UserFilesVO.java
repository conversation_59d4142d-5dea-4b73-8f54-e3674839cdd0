package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户文档案VO类
 *
 */
@Data
@ApiModel(value = "用户文档案VO类")
public class UserFilesVO implements Serializable {

    @JsonProperty("userinfo")
    private List<UserInfoVO> userinfo;

    @JsonProperty("userlesson")
    private List<UserLessonRecordSafeTrain> userlesson;

}