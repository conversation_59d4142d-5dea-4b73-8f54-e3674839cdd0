package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 安全培训课程
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_lesson_safe_train")
@ApiModel(value = "安全培训课程实体")
public class LessonSafeTrain implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("CompanyID")
    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @JsonProperty("DepartID")
    @ApiModelProperty(value = "部门id")
    private Long departId;

    @JsonProperty("LessonDate")
    @ApiModelProperty(value = "课程日期")
    private String lessonDate;

    @JsonProperty("LessonCategoryID")
    @ApiModelProperty(value = "课程分类id")
    private Long lessonCategoryId;

    @JsonProperty("LessonCategoryName")
    @ApiModelProperty(value = "课程分类名称")
    private String lessonCategoryName;

    @JsonProperty("LessonName")
    @ApiModelProperty(value = "课程名称")
    private String lessonName;

    @JsonProperty("Introduce")
    @ApiModelProperty(value = "课程介绍")
    private String introduce;

    @JsonProperty("LessonSummary")
    @ApiModelProperty(value = "课程总结")
    private String lessonSummary;

    @JsonProperty("LessonPic")
    @ApiModelProperty(value = "课程图片")
    private String lessonPic;

    @JsonProperty("StudySignState")
    @ApiModelProperty(value = "是否需要签到")
    private Integer studySignState;

    @JsonProperty("StudyFaceState")
    @ApiModelProperty(value = "是否需要人脸识别")
    private Integer studyFaceState;

    @JsonProperty("StudyCountDownTime")
    @ApiModelProperty(value = "课程倒计时时间")
    private Integer studyCountDownTime;

    @JsonProperty("StudyFailRepeatNum")
    @ApiModelProperty(value = "课程失败后可重复学习次数")
    private Integer studyFailRepeatNum;

    @JsonProperty("CoursFaceNum")
    @ApiModelProperty(value = "课程人脸识别次数")
    private Integer coursFaceNum;

    @JsonProperty("STime")
    @ApiModelProperty(value = "课程开始时间")
    private String sTime;

    @JsonProperty("ETime")
    @ApiModelProperty(value = "课程结束时间")
    private String eTime;

    @JsonProperty("Shape")
    @ApiModelProperty(value = "课程形式")
    private Integer shape;

    @JsonProperty("TotalTimeCount")
    @ApiModelProperty(value = "课程总时长")
    private Integer totalTimeCount;

    @JsonProperty("BelongPlat")
    @ApiModelProperty(value = "所属平台")
    private Integer belongPlat;

    @JsonProperty("HandMode")
    @ApiModelProperty(value = "操作方式")
    private Integer handMode;

    @JsonProperty("Source")
    @ApiModelProperty(value = "数据来源")
    private String source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人")
    private String creatorCode;

    @JsonProperty("CreationTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @JsonProperty("ReservedField1")
    @ApiModelProperty(value = "备用字段1")
    private String reservedField1;

    @JsonProperty("ReservedField2")
    @ApiModelProperty(value = "备用字段2")
    private String reservedField2;

    @JsonProperty("ReservedField3")
    @ApiModelProperty(value = "备用字段3")
    private String reservedField3;

    @JsonProperty("ReservedField4")
    @ApiModelProperty(value = "备用字段4")
    private String reservedField4;

    @JsonProperty("ReservedField5")
    @ApiModelProperty(value = "备用字段5")
    private String reservedField5;
}