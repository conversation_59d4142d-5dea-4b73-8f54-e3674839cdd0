<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.UserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.UserInfo">
        <id column="id" property="id"/>
        <result column="user_code" property="userCode"/>
        <result column="id_card" property="idCard"/>
        <result column="phone" property="phone"/>
        <result column="pass_word" property="passWord"/>
        <result column="union_id" property="unionId"/>
        <result column="open_id" property="openId"/>
        <result column="source" property="source"/>
        <result column="creator_code" property="creatorCode"/>
        <result column="creator_time" property="creatorTime"/>
        <result column="revise_code" property="reviseCode"/>
        <result column="revise_time" property="reviseTime"/>
        <result column="remark" property="remark"/>
        <result column="is_valid" property="isValid"/>
        <result column="job_no" property="jobNo"/>
        <result column="revise_time_pwd" property="reviseTimePwd"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_code, id_card, phone, pass_word, union_id, open_id, source, creator_code, creator_time, revise_code, revise_time, remark, is_valid, job_no, revise_time_pwd
    </sql>

    <select id="selectUserByUserName" resultType="com.fzkj.project.system.entity.UserInfo">
        select u.*,
               uc.user_name,
               uc.identity,
               c.id         as company_id,
               c.company_name,
               c.area_code,
               ce.open_sign as CompanyAutoSign
        from t_d_user_info u
                 left join t_d_user_company uc on u.user_code = uc.user_code
                 left join t_d_company c on uc.company_id = c.id
                 left join t_d_company_extend ce ON ce.company_id = uc.company_id
        where u.phone = #{username}
           or u.id_card = #{username}
           or u.job_no = #{username}
    </select>

    <select id="selectUserByUserCode" resultType="com.fzkj.project.system.vo.AuthUserInfoVO">
        select u.id,
               u.user_code,
               u.id_card,
               u.phone,
               uc.user_name,
               uc.identity,
               c.id         as company_id,
               c.id         as organizationId,
               c.company_name,
               c.area_code,
               u.job_no as JobNo,
               ce.open_sign as CompanyAutoSign,
               uc.user_photo,
               uc.depart_id as departID,
               ccc.company_name AS departName,
               uc.work_id   as workID,
               dc.work_name as workName,
               uc.sign_url  as signUrl
        from t_d_user_info u
                 left join t_d_user_company uc on u.user_code = uc.user_code
                 left join t_d_company c on uc.company_id = c.id
                 left join t_d_company_extend ce ON ce.company_id = uc.company_id
                 left join t_d_company_work dc ON uc.work_id = dc.id,
            t_d_company AS ccc
        where u.user_code = #{userCode}
          AND uc.depart_id = ccc.id
    </select>

    <select id="selectUserByUnionId" resultType="com.fzkj.project.system.entity.UserInfo">
        select u.id,
               u.user_code,
               u.id_card,
               u.phone,
               uc.user_name,
               uc.identity,
               c.id         as company_id,
               c.company_name,
               c.area_code,
               ce.open_sign as CompanyAutoSign
        from t_d_user_info u
                 left join t_d_user_company uc on u.user_code = uc.user_code
                 left join t_d_company c on uc.company_id = c.id
                 left join t_d_company_extend ce ON ce.company_id = uc.company_id
        where u.union_id = #{unionId}
    </select>

    <select id="selectUserLoginInfoByUserCode" resultType="com.fzkj.project.system.vo.UserInfoVO">
        select u.*, uc.user_name,uc.user_photo,uc.identity,uc.company_id,uc.depart_id,uc.work_id,uc.is_out as isOut
        from t_d_user_info u
                 left join t_d_user_company uc on u.user_code = uc.user_code
        where u.user_code = #{userCode}
    </select>

    <select id="selectUserInfoPageList" resultType="com.fzkj.project.system.vo.UserInfoVO">
        SELECT
        uc.user_name,
        ui.user_code,
        uc.user_photo,
        ui.id_card,
        uc.company_id,
        dc.company_name companyName,
        uc.depart_id,
        dcd.company_name departName,
        uc.work_id,
        cw.work_name,
        uc.is_out,
        ui.union_id,
        ui.open_id,
        ui.creator_time,
        ui.job_no,
        ui.phone,
        ul.login_time loginDate,
        uc.join_time,
        ui.remark
        FROM
        t_d_user_info ui
        LEFT JOIN t_d_user_company uc ON ui.user_code = uc.user_code
        LEFT JOIN t_d_company dc ON uc.company_id = dc.id
        LEFT JOIN t_d_company dcd ON uc.depart_id = dcd.id
        LEFT JOIN t_d_company_work cw ON uc.work_id = cw.id
        LEFT JOIN ( SELECT user_name, max( login_time ) login_time FROM t_d_user_logininfor GROUP BY user_name ) ul ON
        ui.phone = ul.user_name
        WHERE
        1 = 1
        <if test="companyId != null">AND uc.company_id = #{companyId}</if>
        <if test="departId != null">AND uc.depart_id = #{departId}</if>
        <if test="keyWord != null and keyWord !=''">
            AND (uc.user_name LIKE concat('%',#{keyWord},'%')
            OR ui.phone LIKE concat('%',#{keyWord},'%')
            OR ui.user_code LIKE concat('%',#{keyWord},'%')
            OR ui.job_no LIKE concat('%',#{keyWord},'%')
            OR ui.id_card LIKE concat('%',#{keyWord},'%')
            )
        </if>
        <if test="isOut != null and isOut != -999">AND uc.is_out = #{isOut}</if>
        <if test="workId != null and workId != -999">AND uc.work_id = #{workId}</if>
        <if test="isBind != null and isBind == 0">AND ui.union_id IS NULL</if>
        <if test="isBind != null and isBind == 1">AND ui.union_id IS NOT NULL</if>
        order by uc.is_out desc ,creator_time
    </select>

    <select id="getUserSelect" resultType="com.fzkj.project.system.vo.UserInfoVO">
        SELECT
        uc.user_name,
        ui.user_code,
        ui.job_no,
        ui.id_card as idCard
        FROM
        t_d_user_info ui
        LEFT JOIN t_d_user_company uc ON ui.user_code = uc.user_code
        WHERE
        1 = 1
        <if test="userName != null and userName !=''">
            AND (uc.user_name LIKE concat('%',#{userName},'%')
            OR ui.phone LIKE concat('%',#{userName},'%')
            OR ui.job_no LIKE concat('%',#{userName},'%')
            OR ui.id_card LIKE concat('%',#{userName},'%')
            )
          </if>
        <if test="workId != null">
            AND uc.work_id= #{workId}
        </if>
        <if test="companyId != null and departId == null">
            AND uc.company_id IN (
            SELECT
            id
            FROM
            t_d_company
            WHERE
            FIND_IN_SET ( #{companyId}, ancestors ) UNION
            SELECT
            #{companyId} AS id
            )
        </if>
        <if test="companyId != null and departId != null">
          AND uc.depart_id IN (
            SELECT
            id
            FROM
            t_d_company
            WHERE
            FIND_IN_SET ( #{departId}, ancestors ) UNION
            SELECT
            #{departId} AS id
            )
        </if>
    </select>

    <update id="clearPhotoByUserCode">
        UPDATE t_d_user_company
        SET user_photo = null
        WHERE user_code = #{userCode}
    </update>

    <select id="selectUserInfoByUserCode" resultType="com.fzkj.project.system.vo.UserInfoVO">
        SELECT uc.user_name,
               ui.user_code,
               uc.user_photo,
               ui.id_card,
               ui.phone,
               dc.company_name  companyName,
               dcd.company_name departName,
               cw.work_name
        FROM t_d_user_info ui
                 LEFT JOIN t_d_user_company uc ON ui.user_code = uc.user_code
                 LEFT JOIN t_d_company dc ON uc.company_id = dc.id
                 LEFT JOIN t_d_company dcd ON uc.depart_id = dcd.id
                 LEFT JOIN t_d_company_work cw ON uc.work_id = cw.id
        WHERE ui.user_code = #{userCode}
    </select>
    <select id="selectSendUserInfo" resultType="com.fzkj.project.system.vo.UserManageVO">
        select *
        from (select *
              from (select ul.target_id, c.company_name, ui.public_open_id open_id, uc.user_name, c.fid
                    from t_d_user_limit ul
                             left join t_d_company c on c.id = ul.target_id
                             left join t_d_user_info ui on ui.user_code = ul.user_code
                             left join t_d_user_company uc on uc.user_code = ul.user_code) t
              where t.fid is not null
                and t.open_id != ''
                and t.open_id is not null) t1
        where t1.open_id not in
              (select email from t_d_log_send_email_info where lesson_month = #{month} and company_id = t1.target_id)
    </select>
    <select id="selectAllSendUserInfo" resultType="com.fzkj.project.system.vo.UserManageVO">
        select *
        from (select ul.user_code, ui.public_open_id open_id, ul.target_id, c.fid
              from t_d_user_limit ul
                       left join t_d_company c on c.id = ul.target_id
                       left join t_d_user_info ui on ui.user_code = ul.user_code) t
        where t.fid is not null
          and t.open_id != ''
          and t.open_id is not null
    </select>
    <select id="selectUserInfoAndCompanyInfoByPhoneOrIdCard"
            resultType="com.fzkj.project.system.vo.UserInfoVO">
        SELECT dc.company_name  companyName,
               dcd.company_name departName
        FROM t_d_user_info ui
                 LEFT JOIN t_d_user_company uc ON ui.user_code = uc.user_code
                 LEFT JOIN t_d_company dc ON uc.company_id = dc.id
                 LEFT JOIN t_d_company dcd ON uc.depart_id = dcd.id
        WHERE ui.phone = #{phone}
           OR ui.id_card = #{idCard}
    </select>
    <select id="selectUserByIdCard" resultType="com.fzkj.project.system.vo.AuthUserInfoVO">
        select u.id,
               u.user_code,
               u.id_card,
               u.phone,
               uc.user_name,
               uc.identity,
               c.id         as company_id,
               c.id         as organizationId,
               c.company_name,
               c.area_code,
               ce.open_sign as CompanyAutoSign,
               uc.user_photo,
               uc.depart_id as departID,
               ccc.company_name AS departName,
               uc.work_id   as workID,
               dc.work_name as workName,
               uc.sign_url  as signUrl
        from t_d_user_info u
                 left join t_d_user_company uc on u.user_code = uc.user_code
                 left join t_d_company c on uc.company_id = c.id
                 left join t_d_company_extend ce ON ce.company_id = uc.company_id
                 left join t_d_company_work dc ON uc.work_id = dc.id,
             t_d_company AS ccc
        where u.id_card = #{idCard}
          AND uc.depart_id = ccc.id
    </select>

</mapper>