package com.fzkj.project.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.CompanyWork;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.mapper.CompanyMapper;
import com.fzkj.project.system.mapper.CompanyWorkMapper;
import com.fzkj.project.system.service.CompanyWorkService;
import com.fzkj.project.system.vo.UserInfoVO;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 岗位 服务实现类
 * </p>
 *
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CompanyWorkServiceImpl extends ServiceImpl<CompanyWorkMapper, CompanyWork> implements CompanyWorkService {

    private final CompanyMapper companyMapper;

    @Override
    public String listPageCompanyWork(CompanyWork companyWork, PageEntity pageEntity) {

        PageHelper.startPage(pageEntity.getPageIndex(),pageEntity.getPageSize());
        List<CompanyWork> list = getBaseMapper().selectCompanyWorkList(companyWork.getCompanyId());
        PageUtils pageUtils = new PageUtils(pageEntity,list);
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("total",pageUtils.getTotalCount());
        paramMap.put("rows",pageUtils.getList());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("WorkInfo",new JSONObject(paramMap).toString());
        return jsonObject.toString();
    }

    @Override
    public PageUtils getWorkPage(CompanyWork companyWork, PageEntity pageEntity) {

        PageHelper.startPage(pageEntity.getPageIndex(),pageEntity.getPageSize());
        List<CompanyWork> list = getBaseMapper().selectCompanyWorkPage(companyWork.getCompanyId(),companyWork.getWorkName());
        return new PageUtils(pageEntity,list);
    }
}