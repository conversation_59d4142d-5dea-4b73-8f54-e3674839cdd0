package com.fzkj.project.wxPay.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_order")
@ApiModel("订单")
public class Order {

    /**
     * 主键自增id
     */
    @ApiModelProperty("主键id")
    @TableId(value = "id")
    private Long id;
    /**
     * 微信用户ipenID
     */
    @ApiModelProperty("微信用户ipenID")
    @TableField(value = "open_id")
    private String openId;
    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    @TableField(value = "order_num")
    private String orderNum;
    /**
     * 0:报名，1：会员费用
     */
    @ApiModelProperty("0:报名，1：会员费用")
    @TableField(value = "type")
    private Integer type;
    /**
     * 支付金额
     */
    @ApiModelProperty("支付金额")
    @TableField(value = "money")
    private Double money;
    /**
     * 数据id
     */
    @ApiModelProperty("数据id")
    @TableField(value = "data_id")
    private String dataId;
    /**
     * 数据id
     */
    @ApiModelProperty("订单状态，0：支付中，1：已支付，2:活动已结束")
    @TableField(value = "status")
    private Integer status;
    /**
     * 数据id
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    @TableField(exist = false)
    private String title;

    /**
     * 用户名
     */
    @TableField(value = "user_code")
    public String userCode;
    /**
     * 身份证
     */
    @TableField(exist = false)
    public String cardId;
    /**
     * 实名
     */
    @TableField(exist = false)
    public String name;
}