package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 安全培训课程分发学员学习记录
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="安全培训课程分发学员考试记录VO")
public class SafeTrainCompanyDetialUserExamRecordVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "用户ID")
    @JsonProperty("UserID")
    private String userId;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "得分")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty(value = "通过分数")
    @JsonProperty("PassMark")
    private Integer passMark;

    @ApiModelProperty(value = "车牌号")
    @JsonProperty("PlateNumber")
    private String plateNumber;

    @ApiModelProperty(value = "考试完成时间")
    @JsonProperty("ExamCompleteTime")
    private String examCompleteTime;

    @ApiModelProperty(value = "签名url")
    @JsonProperty("SignUrl")
    private String signUrl;

}