package com.fzkj.project.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.LessonSafeTrain;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.vo.*;

import java.util.List;

/**
 * <p>
 * 安全培训课程 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface LessonSafeTrainService extends IService<LessonSafeTrain> {

    PageUtils listLessonSafeTrain(LessonSafeTrainRequest request, PageEntity pageEntity);

    PageUtils listByPage(LessonSafeTrainRequest request, PageEntity pageEntity);

    LessonSafeTrainVO findLessonSafeTrain(Long id);

    Long saveLessonSafeTrain(LessonSafeTrainVO vo);

    boolean updateLessonSafeTrain(LessonSafeTrainVO vo);

    boolean removeLessonSafeTrain(Long id);

    PageUtils getCourseByLessonSafeTrainID(LessonSafeTrainCourseRequest request, PageEntity pageEntity);

    Long editLessonSafeTrain(LessonSafeTrainVO request);

    boolean editLessonSafeTrainDel(LessonSafeTrainVO request);

    Long editLessonSafeTrainCopy(LessonSafeTrainVO request);

    PageUtils getLessonSafeTrainListBus(LessonSafeTrainRequest request, PageEntity pageEntity);

    PageUtils getLessonSafeTrainListByPlanIdBus(LessonSafeTrainRequest request, PageEntity pageEntity);

    boolean editLessonSafeTrainChoiceDistribute(LessonSafeTrainDisVO request);

    PageUtils getLessonSafeTrainDisCompanyList(LessonSafeTrainQueryCompanyRequest request, PageEntity pageEntity);

    PageUtils getLessonDisedUserList(LessonSafeTrainQueryUserRequest request, PageEntity pageEntity);

    PageUtils getLessonNoDisUserList(LessonSafeTrainQueryUserRequest request, PageEntity pageEntity);

    PageUtils getLessonNoDisUserListC(LessonSafeTrainQueryUserRequest request, PageEntity pageEntity);

    boolean editLessonSafeTrainChoiceUserDistribute(LessonSafeTrainDisVO request);

    boolean editLessonSafeTrainChoiceUserDistributeC(LessonSafeTrainDisVO request);

    boolean delUserLessonRecordSafeTrain(LessonSafeTrainDisVO request);

     boolean delUserLessonRecordSafeTrainAll(String userCode);

    boolean editLessonSafeTrainCompanyDel(LessonSafeTrainDisVO request);

    boolean editLessonSafeTrainOnekeyDistribute(LessonSafeTrainDisVO request);

    PageUtils getLessonSafeTrainListC(LessonSafeTrainRequest request, PageEntity pageEntity);

    boolean recoveryUserLessonRecordSafeTrain(LessonSafeTrainDisVO request);

    List<GetHomeLessonInfoVO> getHomeLessonInfo(GetHomeLessonInfoRequest request);

    List<GetLessonInfoDetailVO> getLessonInfoDetail(GetLessonInfoDetailRequest request);

    boolean editUserLessonRecordSign(EditUserLessonRecordSignRequest request);

    List<GetMyLessonInfoVO> getMyLessonInfo(GetMyLessonInfoRequest request);

    List<GetUserTrainTypeVO> getUserTrainType(GetUserTrainTypeRequest request);

    boolean authFaceRecognition(AuthFaceRecognitionRequest request);

    List<GetUserRedNumVO> getUserRedNum(GetUserRedNumRequest request);

    List<GetUserLessonTrainDetailVO> getUserLessonTrainDetail(GetUserLessonTrainDetailRequest request);

    String getLearnCertificatePic(GetLearnCertificatePicRequest request);

    GetLearnRedNumVO getLearnRedNum(GetLearnRedNumRequest request);

    GetLessonSafeTrainListAPPVO getLessonSafeTrainListAPP(GetLessonSafeTrainListAPPRequest request, PageEntity pageEntity);

    List<GetCompanyLessonTrainTypeListAPPVO> getCompanyLessonTrainTypeListAPP(GetCompanyLessonTrainTypeListAPPRequest request);

    List<GetCourseAPPVO> getCourseAPP(GetCourseAPPRequest request);

    GetCompanySafeTrainFaceRecordVO getCompanySafeTrainFaceRecord(GetCompanySafeTrainFaceRecordRequest request);

    SafeTrainFilesVO safeTrainFiles(SafeTrainFilesRequest request);

    boolean getCopyExamInfo(GetCopyExamInfoRequest request);

    GetUserSurveyVO getUserSurvey(GetUserSurveyRequest request);

    PageUtils getUserLessonRecordByUserCode(GetUserSurveyRequest request,PageEntity pageEntity);

    String getUserSafeTrainLessonRecordExport(GetUserSurveyRequest request);

    String userToCompanyIsEnable(UserToCompanyIsEnableRequest request);

    PageUtils getUserNotDisSafeLessonList(LessonSafeTrainRequest lessonSafeTrainRequest,PageEntity pageEntity);
}
