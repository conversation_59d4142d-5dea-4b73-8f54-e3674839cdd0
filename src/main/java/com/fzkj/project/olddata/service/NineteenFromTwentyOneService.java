package com.fzkj.project.olddata.service;

import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.olddata.dto.request.NineteenFromTwentyOneRequest;
import com.fzkj.project.system.request.UserSafeTrainFilesDetailRequest;
import com.fzkj.project.system.vo.UserSafeTrainFilesDetailVO;

public interface NineteenFromTwentyOneService {
    PageUtils getOrgList(NineteenFromTwentyOneRequest request, PageEntity pageEntity);

    PageUtils getTwentyOneSafeTrainUserList(NineteenFromTwentyOneRequest request, PageEntity pageEntity);

    UserSafeTrainFilesDetailVO getTwentyOneSafeTrainFilesDetail(UserSafeTrainFilesDetailRequest request);
}
