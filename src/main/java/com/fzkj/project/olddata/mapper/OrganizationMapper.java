package com.fzkj.project.olddata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.project.olddata.dto.OrganizationEntity;
import com.fzkj.project.olddata.dto.request.NineteenFromTwentyOneRequest;
import com.fzkj.project.system.entity.Company;
import com.fzkj.project.system.mongo.UserCourseRecordSafeTrain;
import com.fzkj.project.system.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;

@Mapper
public interface OrganizationMapper extends BaseMapper<OrganizationEntity> {
    List<Long> getSubDeptIds(Long orgId);

    List<UserLessonRecordLearnNumVO> getLessonLearnNumByCompanyDepartId(String stageName);

    List<UserLessonRecordSafeTrainUserVO> getTwentyOneUserLessonRecordSafeTrainBus(@Param("staticStatusList") List<Integer> staticStatusList,
                                                                                   @Param("lessonIds") List<Long> lessonIds,
                                                                                   @Param("subDeptIds") List<Long> subDeptIds,
                                                                                   @Param("keyWord") String keyWord);

    LessonRecordVO getUserSafeTrainFilesDetail(@Param("userCode")String userCode, @Param("lessonId")Long lessonId);

    UserExamInfoVo getUserExamInfo(@Param("userCode") String userCode, @Param("list") List<Long> list);

    List<UserCourseRecordSafeTrain> getUserCourse(@Param("lessonId")Long lessonId,@Param("userCode") String userCode);

    List<CourseRecordVO> selectCourseRecord(@Param("lessonId")Long lessonId,@Param("userCode") String userCode);
}
