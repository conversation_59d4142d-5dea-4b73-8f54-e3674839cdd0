package com.fzkj.common.utils.file;

import com.fzkj.common.constant.Constants;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.exception.file.FileNameLengthLimitExceededException;
import com.fzkj.common.exception.file.FileSizeLimitExceededException;
import com.fzkj.common.exception.file.InvalidExtensionException;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.common.utils.oss.OSSClientUtil;
import com.fzkj.common.utils.oss.OSSConst;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.common.utils.security.Md5Utils;
import com.fzkj.framework.config.AppConfig;
import org.apache.commons.io.FilenameUtils;
import org.jaudiotagger.audio.AudioFileIO;
import org.jaudiotagger.audio.mp3.MP3AudioHeader;
import org.jaudiotagger.audio.mp3.MP3File;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 文件上传工具类
 *
 * <AUTHOR>
 */
public class FileUploadUtils {
    /**
     * 默认大小 50M
     */
    public static final long DEFAULT_MAX_SIZE = 50 * 1024 * 1024;

    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;

    /**
     * 默认上传的地址
     */
    private static String defaultBaseDir = AppConfig.getProfile();

    private static int counter = 0;

    public static void setDefaultBaseDir(String defaultBaseDir) {
        FileUploadUtils.defaultBaseDir = defaultBaseDir;
    }

    public static String getDefaultBaseDir() {
        return defaultBaseDir;
    }

    /**
     * 以默认配置进行文件上传
     *
     * @param file 上传的文件
     * @return 文件名称
     * @throws Exception
     */
    public static final String upload(MultipartFile file) throws IOException {
        try {
            return upload(getDefaultBaseDir(), file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 根据文件路径上传
     *
     * @param baseDir 相对应用的基目录
     * @param file    上传的文件
     * @return 文件名称
     * @throws IOException
     */
    public static final String upload(String baseDir, MultipartFile file) throws IOException {
        try {
            return upload(baseDir, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 文件上传
     *
     * @param baseDir          相对应用的基目录
     * @param file             上传的文件
     * @param allowedExtension 上传文件类型
     * @return 返回上传成功的文件名
     * @throws FileSizeLimitExceededException       如果超出最大大小
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws IOException                          比如读写文件出错时
     * @throws InvalidExtensionException            文件校验异常
     */
    public static final String upload(String baseDir, MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException,
            InvalidExtensionException {
        int fileNamelength = file.getOriginalFilename().length();
        if (fileNamelength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }

        assertAllowed(file, allowedExtension);

        String fileName = extractFilename(file);

        File desc = getAbsoluteFile(baseDir, fileName);
        file.transferTo(desc);
        String pathFileName = getPathFileName(baseDir, fileName);
        return pathFileName;
    }

    /**
     * 通过OSS上传文件
     *
     * @param file
     * @return java.lang.String
     */
    public static final UploadFile uploadOss(MultipartFile file, String group) throws IOException {
        try {
            int fileNamelength = file.getOriginalFilename().length();
            if (fileNamelength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
                throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
            }
            assertAllowed(file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
            String extension = getExtension(file);
            String fileName = group + "/" + DateUtils.dateTime() + "/" + extractFilename(file);
            if (extension.equalsIgnoreCase("mp4")) {
               /* UploadFile uploadFile = OSSClientUtil.uploadVideoFile(OSSConst.BUCKET_NAME, fileName, file.getInputStream());
                uploadFile.setSize(file.getSize());
                uploadFile.setDuration(getVideoDuration(file));
                return uploadFile;*/
                throw new CustomException("暂不支持MP4文件上传");
            } else {
                OSSClientUtil.uploadUrlFile(OSSConst.BUCKET_NAME, fileName, file.getInputStream());
                String fileUrl = OSSClientUtil.getFileUrl(OSSConst.BUCKET_NAME, fileName);
                UploadFile uploadFile = new UploadFile();
                uploadFile.setMediaUrl(fileUrl);
                uploadFile.setUrl(fileUrl);
                uploadFile.setSize(file.getSize());
                if (extension.equalsIgnoreCase("mp3")) {
                    uploadFile.setDuration(getVideoDuration(file));
                }
                return uploadFile;
            }

        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 后端导出excel上传方法
     */
    public static final UploadFile uploadOssInputStream(InputStream inputStream, String group, String name) throws IOException {
        try {
            int fileNamelength = name.length();
            if (fileNamelength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
                throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
            }
            String fileName = group + "/" + DateUtils.dateTime() + "/" + name;
            if (!name.contains(".")) {
                fileName += ".xlsx";
            }
            OSSClientUtil.uploadTempUrlFile(OSSConst.BUCKET_NAME, fileName, inputStream);
            String fileUrl = OSSClientUtil.getFileUrl(OSSConst.BUCKET_NAME, fileName);
            UploadFile uploadFile = new UploadFile();
            uploadFile.setMediaUrl(fileUrl);
            uploadFile.setUrl(fileUrl);
            return uploadFile;
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 编码文件名
     */
    public static final String extractFilename(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        fileName = System.currentTimeMillis() + encodingFilename(fileName).substring(0, 7) + "." + extension;
        return fileName;
    }

    private static final File getAbsoluteFile(String uploadDir, String fileName) throws IOException {
        File desc = new File(uploadDir + File.separator + fileName);

        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        if (!desc.exists()) {
            desc.createNewFile();
        }
        return desc;
    }

    private static final String getPathFileName(String uploadDir, String fileName) throws IOException {
        int dirLastIndex = AppConfig.getProfile().length() + 1;
        String currentDir = StringUtils.substring(uploadDir, dirLastIndex);
        String pathFileName = Constants.RESOURCE_PREFIX + "/" + currentDir + "/" + fileName;
        return pathFileName;
    }

    /**
     * 编码文件名
     */
    private static final String encodingFilename(String fileName) {
        fileName = fileName.replace("_", " ");
        fileName = Md5Utils.hash(fileName + System.nanoTime() + counter++);
        return fileName;
    }

    /**
     * 文件大小校验
     *
     * @param file 上传的文件
     * @return
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws InvalidExtensionException
     */
    public static final void assertAllowed(MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, InvalidExtensionException {
        long size = file.getSize();
        String extension = getExtension(file);
        long maxSize = DEFAULT_MAX_SIZE;
        if ("mp4".equals(extension)) {
            maxSize = 1024 * 1024 * 1024;
        }
        if (maxSize != -1 && size > maxSize) {
            throw new FileSizeLimitExceededException(maxSize / 1024 / 1024);
        }
        String fileName = file.getOriginalFilename();
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension)) {
            if (allowedExtension == MimeTypeUtils.IMAGE_EXTENSION) {
                throw new InvalidExtensionException.InvalidImageExtensionException(allowedExtension, extension,
                        fileName);
            } else if (allowedExtension == MimeTypeUtils.FLASH_EXTENSION) {
                throw new InvalidExtensionException.InvalidFlashExtensionException(allowedExtension, extension,
                        fileName);
            } else if (allowedExtension == MimeTypeUtils.MEDIA_EXTENSION) {
                throw new InvalidExtensionException.InvalidMediaExtensionException(allowedExtension, extension,
                        fileName);
            } else {
                throw new InvalidExtensionException(allowedExtension, extension, fileName);
            }
        }

    }

    /**
     * 判断MIME类型是否是允许的MIME类型
     *
     * @param extension
     * @param allowedExtension
     * @return
     */
    public static final boolean isAllowedExtension(String extension, String[] allowedExtension) {
        for (String str : allowedExtension) {
            if (str.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     * @return 后缀名
     */
    public static final String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(file.getContentType());
        }
        return extension;
    }

    private static double getVideoDuration(MultipartFile file) throws Exception {
        String filePath = System.getProperty("java.io.tmpdir")+File.separator+System.currentTimeMillis()+".mp3";
        try {
            InputStream inputStream = file.getInputStream();
            FileOutputStream outputStream = new FileOutputStream(filePath);
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.close();
            inputStream.close();
            Float duration = getMp3Duration(filePath);
            return duration.doubleValue();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            File tempFile = new File(filePath);
            if(tempFile.exists()){
                tempFile.delete();
            }
        }
    }

    public static Float getMp3Duration(String filePath){
        try {
            File mp3File = new File(filePath);
            MP3File f = (MP3File) AudioFileIO.read(mp3File);
            MP3AudioHeader audioHeader = (MP3AudioHeader)f.getAudioHeader();
            return Float.parseFloat(audioHeader.getTrackLength()+"");
        } catch(Exception e) {
            e.printStackTrace();
            return 0f;
        }
    }
}
