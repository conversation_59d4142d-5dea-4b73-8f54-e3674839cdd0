package com.fzkj.project.system.controller;

import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.security.handle.PdfHeaderMarker;
import com.fzkj.project.system.request.DownloadPdfRequest;
import com.fzkj.project.system.request.GetCompanySafeTrainFaceRecordRequest;
import com.fzkj.project.system.request.SafeTrainCompanyDetialRequest;
import com.fzkj.project.system.request.SafeTrainFilesRequest;
import com.fzkj.project.system.service.LessonSafeTrainPlanService;
import com.fzkj.project.system.service.LessonSafeTrainService;
import com.fzkj.project.system.vo.GetCompanySafeTrainFaceRecordVO;
import com.fzkj.project.system.vo.SafeTrainDataExportVO;
import com.fzkj.project.system.vo.SafeTrainFilesVO;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.styledxmlparser.jsoup.Jsoup;
import com.itextpdf.styledxmlparser.jsoup.nodes.Document;
import com.itextpdf.styledxmlparser.jsoup.nodes.Element;
import com.itextpdf.styledxmlparser.jsoup.select.Elements;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.util.UriUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Controller
@RequiredArgsConstructor
@RequestMapping("ShowInfo/Show")
public class HtmlController {
    @Value("${app.profile}")
    private String uploadPath;
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);
    private final LessonSafeTrainService lessonSafeTrainService;
    private final LessonSafeTrainPlanService LessonSafeTrainPlanServiceImpl;

    @RequestMapping("Get_CompanySafeTrainFiles")
    @ApiOperation(value = "培训统计-台账下载")
    public String Get_CompanySafeTrainFiles(SafeTrainCompanyDetialRequest request, Model model) {
        SafeTrainDataExportVO vo = LessonSafeTrainPlanServiceImpl.getSafeTrainCompanyDetial(request);
        String title = " 安全培训数据";
        title = request.getLessonMonth().substring(0, 7).replace("-", "年") + "月 " + request.getCompanyName() + title;
        vo.setLessonDate(request.getLessonMonth().substring(0, 7).replace("-", "年") + "月");
        vo.setTitle(title);
        vo.setCompanyName(request.getCompanyName());
        model.addAttribute("model", vo);
        return "CompanySafeTrainFiles";
    }

    @GetMapping("SafeTrainFiles")
    @ApiOperation(value = "培训统计-档案下载")
    public String SafeTrainFiles(SafeTrainFilesRequest request, Model model) {
        SafeTrainFilesVO vo = lessonSafeTrainService.safeTrainFiles(request);
        vo.setTitle(request.getCompanyName() + "线上安全培训大纲");
        vo.setCompanyName(request.getCompanyName());
        model.addAttribute("model", vo);
        return "SafeTrainFiles";
    }

    @RequestMapping("Get_CompanySafeTrainFaceRecord")
    @ApiOperation(value = "培训统计-人像档案")
    public String Get_CompanySafeTrainFaceRecord(GetCompanySafeTrainFaceRecordRequest request, Model model) {
        GetCompanySafeTrainFaceRecordVO vo = lessonSafeTrainService.getCompanySafeTrainFaceRecord(request);
        vo.setTitle(request.getLessonMonth().substring(0, 7).replace("-", "年") + "月" + request.getCompanyName() + "安全培训数据人像照片");
        model.addAttribute("model", vo);
        return "FaceRecord";
    }

    @RequestMapping("DownloadPdf")
    @ApiOperation(value = "Pdf文件下载")
    public ResponseEntity DownloadPdf(DownloadPdfRequest request) throws IOException {
        Long t1 = System.currentTimeMillis();
        try {
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(pdfOutputStream);
            PdfDocument pdf = new PdfDocument(writer);
            pdf.setDefaultPageSize(PageSize.A4);
            DefaultFontProvider fontProvider = new DefaultFontProvider(false, false, false);
            fontProvider.addFont("fonts/microsoftyahei.ttf");
            PdfFont pdfFont = fontProvider.getFontSet().get("microsoftyahei").stream().findFirst().map(fontProvider::getPdfFont)
                    .orElse(null);
            pdf.addEventHandler(PdfDocumentEvent.START_PAGE, new PdfHeaderMarker(pdfFont, "在线教育平台"));
            ConverterProperties converterProperties = new ConverterProperties();
            converterProperties.setFontProvider(fontProvider);
            HtmlConverter.convertToPdf(getHtmlContentFromUrl(request.getUrl()), pdf, converterProperties);
            pdf.close();
            // 读取PDF文件内容
            byte[] fileContent = pdfOutputStream.toByteArray();
            fileContent = addFooterWithPageNumbers(fileContent);
            String encodedFilename = UriUtils.encode(request.getFilename(), StandardCharsets.UTF_8) + ".pdf";
            // 返回文件内容
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFilename)
                    .body(fileContent);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        } finally {
            Long t2 = System.currentTimeMillis();
            log.info("下载PDF一共花费了"+(t2 - t1)/1000);
        }
    }

    public byte[] addFooterWithPageNumbers(byte[] pdfBytes) throws IOException {
        // 读取现有的PDF
        PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(outputStream);
        PdfDocument pdfDoc = new PdfDocument(reader, writer);
        DefaultFontProvider fontProvider = new DefaultFontProvider(false, false, false);
        fontProvider.addFont("fonts/microsoftyahei.ttf");
        PdfFont font = fontProvider.getFontSet().get("microsoftyahei").stream().findFirst().map(fontProvider::getPdfFont)
                .orElse(null);
        // 添加页脚，即页码
        int totalPages = pdfDoc.getNumberOfPages();
        for (int i = 1; i <= totalPages; i++) {
            PdfPage page = pdfDoc.getPage(i);
            int pageNumber = pdfDoc.getPageNumber(page);
            Rectangle pageSize = page.getPageSize();
            PdfCanvas pdfCanvas = new PdfCanvas(page.getLastContentStream(), page.getResources(), pdfDoc);
            Canvas canvas = new Canvas(pdfCanvas, pageSize);
            float x = (pageSize.getLeft() + pageSize.getRight()) / 2;
            float y = pageSize.getBottom() + 25;
            int totalPage = pdfDoc.getNumberOfPages();
            Paragraph p = new Paragraph("第" + pageNumber + "页，共" + totalPage + "页")
                    .setFontSize(10)
                    .setFont(font);
            canvas.showTextAligned(p, x, y, TextAlignment.CENTER);
            canvas.close();
        }
        pdfDoc.close();
        return outputStream.toByteArray();
    }

    public String encodeUrl(String urlStr) throws UnsupportedEncodingException {
        String[] parts = urlStr.split("\\?", 2);
        String baseUrl = parts[0];
        String query = parts[1];
        String encodedQuery = encodeQuery(query);
        return baseUrl + "?" + encodedQuery;
    }

    public String encodeQuery(String query) throws UnsupportedEncodingException {
        String[] params = query.split("&");
        StringBuilder encodedQuery = new StringBuilder();
        for (String param : params) {
            String[] keyValue = param.split("=", 2);
            String encodedKey = URLEncoder.encode(keyValue[0], "UTF-8");
            String encodedValue = URLEncoder.encode(keyValue[1], "UTF-8");
            if (encodedQuery.length() > 0) {
                encodedQuery.append("&");
            }
            encodedQuery.append(encodedKey).append("=").append(encodedValue);
        }
        return encodedQuery.toString();
    }

    private String downloadImage(String imageUrl, String folder) {
        try {
            URL url = new URL(imageUrl);
            String fileName = Paths.get(url.getPath()).getFileName().toString();
            Path targetPath = Paths.get(folder, fileName);
            File localImageFile = targetPath.toFile();
            if (!localImageFile.exists()) {
                try (InputStream in = url.openStream();
                     OutputStream out = new FileOutputStream(targetPath.toFile())) {
                    // 使用ImageIO读取流中的图片，并直接写入输出流中进行压缩
                    BufferedImage originalImage = ImageIO.read(in);
                    String formatName = fileName.substring(fileName.lastIndexOf(".") + 1);
                    ImageIO.write(originalImage, formatName, out); // 写入输出流
                }
            }
            return localImageFile.getAbsolutePath();
        } catch (Exception e) {
            e.printStackTrace();
            return imageUrl;
        }
    }

    private BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, originalImage.getType());
        Graphics2D graphics2D = resizedImage.createGraphics();
        graphics2D.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        graphics2D.dispose();
        return resizedImage;
    }

    private String convertToLocalPath(String imageUrl, String folder) {
        // 移除URL协议和域名部分，只保留路径部分
        imageUrl = imageUrl.substring(0,imageUrl.lastIndexOf("?"));
        imageUrl = imageUrl.substring(imageUrl.lastIndexOf("/"));
        // 转换为本地文件系统路径
        return "file:///"+Paths.get(folder, imageUrl).normalize();
    }

    public String getHtmlContentFromUrl(String urlString) throws Exception {
        URL html = new URL(encodeUrl(urlString));
        HttpURLConnection connection = (HttpURLConnection) html.openConnection();
        connection.setRequestMethod("GET");

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            StringBuilder response = new StringBuilder();
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();
            String htmlContent = response.toString();
            List<String> imageUrls = new ArrayList<>();
            Document doc = Jsoup.parse(htmlContent);
            Elements imgElements = doc.select("img[src]"); // 选择所有带有src属性的img标签

            for (Element imgElement : imgElements) {
                String imageUrl = imgElement.attr("abs:src"); // 获取图片的绝对URL
                if (StringUtils.isNotEmpty(imageUrl) && StringUtils.startsWith(imageUrl, "http") && !imageUrls.contains(imageUrl)) { // 避免重复
                    imageUrls.add(imageUrl);
                }
            }
            File file = new File(uploadPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            Long t1 = System.currentTimeMillis();
            List<CompletableFuture<String>> futures = imageUrls.stream()
                    .map(url -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return downloadImage(url, uploadPath);
                        } catch (Exception e) {
                            throw new CompletionException(e);
                        }
                    }))
                    .collect(Collectors.toList());

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();

            List<String> successfulDownloads = futures.stream()
                    .map(future -> {
                        try {
                            return future.get();
                        } catch (InterruptedException | ExecutionException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toList());
            Long t2 = System.currentTimeMillis();
            log.info("下载图片一共花费了"+(t2 - t1)/1000);
            for (Element imgElement : imgElements) {
                String imageUrl = imgElement.attr("abs:src");
                if (StringUtils.isNotEmpty(imageUrl) && imageUrl.contains("http") && !successfulDownloads.contains(imageUrl)) {
                    String localPath = convertToLocalPath(imageUrl, uploadPath);
                    imgElement.attr("src", localPath);
                }
            }
            htmlContent = doc.toString();

            return htmlContent;

        } else {
            throw new RuntimeException("Failed to get HTML content. HTTP response code: " + responseCode);
        }
    }


    public void main(String[] args) {
        try {
            String htmlContent = getHtmlContentFromUrl("http://localhost:30003/edu/ShowInfo/Show/Get_CompanySafeTrainFiles?LessonMonth=2024-07&CompanyID=613&DepartID=null&LessonID=140&CompanyName=111&LearnFlag=null");
            System.out.println(htmlContent);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
