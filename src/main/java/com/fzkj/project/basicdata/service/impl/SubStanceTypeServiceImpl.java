package com.fzkj.project.basicdata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.ServletUtils;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.framework.security.service.TokenService;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.basicdata.entity.*;
import com.fzkj.project.basicdata.mapper.SubStanceManageMapper;
import com.fzkj.project.basicdata.mapper.SubStanceTypeMapper;
import com.fzkj.project.basicdata.service.SubStanceManageService;
import com.fzkj.project.basicdata.service.SubStanceTypeService;
import com.fzkj.project.system.entity.UserCompany;
import com.fzkj.project.system.service.UserCompanyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 13:53
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class SubStanceTypeServiceImpl extends ServiceImpl<SubStanceTypeMapper, SubStanceType> implements SubStanceTypeService {

    @Resource
    UserCompanyService userCompanyService;

    @Autowired
    private TokenService tokenService;

    @Resource
    SubStanceManageMapper subStanceManageMapper;

    @Override
    public PageUtils listByPage(SubStanceType request, PageEntity pageEntity) {
        SubStanceType entity = (SubStanceType) DataTransfer.transfer(request, SubStanceType.class);
        LambdaQueryWrapper<SubStanceType> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtils.isEmpty(entity.getId())) {
            queryWrapper.eq(SubStanceType::getIsValid, entity.getIsValid());
        }
        if (!ObjectUtils.isEmpty(entity.getTypeName())) {
            queryWrapper.like(SubStanceType::getTypeName, entity.getTypeName());
        }
        if (!ObjectUtils.isEmpty(entity.getMarkId())) {
            queryWrapper.like(SubStanceType::getMarkId, entity.getMarkId());
        }
        if (!ObjectUtils.isEmpty(entity.getId())) {
            queryWrapper.eq(SubStanceType::getId, entity.getId());
        }
        if (!ObjectUtils.isEmpty(entity.getIsShow())) {
            queryWrapper.eq(SubStanceType::getIsShow, entity.getIsShow());
        }
        IPage<SubStanceType> page = this.page(
                new Query<SubStanceType>().getPage(pageEntity), queryWrapper);
        page.getRecords().forEach(a -> {
            a.setSubstanceNum(subStanceManageMapper.selectCount(new LambdaQueryWrapper<SubStanceManage>()
                    .eq(SubStanceManage::getIsValid,1)
                    .eq(SubStanceManage::getSubStanceTypeId, a.getId())));
        });
        return new PageUtils(page);
    }

    @Override
    public Long insertSubStanceType(SubStanceType request) {
        SubStanceType entity = (SubStanceType) DataTransfer.transfer(request, SubStanceType.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        entity.setCreatorName(userCompany.getUserName());
        entity.setCreatorCode(userCompany.getUserCode());
        entity.setCreationTime(DateUtils.dateTimeNew(new Date()));
        entity.setIsValid(1);
        this.save(entity);
        return request.getId();
    }

    @Override
    public Boolean updateSubStanceType(SubStanceType request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        SubStanceType entity = (SubStanceType) DataTransfer.transfer(request, SubStanceType.class);
        entity.setReviseCode(userCompany.getUserCode());
        entity.setReviseName(userCompany.getUserName());
        entity.setReviseTime(DateUtils.dateTimeNew(new Date()));
        return this.saveOrUpdate(entity);
    }

    @Override
    public Boolean deleteSubStanceType(Long id) {

        SubStanceType byId = this.getById(id);
        if (byId.getIsValid().equals(0)) {
            byId.setIsValid(1);
        } else {
            byId.setIsValid(0);
        }
        return this.saveOrUpdate(byId);
    }

    @Override
    public String exportSubStanceType(SubStanceType entity, PageEntity pageEntity) {
        List<SubStanceTypeExportVo> labelExportVos = new ArrayList<>();
        pageEntity.setPageIndex(1);
        pageEntity.setPageSize(9999);
        LambdaQueryWrapper<SubStanceType> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtils.isEmpty(entity.getId())) {
            queryWrapper.eq(SubStanceType::getIsValid, entity.getIsValid());
        }
        if (!ObjectUtils.isEmpty(entity.getTypeName())) {
            queryWrapper.like(SubStanceType::getTypeName, entity.getTypeName());
        }
        if (!ObjectUtils.isEmpty(entity.getMarkId())) {
            queryWrapper.like(SubStanceType::getMarkId, entity.getMarkId());
        }
        if (!ObjectUtils.isEmpty(entity.getId())) {
            queryWrapper.eq(SubStanceType::getId, entity.getId());
        }
        if (!ObjectUtils.isEmpty(entity.getIsShow())) {
            queryWrapper.eq(SubStanceType::getIsShow, entity.getIsShow());
        }
        IPage<SubStanceType> page = this.page(
                new Query<SubStanceType>().getPage(pageEntity), queryWrapper);
        page.getRecords().forEach(a -> {
            SubStanceTypeExportVo subStanceTypeExportVo = new SubStanceTypeExportVo();
            subStanceTypeExportVo.setName(a.getTypeName());
            subStanceTypeExportVo.setType(a.getMarkId().equals(1) ? "发现" : "内容");
            subStanceTypeExportVo.setNum(subStanceManageMapper.selectCount(new LambdaQueryWrapper<SubStanceManage>()
                    .eq(SubStanceManage::getSubStanceTypeId, a.getId())));
            subStanceTypeExportVo.setCode(a.getSort());
            subStanceTypeExportVo.setSort(a.getHomeSortRule().equals(1) ? "随机排序" : a.getHomeSortRule().equals(2) ?
                    "时间倒序" : a.getHomeSortRule().equals(3) ? "阅读数" : "序号");
            subStanceTypeExportVo.setHomeNum(a.getHomeNum());
            subStanceTypeExportVo.setIsShowHome(a.getIsShowHome().equals(1) ? "展示" : "不展示");
            subStanceTypeExportVo.setIsShowChange(a.getIsShowChange().equals(1) ? "展示" : "不展示");
            subStanceTypeExportVo.setIsShow(a.getIsShow().equals(1) ? "展示" : "不展示");
            labelExportVos.add(subStanceTypeExportVo);
        });
        try {
            ExcelUtil<SubStanceTypeExportVo> util = new ExcelUtil<SubStanceTypeExportVo>(SubStanceTypeExportVo.class);
            String fileName = util.encodingDateFilename("内容分类列表");
            InputStream inputStream = util.exportExcelFile(labelExportVos, fileName);
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "basicdata", fileName);
            return fileName + ".xlsx," + uploadFile.getUrl();
        } catch (Exception e) {
            log.error("内容分类导出上传异常");
            return "内容分类导出上传异常";
        }
    }

    @Override
    public PageUtils getSubStanceTypeAPP(SubStanceType request, PageEntity pageEntity) {
        LambdaQueryWrapper<SubStanceType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubStanceType::getIsShow,1);
        queryWrapper.eq(SubStanceType::getIsValid,1);
        queryWrapper.eq(SubStanceType::getPutInPlatform,256);
        queryWrapper.eq(SubStanceType::getIsShowHome,1);
        IPage<SubStanceType> page = this.page(
                new Query<SubStanceType>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }
}
