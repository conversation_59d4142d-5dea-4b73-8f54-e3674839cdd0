package com.fzkj.project.system.controller;


import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.domain.SysUser;
import com.fzkj.project.system.service.ISysUserService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(tags = "用戶管理")
@RequestMapping("/user")
@RequiredArgsConstructor
public class SysUserController {

    private final ISysUserService sysUserService;
    /**
     * 查询用户列表
     * @param request
     * @return
     */
    @PostMapping("list")
    public AjaxResult list(@RequestBody CommonRequest request) {
        SysUser user = (SysUser) request.getRequest(SysUser.class);
        List<SysUser> list = sysUserService.list(user);
        return AjaxResult.success(list);
    }

    /**
     * 根据条件分页查询用户列表
     * @param request
     * @return
     */
    @PostMapping("listByPage")
    public AjaxResult listByPage(@RequestBody CommonRequest request) {
        SysUser user = (SysUser) request.getRequest(SysUser.class);
        PageUtils pageUtils = sysUserService.listByPage(user, request.getPageEntity());
        return AjaxResult.success(pageUtils);
    }

}
