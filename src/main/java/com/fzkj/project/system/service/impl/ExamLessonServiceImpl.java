package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.ExamLesson;
import com.fzkj.project.system.vo.ExamLessonVO;
import com.fzkj.project.system.request.ExamLessonRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ExamLessonMapper;
import com.fzkj.project.system.service.ExamLessonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 考试课程关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExamLessonServiceImpl extends ServiceImpl<ExamLessonMapper, ExamLesson> implements ExamLessonService {

    @Override
    public List<ExamLesson> listExamLesson(ExamLessonRequest request) {
        ExamLesson entity = (ExamLesson) DataTransfer.transfer(request, ExamLesson.class);
        LambdaQueryWrapper<ExamLesson> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ExamLessonRequest request, PageEntity pageEntity) {
        ExamLesson entity = (ExamLesson) DataTransfer.transfer(request, ExamLesson.class);
        LambdaQueryWrapper<ExamLesson> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<ExamLesson> page = this.page(
                new Query<ExamLesson>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ExamLessonVO findExamLesson(Long id) {
        ExamLesson entity = this.getById(id);
        ExamLessonVO vo = (ExamLessonVO) DataTransfer.transfer(entity, ExamLessonVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExamLesson(ExamLessonVO vo) {
        ExamLesson entity = (ExamLesson) DataTransfer.transfer(vo, ExamLesson.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamLesson(ExamLessonVO vo) {
        ExamLesson entity = (ExamLesson) DataTransfer.transfer(vo, ExamLesson.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExamLesson(Long id) {
        return this.removeById(id);
    }
}