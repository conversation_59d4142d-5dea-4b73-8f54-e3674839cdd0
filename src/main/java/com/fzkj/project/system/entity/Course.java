package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 课件信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_course")
@ApiModel(value = "课件信息表实体")
public class Course implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "课件名称")
    @JsonProperty("CourseName")
    private String courseName;

    @ApiModelProperty(value = "课件分类ID")
    @JsonProperty("CategoryID")
    private Integer categoryId;

    @ApiModelProperty(value = "分类名称")
    @JsonProperty("CategoryName")
    private String categoryName;

    @ApiModelProperty(value = "讲师编号")
    @JsonProperty("TearcherCode")
    private String tearcherCode;

    @ApiModelProperty(value = "讲师名称")
    @JsonProperty("TearcherName")
    private String tearcherName;

    @ApiModelProperty(value = "课件介绍")
    @JsonProperty("Summary")
    private String summary;

    @ApiModelProperty(value = "课件内容")
    @JsonProperty("Content")
    private String content;

    @ApiModelProperty(value = "音频内容 0:输入音频内容(默认) 1:上传音频内容")
    @JsonProperty("AudioType")
    private Integer audioType;

    @ApiModelProperty(value = "视频内容 （上传音频内容 当audio_type=1时 改字段写入值）")
    @JsonProperty("VideoContent")
    private String videoContent;

    @ApiModelProperty(value = "课件封面图")
    @JsonProperty("ImgUrl")
    private String imgUrl;

    @ApiModelProperty(value = "课件URL")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "课件大小")
    @JsonProperty("FileSize")
    private Long fileSize;

    @ApiModelProperty(value = "课件时长")
    @JsonProperty("TimeCount")
    private Long timeCount;

    @ApiModelProperty(value = "课件类型（1：视频，2：文件，3：图文，4：音频）")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(value = "文件ID")
    @JsonProperty("FileID")
    private String fileId;

    @ApiModelProperty(value = "课件所属来源 （1：平台 2：企业）")
    @JsonProperty("BelongPlat")
    private Integer belongPlat;

    @ApiModelProperty(value = "是否显示给企业查看（1：显示 2：不显示）")
    @JsonProperty("IsShow")
    private Integer isShow;

    @ApiModelProperty(value = "创建人姓名")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "修改人姓名")
    @JsonProperty("ReviseName")
    private String reviseName;

    @ApiModelProperty(value = "来源（1：行业版）")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1是 0否 默认有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1（文件名称：上传文件的文件名称）")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2（学习人次）")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3 （上传音频内容 当audio_type=1时 改字段写上传的文件名）")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @JsonProperty("ReservedField5")
    private String reservedField5;


    @ApiModelProperty(value = "二级标题")
    @JsonProperty("SecondTitle")
    private String secondTitle;

    @ApiModelProperty(value = "编码")
    @JsonProperty("courseCode")
    private String courseCode;
}