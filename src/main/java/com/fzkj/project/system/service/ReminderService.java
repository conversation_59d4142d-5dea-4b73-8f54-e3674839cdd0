package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.Reminder;
import com.fzkj.project.system.vo.ReminderVO;
import com.fzkj.project.system.request.ReminderRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 提醒管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-04
 */
public interface ReminderService extends IService<Reminder> {

    List<Reminder> listReminder(ReminderRequest request);

    PageUtils listByPage(ReminderRequest request, PageEntity pageEntity);

    ReminderVO findReminder(Long id);

    List<Reminder> findReminderByTrainType(Integer trainType);

    Reminder findReminderByTrainTypAndRemindType(Integer trainType, Integer remindType);

    Long saveReminder(ReminderVO vo);

    boolean updateReminder(ReminderVO vo);

    boolean removeReminder(Long id);
}
