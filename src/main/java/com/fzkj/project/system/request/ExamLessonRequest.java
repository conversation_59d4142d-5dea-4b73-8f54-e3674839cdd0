package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试课程关联表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value = "考试课程关联表Request")
public class ExamLessonRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "考试ID")
    private Long examId;

    @ApiModelProperty(value = "课程ID")
    private Long lessonId;

    @ApiModelProperty(value = "来源")
    private String source;

    @JsonProperty("Flag")
    private String flag;
}