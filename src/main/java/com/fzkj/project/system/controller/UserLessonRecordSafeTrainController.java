package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.request.LessonSafeTrainCompanyUserTemplateRequest;
import com.fzkj.project.system.service.UserLessonRecordSafeTrainMongoService;
import com.fzkj.project.system.vo.UserLessonRecordSafeTrainVO;
import com.fzkj.project.system.request.UserLessonRecordSafeTrainRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 安全培训课程分发学员学习记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "userLessonRecordSafeTrain")
public class UserLessonRecordSafeTrainController extends BaseEntity {

    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;
    @PostMapping("Get_PayUserList")
    @ApiOperation(value = "培训设置-查询参培学员列表")
    public AjaxResult Get_PayUserList(@RequestBody CommonRequest commonRequest) {
        UserLessonRecordSafeTrainVO request =
                (UserLessonRecordSafeTrainVO) commonRequest.getRequest(UserLessonRecordSafeTrainVO.class);
        return AjaxResult.success(userLessonRecordSafeTrainMongoService.getPayUserList(request,
                commonRequest.getPageEntity()));
    }
}
