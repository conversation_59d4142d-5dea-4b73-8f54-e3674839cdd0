package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.project.system.entity.UserCompany;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.vo.UserCompanyVO;
import com.fzkj.project.system.request.UserCompanyRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.UserCompanyMapper;
import com.fzkj.project.system.service.UserCompanyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserCompanyServiceImpl extends ServiceImpl<UserCompanyMapper, UserCompany> implements UserCompanyService {

    @Override
    public List<UserCompany> listUserCompany(UserCompanyRequest request) {
        UserCompany entity = (UserCompany) DataTransfer.transfer(request, UserCompany.class);
        LambdaQueryWrapper<UserCompany> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(UserCompanyRequest request, PageEntity pageEntity) {
        UserCompany entity = (UserCompany) DataTransfer.transfer(request, UserCompany.class);
        LambdaQueryWrapper<UserCompany> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<UserCompany> page = this.page(
                new Query<UserCompany>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public UserCompanyVO findUserCompany(Long id) {
        UserCompany entity = this.getById(id);
        UserCompanyVO vo = (UserCompanyVO) DataTransfer.transfer(entity, UserCompanyVO.class);
        return vo;
    }

    @Override
    public UserCompany selectUserCompany(UserInfo loginUser) {
        return this.getOne(new LambdaQueryWrapper<UserCompany>().eq(UserCompany::getUserCode,loginUser.getUserCode()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveUserCompany(UserCompanyVO vo) {
        UserCompany entity = (UserCompany) DataTransfer.transfer(vo, UserCompany.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserCompany(UserCompanyVO vo) {
        UserCompany entity = (UserCompany) DataTransfer.transfer(vo, UserCompany.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserCompany(Long id) {
        return this.removeById(id);
    }
}