package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 安全培训课程分发学员学习记录
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "安全培训课程分发学员学习记录Request")
public class UserLessonRecordSafeTrainRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "企业ID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "部门ID")
    private Long departId;

    @ApiModelProperty(value = "岗位ID")
    private Long workId;

    @ApiModelProperty(value = "部门名称")
    private String departName;

    @ApiModelProperty(value = "岗位名称")
    private String workName;

    @ApiModelProperty(value = "课程分类ID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课程分类名称")
    private String lessonCategoryName;

    @ApiModelProperty(value = "课程ID")
    private Long lessonId;

    @ApiModelProperty(value = "课程月份")
    private String lessonDate;

    @ApiModelProperty(value = "课程名称")
    private String lessonName;

    @ApiModelProperty(value = "课件数量")
    private Integer lessonCourseCount;

    @ApiModelProperty(value = "试卷ID")
    private Long examId;

    @ApiModelProperty(value = "考试分数")
    private double score;

    @ApiModelProperty(value = "课程形式")
    private Integer shape;

    @ApiModelProperty(value = "用户可补考次数，默认1次")
    private Integer resitNumber;

    @ApiModelProperty(value = "学习状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）")
    private Integer isComplete;

    @ApiModelProperty(value = "课程统计状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）")
    private Integer staticStatus;

    @ApiModelProperty(value = "开始学习时间")
    private String startStudyTime;

    @ApiModelProperty(value = "学习完成时间")
    private String completeTime;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "用户头像")
    private String userPhoto;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "是否作为统计对象")
    private Integer isStatic;

    @ApiModelProperty(value = "课程支付类型：2：学员免费；3:学员付费")
    private Integer isPayType;

    @ApiModelProperty(value = "课程支付状态：0：未支付；1：已支付")
    private Integer isPay;

    @ApiModelProperty(value = "培训开始时间")
    private String trainStartTime;

    @ApiModelProperty(value = "培训结束时间")
    private String trainEndTime;

    @ApiModelProperty(value = "签名图片")
    private String signImg;

    @ApiModelProperty(value = "培训时长")
    private Long trainTimeCount;

    @ApiModelProperty(value = "地区编码")
    private String areaCode;

    @ApiModelProperty(value = "地区名称")
    private String areaName;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    private String reviseCode;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "是否有效，1：是，0：否，默认有效")
    private Integer isValid;

    @ApiModelProperty(value = "分发来源，1：售后服务端分发的，0：用户自己购买分发的")
    private Integer disSource;

    @ApiModelProperty(value = "参培课程（1：通用课程，2：定制课程）")
    private Integer handMode;

    @ApiModelProperty(value = "预留字段1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    private String reservedField5;

}