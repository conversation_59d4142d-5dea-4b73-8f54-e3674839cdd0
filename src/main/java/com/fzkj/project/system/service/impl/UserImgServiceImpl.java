package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.PlatformMenu;
import com.fzkj.project.system.entity.UserImg;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.mapper.UserImgMapper;
import com.fzkj.project.system.service.UserImgService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserImgServiceImpl extends ServiceImpl<UserImgMapper, UserImg> implements UserImgService {

    @Override
    public PageUtils getUserImg(UserImg userImg, PageEntity pageEntity) {
        UserInfo loginUser = SecurityUtils.getLoginUser().getUser();
        LambdaQueryWrapper<UserImg> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserImg::getUserCode,loginUser.getUserCode());
        lambdaQueryWrapper.eq(UserImg::getType,userImg.getType());
        lambdaQueryWrapper.eq(UserImg::getIsValid,1);
        IPage<UserImg> page = this.page(
                new Query<UserImg>().getPage(pageEntity), lambdaQueryWrapper);
        return new PageUtils(page);
    }

    @Override
    public AjaxResult editUserImg(UserImg userImg) {
        if(userImg.getFlag().equals("add")){
            userImg.setIsValid(1);
            userImg.setCreationTime(LocalDateTime.now());
            return AjaxResult.success(this.baseMapper.insert(userImg));
        }else if(userImg.getFlag().equals("del")){
            return AjaxResult.success(this.baseMapper.deleteById(userImg.getId()));
        }else{
            return AjaxResult.error("请求类型错误","");
        }
    }
}