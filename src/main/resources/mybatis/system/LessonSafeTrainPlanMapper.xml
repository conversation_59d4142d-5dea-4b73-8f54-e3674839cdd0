<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.LessonSafeTrainPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.LessonSafeTrainPlan">
        <id column="id" property="id" />
        <result column="plan_name" property="planName" />
        <result column="month_str" property="monthStr" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="reserved_field1" property="reservedField1" />
        <result column="reserved_field2" property="reservedField2" />
        <result column="reserved_field3" property="reservedField3" />
        <result column="reserved_field4" property="reservedField4" />
        <result column="reserved_field5" property="reservedField5" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, plan_name, month_str, source, sort, creator_code, creation_time, revise_code, revise_time, remark, is_valid, reserved_field1, reserved_field2, reserved_field3, reserved_field4, reserved_field5
    </sql>
    <select id="getBusSafeTrainList" resultType="com.fzkj.project.system.vo.LessonSafeTrainPlanVO">
        select p.*,
        (select count(1) from t_d_lesson_safe_train_plan_relationship r where r.plan_id = p.id and r.is_valid = 1)as lessonNum
        from t_d_lesson_safe_train_plan p
        <where>
            is_valid = 1 and month_str = #{lessonMonth}
            <if test="keyWords != null and keyWords != ''">
                AND p.plan_name like concat('%', #{keyWords}, '%')
            </if>
        </where>
        order by p.id desc
    </select>
    <select id="selectPlanLessonTotalTime" resultType="com.fzkj.project.system.vo.LessonTimeCountVO">
        SELECT
        t.lesson_id,
        CONCAT(
        LPAD(FLOOR(totalTimeCount / 3600), 2, '0'), '时',
        LPAD(FLOOR(totalTimeCount % 3600 / 60), 2, '0'), '分',
        LPAD(FLOOR(totalTimeCount % 3600 % 60), 2, '0'), '秒'
        ) AS totalTimeCount
        FROM (
        SELECT lesson_id, (
        SELECT IFNULL(SUM(time_count), 0)
        FROM t_d_course
        WHERE id IN (
        SELECT course_id
        FROM t_d_lesson_course_safe_train
        WHERE is_valid = 1 AND lesson_id = s.lesson_id
        )
        ) AS totalTimeCount
        FROM t_d_lesson_safe_train_plan_relationship s
        WHERE is_valid = 1 AND plan_id = #{planId}
        ) t
    </select>
    <select id="selectCompanyPlanTime" resultType="com.fzkj.project.system.vo.CompanyPlanTimeVO">
        select
        c.parent_id companyId,min(s_time)sTime,max(e_time)eTime
        from t_d_lesson_safe_train_company sc
        left join t_d_lesson_safe_train l on sc.lesson_id = l.id
        left join t_d_lesson_safe_train_plan_relationship r on l.id = r.lesson_id
        left join (select id,case when parent_id=543 or parent_id=0 then id else parent_id end parent_id from t_d_company where id in
            <foreach item="id" collection="parentIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        ) c on sc.company_id = c.id
        where r.plan_id = #{planId} and r.is_valid =1 and l.is_valid =1 and sc.is_valid =1 and sc.company_id in
        <foreach item="id" collection="parentIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by c.parent_id
    </select>
    <select id="getSafeTrainCompanyStatistics" resultType="com.fzkj.project.system.vo.SafeTrainCompanyStatisticsVO">
        select
        lstc.lesson_id,
        c.area_name,
        lstc.company_name,
        lstc.company_id,
        l.lesson_name,
        l.lesson_category_name,
        l.lesson_category_id,
        lstc.hand_mode
        from
        t_d_lesson_safe_train_company lstc
        left join t_d_company c on
        c.id = lstc.company_id
        left join t_d_lesson_safe_train l on
        l.id = lstc.lesson_id
        where
        lstc.is_valid = 1
        <if test="companyId != null">
            and (lstc.company_id = #{companyId} )
        </if>
        AND SUBSTR(l.lesson_date, 1, 7) = SUBSTR(#{lessonMonth}, 1, 7)
    </select>
    <select id="getSafeTrainCompanyStatistics2" resultType="com.fzkj.project.system.vo.SafeTrainCompanyStatisticsVO">
        SELECT
        l.lesson_date,
        l.s_time,
        lstc.lesson_id,
        c.area_name,
        lstc.company_name,
        lstc.company_id,
        l.lesson_name,
        l.lesson_category_name,
        l.e_time,
        0 totalUserNum,
        0 cxUserNum,
        0 wcUserNum,
        '0%' cxl,
        '0%' wcl
        FROM
        t_d_lesson_safe_train_company lstc
        LEFT JOIN t_d_company c ON
        c.id = lstc.company_id
        LEFT JOIN t_d_lesson_safe_train l ON
        l.id = lstc.lesson_id
        WHERE
        lstc.is_valid = 1
        <if test="companyId != null">
            and (lstc.company_id = #{companyId} )
        </if>
        AND SUBSTR(l.lesson_date, 1, 7) = SUBSTR(#{lessonMonth}, 1, 7)
        AND DATEDIFF(CURDATE(),l.s_time)>=0
    </select>

</mapper>