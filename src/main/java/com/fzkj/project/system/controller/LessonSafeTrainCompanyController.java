package com.fzkj.project.system.controller;

import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.LessonSafeTrainCompanyService;
import com.fzkj.project.system.vo.LessonSafeTrainCompanyVO;
import com.fzkj.project.system.request.LessonSafeTrainCompanyRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 给企业分发的安全培训课程关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "lessonSafeTrainCompany")
@Log
public class LessonSafeTrainCompanyController extends BaseEntity {

    private final LessonSafeTrainCompanyService LessonSafeTrainCompanyServiceImpl;


}
