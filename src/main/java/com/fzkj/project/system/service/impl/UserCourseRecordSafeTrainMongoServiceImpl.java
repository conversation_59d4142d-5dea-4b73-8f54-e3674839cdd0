package com.fzkj.project.system.service.impl;

import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.mongo.UserCourseRecordSafeTrain;
import com.fzkj.project.system.request.EditCompleteCourseRequest;
import com.fzkj.project.system.request.EditUserLessonRecordSignRequest;
import com.fzkj.project.system.request.GetCompanySafeTrainFaceRecordRequest;
import com.fzkj.project.system.service.UserCourseRecordSafeTrainMongoService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.fzkj.common.utils.MongoQueryUtil.*;

@Service
@RequiredArgsConstructor
public class UserCourseRecordSafeTrainMongoServiceImpl implements UserCourseRecordSafeTrainMongoService {

    public static final String TABLE_SAFE_TRAIN_USER_COURSE = "_tableSafeTrainUserCourse";
    @Autowired
    @Qualifier("mongoTemplateMaster")
    private MongoTemplate mongoTemplateMaster;

    @Autowired
    @Qualifier("mongoTemplateHistory")
    private MongoTemplate mongoTemplateHistory;

    @Override
    public void delUserCourse(Long lessonId, List<String> userCodes) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        Query query = new Query();
        query.addCriteria(Criteria.where("userCode").in(userCodes).and("lessonId").is(lessonId).and("isValid").is(1));
        Update update = new Update();
        update.set("isValid", 0);
        update.set("reviseCode", user.getUserCode());
        update.set("reviseTime", now);
        // 执行批量更新操作
        mongoTemplateMaster.updateMulti(query, update, UserCourseRecordSafeTrain.class);
        mongoTemplateHistory.updateMulti(query, update, UserCourseRecordSafeTrain.class);
    }

    @Override
    public void delUserCourses(String userCode) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        Query query = new Query();
        query.addCriteria(Criteria.where("userCode").is(userCode)
                .and("isValid").is(1));
        Update update = new Update();
        update.set("isValid", 0);
        update.set("reviseCode", user.getUserCode());
        update.set("reviseTime", now);
        // 执行批量更新操作
        mongoTemplateMaster.updateMulti(query, update, UserCourseRecordSafeTrain.class);
        mongoTemplateHistory.updateMulti(query, update, UserCourseRecordSafeTrain.class);
    }

    @Override
    public List<UserCourseRecordSafeTrain> getUserCourse(UserCourseRecordSafeTrain request) {
        List<String> userCodes = null;
        if (null != request.getUserCodes()) {
            userCodes = Arrays.asList(request.getUserCodes().split(","));
        }
        List<AggregationOperation> other = Arrays.asList(
                getConditionalStage("userCode", userCodes),
                getConditionalStage("departId", request.getDepartIds())
        );
        Aggregation aggregation = buildAggregationFromRequest(request, other);
        return mergeList(mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_COURSE, UserCourseRecordSafeTrain.class).getMappedResults(),
                mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_COURSE, UserCourseRecordSafeTrain.class).getMappedResults());
    }

    @Override
    public int completeUserCourse(EditCompleteCourseRequest request) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userCode").is(request.getUserCode())
                .and("courseId").is(request.getCourseId())
                .and("lessonId").is(request.getLessonId())
                .and("courseIsComplete").in(Arrays.asList(0, 3))
                .and("isValid").is(1));
        Update update = new Update();
        update.set("courseIsComplete", request.getIsComplete());
        update.set("studyTimeCount", request.getNowStudyTimeCount());
        update.set("completeTime", LocalDateTime.now());
        return (int) (mongoTemplateMaster.updateMulti(query, update, UserCourseRecordSafeTrain.class).getMatchedCount() + mongoTemplateHistory.updateMulti(query, update, UserCourseRecordSafeTrain.class).getMatchedCount());
    }

    @Override
    public long selectCompleteNum(EditCompleteCourseRequest request) {
        Query query = new Query();
        query.addCriteria(Criteria.where("isValid").is(1).and("userCode").is(request.getUserCode()).and("courseIsComplete").in(Arrays.asList(1, 2, 4)).and("lessonId").is(request.getLessonId()));
        return mongoTemplateMaster.count(query, UserCourseRecordSafeTrain.class)+ mongoTemplateHistory.count(query, UserCourseRecordSafeTrain.class);
    }

    @Override
    public int selectLearnNum(Long courseId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("isValid").is(1).and("courseIsComplete").ne(-3).and("courseId").is(courseId));
        // 查询记录条数
        long count1 = mongoTemplateMaster.count(query, UserCourseRecordSafeTrain.class);
        long count2 = mongoTemplateHistory.count(query, UserCourseRecordSafeTrain.class);
        return (int) (count1 + count2);
    }

    @Override
    public boolean updateUserCourse(UserCourseRecordSafeTrain userCourseRecordSafeTrain) {
        // 创建查询条件
        Query query = new Query();
        query.addCriteria(Criteria.where("id").is(userCourseRecordSafeTrain.getId()));
        // 根据 ID 查询记录
        UserCourseRecordSafeTrain userCourse = mongoTemplateMaster.findOne(query, UserCourseRecordSafeTrain.class);
        if (userCourse == null) {
            mongoTemplateHistory.save(userCourseRecordSafeTrain);
        } else {
            mongoTemplateMaster.save(userCourseRecordSafeTrain);
        }
        return true;
    }

    @Override
    public void recoveryUserCourse(Long lessonId, String userCodes) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        Query query = new Query();
        query.addCriteria(Criteria.where("isValid").is(0).and("lessonId").is(lessonId).and("userCode").in(userCodes.split(",")));
        Update update = new Update();
        update.set("isValid", 1);
        update.set("reviseCode", user.getUserCode());
        update.set("reviseTime", LocalDateTime.now());
        mongoTemplateMaster.updateMulti(query, update, UserCourseRecordSafeTrain.class);
        mongoTemplateHistory.updateMulti(query, update, UserCourseRecordSafeTrain.class);
    }

    @Override
    public void save(UserCourseRecordSafeTrain courseRecordSafeTrain) {
        mongoTemplateMaster.save(courseRecordSafeTrain);
    }

    @Override
    public List<UserCourseRecordSafeTrain> getUserCourseFaceMap(GetCompanySafeTrainFaceRecordRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("lessonId").is(request.getLessonId())
                        .and("companyId").is(request.getCompanyId())),
                getConditionalStage("departId", request.getSubDeptIds()),
                Aggregation.project("userCode", "faceDistinguishImg")
        );
        List<UserCourseRecordSafeTrain> tableSafeTrainUserCourse = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_COURSE, UserCourseRecordSafeTrain.class).getMappedResults();
        List<UserCourseRecordSafeTrain> tableSafeTrainUserCoursePre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_COURSE, UserCourseRecordSafeTrain.class).getMappedResults();
        return mergeList(tableSafeTrainUserCourse, tableSafeTrainUserCoursePre);
    }

    @Override
    public long countCourse(EditUserLessonRecordSignRequest request) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userCode").is(request.getUserCode()).and("isValid").is(1)
                .and("courseIsComplete").in(1,2,4).and("lessonId").is(request.getLessonId()));
        // 查询记录条数
        return mongoTemplateMaster.count(query, UserCourseRecordSafeTrain.class)+mongoTemplateHistory.count(query, UserCourseRecordSafeTrain.class);
    }

    @Override
    public void updateCourseRecord(Long companyId, Long departId, String companyName, String userCode) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userCode").is(userCode));
        Update update = new Update();
        update.set("departId", departId);
        if(null != companyId){
            update.set("companyId", companyId);
            update.set("companyName", companyName);
        }
        mongoTemplateMaster.updateMulti(query, update, UserCourseRecordSafeTrain.class);
    }

    @Override
    public long getLearnNum(List<Long> courseIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("courseId").in(courseIds));
        // 查询记录条数
        return mongoTemplateMaster.count(query, UserCourseRecordSafeTrain.class) + mongoTemplateHistory.count(query, UserCourseRecordSafeTrain.class);
    }
}
