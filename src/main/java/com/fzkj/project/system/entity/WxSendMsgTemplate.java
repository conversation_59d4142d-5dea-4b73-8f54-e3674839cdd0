package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("t_d_wx_send_msg_template")
@ApiModel(value = "WxSendMsgTemplate", description = "微信发送消息模板表")
public class WxSendMsgTemplate {

    @TableId(value = "template_id",  type = IdType.AUTO)
    @JsonProperty("TemplateID")
    @ApiModelProperty(value = "模板ID")
    private String templateId;

    @TableField("code")
    @JsonProperty("Code")
    @ApiModelProperty(value = "模板代码")
    private String code;

    @TableField("template_title")
    @JsonProperty("TemplateTitle")
    @ApiModelProperty(value = "模板标题")
    private String templateTitle;

    @TableField("template_content")
    @JsonProperty("TemplateContent")
    @ApiModelProperty(value = "模板内容")
    private String templateContent;

    @TableField("app_id")
    @JsonProperty("AppID")
    @ApiModelProperty(value = "应用ID")
    private String appId;

    @TableField("is_valid")
    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;
}
