package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.ExamUserRecord;
import com.fzkj.project.system.vo.ExamUserRecordVO;
import com.fzkj.project.system.request.ExamUserRecordRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ExamUserRecordMapper;
import com.fzkj.project.system.service.ExamUserRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户考试记录表（正式） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExamUserRecordServiceImpl extends ServiceImpl<ExamUserRecordMapper, ExamUserRecord> implements ExamUserRecordService {

    @Override
    public List<ExamUserRecord> listExamUserRecord(ExamUserRecordRequest request) {
        ExamUserRecord entity = (ExamUserRecord) DataTransfer.transfer(request, ExamUserRecord.class);
        LambdaQueryWrapper<ExamUserRecord> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ExamUserRecordRequest request, PageEntity pageEntity) {
        ExamUserRecord entity = (ExamUserRecord) DataTransfer.transfer(request, ExamUserRecord.class);
        LambdaQueryWrapper<ExamUserRecord> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<ExamUserRecord> page = this.page(
                new Query<ExamUserRecord>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ExamUserRecordVO findExamUserRecord(Long id) {
        ExamUserRecord entity = this.getById(id);
        ExamUserRecordVO vo = (ExamUserRecordVO) DataTransfer.transfer(entity, ExamUserRecordVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExamUserRecord(ExamUserRecordVO vo) {
        ExamUserRecord entity = (ExamUserRecord) DataTransfer.transfer(vo, ExamUserRecord.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamUserRecord(ExamUserRecordVO vo) {
        ExamUserRecord entity = (ExamUserRecord) DataTransfer.transfer(vo, ExamUserRecord.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExamUserRecord(Long id) {
        return this.removeById(id);
    }
}