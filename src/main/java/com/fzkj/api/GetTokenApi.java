package com.fzkj.api;

import com.alibaba.fastjson.JSON;
import com.fzkj.api.entity.vo.ErpAccessVo;
import com.fzkj.api.utils.HttpClientUtils;
import com.fzkj.api.utils.SecuritySHA1Utils;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @BelongsProject: fkxt
 * @BelongsPackage: com.fzkj.api
 * @Author: liuweijun
 * @CreateTime: 2022-05-19  10:45
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
public class GetTokenApi {
    private static final String URL = "http://*************:7031/platform/api/v1.0/passport/access?";

    private static final String APP_ID = "cqgj_fengkong";

    private static final String APP_SECERET = "442534DC5FA44D3DA14A9613ACB90C6A";


    public String getToken() {
        String url = getUrl();
        String result = HttpClientUtils.doPost(url);
        ErpAccessVo access = JSON.parseObject(result, ErpAccessVo.class);
        if (access!=null&&access.getErrorCode()==0){
            return access.getToken();
        }else {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String date = format.format(new Date());
            log.error(date+"请求access错误："+access.getErrorDesc()+"请求url："+url);
        }
        return null;
    }

    private String getUrl(){
        String timestamp = getDateToTimestamp();
        List<String> list = new ArrayList();
        String random = String.valueOf((int) (Math.random() * 100000) % 100);
        list.add(APP_SECERET);
        list.add(timestamp);
        list.add(random);
        Collections.sort(list);
        StringBuffer sb = new StringBuffer();
        for (int i=0;i<list.size();i++){
            sb.append(list.get(i));
        }
        String signature = SecuritySHA1Utils.getHmacSha1(sb.toString(),APP_SECERET);
        String url = URL+"appid="+APP_ID+"&nonce="+random+"&timestamp="+timestamp+"&signature="+signature;
        return  url;
    }

    private String getDateToTimestamp(){
        TimeZone timeZone = TimeZone.getTimeZone("GMT+08:00");
        Calendar calendarTime = Calendar.getInstance();
        calendarTime.setTimeZone(timeZone);
        calendarTime.add(Calendar.HOUR,8);
        return String.valueOf(calendarTime.getTimeInMillis()/1000);
    }
}
