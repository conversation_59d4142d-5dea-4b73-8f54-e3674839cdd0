package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetUserSurveyRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "用户编码")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "开始月份")
    @JsonProperty("StartMonth")
    private String startMonth;

    @ApiModelProperty(value = "结束月份")
    @JsonProperty("EndMonth")
    private String endMonth;

    @ApiModelProperty(value = "课程统计状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）")
    @JsonProperty("StaticStatus")
    private Integer staticStatus;

    @ApiModelProperty(value = "记录状态")
    @JsonProperty("IsValid")
    private Integer isValid;
}
