package com.fzkj.project.system.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@TableName("t_d_lesson_safe_train")
@ApiModel(value = "培训课程名称对应id表")
public class lessonSafeTrainTd {

    @JsonProperty("userID")
    private  String userId;

    @JsonProperty("signUrl")
    private String signUrl;

    @JsonProperty("lessonId")
    private Long lessonId;


}
