<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.PlatformMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.PlatformMenu">
        <id column="id" property="id" />
        <result column="fid" property="fid" />
        <result column="name" property="name" />
        <result column="url" property="url" />
        <result column="authority" property="authority" />
        <result column="icon" property="icon" />
        <result column="menu_class" property="menuClass" />
        <result column="menu_type" property="menuType" />
        <result column="oper_platform" property="operPlatform" />
        <result column="is_show" property="isShow" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fid, name, url, authority, icon, menu_class, menu_type, oper_platform, is_show, source, sort, creator_code, creation_time, revise_code, revise_time, remark, is_valid
    </sql>


    <select id="platformMenuList" resultType="java.lang.String">
        SELECT
             menu_id
        FROM
            t_d_platform_role_menu
        WHERE
        role_id IN ( SELECT role_id FROM t_d_user_role WHERE
        oper_platform = #{platform}
         AND user_code = #{userCode}
         AND is_valid = 1)
    </select>
</mapper>