package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.ExamQuestion;
import com.fzkj.project.system.vo.ExamQuestionVO;
import com.fzkj.project.system.request.ExamQuestionRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 考试试题表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExamQuestionService extends IService<ExamQuestion> {

    List<ExamQuestion> listExamQuestion(ExamQuestionRequest request);

    PageUtils listByPage(ExamQuestionRequest request, PageEntity pageEntity);

    ExamQuestionVO findExamQuestion(Long id);

    Long saveExamQuestion(ExamQuestionVO vo);

    boolean updateExamQuestion(ExamQuestionVO vo);

    boolean removeExamQuestion(Long id);
}
