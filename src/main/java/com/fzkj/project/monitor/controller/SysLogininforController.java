package com.fzkj.project.monitor.controller;

import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.aspectj.lang.enums.BusinessType;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.monitor.domain.SysLogininfor;
import com.fzkj.project.monitor.service.ISysLogininforService;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统访问记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLogininforController{
    @Autowired
    private ISysLogininforService logininforService;

    @PostMapping("/list")
    public AjaxResult list(@RequestBody CommonRequest request) {
        SysLogininfor logininfor = (SysLogininfor) request.getRequest(SysLogininfor.class);
        PageHelper.startPage(request.getPageIndex(), request.getPageSize());
        List list = logininforService.selectLogininforList(logininfor);
        return AjaxResult.success(new PageUtils(request.getPageEntity(), list));
    }

    @Log(title = "登录日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(SysLogininfor logininfor) {
        List list = logininforService.selectLogininforList(logininfor);
        ExcelUtil<SysLogininfor> util = new ExcelUtil<SysLogininfor>(SysLogininfor.class);
        return util.exportExcel(list, "登录日志");
    }


}
