package com.fzkj.project.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.Course;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.vo.*;

import java.util.List;

/**
 * <p>
 * 课件信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CourseService extends IService<Course> {

    List<Course> listCourse(CourseRequest request);

    PageUtils listByPage(CourseQueryRequest request, PageEntity pageEntity);

    CourseVO findCourse(Long id);

    Long saveCourse(CourseVO vo);

    boolean updateCourse(CourseVO vo);

    boolean removeCourse(Long id);

    CourseStatisticsVO courseStatistics();

    PageUtils getCourse(CourseQueryRequest request, PageEntity pageEntity);

    List<GetCourseListVO> getCourseList(GetCourseListRequest request);

    boolean editCompleteCourse(EditCompleteCourseRequest request);

    PageUtils getCommentAPP(GetCommentAPPRequest request, PageEntity pageEntity);

    List<GetUserCourseDetailVO> getUserCourseDetail(GetUserCourseDetailRequest request);

    String editUserCourseFirst(EditUserCourseFirstRequest request);

    int textModeration(String text);

    boolean editUserCourseFaceImg(EditUserCourseFaceImgRequest request);

    boolean CheckCourseCode(EditCompleteCourseRequest request);
}
