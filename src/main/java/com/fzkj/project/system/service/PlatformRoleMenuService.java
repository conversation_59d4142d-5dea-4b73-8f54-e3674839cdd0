package com.fzkj.project.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.PlatformRoleMenu;

import java.util.List;

public interface PlatformRoleMenuService extends IService<PlatformRoleMenu> {

    /**
     * 查询角色菜单关联
     *
     * @param id 角色菜单关联主键
     * @return 角色菜单关联
     */
     PlatformRoleMenu selectPlatformRoleMenuById(Long id);

    /**
     * 查询角色菜单关联列表
     *
     * @param tDPlatformRoleMenu 角色菜单关联
     * @return 角色菜单关联集合
     */
    PageUtils selectPlatformRoleMenuList(PlatformRoleMenu tDPlatformRoleMenu, PageEntity pageEntity);

    /**
     * 新增角色菜单关联
     *
     * @param tDPlatformRoleMenu 角色菜单关联
     * @return 结果
     */
     Long editPlatformRoleMenu(PlatformRoleMenu tDPlatformRoleMenu);

    /**
     * 删除角色菜单关联信息
     *
     * @param id 角色菜单关联主键
     * @return 结果
     */
    Long deletePlatformRoleMenuById(Long id);

    Long updateRoleMenu(PlatformRoleMenu vo);
}
