package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 安全培训课程分类表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_train_type")
@ApiModel(value = "安全培训课程分类表实体")
public class TrainType implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "父级ID")
    @JsonProperty("FID")
    private Long fid;

    @ApiModelProperty(value = "名称")
    @JsonProperty("TrainTypeName")
    private String trainTypeName;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;
}