package com.fzkj;

import com.fzkj.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class ImportTestVo {

    @Excel(name = "分公司")
    private String depart;
    @Excel(name = "岗位")
    private String job;
    @Excel(name = "姓名")
    private String name;
    @Excel(name = "身份证号")
    private String idCard;
    @Excel(name = "手机号码")
    private String phone;
    @Excel(name = "工号")
    private String jobNo;

    private String enterprise;
}
