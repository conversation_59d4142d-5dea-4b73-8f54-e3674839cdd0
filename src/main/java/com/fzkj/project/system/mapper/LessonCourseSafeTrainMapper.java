package com.fzkj.project.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.entity.LessonCourseSafeTrain;
import com.fzkj.project.system.vo.CourseRecordVO;
import com.fzkj.project.system.vo.CourseVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 安全培训课程课件关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface LessonCourseSafeTrainMapper extends BaseMapper<LessonCourseSafeTrain> {

    List<CourseVO> selectCourseListByLessonId(Long lessonId);

    List<CourseRecordVO> selectCourseRecord(Map<String, Object> params);

    List<String> checkCourseCode(String courseCode);
}