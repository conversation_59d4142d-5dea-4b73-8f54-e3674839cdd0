package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.UserCourseRecordSafeTrainLog;
import com.fzkj.project.system.entity.UserCourseRecordSafeTrainLogAll;
import com.fzkj.project.system.mapper.UserCourseRecordSafeTrainLogAllMapper;
import com.fzkj.project.system.mapper.UserCourseRecordSafeTrainLogMapper;
import com.fzkj.project.system.request.AddUserCourseLogRequest;
import com.fzkj.project.system.request.GetUserCourseStudyTimeRequest;
import com.fzkj.project.system.request.UserCourseRecordSafeTrainLogRequest;
import com.fzkj.project.system.service.UserCourseRecordSafeTrainLogService;
import com.fzkj.project.system.vo.GetUserCourseStudyTimeVO;
import com.fzkj.project.system.vo.UserCourseRecordSafeTrainLogVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

/**
 * <p>
 * 安全培训课件分发学员学习记录日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserCourseRecordSafeTrainLogServiceImpl extends ServiceImpl<UserCourseRecordSafeTrainLogMapper, UserCourseRecordSafeTrainLog> implements UserCourseRecordSafeTrainLogService {
    private final UserCourseRecordSafeTrainLogAllMapper userCourseRecordSafeTrainLogAllMapper;

    @Override
    public List<UserCourseRecordSafeTrainLog> listUserCourseRecordSafeTrainLog(UserCourseRecordSafeTrainLogRequest request) {
        UserCourseRecordSafeTrainLog entity = (UserCourseRecordSafeTrainLog) DataTransfer.transfer(request, UserCourseRecordSafeTrainLog.class);
        LambdaQueryWrapper<UserCourseRecordSafeTrainLog> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(UserCourseRecordSafeTrainLogRequest request, PageEntity pageEntity) {
        UserCourseRecordSafeTrainLog entity = (UserCourseRecordSafeTrainLog) DataTransfer.transfer(request, UserCourseRecordSafeTrainLog.class);
        LambdaQueryWrapper<UserCourseRecordSafeTrainLog> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<UserCourseRecordSafeTrainLog> page = this.page(
                new Query<UserCourseRecordSafeTrainLog>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public UserCourseRecordSafeTrainLogVO findUserCourseRecordSafeTrainLog(Long id) {
        UserCourseRecordSafeTrainLog entity = this.getById(id);
        UserCourseRecordSafeTrainLogVO vo = (UserCourseRecordSafeTrainLogVO) DataTransfer.transfer(entity, UserCourseRecordSafeTrainLogVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveUserCourseRecordSafeTrainLog(UserCourseRecordSafeTrainLogVO vo) {
        UserCourseRecordSafeTrainLog entity = (UserCourseRecordSafeTrainLog) DataTransfer.transfer(vo, UserCourseRecordSafeTrainLog.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserCourseRecordSafeTrainLog(UserCourseRecordSafeTrainLogVO vo) {
        UserCourseRecordSafeTrainLog entity = (UserCourseRecordSafeTrainLog) DataTransfer.transfer(vo, UserCourseRecordSafeTrainLog.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserCourseRecordSafeTrainLog(Long id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addUserCourseLog(AddUserCourseLogRequest request) {
        LocalDateTime now = LocalDateTime.now();
        //添加一条日志记录（总表）
        UserCourseRecordSafeTrainLogAll logAll = new UserCourseRecordSafeTrainLogAll();
        logAll.setOrganizationId(request.getOrganizationId());
        logAll.setStudyTimeCount(request.getStudyTimeCount());
        logAll.setReviseTime(now);
        logAll.setUserCode(request.getUserCode());
        logAll.setLessonId(request.getLessonId());
        logAll.setCourseId(request.getCourseId());
        logAll.setRecordType(request.getRecordType());
        //记录类型：1-开始学习，2-学习中，3-人脸识别，4-完成学习
        logAll.setRecordTypeStr(request.getRecordType() == 1 ? "开始学习" : request.getRecordType() == 2 ? "学习中" : request.getRecordType() == 3 ? "人脸识别" : request.getRecordType() == 4 ? "完成学习" : "其他类型");
        logAll.setIsValid(1);
        logAll.setCreationTime(now);
        logAll.setSource(request.getTrainType());
        userCourseRecordSafeTrainLogAllMapper.insert(logAll);
        //查询日志表是否有相关数据
        LambdaQueryWrapper<UserCourseRecordSafeTrainLog> queryWrapper = Wrappers.lambdaQuery(UserCourseRecordSafeTrainLog.class)
                .eq(UserCourseRecordSafeTrainLog::getUserCode, request.getUserCode())
                .eq(UserCourseRecordSafeTrainLog::getCourseId, request.getCourseId())
                .eq(UserCourseRecordSafeTrainLog::getLessonId, request.getLessonId())
                .eq(UserCourseRecordSafeTrainLog::getOrganizationId, request.getOrganizationId())
                .eq(UserCourseRecordSafeTrainLog::getIsValid, 1);
        List<UserCourseRecordSafeTrainLog> logs = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(logs)) {
            logs.forEach(item -> {
                item.setStudyTimeCount(request.getStudyTimeCount());
                item.setReviseTime(now);
            });
            boolean batchById = this.updateBatchById(logs);
            if (!batchById) {
                throw new CustomException(2004, "课件时长修改失败");
            }
        } else {
            UserCourseRecordSafeTrainLog log = new UserCourseRecordSafeTrainLog();
            log.setOrganizationId(request.getOrganizationId());
            log.setStudyTimeCount(request.getStudyTimeCount());
            log.setReviseTime(now);
            log.setUserCode(request.getUserCode());
            log.setLessonId(request.getLessonId());
            log.setCourseId(request.getCourseId());
            log.setIsValid(1);
            log.setCreationTime(now);
            return this.save(log);
        }
        return true;
    }

    @Override
    public GetUserCourseStudyTimeVO getUserCourseStudyTime(GetUserCourseStudyTimeRequest request) {
        GetUserCourseStudyTimeVO vo = new GetUserCourseStudyTimeVO();
        LambdaQueryWrapper<UserCourseRecordSafeTrainLog> queryWrapper = Wrappers.lambdaQuery(UserCourseRecordSafeTrainLog.class);
        queryWrapper.eq(UserCourseRecordSafeTrainLog::getUserCode,request.getUserCode());
        queryWrapper.eq(UserCourseRecordSafeTrainLog::getIsValid,1);
        queryWrapper.eq(UserCourseRecordSafeTrainLog::getLessonId,request.getLessonId());
        queryWrapper.eq(UserCourseRecordSafeTrainLog::getCourseId,request.getCourseId());
        queryWrapper.eq(UserCourseRecordSafeTrainLog::getOrganizationId,request.getOrganizationId());
        List<UserCourseRecordSafeTrainLog> courseRecordSafeTrains = list(queryWrapper);
        if (CollectionUtils.isNotEmpty(courseRecordSafeTrains)) {
            courseRecordSafeTrains.stream().max(Comparator.comparing(UserCourseRecordSafeTrainLog::getStudyTimeCount)).ifPresent(userCourseRecordSafeTrainLog -> vo.setStudyTimeCount(userCourseRecordSafeTrainLog.getStudyTimeCount()));
        }
        return vo;
    }
}