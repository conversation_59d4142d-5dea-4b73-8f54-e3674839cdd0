// service
package com.fzkj.project.wxPay.service;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.project.wxPay.dao.WxPayDto;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public interface WxPayService {

    String payBack(String resXml);

    Map doUnifiedOrder(HttpServletRequest request, WxPayDto wxPayDto) throws Exception;

    Boolean payOk(WxPayDto wxPayDto);


    AjaxResult wxRefund(WxPayDto wxPayDto);
}

