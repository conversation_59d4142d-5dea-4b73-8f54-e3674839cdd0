package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CourseCategoryQueryVO {
    //\"id\":17,\"CategoryName\":\"测试\",\"sort\":1,\"CreationTime\":\"2024-03-04T14:01:59.73\",\"ReviseTime\":\"2024-03-04T14:05:05.637\",\"IsShow\":1,\"CreatorName\":\"公交\",\"ReviseName\":\"公交\",\"CourseNum\":0}

    @ApiModelProperty("课件分类主键ID")
    @JsonProperty("id")
    private int id;

    @ApiModelProperty("课件分类名称")
    @JsonProperty("CategoryName")
    private int categoryName;

    @ApiModelProperty("排序")
    @JsonProperty("sort")
    private int sort;

    @ApiModelProperty("创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty("修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty("是否显示")
    @JsonProperty("IsShow")
    private int isShow;

    @ApiModelProperty("创建人")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty("修改人")
    @JsonProperty("ReviseName")
    private String reviseName;

    @ApiModelProperty("课件数量")
    @JsonProperty("CourseNum")
    private int courseNum;

}
