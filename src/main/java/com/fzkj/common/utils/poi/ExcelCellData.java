package com.fzkj.common.utils.poi;

import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
public class ExcelCellData {
    private String content;
    private String style ="formatVal";
    private int column = 1;
    private short width = 0;
    private short height = 0;
    private String imageUrl;  // 图片 URL（用于处理图片插入）

    public ExcelCellData(){}
    public ExcelCellData(String content){
        this.content = content;
    }
    public ExcelCellData(String content,String style){
        this.content = content;
        this.style = style;
    }
    public ExcelCellData(String content,String style,short width){
        this.content = content;
        this.style = style;
        this.width = width;
    }
    public ExcelCellData(String content,String style,short width,short height){
        this.content = content;
        this.style = style;
        this.width = width;
        this.height = height;
    }
    public ExcelCellData(String content,int column){
        this.content = content;
        this.column = column;
    }

    // 新增构造方法：用来处理图片URL
    public ExcelCellData( int column,String imageUrl) {
        this.imageUrl = imageUrl;
        this.column = column;
    }
    public ExcelCellData(String content,String style,int column){
        this.content = content;
        this.style = style;
        this.column = column;
    }
    public ExcelCellData(String content,String style,int column,short width){
        this.content = content;
        this.style = style;
        this.column = column;
        this.width = width;
    }
    public ExcelCellData(String content,String style,int column,short width,short height){
        this.content = content;
        this.style = style;
        this.column = column;
        this.width = width;
        this.height = height;
    }
}
