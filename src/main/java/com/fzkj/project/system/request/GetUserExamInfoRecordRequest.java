package com.fzkj.project.system.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetUserExamInfoRecordRequest {

    @ApiModelProperty(value = "用户编码")
    private String userId;
    @ApiModelProperty(value = "课程ID")
    private String lessonId;
    @ApiModelProperty(value = "开始日期")
    private String sDate;
    @ApiModelProperty(value = "结束日期")
    private String eDate;
    @ApiModelProperty(value = "操作标识")
    private Integer flag;
    @ApiModelProperty(value = "考试ID")
    private Long examId;

}
