package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 考试信息表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@TableName("t_d_exam")
@ApiModel(value = "考试信息表实体")
public class Exam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "考试名称")
    @JsonProperty("ExamName")
    private String examName;

    @ApiModelProperty(value = "题目数量")
    @JsonProperty("QuestionCount")
    private Integer questionCount;

    @ApiModelProperty(value = "考试时长")
    @JsonProperty("TimeCount")
    private Integer timeCount;

    @ApiModelProperty(value = "考试总分")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty(value = "及格分数")
    @JsonProperty("PassMark")
    private Integer passMark;

    @ApiModelProperty(value = "考试重复次数")
    @JsonProperty("ResitNumber")
    private Integer resitNumber;

    @ApiModelProperty(value = "分组排考类型")
    @JsonProperty("GroupRollType")
    private Integer groupRollType;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @JsonProperty("CreatorID")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @JsonProperty("ReviseID")
    private String reviseId;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效：0：无效，1：有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "组织ID")
    @JsonProperty("OrgID")
    private String orgId;

    @ApiModelProperty(value = "来源")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "考试签名状态：0：禁用，1：启用")
    @JsonProperty("ExamSignState")
    private Integer examSignState;

    @ApiModelProperty(value = "考试人像识别状态：0：禁用，1：启用")
    @JsonProperty("ExamFaceState")
    private Integer examFaceState;

    @ApiModelProperty(value = "考试倒计时时长")
    @JsonProperty("ExamCountDownTime")
    private Integer examCountDownTime;

    @ApiModelProperty(value = "考试识别失败的次数")
    @JsonProperty("ExamFailRepeatNum")
    private Integer examFailRepeatNum;

    @ApiModelProperty(value = "是否提示答案：0：不提示 1：提示")
    @JsonProperty("IsPromptAnswer")
    private Integer isPromptAnswer;

    @ApiModelProperty(value = "是否需要模拟考试：0：不需要 1：需要")
    @JsonProperty("IsMockExam")
    private Integer isMockExam;

    @ApiModelProperty(value = "是否需要习题练习：0：不需要 1：需要")
    @JsonProperty("IsPractice")
    private Integer isPractice;

    @ApiModelProperty(value = "是否开启模拟考试人脸识别：0：不需要 1：需要")
    @JsonProperty("IsMockExamFace")
    private Integer isMockExamFace;


}