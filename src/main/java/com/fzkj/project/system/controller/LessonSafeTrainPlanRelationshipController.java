package com.fzkj.project.system.controller;

import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.LessonSafeTrainPlanRelationshipService;
import com.fzkj.project.system.vo.LessonSafeTrainPlanRelationshipVO;
import com.fzkj.project.system.request.LessonSafeTrainPlanRelationshipRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 给公交分发的培训课程关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "lessonSafeTrainPlanRelationship")
@ThirdPart
@Log
public class LessonSafeTrainPlanRelationshipController extends BaseEntity {

    private final LessonSafeTrainPlanRelationshipService LessonSafeTrainPlanRelationshipServiceImpl;

    @PostMapping("Edit_LessonSafeTrainPlanRelationship")
    @ApiOperation(value = "培训计划-设置课程")
    public AjaxResult Edit_LessonSafeTrainPlanRelationship(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainPlanRelationshipVO request = (LessonSafeTrainPlanRelationshipVO) commonRequest.getRequest(LessonSafeTrainPlanRelationshipVO.class);
        return AjaxResult.success(LessonSafeTrainPlanRelationshipServiceImpl.editLessonSafeTrainPlanRelationship(request));
    }

}
