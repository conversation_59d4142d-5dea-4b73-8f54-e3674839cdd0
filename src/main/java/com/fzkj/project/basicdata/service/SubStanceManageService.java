package com.fzkj.project.basicdata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.basicdata.entity.SubStanceManage;
import com.fzkj.project.basicdata.request.SubStanceManageRequest;
import com.fzkj.project.system.vo.SubStanceManageVO;

import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 13:52
 * @Version: 1.0
 */
public interface SubStanceManageService extends IService<SubStanceManage> {
    PageUtils listByPage(SubStanceManage request, PageEntity pageEntity);

    /**
     * 新增内容管理
     *
     * @param subStanceManage 内容管理
     * @return 结果
     */
    public Long insertSubStanceManage(SubStanceManage subStanceManage);

    /**
     * 修改内容管理
     *
     * @param subStanceManage 内容管理
     * @return 结果
     */
    public Boolean updateSubStanceManage(SubStanceManage subStanceManage);

    /**
     * 删除内容管理
     *
     * @param id 内容管理主键
     * @return 结果
     */
    public Boolean deleteSubStanceManage(Long id);


    PageUtils getHomeContentMobile(SubStanceManageRequest request, PageEntity pageEntity);

    Integer addSubStanceBrowsingRecord(SubStanceManageRequest request);

    List<SubStanceManage> getBanner();

    List<SubStanceManageVO> getSubStanceAPP(Long id);
}
