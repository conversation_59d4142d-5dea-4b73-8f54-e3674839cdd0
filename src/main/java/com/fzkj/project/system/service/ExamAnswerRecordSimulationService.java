package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.ExamAnswerRecordSimulation;
import com.fzkj.project.system.vo.ExamAnswerRecordSimulationVO;
import com.fzkj.project.system.request.ExamAnswerRecordSimulationRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户模拟考答题记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExamAnswerRecordSimulationService extends IService<ExamAnswerRecordSimulation> {

    List<ExamAnswerRecordSimulation> listExamAnswerRecordSimulation(ExamAnswerRecordSimulationRequest request);

    PageUtils listByPage(ExamAnswerRecordSimulationRequest request, PageEntity pageEntity);

    ExamAnswerRecordSimulationVO findExamAnswerRecordSimulation(Long id);

    Long saveExamAnswerRecordSimulation(ExamAnswerRecordSimulationVO vo);

    boolean updateExamAnswerRecordSimulation(ExamAnswerRecordSimulationVO vo);

    boolean removeExamAnswerRecordSimulation(Long id);
}
