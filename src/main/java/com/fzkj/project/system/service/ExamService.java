package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.Exam;
import com.fzkj.project.system.entity.ExamQuestion;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.vo.*;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 考试信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExamService extends IService<Exam> {

    PageUtils getExamInfo(ExamRequest request, PageEntity pageEntity);

    ExamVO findExam(Long id);

    Long saveExam(ExamVO vo);

    boolean updateExam(ExamVO vo);

    boolean removeExam(Long id);

    PageUtils getExamInfoQuestion(ExamRequest request, PageEntity pageEntity);

    Long editExamInfo(ExamVO request);

    boolean editExamLesson(ExamVO request);

    boolean editExamQuestion(ExamQuestionVO request);

    int importExamQuestion(ExamQuestionImportVO request);

    String editExamInfoIssueExam(ExamVO request);

    List<ExamQuestion> getGroupRoll(GroupRollRequest request);

    List<GetUserExamInfoVO> getUserExamInfo(GetUserExamInfoRequest request);

    List<GetUserExamInfoRecordVO> getUserExamInfoRecord(GetUserExamInfoRecordRequest request);

    boolean editExamSubMitAnswer1(EditExamSubMitAnswer1Request request);

    boolean authPortraitVerification(AuthPortraitVerificationRequest request);

    List<GetUserExamDetailRecordVO> getUserExamDetailRecord(GetUserExamDetailRecordRequest request);

    List<ExamFaceRecordVO> getExamFaceRecord(GetCompanySafeTrainFaceRecordRequest request);

    PageUtils getExamInfoQuestionByPage(ExamRequest request, PageEntity pageEntity);
}
