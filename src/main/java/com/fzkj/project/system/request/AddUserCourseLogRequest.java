package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AddUserCourseLogRequest {
    @JsonProperty("UserCode")
    private String userCode;

    @JsonProperty("LessonID")
    private Long lessonId;

    @JsonProperty("OrganizationID")
    private Long organizationId;

    @JsonProperty("CourseID")
    private Long courseId;

    @JsonProperty("StudyTimeCount")
    private Long studyTimeCount;

    @JsonProperty("RecordType")
    private Integer recordType;

    @JsonProperty("StartLearningTime")
    private String startLearningTime;

    @JsonProperty("TrainType")
    private String trainType;
}
