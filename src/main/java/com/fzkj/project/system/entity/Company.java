package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 企业表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_company")
@ApiModel(value = "企业表实体")
public class Company implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @JsonProperty("FID")
    @ApiModelProperty(value = "FID")
    private Long fid;

    @JsonProperty("ParentID")
    @ApiModelProperty(value = "parentID")
    private Long parentId;

    @JsonProperty("Ancestors")
    @ApiModelProperty(value = "parentID")
    private String ancestors;

    @ApiModelProperty(value = "组织编号")
    @JsonProperty("OrgCode")
    private String orgCode;

    @JsonProperty("CompanyName")
    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @JsonProperty("DeptType")
    @ApiModelProperty(value = "部门类型（1主营公司，2部门，3分公司，4其他，0集团）")
    private String deptType;

    @JsonProperty("AreaCode")
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    @JsonProperty("AreaName")
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @JsonProperty("AbbrName")
    @ApiModelProperty(value = "企业简称")
    private String abbrName;

    @JsonProperty("Address")
    @ApiModelProperty(value = "企业地址")
    private String address;

    @JsonProperty("Leader")
    @ApiModelProperty(value = "负责人")
    private String leader;

    @JsonProperty("Phone")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    @JsonProperty("Email")
    @ApiModelProperty(value = "企业邮箱")
    private String email;

    @JsonProperty("CompanyLabel")
    @ApiModelProperty(value = "企业标签")
    private String companyLabel;

    @JsonProperty("OperPlatform")
    @ApiModelProperty(value = "操作平台")
    private Integer operPlatform;

    @JsonProperty("Source")
    @ApiModelProperty(value = "数据来源")
    private String source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @TableField(exist = false)
    @JsonProperty("Children")
    private List<Company> children;
}