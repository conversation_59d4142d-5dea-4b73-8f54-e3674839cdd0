package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 点赞信息表
 *
 */
@Data
@TableName("t_d_like")
@ApiModel(value = "点赞信息表")
public class Like implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("FunType")
    @ApiModelProperty(value = "功能模块（10：课件 11：内容）")
    private Integer funType;

    @JsonProperty("UserCode")
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @JsonProperty("IDCard")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @JsonProperty("UserName")
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @JsonProperty("Phone")
    @ApiModelProperty(value = "手机号")
    private String phone;

    @JsonProperty("UserPhoto")
    @ApiModelProperty(value = "用户头像")
    private String userPhoto;

    @JsonProperty("TargetID")
    @ApiModelProperty(value = "目标id(如内容ID)")
    private Long targetId;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @JsonProperty("Source")
    @ApiModelProperty(value = "来源")
    private Integer source;

}