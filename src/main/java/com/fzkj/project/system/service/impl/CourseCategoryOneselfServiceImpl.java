package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.CourseCategoryOneself;
import com.fzkj.project.system.vo.CourseCategoryOneselfVO;
import com.fzkj.project.system.request.CourseCategoryOneselfRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.CourseCategoryOneselfMapper;
import com.fzkj.project.system.service.CourseCategoryOneselfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 企业自建课件分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CourseCategoryOneselfServiceImpl extends ServiceImpl<CourseCategoryOneselfMapper, CourseCategoryOneself> implements CourseCategoryOneselfService {

    @Override
    public List<CourseCategoryOneself> listCourseCategoryOneself(CourseCategoryOneselfRequest request) {
        CourseCategoryOneself entity = (CourseCategoryOneself) DataTransfer.transfer(request, CourseCategoryOneself.class);
        LambdaQueryWrapper<CourseCategoryOneself> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(CourseCategoryOneselfRequest request, PageEntity pageEntity) {
        CourseCategoryOneself entity = (CourseCategoryOneself) DataTransfer.transfer(request, CourseCategoryOneself.class);
        LambdaQueryWrapper<CourseCategoryOneself> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<CourseCategoryOneself> page = this.page(
                new Query<CourseCategoryOneself>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public CourseCategoryOneselfVO findCourseCategoryOneself(Long id) {
        CourseCategoryOneself entity = this.getById(id);
        CourseCategoryOneselfVO vo = (CourseCategoryOneselfVO) DataTransfer.transfer(entity, CourseCategoryOneselfVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCourseCategoryOneself(CourseCategoryOneselfVO vo) {
        CourseCategoryOneself entity = (CourseCategoryOneself) DataTransfer.transfer(vo, CourseCategoryOneself.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCourseCategoryOneself(CourseCategoryOneselfVO vo) {
        CourseCategoryOneself entity = (CourseCategoryOneself) DataTransfer.transfer(vo, CourseCategoryOneself.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCourseCategoryOneself(Long id) {
        return this.removeById(id);
    }
}