package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 给公交分发的培训课程关系表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_lesson_safe_train_plan_relationship")
@ApiModel(value = "给公交分发的培训课程关系表实体")
public class LessonSafeTrainPlanRelationship implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "计划ID（t_d_lessonsafetrainplan）")
    @JsonProperty("PlanID")
    private Long planId;

    @ApiModelProperty(value = "轨道课程ID（t_d_lessonsafetrain）")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "来源")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否 默认有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @JsonProperty("ReservedField5")
    private String reservedField5;


}