package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.UserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.vo.UserRoleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户关联角色 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRole> {

    List<UserRoleVO> selectUserRolePage(@Param(value = "keyWord") String keyWord,
                                        @Param(value = "roleId")Long roleId,
                                        @Param(value = "isValid")Integer isValid);

    Integer selectUserCodeByTargetId(Long departID, String userCode, int plat);
}