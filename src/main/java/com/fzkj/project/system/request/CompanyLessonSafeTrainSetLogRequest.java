package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 企业安全培训课程设置表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "企业安全培训课程设置表Request")
public class CompanyLessonSafeTrainSetLogRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "企业安全培训课程设置表ID（T_D_CompanyLessonSafeTrainSet）")
    private Long safeTrainSetId;

    @ApiModelProperty(value = "操作名称（修改，创建，增减）")
    private String operationName;

    @ApiModelProperty(value = "操作列名称")
    private String objectName;

    @ApiModelProperty(value = "操作列值")
    private String objectValues;

    @ApiModelProperty(value = "参培分类名称")
    private String lessonCategoryName;

    @ApiModelProperty(value = "创建人ID")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否 默认有效")
    private Integer isValid;

}