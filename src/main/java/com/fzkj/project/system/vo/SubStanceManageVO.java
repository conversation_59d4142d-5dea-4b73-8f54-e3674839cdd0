package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SubStanceManageVO implements Serializable {

    @JsonProperty("ID")
    private Long id;

    @JsonProperty("SubStanceTypeID")
    private Long subStanceTypeId;

    @JsonProperty("SubStanceTypeName")
    private String subStanceTypeName;

    @JsonProperty("SubStanceLabelIDs")
    private String subStanceLabelIds;

    @JsonProperty("SubStanceLabelQuery")
    private String subStanceLabelQuery;

    @JsonProperty("SubStanceLabelNames")
    private String subStanceLabelNames;

    @JsonProperty("SubStanceTitle")
    private String subStanceTitle;

    @JsonProperty("Summary")
    private String summary;

    @JsonProperty("Pictures")
    private String pictures;

    @JsonProperty("FileType")
    private Integer fileType;

    @JsonProperty("FileUrl")
    private String fileUrl;

    @JsonProperty("LinkUrl")
    private String linkUrl;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("ReleaseTime")
    private String releaseTime;

    @JsonProperty("IsLogin")
    private Integer isLogin;

    @JsonProperty("ReadNum")
    private Integer readNum;

    @JsonProperty("Sort")
    private Integer sort;

    @JsonProperty("CreatorCode")
    private String creatorCode;

    @JsonProperty("CreationTime")
    private String creationTime;

    @JsonProperty("ReviseCode")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    private String reviseTime;

    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("FileID")
    private String fileId;

    @JsonProperty("CreatorName")
    private String creatorName;

    @JsonProperty("ReviseName")
    private String reviseName;

    @JsonProperty("Author")
    private String author;

    @JsonProperty("OrganizationID")
    private Long organizationId;

    @JsonProperty("PutinPlatform")
    private Integer putinPlatform;

    @JsonProperty("PutinPlatformStr")
    private String putinPlatformStr;

    @JsonProperty("ReadCoefficient")
    private BigDecimal readCoefficient;

    @JsonProperty("IsOriginal")
    private Integer isOriginal;

    @JsonProperty("Flag")
    private String flag;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("CommentNum")
    private Integer commentNum;

    @JsonProperty("CollectionNum")
    private Integer collectionNum;

    @JsonProperty("LikeNum")
    private Integer likeNum;

    @JsonProperty("IsCollection")
    private Integer isCollection;

    @JsonProperty("IsLike")
    private Integer isLike;

    @JsonProperty("Duration")
    private String duration;
}