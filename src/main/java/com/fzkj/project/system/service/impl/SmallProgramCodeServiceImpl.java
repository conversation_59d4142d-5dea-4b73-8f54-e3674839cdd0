package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.project.system.entity.SmallProgramCode;
import com.fzkj.project.system.mapper.SmallProgramCodeMapper;
import com.fzkj.project.system.service.SmallProgramCodeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 小程序二维码 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-04
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class SmallProgramCodeServiceImpl extends ServiceImpl<SmallProgramCodeMapper, SmallProgramCode> implements SmallProgramCodeService {
}
