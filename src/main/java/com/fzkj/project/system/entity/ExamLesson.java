package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 考试课程关联表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@TableName("t_d_exam_lesson")
@ApiModel(value = "考试课程关联表实体")
public class ExamLesson implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "考试ID")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "来源")
    @JsonProperty("Source")
    private String source;


}