package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 安全培训课程分类表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "安全培训课程分类表Request")
public class TrainTypeRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "父级ID")
    private Long fid;

    @ApiModelProperty(value = "名称")
    private String trainTypeName;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @ApiModelProperty(value = "企业ID")
    private Long companyId ;

}