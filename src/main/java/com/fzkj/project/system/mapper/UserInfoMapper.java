package com.fzkj.project.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.vo.AuthUserInfoVO;
import com.fzkj.project.system.vo.UserInfoVO;
import com.fzkj.project.system.vo.UserManageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户登录信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface UserInfoMapper extends BaseMapper<UserInfo> {

    /**
     * 获取用户信息
     * @param userCode
     * @return
     */
    AuthUserInfoVO selectUserByUserCode(String userCode);

    /**
     * 根据账户名获取用户
     * @param username
     * @return
     */
    UserInfo selectUserByUserName(String username);

    AuthUserInfoVO selectUserByIdCard(String idCard);
    /**
     * 根据unionId和openId获取用户
     * @param unionId
     * @param openId
     * @return
     */
    UserInfo selectUserByUnionId(String unionId, String openId);

    /**
     * 获取用户登录信息
     * @param userCode
     * @return
     */
    UserInfoVO selectUserLoginInfoByUserCode(String userCode);

    /**
     * 查询用户列表分页
     *
     * @return
     */
    List<UserInfoVO> selectUserInfoPageList(@Param(value = "companyId") Long companyId,
                                            @Param(value = "departId") Long departId,
                                            @Param(value = "keyWord") String keyWord,
                                            @Param(value = "isOut") Integer isOut,
                                            @Param(value = "isBind") Integer isBind,
                                            @Param(value = "workId") Long workId);

    /**
     * 查询用户列表分页
     *
     * @return
     */
    List<UserInfoVO> getUserSelect(@Param(value = "userName") String userName,
                                   @Param(value = "companyId") Long companyId,
                                   @Param(value = "departId") Long departId,
                                   @Param(value = "workId") Long workId);

    /**
     * 查询用户列表分页
     *
     * @return
     */
    Integer clearPhotoByUserCode(@Param(value = "userCode") String userCode);

    List<UserInfoVO> selectUserInfoByUserCode(String userCode);

    List<UserManageVO> selectSendUserInfo(String month);

    List<UserManageVO> selectAllSendUserInfo();

    List<UserInfoVO> selectUserInfoAndCompanyInfoByPhoneOrIdCard(String phone,String idCard);
}