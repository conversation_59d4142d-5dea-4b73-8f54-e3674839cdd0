<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.PlatformRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.PlatformRole">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="role_name" property="roleName" />
        <result column="oper_platform" property="operPlatform" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creator_time" property="creatorTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="is_show" property="isShow" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, role_name, oper_platform, source, sort, creator_code, creator_time, revise_code, revise_time, remark, is_valid, is_show
    </sql>

    <select id="getPlatformRoleList" resultType="com.fzkj.project.system.vo.PlatformRoleVO">
        SELECT
        pr.id,
        pr.role_name roleName,
        pr.company_id companyId,
        pr.oper_platform operPlatform,
        pr.source,
        pr.sort,
        pr.creator_code creatorCode,
        pr.creator_time creatorTime,
        pr.revise_code reviseCode,
        pr.revise_time reviseTime,
        pr.remark,
        pr.is_valid isValid,
        pr.is_show isShow,
        dc.company_name,
        COUNT( dur.id ) personNum,
        rm.menu_id menuId,
        rm.remark showMenuID
        FROM
        t_d_platform_role pr
        LEFT JOIN t_d_company dc ON pr.company_id = dc.id
        LEFT JOIN t_d_user_role dur ON pr.id = dur.role_id
        LEFT JOIN t_d_platform_role_menu rm ON rm.role_id = pr.id
        <where>
            <if test="roleName != null and roleName != ''"> AND pr.role_name LIKE concat('%',#{roleName},'%')</if>
            <if test="isValid != null"> AND pr.is_valid = #{isValid}</if>
            <if test="operPlatform != null"> AND pr.oper_platform = #{operPlatform}</if>
            <if test="type != null and type ==0"> AND pr.company_id = -1</if>
            <if test="type != null and type ==1"> AND pr.company_id != -1</if>
        </where>
        GROUP BY
        pr.id
    </select>

    <select id="getPlatformRoleListByCompanyIdAndOper" resultType="com.fzkj.project.system.vo.PlatformRoleVO">
        SELECT
            *
        FROM
        t_d_platform_role pr
        <where>
            <if test="companyId != null"> AND (pr.company_id = #{companyId} OR pr.company_id = -1)</if>
            <if test="operPlatform != null"> AND pr.oper_platform = #{operPlatform}</if>
        </where>
    </select>

</mapper>