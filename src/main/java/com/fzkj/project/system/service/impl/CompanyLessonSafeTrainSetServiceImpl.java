package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.CompanyLessonSafeTrainSet;
import com.fzkj.project.system.entity.CompanyLessonSafeTrainSetLog;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.mapper.CompanyLessonSafeTrainSetMapper;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetQueryRequest;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetRequest;
import com.fzkj.project.system.service.CompanyLessonSafeTrainSetLogService;
import com.fzkj.project.system.service.CompanyLessonSafeTrainSetService;
import com.fzkj.project.system.service.CompanyService;
import com.fzkj.project.system.service.UserLessonRecordSafeTrainMongoService;
import com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 企业安全培训课程设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CompanyLessonSafeTrainSetServiceImpl extends ServiceImpl<CompanyLessonSafeTrainSetMapper, CompanyLessonSafeTrainSet> implements CompanyLessonSafeTrainSetService {

    private final CompanyLessonSafeTrainSetLogService companyLessonSafeTrainSetLogService;
    private final CompanyService companyService;
    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;

    @Override
    public PageUtils listCompanyLessonSafeTrainSet(CompanyLessonSafeTrainSetQueryRequest request, PageEntity pageEntity) {
        if(null != pageEntity){
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        List list = new ArrayList<>();
        switch (request.getFlag()) {
            case "list1":
                return new PageUtils(pageEntity, getLearnNum(getBaseMapper().selectCompanyLessonSafeTrainSetList1(request)));
            case "list2":
                return new PageUtils(pageEntity, getBaseMapper().selectCompanyLessonSafeTrainSetList2(request));
            case "list3":
                return new PageUtils(pageEntity, getBaseMapper().selectCompanyLessonSafeTrainSetList3(request));
            case "list4":
                return new PageUtils(pageEntity, getBaseMapper().selectCompanyLessonSafeTrainSetList4(request));
            case "byId":
                CompanyLessonSafeTrainSet companyLessonSafeTrainSet = getById(request.getId());
                if (null != companyLessonSafeTrainSet) {
                    list.add(companyLessonSafeTrainSet);
                }
                return new PageUtils(pageEntity, list);
        }
        return null;
    }

    private List<CompanyLessonSafeTrainSetVO> getLearnNum(List<CompanyLessonSafeTrainSetVO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (CompanyLessonSafeTrainSetVO vo : list) {
                Integer learnNum = userLessonRecordSafeTrainMongoService.getUserLessonCount(vo.getCompanyId(), vo.getLessonCategoryId());
                vo.setLearnNum(learnNum);
                vo.setETime(vo.getETime().length() > 7 ? vo.getETime().substring(0, 7) : vo.getETime());
                vo.setSTime(vo.getSTime().length() > 7 ? vo.getSTime().substring(0, 7) : vo.getSTime());
            }
        }
        return list;
    }

    @Override
    public PageUtils listByPage(CompanyLessonSafeTrainSetRequest request, PageEntity pageEntity) {
        CompanyLessonSafeTrainSet entity = (CompanyLessonSafeTrainSet) DataTransfer.transfer(request, CompanyLessonSafeTrainSet.class);
        LambdaQueryWrapper<CompanyLessonSafeTrainSet> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<CompanyLessonSafeTrainSet> page = this.page(
                new Query<CompanyLessonSafeTrainSet>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public CompanyLessonSafeTrainSetVO findCompanyLessonSafeTrainSet(Long id) {
        CompanyLessonSafeTrainSet entity = this.getById(id);
        CompanyLessonSafeTrainSetVO vo = (CompanyLessonSafeTrainSetVO) DataTransfer.transfer(entity, CompanyLessonSafeTrainSetVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCompanyLessonSafeTrainSet(CompanyLessonSafeTrainSetVO vo) {
        CompanyLessonSafeTrainSet entity = (CompanyLessonSafeTrainSet) DataTransfer.transfer(vo, CompanyLessonSafeTrainSet.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCompanyLessonSafeTrainSet(CompanyLessonSafeTrainSetVO vo) {
        CompanyLessonSafeTrainSet entity = (CompanyLessonSafeTrainSet) DataTransfer.transfer(vo, CompanyLessonSafeTrainSet.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCompanyLessonSafeTrainSet(Long id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editCompanyLessonSafeTrainSet(CompanyLessonSafeTrainSetRequest request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        CompanyLessonSafeTrainSetLog log = new CompanyLessonSafeTrainSetLog();
        log.setObjectName("t_d_company_lesson_safe_train_set");
        String operationValues = "参培分类：" + request.getLessonCategoryName() + "|参培学时：" + request.getTotalTimeCount() + "|参培人次：" + request.getPersonNum() + "|培训期始：" + request.getSTime() + "|培训期至：" + request.getETime()+")";
        log.setObjectValues(operationValues);
        log.setLessonCategoryName(request.getLessonCategoryName());
        log.setCreatorCode(user.getUserCode());
        log.setCreationTime(now);
        log.setCreatorName(user.getUserName());
        log.setRemark("");
        log.setIsValid(1);
        String sTime = request.getSTime();
        if (StringUtils.isNotEmpty(sTime) && sTime.length() < 10) {
            request.setSTime(sTime + "-01");
        }
        String eTime = request.getETime();
        if (StringUtils.isNotEmpty(eTime) && eTime.length() < 10) {
            request.setETime(DateUtils.getLastDayOfMonthAsString(eTime));
        }
        switch (request.getFlag()) {
            case "add":
                LambdaQueryWrapper<CompanyLessonSafeTrainSet> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CompanyLessonSafeTrainSet::getCompanyId, request.getCompanyId());
                queryWrapper.eq(CompanyLessonSafeTrainSet::getLessonCategoryId, request.getLessonCategoryId());
                queryWrapper.eq(CompanyLessonSafeTrainSet::getIsValid, 1);
                int existValidData = count(queryWrapper);
                if (existValidData > 0) {
                    throw new CustomException(1001, "操作失败，该企业的培训分类设置已存在");
                }
                LambdaQueryWrapper<CompanyLessonSafeTrainSet> queryWrapper2 = new LambdaQueryWrapper<>();
                queryWrapper2.eq(CompanyLessonSafeTrainSet::getCompanyId, request.getCompanyId());
                queryWrapper2.eq(CompanyLessonSafeTrainSet::getLessonCategoryId, request.getLessonCategoryId());
                queryWrapper2.eq(CompanyLessonSafeTrainSet::getIsValid, 0);
                int existInValidData = count(queryWrapper2);
                if (existInValidData > 0) {
                    throw new CustomException(1001, "新增失败，请恢复该企业的培训分类");
                }
                CompanyLessonSafeTrainSet entity = (CompanyLessonSafeTrainSet) DataTransfer.transfer(request, CompanyLessonSafeTrainSet.class);
                entity.setCreationTime(now);
                entity.setReviseCode(user.getUserCode());
                entity.setCreatorCode(user.getUserCode());
                entity.setReviseTime(now);
                boolean save = this.save(entity);
                if (save) {
                    //添加日志
                    log.setSafeTrainSetId(entity.getId());
                    log.setOperationName("添加方案（");
                    return companyLessonSafeTrainSetLogService.save(log);
                }
                break;
            case "edit":
                LambdaQueryWrapper<CompanyLessonSafeTrainSet> queryWrapper3 = new LambdaQueryWrapper<>();
                queryWrapper3.eq(CompanyLessonSafeTrainSet::getCompanyId, request.getCompanyId());
                queryWrapper3.eq(CompanyLessonSafeTrainSet::getLessonCategoryId, request.getLessonCategoryId());
                queryWrapper3.ne(CompanyLessonSafeTrainSet::getId, request.getId());
                int existOtherData = count(queryWrapper3);
                if (existOtherData > 0) {
                    throw new CustomException(1001, "操作失败，该企业的培训分类设置已存在");
                }
                CompanyLessonSafeTrainSet entity2 = (CompanyLessonSafeTrainSet) DataTransfer.transfer(request, CompanyLessonSafeTrainSet.class);
                entity2.setReviseCode(user.getUserCode());
                entity2.setReviseTime(now);
                boolean update = this.updateById(entity2);
                if (update) {
                    log.setSafeTrainSetId(entity2.getId());
                    //添加日志
                    log.setOperationName("修改方案（");
                    return companyLessonSafeTrainSetLogService.save(log);
                }
                break;
            case "del":
                LambdaUpdateWrapper<CompanyLessonSafeTrainSet> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(CompanyLessonSafeTrainSet::getId, request.getId());
                updateWrapper.set(CompanyLessonSafeTrainSet::getIsValid, 0);
                updateWrapper.set(CompanyLessonSafeTrainSet::getReviseCode, user.getUserCode());
                updateWrapper.set(CompanyLessonSafeTrainSet::getReviseTime, now);
                boolean del = this.update(updateWrapper);
                if (del) {
                    //添加日志
                    log.setSafeTrainSetId(request.getId());
                    log.setOperationName("删除方案（");
                    return companyLessonSafeTrainSetLogService.save(log);
                }
                break;
            case "recovery":
                LambdaUpdateWrapper<CompanyLessonSafeTrainSet> recoveryWrapper = new LambdaUpdateWrapper<>();
                recoveryWrapper.eq(CompanyLessonSafeTrainSet::getId, request.getId());
                recoveryWrapper.set(CompanyLessonSafeTrainSet::getIsValid, 1);
                recoveryWrapper.set(CompanyLessonSafeTrainSet::getReviseCode, user.getUserCode());
                recoveryWrapper.set(CompanyLessonSafeTrainSet::getReviseTime, now);
                boolean recovery = this.update(recoveryWrapper);
                if(recovery){
                    log.setSafeTrainSetId(request.getId());
                    log.setOperationName("恢复方案（");
                    return companyLessonSafeTrainSetLogService.save(log);
                }
                break;
            case "addPersonal":
                LambdaUpdateWrapper<CompanyLessonSafeTrainSet> addPersonWrapper = new LambdaUpdateWrapper<>();
                addPersonWrapper.setSql("person_num = person_num + " + request.getPersonNum())
                        .set(CompanyLessonSafeTrainSet::getReviseCode, user.getUserCode())
                        .set(CompanyLessonSafeTrainSet::getReviseTime, now)
                        .eq(CompanyLessonSafeTrainSet::getId,request.getId());
                // 执行更新操作
                boolean addPerson = this.update(addPersonWrapper);
                if(addPerson){
                    log.setSafeTrainSetId(request.getId());
                    log.setOperationName("增减人次（");
                    operationValues = "参培分类：" + request.getLessonCategoryName() + "|参培人次：" + (request.getPersonNum()>0?"+"+request.getPersonNum():request.getPersonNum()) +")";
                    log.setObjectValues(operationValues);
                    return companyLessonSafeTrainSetLogService.save(log);
                }
                break;
            default:
                break;
        }
        return false;
    }
}