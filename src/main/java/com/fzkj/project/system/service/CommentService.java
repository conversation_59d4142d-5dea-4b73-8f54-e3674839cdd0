package com.fzkj.project.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.Comment;
import com.fzkj.project.system.vo.CommentVO;

/**
 * <p>
 * 评论 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CommentService extends IService<Comment> {

    PageUtils getComment(CommentVO commentVO, PageEntity pageEntity);

    String exportComment(CommentVO commentVO);

    Integer updateComment(CommentVO commentVO);

    AjaxResult editComment(CommentVO commentVO,String funType);

    PageUtils getMyComment(CommentVO commentVO, PageEntity pageEntity);

    PageUtils getCommentAPP(CommentVO commentVO, PageEntity pageEntity);
}
