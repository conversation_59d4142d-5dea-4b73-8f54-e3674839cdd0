package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.CommentReply;
import com.fzkj.project.system.entity.Course;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.vo.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 课件信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface CourseMapper extends BaseMapper<Course> {

    List<CourseQueryVO> selectCourseInfo(CourseQueryRequest request);

    CourseStatisticsVO courseStatistics();

    List<CourseVO> getCourse(CourseQueryRequest request);

    List<GetCommentAPPVO> selectCommentAPP(GetCommentAPPRequest request);

    List<CommentReply> selectCommentReplayList(List<Long> targetIds);

    int selectTextModerationCount(String text);

    List<GetCourseListVO> selectCourseList(GetCourseListRequest request);

    List<LessonCourseSafeTrainInfoVO> selectLessonCourseSafeTrainInfo(EditUserCourseFirstRequest request);

    List<GetUserCourseDetailVO> selectUserCourseDetail(GetUserCourseDetailRequest request);
}