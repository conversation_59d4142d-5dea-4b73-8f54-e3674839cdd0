package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业自建课件分类
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_course_category_oneself")
@ApiModel(value = "企业自建课件分类实体")
public class CourseCategoryOneself implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "分类名称(企业自建课件分类)")
    @JsonProperty("CategoryName")
    private String categoryName;

    @ApiModelProperty(value = "是否显示（1：显示，0：未显示）")
    @JsonProperty("IsShow")
    private Integer isShow;

    @ApiModelProperty(value = "创建人姓名")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "修改人姓名")
    @JsonProperty("ReviseName")
    private String reviseName;

    @ApiModelProperty(value = "来源（1：行业版）")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1是 0否 默认有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @JsonProperty("ReservedField5")
    private String reservedField5;
}