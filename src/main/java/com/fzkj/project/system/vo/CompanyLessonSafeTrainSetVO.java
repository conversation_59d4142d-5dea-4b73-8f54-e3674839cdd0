package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.annotation.FormatField;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 企业安全培训课程设置表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="企业安全培训课程设置表VO")
public class CompanyLessonSafeTrainSetVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "企业ID（T_D_Company）")
    @NotNull(message = "企业ID（T_D_Company）不能为空")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    @NotNull(message = "企业名称不能为空")
    @JsonProperty("CompanyName")
    @Excel(name = "企业名称",order = 1)
    private String companyName;

    @ApiModelProperty(value = "课程分类")
    @NotNull(message = "课程分类不能为空")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课程分类名称")
    @NotNull(message = "课程分类名称不能为空")
    @Excel(name = "参培分类",order = 3)
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty(value = "参培课程（1：（通用课程）自动分发 2：（定制课程）手动分发）")
    @NotNull(message = "参培课程（1：（通用课程）自动分发 2：（定制课程）手动分发）不能为空")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "付费方式（课程支付类型）：1：余额支付（去掉）；2：包干（学员免费）；3:员工支付（学员付费）")
    @NotNull(message = "付费方式（课程支付类型）：1：余额支付（去掉）；2：包干（学员免费）；3:员工支付（学员付费）不能为空")
    @JsonProperty("IsPayType")
    private Integer isPayType;

    @ApiModelProperty(value = "员工支付价格（企业课程）")
    @NotNull(message = "员工支付价格（企业课程）不能为空")
    @JsonProperty("StaffPrice")
    private BigDecimal staffPrice;

    @ApiModelProperty(value = "课程时长（包含关联的所有课件时间）")
    @NotNull(message = "课程时长（包含关联的所有课件时间）不能为空")
    @JsonProperty("TotalTimeCount")
    @Excel(name = "参培学时",order = 4,suffix = "学时")
    private Integer totalTimeCount;

    @ApiModelProperty(value = "是否托管：1：开启0：不开启")
    @NotNull(message = "是否托管：1：开启0：不开启不能为空")
    @JsonProperty("IsTrusteeship")
    private Integer isTrusteeship;

    @ApiModelProperty(value = "参培人数")
    @NotNull(message = "参培人数不能为空")
    @Excel(name = "参培人次",order = 5)
    @JsonProperty("PersonNum")
    private Integer personNum;

    @ApiModelProperty(value = "有效期开始时间")
    @NotNull(message = "有效期开始时间不能为空")
    @JsonProperty("STime")
    @Excel(name = "培训始",order = 7)
    private String sTime;

    @ApiModelProperty(value = "有效期结束时间")
    @NotNull(message = "有效期结束时间不能为空")
    @JsonProperty("ETime")
    @Excel(name = "培训至",order = 8)
    private String eTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @NotNull(message = "创建人ID不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @NotNull(message = "修改人ID不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    @NotNull(message = "备注信息不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否 默认有效")
    @NotNull(message = "是否有效 1：是， 0：否 默认有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("AreaCode")
    private String areaCode;

//    @Excel(name = "地区",order = 2)
    @JsonProperty("AreaName")
    private String areaName;

    @JsonProperty("UserTemplate")
    private String userTemplate;

    @JsonProperty("LessonNames")
    private String lessonNames;

    @ApiModelProperty(value = "已学人数")
    @Excel(name = "已学人数",order = 6)
    @JsonProperty("LearnNum")
    private int learnNum;

    @ApiModelProperty(value = "状态")
    @Excel(name = "状态",order = 9,readConverterExp = "0=停用,1=正常")
    @JsonProperty("State")
    private int state;

    @ApiModelProperty(value = "企业设置ID")
    @JsonProperty("CompanyLessonPayID")
    private int companyLessonPayId;

    @ApiModelProperty(value = "分发学员数量")
    @JsonProperty("DisUserNum")
    private int disUserNum;

    @ApiModelProperty(value = "参学学员数量")
    @JsonProperty("CXUserNum")
    private int cxUserNum;

    @ApiModelProperty(value = "完成学员数量")
    @JsonProperty("WCUserNum")
    private int wcUserNum;


}