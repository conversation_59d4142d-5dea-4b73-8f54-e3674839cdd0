package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.security.handle.CustomParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@CustomParam
public class GetCompanySafeTrainFaceRecordRequest {

    @ApiModelProperty(value = "培训月份")
    @JsonProperty("LessonMonth")
    private String lessonMonth;

    @ApiModelProperty(value = "公司ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "部门ID")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "部门IDs")
    private List<Long> departIds;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "公司名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "统计范围1-按时学习 0-补学数据")
    @JsonProperty("LearnFlag")
    private Integer learnFlag;

    private List<Long> subDeptIds;

}
