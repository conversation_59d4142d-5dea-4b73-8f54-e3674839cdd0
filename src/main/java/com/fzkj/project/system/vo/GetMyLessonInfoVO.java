package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;

import java.time.LocalDateTime;

@Data
public class GetMyLessonInfoVO {
    @ApiModelProperty(value = "id")
    @JsonProperty("Mongo_ObjId")
    private ObjectId id;

    @ApiModelProperty(value = "培训类型", example = "aqpx")
    @JsonProperty("TrainType")
    private String trainType;

    @ApiModelProperty(value = "培训类型名称", example = "安全培训")
    @JsonProperty("TrainTypeName")
    private String trainTypeName;

    @ApiModelProperty(value = "计划ID", example = "-999")
    @JsonProperty("PlanID")
    private String planId;

    @ApiModelProperty(value = "课程ID", example = "4423")
    @JsonProperty("LessonID")
    private String lessonId;

    @ApiModelProperty(value = "课程名称", example = "市租投标演示课程")
    @JsonProperty("LessonName")
    private String lessonName;

    @ApiModelProperty(value = "培训周期", example = "2024年03月01日-2024年03月31日")
    @JsonProperty("TrainCycle")
    private String trainCycle;

    @ApiModelProperty(value = "学习人数", example = "0")
    @JsonProperty("StudyNum")
    private String studyNum;

    @ApiModelProperty(value = "培训状态", example = "1")
    @JsonProperty("StaticStatus")
    private int staticStatus;

    @ApiModelProperty(value = "是否支付", example = "1")
    @JsonProperty("IsPay")
    private String isPay;

    @ApiModelProperty(value = "补考次数")
    @JsonProperty("ResitNumber")
    private String resitNumber;

    @ApiModelProperty(value = "是否统计")
    @JsonProperty("IsStatic")
    private String isStatic;

    @ApiModelProperty(value = "是否完成")
    @JsonProperty("IsComplete")
    private String isComplete;

    @ApiModelProperty(value = "创建时间", example = "2024-02-29 15:32:23")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "培训状态1", example = "0")
    @JsonProperty("StaticStatus1")
    private int staticStatus1;

    @ApiModelProperty(value = "课件形式", example = "1")
    @JsonProperty("Shape")
    private Integer shape;

    @ApiModelProperty(value = "考试状态", example = "0")
    @JsonProperty("ExamStatus")
    private int examStatus;

    @ApiModelProperty(value = "是否完美", example = "0")
    @JsonProperty("IsPerfect")
    private int isPerfect;

    @ApiModelProperty(value = "区域代码")
    @JsonProperty("AreaCode")
    private String areaCode;
    @ApiModelProperty(value = "发票状态", example = "0")
    @JsonProperty("InvoiceStatus")
    private Integer invoiceStatus;

    @ApiModelProperty(value = "阶段", example = "1")
    @JsonProperty("Stage")
    private Integer stage;

    @ApiModelProperty(value = "培训开始时间", example = "2024-03-01")
    @JsonProperty("TrainStartTime")
    private String trainStartTime;

    @ApiModelProperty(value = "培训结束时间")
    @JsonProperty("TrainEndTime")
    private String trainEndTime;

    @ApiModelProperty(value = "计划级别", example = "0")
    @JsonProperty("PlanLevel")
    private int planLevel;

    @ApiModelProperty(value = "支付时间", example = "0001-01-01T00:00:00")
    @JsonProperty("PayDateTime")
    private String payDateTime;
}
