package com.fzkj.project.system.controller;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.entity.Course;
import com.fzkj.project.system.entity.LessonCourseSafeTrain;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.CourseService;
import com.fzkj.project.system.service.LessonCourseSafeTrainService;
import com.fzkj.project.system.vo.CourseStatisticsVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * <p>
 * 课件信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
@RequestMapping(value = "lessonApi")
@Log
public class CourseController extends BaseEntity {

    private final CourseService CourseServiceImpl;
    private final LessonCourseSafeTrainService lessonCourseSafeTrainService;
    private final RedisCache redisCache;

    @PostMapping("Get_Course_Inside")
    @ApiOperation(value = "课件信息表分页查询")
    public AjaxResult Get_Course_Inside(@RequestBody CommonRequest commonRequest) {
        CourseQueryRequest request = (CourseQueryRequest) commonRequest.getRequest(CourseQueryRequest.class);
        return AjaxResult.success(CourseServiceImpl.listByPage(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_CourseStatistics")
    @ApiOperation(value = "课件信息统计数据")
    public AjaxResult Get_CourseStatistics(@RequestBody CommonRequest commonRequest) {
        CourseStatisticsVO courseStatisticsVO = CourseServiceImpl.courseStatistics();
        return AjaxResult.success(JSON.toJSONString(Arrays.asList(courseStatisticsVO)));
    }

    @PostMapping("Edit_Course")
    @ApiOperation(value = "编辑课件")
    public AjaxResult Edit_Course(@RequestBody CommonRequest commonRequest) {
        CourseRequest request = (CourseRequest) commonRequest.getRequest(CourseRequest.class);
        Course entity = (Course) DataTransfer.transfer(request, Course.class);
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        String flag = request.getFlag();
        if ("add".equals(flag)) {
            entity.setCreatorCode(user.getUserCode());
            entity.setCreatorName(user.getUserName());
            entity.setCreationTime(now);
            CourseServiceImpl.save(entity);
            return AjaxResult.success("新增成功", entity.getId());
        }
        if ("edit".equals(flag)) {
            LambdaQueryWrapper<LessonCourseSafeTrain> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LessonCourseSafeTrain::getCourseId, request.getId());
            int count = lessonCourseSafeTrainService.count(queryWrapper);
            if (count > 0) {
                return AjaxResult.error(1001, "该课件已被使用，无法修改");
            }
            entity.setReviseCode(user.getUserCode());
            entity.setReviseName(user.getUserName());
            entity.setReviseTime(now);
            CourseServiceImpl.saveOrUpdate(entity);
            return AjaxResult.success("修改成功", entity.getId());
        }
        if ("del".equals(flag)) {
            LambdaQueryWrapper<LessonCourseSafeTrain> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LessonCourseSafeTrain::getCourseId, request.getId());
            int count = lessonCourseSafeTrainService.count(queryWrapper);
            if (count > 0) {
                return AjaxResult.error(1001, "该课件已被使用，无法删除");
            }
            LambdaUpdateWrapper<Course> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Course::getId, request.getId());
            updateWrapper.set(Course::getIsValid, 0);
            updateWrapper.set(Course::getReviseCode, user.getUserCode());
            updateWrapper.set(Course::getReviseName, user.getUserName());
            updateWrapper.set(Course::getReviseTime, now);
            boolean update = CourseServiceImpl.update(updateWrapper);
            if (update) {
                return AjaxResult.success("删除成功", null);
            }
            return AjaxResult.error("删除失败", null);
        }
        if ("recovery".equals(flag)) {
            LambdaUpdateWrapper<Course> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Course::getId, request.getId());
            updateWrapper.set(Course::getIsValid, 1);
            updateWrapper.set(Course::getReviseCode, user.getUserCode());
            updateWrapper.set(Course::getReviseName, user.getUserName());
            updateWrapper.set(Course::getReviseTime, now);
            boolean update = CourseServiceImpl.update(updateWrapper);
            if (update) {
                return AjaxResult.success("恢复成功", null);
            }
            return AjaxResult.error("恢复失败", null);
        }
        return AjaxResult.success();
    }

    @PostMapping("Get_Course")
    @ApiOperation(value = "课件信息表分页查询")
    public AjaxResult Get_Course(@RequestBody CommonRequest commonRequest) {
        CourseQueryRequest request = (CourseQueryRequest) commonRequest.getRequest(CourseQueryRequest.class);
        if ("list".equals(request.getFlag())) {
            return AjaxResult.success(CourseServiceImpl.getCourse(request, commonRequest.getPageEntity()));
        } else if ("byId".equals(request.getFlag())) {
            if (null == request.getId()) {
                return AjaxResult.success(new ArrayList<>());
            }
            CourseQueryRequest queryRequest = new CourseQueryRequest();
            queryRequest.setId(request.getId());
            return AjaxResult.success(CourseServiceImpl.getCourse(queryRequest, commonRequest.getPageEntity()));
        }
        return AjaxResult.success();
    }

    @PostMapping("Get_CourseList")
    @ApiOperation(value = "小程序-获取课件列表")
    public AjaxResult Get_CourseList(@RequestBody CommonRequest commonRequest) {
        GetCourseListRequest request = (GetCourseListRequest) commonRequest.getRequest(GetCourseListRequest.class);
        return AjaxResult.success(CourseServiceImpl.getCourseList(request));
    }

    @PostMapping("Edit_CompleteCourse")
    @ApiOperation(value = "小程序-完成课件")
    public AjaxResult Edit_CompleteCourse(@RequestBody CommonRequest commonRequest) {
        EditCompleteCourseRequest request = (EditCompleteCourseRequest) commonRequest.getRequest(EditCompleteCourseRequest.class);
        return AjaxResult.success(CourseServiceImpl.editCompleteCourse(request));
    }

    @PostMapping("Check_CourseCode")
    @ApiOperation(value = "小程序-完成课件")
    public AjaxResult Check_CourseCode(@RequestBody CommonRequest commonRequest) {
        EditCompleteCourseRequest request = (EditCompleteCourseRequest) commonRequest.getRequest(EditCompleteCourseRequest.class);
        boolean b = CourseServiceImpl.CheckCourseCode(request);
        if (b){
            return AjaxResult.success(1000,"成功");
        }
        return AjaxResult.error(500,"操作失败");
    }

    @PostMapping("Get_CommentAPP")
    @ApiOperation(value = "小程序-获取课件评论列表")
    public AjaxResult Get_CommentAPP(@RequestBody CommonRequest commonRequest) {
        GetCommentAPPRequest request = (GetCommentAPPRequest) commonRequest.getRequest(GetCommentAPPRequest.class);
        return AjaxResult.success(CourseServiceImpl.getCommentAPP(request,commonRequest.getPageEntity()));
    }

    @PostMapping("Edit_UserCourseFirst")
    @ApiOperation(value = "小程序-播放课件")
    public AjaxResult Edit_UserCourseFirst(@RequestBody CommonRequest commonRequest) {
        EditUserCourseFirstRequest request = (EditUserCourseFirstRequest) commonRequest.getRequest(EditUserCourseFirstRequest.class);
        return AjaxResult.success(CourseServiceImpl.editUserCourseFirst(request));
    }

    @PostMapping("Edit_UserCourseFaceImg")
    @ApiOperation(value = "小程序-课件学习人脸识别图片")
    public AjaxResult Edit_UserCourseFaceImg(@RequestBody CommonRequest commonRequest) {
        EditUserCourseFaceImgRequest request = (EditUserCourseFaceImgRequest) commonRequest.getRequest(EditUserCourseFaceImgRequest.class);
        return AjaxResult.success(CourseServiceImpl.editUserCourseFaceImg(request));
    }

    @PostMapping("Get_EvaluationListAPP")
    @ApiOperation(value = "小程序-获取测评列表-接口暂无")
    public AjaxResult Get_EvaluationListAPP(@RequestBody CommonRequest commonRequest) {
        return AjaxResult.success(new ArrayList());
    }

    @PostMapping("Get_UserCourseDetail")
    @ApiOperation(value = "小程序-获取用户课件学习详情")
    //@ListNoArray
    public AjaxResult Get_UserCourseDetail(@RequestBody CommonRequest commonRequest) {
        GetUserCourseDetailRequest request = (GetUserCourseDetailRequest) commonRequest.getRequest(GetUserCourseDetailRequest.class);
        return AjaxResult.success(CourseServiceImpl.getUserCourseDetail(request));
    }

    @PostMapping("TextModeration")
    @ApiOperation(value = "过敏词判断")
    public AjaxResult TextModeration(@RequestBody CommonRequest commonRequest) {
        String text = (String) commonRequest.getRequest(String.class);
        return AjaxResult.success("操作成功",CourseServiceImpl.textModeration(text));
    }

}
