package com.fzkj.common.utils;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.IoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
    @Slf4j
    public class ImagUrlUtil implements Converter<String> {
        public static int urlConnectTimeout = 2000;
        public static int urlReadTimeout = 6000;

        private static final int MAX_WIDTH = 800; // 最大宽度

        private static final int MAX_HEIGHT = 600; // 最大高度

        @Override
        public Class<?> supportJavaTypeKey() {
            return String.class;
        }
//        @Override
//        public WriteCellData<?> convertToExcelData(String url, ExcelContentProperty contentProperty,GlobalConfiguration globalConfiguration) throws IOException {
//            InputStream inputStream = null;
//            try {
//                URL value = new URL(url);
//                if (ObjectUtils.isEmpty(value)){
//                    return new WriteCellData<>("图片链接为空");
//                }
//                URLConnection urlConnection = value.openConnection();
//                urlConnection.setConnectTimeout(urlConnectTimeout);
//                urlConnection.setReadTimeout(urlReadTimeout);
//                inputStream = urlConnection.getInputStream();
//                byte[] bytes = IoUtils.toByteArray(inputStream);
//                return new WriteCellData<>(bytes);
//            }catch (Exception e){
//                log.info("图片获取异常",e);
//                return new WriteCellData<>("图片获取异常");
//            } finally {
//                if (inputStream != null) {
//                    inputStream.close();
//                }
//            }
//        }

        @Override
        public WriteCellData<?> convertToExcelData(String url, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws IOException {
            if (StringUtils.isBlank(url)) {
                return new WriteCellData<>("图片链接为空");
            }

            try (InputStream inputStream = new URL(url).openConnection().getInputStream()) {
                // 读取图片
                BufferedImage originalImage = ImageIO.read(inputStream);
                if (originalImage == null) {
                    return new WriteCellData<>("图片读取失败");
                }

//                // 获取图片的原始宽高 压缩操作
//                int originalWidth = originalImage.getWidth();
//                int originalHeight = originalImage.getHeight();
//
//                // 计算新的宽高，保持比例不变
//                double scale = Math.min((double) MAX_WIDTH / originalWidth, (double) MAX_HEIGHT / originalHeight);
//                int newWidth = (int) (originalWidth * scale);
//                int newHeight = (int) (originalHeight * scale);
//
//                //创建一个新的 BufferedImage 来存放压缩后的图片
//                BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
//                Graphics2D g2d = resizedImage.createGraphics();
//                g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
//                g2d.dispose();
//
//                //将压缩后的图片转换为字节数组
//                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//                ImageIO.write(resizedImage, "PNG", byteArrayOutputStream); // 可以调整为其他格式，如 PNG
//                byte[] imageBytes = byteArrayOutputStream.toByteArray();
//
//                return new WriteCellData<>(imageBytes);

                // 将图片转换为字节数组，不进行压缩
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                ImageIO.write(originalImage, "PNG", byteArrayOutputStream); // 可以调整为其他格式，如 PNG
                byte[] imageBytes = byteArrayOutputStream.toByteArray();
                return new WriteCellData<>(imageBytes);
            } catch (Exception e) {
                log.error("图片获取异常, URL: {}", url, e);
                return new WriteCellData<>("图片获取异常");
            }
        }
}
