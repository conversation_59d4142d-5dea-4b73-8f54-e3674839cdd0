package com.fzkj.project.system.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "公司课程安全培训设置查询Request")
public class CompanyLessonSafeTrainSetQueryRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long companyId;
    private Long lessonCategoryId;
    private Long lessonId;
    private Integer state;
    private String areaCode;
    private Integer isTrusteeship;
    private String flag;
}
