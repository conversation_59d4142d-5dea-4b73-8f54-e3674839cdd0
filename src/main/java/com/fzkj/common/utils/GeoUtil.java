package com.fzkj.common.utils;


import lombok.extern.slf4j.Slf4j;

import java.awt.geom.GeneralPath;
import java.awt.geom.Point2D;
import java.util.List;

/**
 * <p>Description: 电子围栏工具</p>
 */
@Slf4j
public class GeoUtil {

    /**
     * 地球半径
     */
    private static double EARTH_RADIUS = 6378137;

    public static double pi = 3.1415926535897932384626;
    public static double x_pi = 3.14159265358979324 * 3000.0 / 180.0;
    public static double a = 6378245.0;
    public static double ee = 0.00669342162296594323;

    public static double transformLat(double x, double y) {
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
        return ret;
    }

    public static double transformLon(double x, double y) {
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0 * pi)) * 2.0 / 3.0;
        return ret;
    }
    public static double[] transform(double lat, double lon) {
        if (outOfChina(lat, lon)) {
            return new double[]{lat,lon};
        }
        double dLat = transformLat(lon - 105.0, lat - 35.0);
        double dLon = transformLon(lon - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * pi;
        double magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
        dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
        double mgLat = lat + dLat;
        double mgLon = lon + dLon;
        return new double[]{mgLat,mgLon};
    }
    /**
     * 判断是否在中国
     * @param lat
     * @param lon
     * @return
     */
    public static boolean outOfChina(double lat, double lon) {
        if (lon < 72.004 || lon > 137.8347)
            return true;
        if (lat < 0.8293 || lat > 55.8271)
            return true;
        return false;
    }
    /**
     * 84 ==》 高德
     * @param lat
     * @param lon
     * @return
     */
    public static double[] gps84_To_Gcj02(double lat, double lon) {
        if (outOfChina(lat, lon)) {
            return new double[]{lat,lon};
        }
        double dLat = transformLat(lon - 105.0, lat - 35.0);
        double dLon = transformLon(lon - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * pi;
        double magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
        dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
        double mgLat = lat + dLat;
        double mgLon = lon + dLon;
        return new double[]{mgLat, mgLon};
    }

    /**
     * 高德 ==》 84
     * @param lon * @param lat * @return
     * */
    public static double[] gcj02_To_Gps84(double lat, double lon) {
        double[] gps = transform(lat, lon);
        double lontitude = lon * 2 - gps[1];
        double latitude = lat * 2 - gps[0];
        return new double[]{latitude, lontitude};
    }
    /**
     * 高德 == 》 百度
     * @param lat
     * @param lon
     */
    public static double[] gcj02_To_Bd09(double lat, double lon) {
        double x = lon, y = lat;
        double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
        double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
        double tempLon = z * Math.cos(theta) + 0.0065;
        double tempLat = z * Math.sin(theta) + 0.006;
        double[] gps = {tempLat,tempLon};
        return gps;
    }

    /**
     * 百度 == 》 高德
     * @param lat
     * @param lon
     */
    public static double[] bd09_To_Gcj02(double lat, double lon) {
        double x = lon - 0.0065, y = lat - 0.006;
        double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
        double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
        double tempLon = z * Math.cos(theta);
        double tempLat = z * Math.sin(theta);
        double[] gps = {tempLat,tempLon};
        return gps;
    }

    /**
     * 84 == 》 百度
     * @param lat
     * @param lon
     * @return
     */
    public static double[] gps84_To_bd09(double lat,double lon){
        double[] gcj02 = gps84_To_Gcj02(lat,lon);
        double[] bd09 = gcj02_To_Bd09(gcj02[0],gcj02[1]);
        return bd09;
    }

    /**
     * 百度 == 》 84
     * @param lat
     * @param lon
     * @return
     */
    public static double[] bd09_To_gps84(double lat,double lon){
        double[] gcj02 = bd09_To_Gcj02(lat, lon);
        double[] gps84 = gcj02_To_Gps84(gcj02[0], gcj02[1]);
        //保留小数点后六位
        gps84[0] = retain6(gps84[0]);
        gps84[1] = retain6(gps84[1]);
        return gps84;
    }

    /*
     * 保留小数点后六位
     * @param num
     * @return
     */
    private static double retain6(double num){
        String result = String .format("%.6f", num);
        return Double.valueOf(result);
    }


    /**
     * 判断圆形是否包含传入的经纬度点
     *
     * @param center 圆心
     * @param radius 半径
     * @param point  待判断点
     * @return true:圆形包含这个点,false:圆形未包含这个点
     */
    public static boolean isCircleContainsPoint(Point2D.Double center, double radius, Point2D.Double point) {
        double distance = getDistance(point.getX(), point.getY(), center.getX(), center.getY());
        return distance <= radius;
    }

    /**
     * 判断矩形是否包含传入的经纬度点
     *
     * @param rPoint1 第一个点
     * @param rPoint2 第二个点
     * @param point   待判断点
     * @return true:矩形包含这个点,false:矩形未包含这个点
     */
    public static boolean isRectangleContainsPoint(Point2D.Double rPoint1, Point2D.Double rPoint2, Point2D.Double point) {
        if (point.getX() >= Math.min(rPoint1.getX(), rPoint2.getX())
                && point.getX() <= Math.max(rPoint1.getX(), rPoint2.getX())
                && point.getY() >= Math.min(rPoint1.getY(), rPoint2.getY())
                && point.getY() <= Math.max(rPoint1.getY(), rPoint2.getY())) {
            return true;
        }
        return false;
    }

    /**
     * 计算地球上任意两点(经纬度)距离
     *
     * @param lng1 第一点经度
     * @param lat1 第一点纬度
     * @param lng2 第二点经度
     * @param lat2 第二点纬度
     * @return 返回距离, 单位：米
     */
    public static double getDistance(double lng1, double lat1, double lng2, double lat2) {
        lat1 = lat1 * Math.PI / 180.0;
        lat2 = lat2 * Math.PI / 180.0;
        double a = lat1 - lat2;
        double b = (lng1 - lng2) * Math.PI / 180.0;
        double sa2 = Math.sin(a / 2.0);
        double sb2 = Math.sin(b / 2.0);
        double distance = 2 * EARTH_RADIUS * Math.asin(Math.sqrt(sa2 * sa2 + Math.cos(lat1) * Math.cos(lat2) * sb2 * sb2));
        return distance;
    }

    /**
     * 返回一个点是否在一个多边形区域内
     *
     * @param mPoints 多边形坐标点列表
     * @param point   待判断点
     * @return true:多边形包含这个点,false:多边形未包含这个点
     */
    public static boolean isPolygonContainsPoint(List<Point2D.Double> mPoints, Point2D.Double point) {
        GeneralPath peneralPath = new GeneralPath();
        Point2D.Double first = mPoints.get(0);
        //将一个点添加到路径中
        peneralPath.moveTo(first.x, first.y);
        //通过绘制一条从当前坐标到新指定坐标的直线，将一个点添加到路径中。
        if (mPoints.size() > 1) {
            mPoints.subList(1, mPoints.size()).forEach(d -> peneralPath.lineTo(d.getX(), d.getY()));
        }
        //将几何多边形封闭
        peneralPath.lineTo(first.x, first.y);
        peneralPath.closePath();
        //判断指定的点是否在多边形内。
        return peneralPath.contains(point);
    }

    /**
     * 返回一个点是否在折线范围内
     *
     * @param mPoints     折线坐标点列表
     * @param point       待判断点
     * @param offsetWidth 偏移宽度
     * @return true:点在折线范围,false:点不在折线范围
     */
    public static boolean isPointOnPolyline(List<Point2D.Double> mPoints, Point2D.Double point, double offsetWidth) {
        if (mPoints.size() == 0) {
            return false;
        }
        if (offsetWidth == 0) {
            offsetWidth = 50;
        }
        if (mPoints.size() == 1) {
            double distance = getDistance(point.x, point.y, mPoints.get(0).x, mPoints.get(0).y);
            return distance <= offsetWidth;
        }

        double lon = point.x;
        double lat = point.y;
        double widthSquare = offsetWidth * offsetWidth;
        for (int i = 0; i < mPoints.size() - 1; i++) {
            Point2D.Double b = mPoints.get(i);
            Point2D.Double c = mPoints.get(i + 1);
            double ab = getDistance(lon, lat, b.x, b.y);
            double ac = getDistance(lon, lat, c.x, c.y);
            double bc = getDistance(b.x, b.y, c.x, c.y);
            if (ab == 0) {
                log.debug("car same as path point:car pos=(" + lon + "," + lat + "),path point=(" + b.getX() + "," + b.getY() + ")");
            }
            if (ac == 0) {
                log.debug("car same as next path point:car pos=(" + lon + "," + lat + "),path point=(" + c.getX() + "," + c.getY() + ")");
            }
            if (bc == 0) {
                log.debug("tow path points are same:prev point=(" + b.getX() + "," + b.getY() + "),next point=(" + c.getX() + "," + c.getY() + ")");
            }
            if (getDistanceSquare(ab, ac, bc) < widthSquare) {
                if ((ab * ab - getDistanceSquare(ab, ac, bc) < (bc + offsetWidth) * (bc + offsetWidth))
                        || (ac * ac - getDistanceSquare(ab, ac, bc) < (bc + offsetWidth) * (bc + offsetWidth))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 根据三边长度计算由测点所引垂线的长度，三角形由 ABC 三点围成，返回 A 到 BC 的距离的平方。
     */
    private static double getDistanceSquare(double ab, double ac, double bc) {
        if (ab + ac == bc) {
            return 0;
        }
        //解析几何法
        double t = ((ab * ab - ac * ac + bc * bc) / (2 * bc));
        return ab * ab - t * t;
    }

}
