package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetLearnRedNumVO {

    @ApiModelProperty(value = "学习红点数量", example = "4")
    @JsonProperty("LearnRedNum")
    private int learnRedNum;

    @ApiModelProperty(value = "工具红点数量", example = "4")
    @JsonProperty("ToolsRedNum")
    private int toolsRedNum;

    @ApiModelProperty(value = "消息红点数量", example = "4")
    @JsonProperty("MsgRedNum")
    private int msgRedNum;
}
