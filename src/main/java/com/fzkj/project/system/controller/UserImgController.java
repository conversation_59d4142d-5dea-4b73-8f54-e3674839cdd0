package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.entity.UserImg;
import com.fzkj.project.system.service.LikeService;
import com.fzkj.project.system.service.UserImgService;
import com.fzkj.project.system.vo.LikeVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *  用户签名照片 控制层
 *
 */
@RestController
@RequiredArgsConstructor
public class UserImgController extends BaseEntity {

    private final UserImgService userImgService;

    @RequestMapping(value = "/Get_UserImg", method = RequestMethod.POST)
    @ApiOperation(value = "收藏/点赞（确认/取消）")
    public AjaxResult getUserImg(@RequestBody CommonRequest commonRequest) {
        UserImg request = (UserImg) commonRequest.getRequest(UserImg.class);
        return AjaxResult.success(userImgService.getUserImg(request,commonRequest.getPageEntity()));
    }

    @RequestMapping(value = "/Edit_UserImg", method = RequestMethod.POST)
    @ApiOperation(value = "收藏/点赞（确认/取消）")
    public AjaxResult editUserImg(@RequestBody CommonRequest commonRequest) {
        UserImg request = (UserImg) commonRequest.getRequest(UserImg.class);
        return userImgService.editUserImg(request);
    }
}
