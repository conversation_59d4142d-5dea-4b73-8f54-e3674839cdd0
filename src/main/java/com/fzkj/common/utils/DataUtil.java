package com.fzkj.common.utils;


import java.text.DecimalFormat;
import java.util.List;
import java.util.Random;

public class DataUtil {

	public static final String DecimalFormat_I="#";
	public static final String DecimalFormat_F="#.#";
	public static final String DecimalFormat_D="#.##";

	/**
	 * 随机指定范围内N个不重复的数
	 * 在初始化的无重复待选数组中随机产生一个数放入结果中，
	 * 将待选数组被随机到的数，用待选数组(len-1)下标对应的数替换
	 * 然后从len-2里随机产生下一个随机数，如此类推
	 * @param max  指定范围最大值
	 * @param min  指定范围最小值
	 * @param n  随机数个数
	 * @return int[] 随机数结果集
	 */
	public static int[] randomArray(int min, int max, int n) {
		int len = max - min + 1;

		if (max < min || n > len) {
			return null;
		}

		// 初始化给定范围的待选数组
		int[] source = new int[len];
		for (int i = min; i < min + len; i++) {
			source[i - min] = i;
		}

		int[] result = new int[n];
		Random rd = new Random();
		int index = 0;
		for (int i = 0; i < result.length; i++) {
			// 待选数组0到(len-2)随机一个下标
			index = Math.abs(rd.nextInt() % len--);
			// 将随机到的数放入结果集
			result[i] = source[index];
			// 将待选数组中被随机到的数，用待选数组(len-1)下标对应的数替换
			source[index] = source[len];
		}
		return result;
	}

	/**
	 * 生成从[from, to] 变量之间的随机整数
	 * @param from Integer
	 * @param to Integer
	 * @return Integer
	 */
	public static int randomIntNumber1(int from, int to) {
		Random random= new Random();
		float a = from + (to - from) * (random.nextFloat());
		int b = (int) a;
		int rslt=((a - b) > 0.5 ? 1 : 0) + b;
		rslt=(rslt>to?to:rslt);
		return rslt;
	}

	/**
	 * 生成从[0,end)的随机整数
	 * @param end Integer
	 * @return Integer
	 */
	public static int randomIntNumber1(int end) {
		Random random= new Random();
		return random.nextInt(end);
	}

	/**
	 * 生成0-1的随机数。
	 * @return 随机数
	 */
	public static float randomFloatNumber() {
		Random random= new Random();
		return random.nextFloat();
	}

	/**
	 * 求一个Integer类型 的长度
	 * @param num Integer
	 * @return Integer
	 */
	public static int length(int num) {
		return Integer.toString(num).length();
	}

	public static boolean findKey(List<Integer> list, int listFrom, int listTo, int randomNumber,int currentPos) {
		for (int i = listFrom; i < listTo; i++) {
			if (i!=currentPos&&list.get(i) == randomNumber) {
				return true;
			}
		}
		return false;
	}
	public static boolean findKey(int[] list, int listFrom, int listTo, int randomNumber,int currentPos) {
		for (int i = listFrom; i < listTo; i++) {
			if (i!=currentPos&&list[i] == randomNumber) {
				return true;
			}
		}
		return false;
	}

	public static int rate(long num1, long num2) {
		if (num1 == 0 || num2 == 0) {
			return 0;
		}
		double s = ((double) num1 / (double) num2) * 100;
		DecimalFormat df = new DecimalFormat("#");
		return Integer.parseInt(df.format(s));
	}

    public static int rate(int num1, int num2) {
        if (num1 == 0 || num2 == 0) {
            return 0;
        }
        double s = ((double) num1 / (double) num2) * 100;
        DecimalFormat df = new DecimalFormat("#");
        return Integer.parseInt(df.format(s));
    }

	public static String rate(long num1, long num2,String decimalFormatStr) {
		if (num1 == 0 || num2 == 0) {
			return "0";
		}
		double s = ((double) num1 / (double) num2) * 100;
		DecimalFormat df = new DecimalFormat(decimalFormatStr);
		return df.format(s);
	}

	public static double rateToDouble(long num1, long num2) {
		if (num1 == 0 || num2 == 0) {
			return 0;
		}
		double s = ((double) num1 / (double) num2) * 100;
		DecimalFormat df = new DecimalFormat("#0.00");
		return Double.parseDouble(df.format(s));
	}
}

