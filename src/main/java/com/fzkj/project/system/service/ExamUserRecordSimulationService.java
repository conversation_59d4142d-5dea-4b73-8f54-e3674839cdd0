package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.ExamUserRecordSimulation;
import com.fzkj.project.system.vo.ExamUserRecordSimulationVO;
import com.fzkj.project.system.request.ExamUserRecordSimulationRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户考试记录表（模拟考试） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExamUserRecordSimulationService extends IService<ExamUserRecordSimulation> {

    List<ExamUserRecordSimulation> listExamUserRecordSimulation(ExamUserRecordSimulationRequest request);

    PageUtils listByPage(ExamUserRecordSimulationRequest request, PageEntity pageEntity);

    ExamUserRecordSimulationVO findExamUserRecordSimulation(Long id);

    Long saveExamUserRecordSimulation(ExamUserRecordSimulationVO vo);

    boolean updateExamUserRecordSimulation(ExamUserRecordSimulationVO vo);

    boolean removeExamUserRecordSimulation(Long id);
}
