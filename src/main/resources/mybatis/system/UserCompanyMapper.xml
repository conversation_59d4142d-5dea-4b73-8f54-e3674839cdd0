<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.UserCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.UserCompany">
        <id column="id" property="id" />
        <result column="user_code" property="userCode" />
        <result column="company_id" property="companyId" />
        <result column="depart_id" property="departId" />
        <result column="work_id" property="workId" />
        <result column="user_name" property="userName" />
        <result column="user_photo" property="userPhoto" />
        <result column="sex" property="sex" />
        <result column="birth_day" property="birthDay" />
        <result column="home_address" property="homeAddress" />
        <result column="is_enable" property="isEnable" />
        <result column="is_out" property="isOut" />
        <result column="is_activate" property="isActivate" />
        <result column="plate_number" property="plateNumber" />
        <result column="sign_url" property="signUrl" />
        <result column="train_type_id_str" property="trainTypeIdStr" />
        <result column="account_code" property="accountCode" />
        <result column="oper_platform" property="operPlatform" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creator_time" property="creatorTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="identity" property="identity" />
        <result column="is_valid" property="isValid" />
        <result column="industry_type" property="industryType" />
        <result column="join_time" property="joinTime" />
        <result column="no_accident_certificate" property="noAccidentCertificate" />
        <result column="no_accident_file_name" property="noAccidentFileName" />
        <result column="xattrs_val" property="xattrsVal" />
        <result column="edu_level" property="eduLevel" />
        <result column="id_card_json" property="idCardJson" />
        <result column="sos_name" property="sosName" />
        <result column="sos_phone" property="sosPhone" />
        <result column="learn_type" property="learnType" />
        <result column="company_label" property="companyLabel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_code, company_id, depart_id, work_id, user_name, user_photo, sex, birth_day, home_address, is_enable, is_out, is_activate, plate_number, sign_url, train_type_id_str, account_code, oper_platform, source, sort, creator_code, creator_time, revise_code, revise_time, remark, identity, is_valid, industry_type, join_time, no_accident_certificate, no_accident_file_name, xattrs_val, edu_level, id_card_json, sos_name, sos_phone, learn_type, company_label
    </sql>

    <update id="userOut">
        UPDATE t_d_user_company SET is_out = 0 WHERE user_code = #{userCode}
    </update>
    <update id="updateCompany">
        update t_d_user_company set company_id=#{companyId} ,depart_id=#{departId},is_out=1 where user_code = #{userCode}
    </update>
    <select id="selectPlateNumberByCompanyId" resultType="com.fzkj.project.system.vo.UserCompanyVO">
        select
        case
        when plate_number = '' then '--'
        when plate_number = null then '--'
        else plate_number
        end
        as plate_number,
        user_code
        from
        t_d_user_company
        where is_valid =1
        <if test="companyId!=null">
            and company_id = #{companyId}
        </if>
    </select>
    <select id="selectUserCompanyList" resultType="com.fzkj.project.system.vo.UserCompanyVO">
        select
        <include refid="Base_Column_List"/>
        from
        t_d_user_company t
    </select>
    <select id="selectPayUserList" resultType="com.fzkj.project.system.vo.UserInfoVO">
        SELECT tduc.user_name, tdui.id_card, tduc.user_code
        FROM `t_d_user_company` as tduc,
        t_d_user_info as tdui
        where tduc.user_code=tdui.user_code
    </select>
    <select id="selectUserInfoByPhoneOrIdCard" resultType="com.fzkj.project.system.vo.UserCompanyVO">
        SELECT
               uc.id,
               uc.user_code as userCode,
               uc.company_id companyId,
               uc.depart_id departId,
               uc.work_id workId,
               uc.user_name userName,
               uc.user_photo userPhoto,
               uc.sex,
               uc.birth_day birthDay,
               uc.home_address homeAddress,
               uc.is_enable isEnable,
               uc.is_out isOut,
               uc.is_activate isActivate,
               uc.plate_number plateNumber,
               uc.sign_url signUrl,
               uc.train_type_id_str trainTypeIdStr,
               uc.account_code accountCode,
               uc.oper_platform operPlatform,
               uc.source,
               uc.sort,
               uc.creator_code creatorCode,
               uc.creator_time creatorTime,
               uc.revise_code reviseCode,
               uc.revise_time reviseTime,
               uc.remark,
               uc.identity,
               uc.is_valid isValid,
               uc.industry_type industryType,
               uc.join_time joinTime,
               uc.no_accident_certificate noAccidentCertificate,
               uc.no_accident_file_name noAccidentFileName,
               uc.xattrs_val xattrsVal,
               uc.edu_level eduLevel,
               uc.id_card_json idCardJson,
               uc.sos_name sosName,
               uc.sos_phone sosPhone,
               uc.learn_type learnType
        FROM t_d_user_info ui
                 LEFT JOIN t_d_user_company uc ON ui.user_code = uc.user_code
                 LEFT JOIN t_d_company dc ON uc.company_id = dc.id
                 LEFT JOIN t_d_company dcd ON uc.depart_id = dcd.id
        WHERE ui.phone = #{phone}
          and ui.id_card = #{idCard}
    </select>

</mapper>