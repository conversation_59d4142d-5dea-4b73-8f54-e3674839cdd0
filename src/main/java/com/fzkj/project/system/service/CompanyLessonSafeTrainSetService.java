package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.CompanyLessonSafeTrainSet;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetQueryRequest;
import com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 企业安全培训课程设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CompanyLessonSafeTrainSetService extends IService<CompanyLessonSafeTrainSet> {

    PageUtils listCompanyLessonSafeTrainSet(CompanyLessonSafeTrainSetQueryRequest request, PageEntity pageEntity);

    PageUtils listByPage(CompanyLessonSafeTrainSetRequest request, PageEntity pageEntity);

    CompanyLessonSafeTrainSetVO findCompanyLessonSafeTrainSet(Long id);

    Long saveCompanyLessonSafeTrainSet(CompanyLessonSafeTrainSetVO vo);

    boolean updateCompanyLessonSafeTrainSet(CompanyLessonSafeTrainSetVO vo);

    boolean removeCompanyLessonSafeTrainSet(Long id);

    boolean editCompanyLessonSafeTrainSet(CompanyLessonSafeTrainSetRequest request);
}
