package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.ExaminationPaperRule;
import com.fzkj.project.system.vo.ExaminationPaperRuleVO;
import com.fzkj.project.system.request.ExaminationPaperRuleRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 随机组卷规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExaminationPaperRuleService extends IService<ExaminationPaperRule> {

    List<ExaminationPaperRule> listExaminationPaperRule(ExaminationPaperRuleRequest request);

    PageUtils listByPage(ExaminationPaperRuleRequest request, PageEntity pageEntity);

    ExaminationPaperRuleVO findExaminationPaperRule(Long id);

    Long saveExaminationPaperRule(ExaminationPaperRuleVO vo);

    boolean updateExaminationPaperRule(ExaminationPaperRuleVO vo);

    boolean removeExaminationPaperRule(Long id);
}
