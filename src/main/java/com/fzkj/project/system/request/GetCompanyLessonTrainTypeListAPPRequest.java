package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetCompanyLessonTrainTypeListAPPRequest {

    @ApiModelProperty(value = "操作标识", example = "ListAll")
    @JsonProperty("Flag")
    private String flag;

    @ApiModelProperty(value = "公司ID", example = "2798")
    @JsonProperty("CompanyID")
    private Long companyId;
}
