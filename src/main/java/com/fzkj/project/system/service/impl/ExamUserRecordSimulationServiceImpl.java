package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.ExamUserRecordSimulation;
import com.fzkj.project.system.vo.ExamUserRecordSimulationVO;
import com.fzkj.project.system.request.ExamUserRecordSimulationRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ExamUserRecordSimulationMapper;
import com.fzkj.project.system.service.ExamUserRecordSimulationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户考试记录表（模拟考试） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExamUserRecordSimulationServiceImpl extends ServiceImpl<ExamUserRecordSimulationMapper, ExamUserRecordSimulation> implements ExamUserRecordSimulationService {

    @Override
    public List<ExamUserRecordSimulation> listExamUserRecordSimulation(ExamUserRecordSimulationRequest request) {
        ExamUserRecordSimulation entity = (ExamUserRecordSimulation) DataTransfer.transfer(request, ExamUserRecordSimulation.class);
        LambdaQueryWrapper<ExamUserRecordSimulation> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ExamUserRecordSimulationRequest request, PageEntity pageEntity) {
        ExamUserRecordSimulation entity = (ExamUserRecordSimulation) DataTransfer.transfer(request, ExamUserRecordSimulation.class);
        LambdaQueryWrapper<ExamUserRecordSimulation> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<ExamUserRecordSimulation> page = this.page(
                new Query<ExamUserRecordSimulation>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ExamUserRecordSimulationVO findExamUserRecordSimulation(Long id) {
        ExamUserRecordSimulation entity = this.getById(id);
        ExamUserRecordSimulationVO vo = (ExamUserRecordSimulationVO) DataTransfer.transfer(entity, ExamUserRecordSimulationVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExamUserRecordSimulation(ExamUserRecordSimulationVO vo) {
        ExamUserRecordSimulation entity = (ExamUserRecordSimulation) DataTransfer.transfer(vo, ExamUserRecordSimulation.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamUserRecordSimulation(ExamUserRecordSimulationVO vo) {
        ExamUserRecordSimulation entity = (ExamUserRecordSimulation) DataTransfer.transfer(vo, ExamUserRecordSimulation.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExamUserRecordSimulation(Long id) {
        return this.removeById(id);
    }
}