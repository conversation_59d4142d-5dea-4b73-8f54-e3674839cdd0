package com.fzkj.project.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fzkj.api.entity.vo.ErpAccessVo;
import com.fzkj.common.constant.Constants;
import com.fzkj.common.core.lang.UUID;
import com.fzkj.common.utils.MessageUtils;
import com.fzkj.common.utils.PlatHttpUtil;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.manager.AsyncManager;
import com.fzkj.framework.manager.factory.AsyncFactory;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.framework.security.service.SysPermissionService;
import com.fzkj.framework.security.service.TokenService;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.domain.SsoUserInfo;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.mapper.PlatformMenuMapper;
import com.fzkj.project.system.service.ILoginService;
import com.fzkj.project.system.service.PlatformMenuService;
import com.fzkj.project.system.service.UserInfoService;
import com.fzkj.project.system.service.WxService;
import com.fzkj.project.system.vo.AuthUserInfoVO;
import com.fzkj.project.system.vo.LoginResultVO;
import com.fzkj.project.system.vo.UserInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class LoginServiceImpl implements ILoginService {
    @Autowired
    private TokenService tokenService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private PlatformMenuMapper platformMenuMapper;

    @Autowired
    private WxService wxService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Resource
    private UserDetailsService userDetailsService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysPermissionService permissionService;

    // 令牌有效期（默认30分钟）
    @Value("${token.expireTime}")
    private int expireTime;

    @Override
    public LoginResultVO login(String username, String password, int platform, String unionId, String openId) {
        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        UserInfoVO userInfoVO = userInfoService.selectUserLoginInfoByUserCode(loginUser.getUser().getUserCode());
        if (userInfoVO.getIsOut().equals(0)){
            LoginResultVO loginResultVO = new LoginResultVO();
            loginResultVO.setToken("isOut");
            return loginResultVO;
        }
        if (platform == 7) {
            List<String> list = platformMenuMapper.platformMenuList(loginUser.getUser().getUserCode(), 16, null);
            if (list.size() == 0 && !loginUser.getUser().getId().equals(1L)) {
                LoginResultVO loginResultVO = new LoginResultVO();
                loginResultVO.setToken("error");
                return loginResultVO;
            }
        }
        if (platform == 6) {
            List<String> list = platformMenuMapper.platformMenuList(loginUser.getUser().getUserCode(), 4, null);
            if (list.size() == 0 && !loginUser.getUser().getId().equals(1L)) {
                LoginResultVO loginResultVO = new LoginResultVO();
                loginResultVO.setToken("error");
                return loginResultVO;
            }
        }
        loginUser.setPlatform(platform);
        if (StringUtils.isNotEmpty(unionId) && !unionId.equals(loginUser.getUser().getUnionId())) {
            loginUser.getUser().setUnionId(unionId);
            //更新unionId
            //删除原来所有当前unionId绑定的账户
            LambdaUpdateWrapper<UserInfo> deleteUnionIdWrapper = new LambdaUpdateWrapper<>();
            deleteUnionIdWrapper.eq(UserInfo::getUnionId, unionId);
            deleteUnionIdWrapper.set(UserInfo::getUnionId, null);
            deleteUnionIdWrapper.set(UserInfo::getOpenId, null);
            deleteUnionIdWrapper.set(UserInfo::getPublicOpenId, null);
            userInfoService.update(deleteUnionIdWrapper);
            //更新当前账户的unionId
            LambdaUpdateWrapper<UserInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(UserInfo::getUnionId, unionId);
            updateWrapper.set(UserInfo::getOpenId, openId);
            updateWrapper.eq(UserInfo::getId, loginUser.getUser().getId());
            userInfoService.update(updateWrapper);
            //异步更新publicOpenId
            AsyncManager.me().execute(new TimerTask() {
                @Override
                public void run() {
                    wxService.getPublicOpenIdByUnionId(unionId);
                }
            });
        }
        // 生成token
        return createLoginResultVO(loginUser);
    }

    @Override
    public LoginResultVO loginByUnionId(String unionId, String openId, int platform) {
        UserInfo userInfo = userInfoService.selectUserByUnionId(unionId, openId);
        if (null == userInfo) {
            return null;
        }
        userInfo.setPlatform(platform);
        return createLoginResultVO(userInfo);
    }

    @Override
    public LoginResultVO loginByAuthCode(String account, int platform) {
        UserInfo userInfo = userInfoService.selectUserByUserName(account);
        if (null == userInfo) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(account, platform, Constants.LOGIN_FAIL, MessageUtils.message("user.notfound")));
            return null;
        }
        userInfo.setPlatform(platform);
        return createLoginResultVO(userInfo);
    }

    @Override
    public LoginResultVO loginBySSO(String account, int platform) {
        UserInfo userInfo = userInfoService.selectUserByUserName(account);
        if (null == userInfo) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(account, platform, Constants.LOGIN_FAIL, MessageUtils.message("user.notfound")));
            return null;
        }
        userInfo.setPlatform(platform);
        return createLoginResultVO(userInfo);
    }
    @Override
    public SsoUserInfo getUserSso(String account) {
        String result = PlatHttpUtil.sendGetParams("https://nsso.cqphx.cn:4443/api/api/userInfo?code=" + account + "&systemId=a07c9ff14886c8852dd4207318f14afb", "UTF-8");
        log.info("SSO登录返回体："+result);
//                String result="{\n" +
//                "    \"msg\": \"查询成功\",\n" +
//                "    \"code\": 200,\n" +
//                "    \"data\": \"Pg5u1+2ud/189vpJiTTzlPTDOXvUzdS0LMZRofRugbaVIlVQucIpDKcXRpqC6JZg7RxA8exa6CR0Re2iPtOvahGnfSbyGIj7ScmTqJTnOhLry4uEZEFS8btqoWr2/w6yKkeuLB5ImPp0EhqFFBGjgMj3mLPO1fXdAwTCAZBK+Ka2vfsy+LnuggagudesbY+mKPnAtWNfkqyJnfhTffxhzPvg/rZ2HJ3iqgtqCtkrrKtmgKALuZ7EilkBHDdeW+eAn6YWmJ/9o/fPEhPDGhreK2SfmfCxx7p4AF8QrUhXXjF3l4PCYc8Ntfv31BoRVdnlkVKhE1S0JpnEDaJJqcWsvA==\"\n" +
//                "}";
        return JSON.parseObject(result, SsoUserInfo.class);
    }
    @Override
    public UserInfoVO getUserInfo(String userCode) {
        return userInfoService.selectUserLoginInfoByUserCode(userCode);
    }

    @Override
    public boolean verifySessionId(String userCode, String sessionId) {
        String sessionIdInCache = redisCache.getCacheObject(userCode);
        return null != sessionIdInCache && sessionIdInCache.equals(sessionId);
    }

    @Override
    public void userExit(CommonRequest request) {
        String userCode = (String) request.getRequest(String.class);
        int platform = request.getPlatform();
        LambdaUpdateWrapper<UserInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(UserInfo::getUnionId, null);
        updateWrapper.set(UserInfo::getPublicOpenId, null);
        updateWrapper.set(UserInfo::getReviseCode, userCode);
        updateWrapper.set(UserInfo::getReviseTime, LocalDateTime.now());
        updateWrapper.eq(UserInfo::getUserCode, userCode);
        userInfoService.update(updateWrapper);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (null != loginUser) {
            String userName = loginUser.getUsername();
            tokenService.delLoginUser(loginUser.getToken());
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, platform, Constants.LOGOUT, "退出成功"));
        }
    }

    /**
     * 构建登录返回信息
     *
     * @param userInfo
     * @return
     */
    private LoginResultVO createLoginResultVO(UserInfo userInfo) {
        LoginUser loginUser = new LoginUser(userInfo, permissionService.getMenuPermission(userInfo));
        loginUser.setPlatform(userInfo.getPlatform());
        return createLoginResultVO(loginUser);
    }

    /**
     * 构建登录返回信息
     *
     * @param loginUser
     * @return
     */
    private LoginResultVO createLoginResultVO(LoginUser loginUser) {
        String token = tokenService.createToken(loginUser);
        loginUser.setToken(token);
        LoginResultVO result = new LoginResultVO();
        result.setToken(token);
        result.setUserinfo(getUserInfo(loginUser.getUser().getUserCode()));
        if (Constants.PLATFORM_MINI == loginUser.getPlatform()) {
            String sessionId = generateSessionId();
            result.setSessionid(sessionId);
            redisCache.setCacheObject(loginUser.getUser().getUserCode(), sessionId, 36500, TimeUnit.DAYS);
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginUser.getUsername(), loginUser.getPlatform(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        return result;
    }

    /**
     * 生成sessionId
     *
     * @return
     */
    private String generateSessionId() {
        UUID uuid = UUID.randomUUID();
        String uuidString = uuid.toString().replaceAll("-", "");
        return uuidString.substring(0, 24);
    }
}
