package com.fzkj.framework.security.handle;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.core.MethodParameter;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.lang.reflect.Field;

public class CustomParamResolver implements HandlerMethodArgumentResolver {
    private final ConversionService conversionService = new DefaultConversionService();

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return null != parameter.getParameterType().getAnnotation(CustomParam.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        Object o = parameter.getParameterType().newInstance();
        for (Field field : parameter.getParameterType().getDeclaredFields()) {
            JsonProperty annotation = field.getAnnotation(JsonProperty.class);
            if (annotation != null) {
                String paramName = annotation.value();
                Object paramValue = webRequest.getParameter(paramName);
                if (null == paramValue || "-999".equals(String.valueOf(paramValue)) || "null".equals(String.valueOf(paramValue))) {
                    paramValue = null;
                }
                field.setAccessible(true);
                if (paramValue != null) {
                    Object convertedValue = conversionService.convert(paramValue, field.getType());
                    field.set(o, convertedValue);
                }
            } else {
                Object paramValue = webRequest.getParameter(field.getName());
                if (null == paramValue || "-999".equals(String.valueOf(paramValue)) || "null".equals(String.valueOf(paramValue))) {
                    paramValue = null;
                }
                field.setAccessible(true);
                if (paramValue != null) {
                    Object convertedValue = conversionService.convert(paramValue, field.getType());
                    field.set(o, convertedValue);
                }
            }
        }
        return o;
    }
}