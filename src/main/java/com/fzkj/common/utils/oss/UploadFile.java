package com.fzkj.common.utils.oss;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class UploadFile {
    @JsonProperty("Status")
    private String status;
    @JsonProperty("FileID")
    private String fileId;
    @JsonProperty("Size")
    private Long size;
    @JsonProperty("Duration")
    private Double duration;
    @JsonProperty("MediaUrl")
    private String mediaUrl;
    @JsonProperty("url")
    private String url;
}
