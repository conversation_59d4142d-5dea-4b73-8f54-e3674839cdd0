package com.fzkj.project.system.controller;

import com.fzkj.common.constant.Constants;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.PlatformRoleService;
import com.fzkj.project.system.vo.PlatformRoleVO;
import com.fzkj.project.system.request.PlatformRoleRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 平台角色 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userPort")
public class PlatformRoleController extends BaseEntity {

    private final PlatformRoleService PlatformRoleServiceImpl;

    @PostMapping("GetRole")
    @ApiOperation(value = "平台角色列表查询")
    public AjaxResult listPlatformRole(@RequestBody CommonRequest commonRequest) {
        PlatformRoleRequest request = (PlatformRoleRequest) commonRequest.getRequest(PlatformRoleRequest.class);
        if(request.getFlag().equals("nbd") && request.getCompanyId() != null){
            return AjaxResult.success(PlatformRoleServiceImpl.listPlatformRoleSelect(request));
        }else {
            return AjaxResult.success(PlatformRoleServiceImpl.listPlatformRole(request, commonRequest.getPageEntity()));
        }
    }

    @PostMapping("listByPage")
    @ApiOperation(value = "平台角色分页查询")
    public AjaxResult listPlatformRoleByPage(@RequestBody CommonRequest commonRequest) {
        PlatformRoleRequest request = (PlatformRoleRequest) commonRequest.getRequest(PlatformRoleRequest.class);
        return AjaxResult.success(PlatformRoleServiceImpl.listByPage(request, commonRequest.getPageEntity()));
    }

    @PostMapping("EditRole")
    @ApiOperation(value = "编辑角色")
    public AjaxResult savePlatformRole(@RequestBody CommonRequest commonRequest) {
        PlatformRoleVO vo = (PlatformRoleVO) commonRequest.getRequest(PlatformRoleVO.class);
        if(vo.getFlag().equals(Constants.ADD_DATA)){
            return AjaxResult.success(PlatformRoleServiceImpl.savePlatformRole(vo));
        }else if(vo.getFlag().equals(Constants.EDIT_DATA)){
            return AjaxResult.success(PlatformRoleServiceImpl.updatePlatformRole(vo));
        }else if(vo.getFlag().equals(Constants.DEL_DATA)){
            return AjaxResult.success(PlatformRoleServiceImpl.removePlatformRole(vo.getId()));
        }else{
            return AjaxResult.error("类型异常");
        }
    }

}
