package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 给企业分发的安全培训课程关系表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_lesson_safe_train_company")
@ApiModel(value = "给企业分发的安全培训课程关系表实体")
public class LessonSafeTrainCompany implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("CompanyID")
    @ApiModelProperty(value = "企业id")
    private Long companyId;

    @JsonProperty("CompanyName")
    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @JsonProperty("LessonID")
    @ApiModelProperty(value = "课程id")
    private Long lessonId;

    @JsonProperty("LessonCategoryID")
    @ApiModelProperty(value = "课程分类id")
    private Long lessonCategoryId;

    @JsonProperty("LessonCategoryName")
    @ApiModelProperty(value = "课程分类名称")
    private String lessonCategoryName;

    @JsonProperty("HandMode")
    @ApiModelProperty(value = "操作方式")
    private Integer handMode;

    @JsonProperty("Source")
    @ApiModelProperty(value = "来源")
    private String source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreationTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;
}