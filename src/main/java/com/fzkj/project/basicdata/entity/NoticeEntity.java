package com.fzkj.project.basicdata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @Author: Zhanghongyin
 * @String: Created in 2024-03-13 11:25
 * @Version: 1.0
 */
@TableName("t_d_notice")
@ApiModel(value = "公告实体")
public class NoticeEntity  implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    /** 内容 */
    @ApiModelProperty(name = "内容")
    @JsonProperty("Title")
    private String title;
    /** 内容 */
    @ApiModelProperty(name = "内容")
    @JsonProperty("Content")
    private String content;
    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("Type")
    private Integer type;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("LinkUrl")
    private String linkUrl;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("Source")
    private String source;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("StartTime")
    private String startTime;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("EndTime")
    private String endTime;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("Sort")
    private Integer sort;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("CreationTime")
    private String creationTime;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("ReviseTime")
    private String reviseTime;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("Remark")
    private String remark;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("IsValid")
    private Integer isValid;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("CreatorName")
    private String creatorName;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("ReviseName")
    private String reviseName;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("Author")
    private String author;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("PutInPlatform")
    private Integer putInPlatform;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("PutInPlatformStr")
    private String putInPlatformStr;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("OrganizationID")
    private Long organizationId;

    /** $column.columnComment */
    @ApiModelProperty(name = "")
    @JsonProperty("IsPush")
    private Integer isPush;

    @TableField(exist = false)
    @JsonProperty("IsExpire")
    private Integer isExpire;

    @TableField(exist = false)
    @JsonProperty("Platform")
    private Integer platform;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setType(Integer type)
    {
        this.type = type;
    }

    public Integer getType()
    {
        return type;
    }
    public void setLinkUrl(String linkUrl)
    {
        this.linkUrl = linkUrl;
    }

    public String getLinkUrl()
    {
        return linkUrl;
    }
    public void setSource(String source)
    {
        this.source = source;
    }

    public String getSource()
    {
        return source;
    }
    public void setStartTime(String startTime)
    {
        this.startTime = startTime;
    }

    public String getStartTime()
    {
        return startTime;
    }
    public void setEndTime(String endTime)
    {
        this.endTime = endTime;
    }

    public String getEndTime()
    {
        return endTime;
    }
    public void setSort(Integer sort)
    {
        this.sort = sort;
    }

    public Integer getSort()
    {
        return sort;
    }
    public void setCreatorCode(String creatorCode)
    {
        this.creatorCode = creatorCode;
    }

    public String getCreatorCode()
    {
        return creatorCode;
    }
    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public String getRemark()
    {
        return remark;
    }
    public void setCreationTime(String creationTime)
    {
        this.creationTime = creationTime;
    }

    public String getCreationTime()
    {
        return creationTime;
    }
    public void setReviseCode(String reviseCode)
    {
        this.reviseCode = reviseCode;
    }

    public String getReviseCode()
    {
        return reviseCode;
    }
    public void setReviseTime(String reviseTime)
    {
        this.reviseTime = reviseTime;
    }

    public String getReviseTime()
    {
        return reviseTime;
    }
    public void setIsValid(Integer isValid)
    {
        this.isValid = isValid;
    }

    public Integer getIsValid()
    {
        return isValid;
    }
    public void setCreatorName(String creatorName)
    {
        this.creatorName = creatorName;
    }

    public String getCreatorName()
    {
        return creatorName;
    }
    public void setReviseName(String reviseName)
    {
        this.reviseName = reviseName;
    }

    public String getReviseName()
    {
        return reviseName;
    }
    public void setAuthor(String author)
    {
        this.author = author;
    }

    public String getAuthor()
    {
        return author;
    }
    public void setPutInPlatform(Integer putInPlatform)
    {
        this.putInPlatform = putInPlatform;
    }

    public Integer getPutInPlatform()
    {
        return putInPlatform;
    }
    public void setPutInPlatformStr(String putInPlatformStr)
    {
        this.putInPlatformStr = putInPlatformStr;
    }

    public String getPutInPlatformStr()
    {
        return putInPlatformStr;
    }
    public void setOrganizationId(Long organizationId)
    {
        this.organizationId = organizationId;
    }

    public Long getOrganizationId()
    {
        return organizationId;
    }
    public void setIsPush(Integer isPush)
    {
        this.isPush = isPush;
    }

    public Integer getIsPush()
    {
        return isPush;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("type", getType())
                .append("linkUrl", getLinkUrl())
                .append("source", getSource())
                .append("startTime", getStartTime())
                .append("endTime", getEndTime())
                .append("sort", getSort())
                .append("creatorCode", getCreatorCode())
                .append("creationTime", getCreationTime())
                .append("reviseCode", getReviseCode())
                .append("reviseTime", getReviseTime())
                .append("remark", getRemark())
                .append("isValid", getIsValid())
                .append("creatorName", getCreatorName())
                .append("reviseName", getReviseName())
                .append("author", getAuthor())
                .append("putInPlatform", getPutInPlatform())
                .append("putInPlatformStr", getPutInPlatformStr())
                .append("organizationId", getOrganizationId())
                .append("isPush", getIsPush())
                .toString();
    }

    public Integer getIsExpire() {
        return isExpire;
    }

    public void setIsExpire(Integer isExpire) {
        this.isExpire = isExpire;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }
}
