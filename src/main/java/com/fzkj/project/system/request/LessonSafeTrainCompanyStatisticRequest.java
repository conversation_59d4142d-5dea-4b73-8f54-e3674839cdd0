package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LessonSafeTrainCompanyStatisticRequest implements Serializable {
    @ApiModelProperty("课程月份")
    @JsonProperty("LessonMonth")
    private String lessonMonth;

    @ApiModelProperty("课程月份")
    @JsonProperty("LessonDate")
    private String lessonDate;

    @ApiModelProperty("区域编码")
    @JsonProperty("AreaCode")
    private String areaCode;

    @ApiModelProperty("企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty("部门ID")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty("部门IDs")
    @JsonProperty("DepartIDs")
    private List<Long> departIds;

    @ApiModelProperty("排序类型")
    @JsonProperty("SortType")
    private Integer sortType;

    @ApiModelProperty("学习标识")
    @JsonProperty("LearnFlag")
    private Integer learnFlag;

    @ApiModelProperty("统计分组")
    @JsonProperty("StatisticalGroup")
    private Integer statisticalGroup;

    @ApiModelProperty("课程Ids")
    private List<Long> lessonIds;
}
