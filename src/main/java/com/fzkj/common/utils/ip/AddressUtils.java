package com.fzkj.common.utils.ip;

import com.fzkj.common.utils.StringUtils;

/**
 * 获取地址类
 *
 * <AUTHOR>
 */
public class AddressUtils
{

    public static String getRealAddressByIP(String ip)
    {
        if(StringUtils.isEmpty(ip)){
            return "获取IP失败";
        }

        String address = "XX XX";
        // 内网不查询
        if (IpUtils.internalIp(ip))
        {
            return "内网IP";
        }
        return address;
    }
}
