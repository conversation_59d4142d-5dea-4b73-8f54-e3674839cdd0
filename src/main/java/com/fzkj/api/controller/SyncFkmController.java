package com.fzkj.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.fzkj.api.entity.FkmReq;
import com.fzkj.api.entity.vo.TrainingYesCourse;
import com.fzkj.api.task.SyncFkmTask;
import com.fzkj.api.utils.SM4;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PlatHttpUtil;
import com.fzkj.common.utils.aliyun.SmsUtil;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.vo.CommentVO;
import com.fzkj.project.system.vo.UserLessonRecordSafeTrainUserVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.fzkj.common.utils.MongoQueryUtil.getConditionalStage;
import static com.fzkj.common.utils.MongoQueryUtil.getConditionalStageOrLike;

@RestController
@RequestMapping("/sync/hand")
@Slf4j
public class SyncFkmController {

    @Autowired
    @Qualifier("mongoTemplateMaster")
    private MongoTemplate mongoTemplateMaster;

    @Autowired
    @Qualifier("mongoTemplateHistory")
    private MongoTemplate mongoTemplateHistory;


    @PostMapping("/fkm")
    @ApiOperation(value = "课件，内容评论分页查询")
    public AjaxResult getComment(@RequestBody FkmReq req) {
        log.info("开始同步培训学习数据");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        List<Integer> staticStatusList = new ArrayList<>();
        staticStatusList.add(1);
        staticStatusList.add(2);
        staticStatusList.add(4);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)),
                getConditionalStage("statisticalGroup", 0),
                getConditionalStageOrLike(req.getTime(), "completeTime"),
                getConditionalStage("staticStatus", staticStatusList));
        List<UserLessonRecordSafeTrainUserVO> mappedResults = mongoTemplateMaster.aggregate(aggregation, "_tableSafeTrainUserLesson", UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        List<UserLessonRecordSafeTrainUserVO> mappedResultsHistory = mongoTemplateHistory.aggregate(aggregation, "_tableSafeTrainUserLesson", UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        List<UserLessonRecordSafeTrainUserVO> list = SyncFkmTask.mergeList(mappedResults, mappedResultsHistory);
        String encrypt = null;
        List<TrainingYesCourse> trainingYesCourses = new ArrayList<>();
        for (UserLessonRecordSafeTrainUserVO mappedResult : list) {
            String month = null;
            try {
                month = DateUtils.getMonthNow(format.parse(mappedResult.getLessonDate()));
                TrainingYesCourse trainingYesCourse = new TrainingYesCourse();
                trainingYesCourse.setName(mappedResult.getUserName());
                trainingYesCourse.setIdno(mappedResult.getIdCard());
                trainingYesCourse.setPhone(mappedResult.getPhone());
                trainingYesCourse.setJob("驾驶员");
                trainingYesCourse.setStudentImg(mappedResult.getUserPhoto());
                trainingYesCourse.setMonth(month);
                trainingYesCourse.setCourseName(mappedResult.getLessonName());
                trainingYesCourse.setCourseStat(3);
                trainingYesCourse.setLearnStat(3);
                trainingYesCourse.setCompeleteTime(mappedResult.getCompleteTime());
                trainingYesCourse.setDuration(mappedResult.getTrainTimeCount());
                trainingYesCourse.setExamineStat(1);
                String score = mappedResult.getScore();
                if (score.contains(".")) {
                    String[] split = score.split("\\.");
                    score = split[0];
                }
                trainingYesCourse.setScore(Long.valueOf(score));
                trainingYesCourse.setTestTime(mappedResult.getTrainEndTime());
                trainingYesCourse.setCompName(mappedResult.getDepartName());
                trainingYesCourse.setCompCode(String.valueOf(mappedResult.getDepartId()));
                if (mappedResult.getIdCard().equals("500112198504241971")) {
                    log.info("500112198504241971已推送！"+trainingYesCourse);
                }
                trainingYesCourses.add(trainingYesCourse);
            } catch (ParseException e) {
                log.info(mappedResult.getUserName() + "->" + mappedResult.getIdCard() + "推送失败！");
            }
        }
        try {
            encrypt = SM4.encrypt(JSONObject.toJSONString(trainingYesCourses), SM4.KEY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        sendT(encrypt);
        log.info("日期：" + req.getTime());
        log.info("条数：" + trainingYesCourses.size() + "条");
        log.info("结束同步培训学习数据");
        if (trainingYesCourses.size() < 10) {
            //短信通知
            SmsUtil.sendSMS("15213769431", "9" + trainingYesCourses.size() + "99");
        } else if (trainingYesCourses.size() < 100) {
            SmsUtil.sendSMS("15213769431", "9" + trainingYesCourses.size() + "9");
        } else if (trainingYesCourses.size() < 1000) {
            SmsUtil.sendSMS("15213769431", "9" + trainingYesCourses.size());
        } else {
            SmsUtil.sendSMS("15213769431", String.valueOf(trainingYesCourses.size()));
        }
        return AjaxResult.success("条数：" + trainingYesCourses.size() + "条");
    }

    @Async
    public void sendT(String encrypt) {
        log.info("开始推送！");
        PlatHttpUtil.sendPostParams("https://fkm.cqphx.cn:4443/prod-api/api/training/saveTrain", encrypt, null,
                "UTF-8", null);
        log.info("结束推送！");
    }
}
