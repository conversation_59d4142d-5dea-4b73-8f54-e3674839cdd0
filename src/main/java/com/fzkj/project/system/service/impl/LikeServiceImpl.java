package com.fzkj.project.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.Collection;
import com.fzkj.project.system.entity.Like;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.mapper.CollectionMapper;
import com.fzkj.project.system.mapper.LikeMapper;
import com.fzkj.project.system.mapper.UserInfoMapper;
import com.fzkj.project.system.service.LikeService;
import com.fzkj.project.system.vo.LikeVO;
import com.fzkj.project.system.vo.UserInfoVO;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class LikeServiceImpl  extends ServiceImpl<LikeMapper, Like> implements LikeService {

    private final UserInfoMapper userInfoMapper;

    private final CollectionMapper collectionMapper;

    @Override
    public Boolean edit_LikeShareCollectionAPP(LikeVO likeVO) {
        UserInfo loginUser = SecurityUtils.getLoginUser().getUser();
        if(likeVO.getFlag().equals("like")){
            //点赞/取消点赞
            //判断当前用户是否已经点赞
            LambdaQueryWrapper<Like> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Like::getUserCode,loginUser.getUserCode());
            lambdaQueryWrapper.eq(Like::getTargetId,likeVO.getTargetId());
            lambdaQueryWrapper.eq(Like::getFunType,11);
            Like like = this.baseMapper.selectOne(lambdaQueryWrapper);
            if(like != null && like.getId() >0){
                //存在就删除
                this.baseMapper.deleteById(like.getId());
            }else{
                //不存在就新增
                UserInfoVO userInfoVO = userInfoMapper.selectUserLoginInfoByUserCode(loginUser.getUserCode());
                Like like1 = new Like();
                like1.setFunType(11);
                like1.setUserCode(loginUser.getUserCode());
                like1.setIdCard(userInfoVO.getIdCard());
                like1.setUserName(userInfoVO.getUserName());
                like1.setPhone(userInfoVO.getPhone());
                like1.setUserPhoto(userInfoVO.getUserPhoto());
                like1.setTargetId(likeVO.getTargetId());
                like1.setSort(1);
                like1.setCreatorCode(loginUser.getUserCode());
                like1.setCreatorTime(LocalDateTime.now());
                like1.setIsValid(1);
                like1.setSource(256);
                this.baseMapper.insert(like1);
            }
        }else if(likeVO.getFlag().equals("collection")){
            //收藏/取消收藏
            //判断当前用户是否已经收藏
            LambdaQueryWrapper<Collection> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Collection::getUserCode,loginUser.getUserCode());
            lambdaQueryWrapper.eq(Collection::getTargetId,likeVO.getTargetId());
            lambdaQueryWrapper.eq(Collection::getFunType,11);
            Collection collection = collectionMapper.selectOne(lambdaQueryWrapper);
            if(collection != null && collection.getId() >0){
                //存在就删除
                collectionMapper.deleteById(collection.getId());
            }else{
                //不存在就新增
                UserInfoVO userInfoVO = userInfoMapper.selectUserLoginInfoByUserCode(loginUser.getUserCode());
                Collection collection1 = new Collection();
                collection1.setFunType(11);
                collection1.setUserCode(loginUser.getUserCode());
                collection1.setIdCard(userInfoVO.getIdCard());
                collection1.setUserName(userInfoVO.getUserName());
                collection1.setPhone(userInfoVO.getPhone());
                collection1.setUserPhoto(userInfoVO.getUserPhoto());
                collection1.setTargetId(likeVO.getTargetId());
                collection1.setSort(1);
                collection1.setCreatorCode(loginUser.getUserCode());
                collection1.setCreatorTime(LocalDateTime.now());
                collection1.setIsValid(1);
                collection1.setSource(256);
                collectionMapper.insert(collection1);
            }
        }
        return true;
    }

    @Override
    public Object getMyIntegralTotal() {
        UserInfo loginUser = SecurityUtils.getLoginUser().getUser();
        //获取本人收藏总数
        LambdaQueryWrapper<Collection> collectionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        collectionLambdaQueryWrapper.eq(Collection::getUserCode,loginUser.getUserCode());
        collectionLambdaQueryWrapper.eq(Collection::getFunType,11);
        collectionLambdaQueryWrapper.eq(Collection::getIsValid,1);
        Integer collectionNum = collectionMapper.selectCount(collectionLambdaQueryWrapper);
        //获取本人点赞总数
        LambdaQueryWrapper<Like> likeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        likeLambdaQueryWrapper.eq(Like::getUserCode,loginUser.getUserCode());
        likeLambdaQueryWrapper.eq(Like::getFunType,11);
        likeLambdaQueryWrapper.eq(Like::getIsValid,1);
        Integer likeNum = this.baseMapper.selectCount(likeLambdaQueryWrapper);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("LikeNum",likeNum);
        jsonObject.put("CollectionNum",collectionNum);
        return jsonObject;
    }

    @Override
    public PageUtils getMyLikeCollection(String flag, PageEntity pageEntity) {
        UserInfo loginUser = SecurityUtils.getLoginUser().getUser();
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<?> list = null;
        if(flag.equals("like")){
            list = this.baseMapper.getMyLikeList(loginUser.getUserCode());
        }else if(flag.equals("collection")){
            list = this.baseMapper.getMyCollectionList(loginUser.getUserCode());
        }
        return new PageUtils(pageEntity, list);
    }
}