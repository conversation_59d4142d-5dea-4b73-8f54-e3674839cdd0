package com.fzkj.project.system.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserExamInfoVo {
    private Long examId;
    private Integer questionCount;
    private double totalScore;
    private int timeCount;
    private int resitNumber;
    private int passMark;
    private int examSignState;
    private int examFaceState;
    private int examCountDownTime;
    private int examFailRepeatNum;
    private Integer isMockExamFace;
    private Integer isMockExam;
    private Integer isPractice;
    private Long lessonId;
    private int examCount;
    private int score;
    private int syResitNumber;
    private String photoUrl;
    private String signUrl;
    private String remark;
    private LocalDateTime creationTime;
    private int errorCount;
    private int userExamCount;
    private double accuracy;
    private int useTimeCount;
    private String idCard;
    private String userName;
    private String userPhone;
    private String userId;
    private Long organizationId;
}
