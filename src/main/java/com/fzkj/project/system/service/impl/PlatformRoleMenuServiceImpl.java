package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.PlatformMenu;
import com.fzkj.project.system.entity.PlatformRoleMenu;
import com.fzkj.project.system.entity.UserRole;
import com.fzkj.project.system.mapper.PlatformRoleMenuMapper;
import com.fzkj.project.system.mapper.UserRoleMapper;
import com.fzkj.project.system.request.UserRoleRequest;
import com.fzkj.project.system.service.PlatformRoleMenuService;
import com.fzkj.project.system.service.UserRoleService;
import com.fzkj.project.system.vo.UserRoleVO;
import lombok.RequiredArgsConstructor;
import nl.jworks.markdown_to_asciidoc.code.Linguist;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class PlatformRoleMenuServiceImpl extends ServiceImpl<PlatformRoleMenuMapper, PlatformRoleMenu> implements PlatformRoleMenuService {


    @Override
    public PlatformRoleMenu selectPlatformRoleMenuById(Long id) {
        return this.getById(id);
    }

    @Override
    public PageUtils selectPlatformRoleMenuList(PlatformRoleMenu tDPlatformRoleMenu, PageEntity pageEntity) {
        LambdaQueryWrapper<PlatformRoleMenu> queryWrapper = new LambdaQueryWrapper<>(tDPlatformRoleMenu);
        IPage<PlatformRoleMenu> page = this.page(
                new Query<PlatformRoleMenu>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long editPlatformRoleMenu(PlatformRoleMenu tDPlatformRoleMenu) {

        PlatformRoleMenu isHave = this.getById(tDPlatformRoleMenu.getId());
        if(isHave != null && isHave.getId()>0){
            this.updateById(tDPlatformRoleMenu);
        }else{
            this.save(tDPlatformRoleMenu);
        }
        return tDPlatformRoleMenu.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long deletePlatformRoleMenuById(Long id) {
        this.removeById(id);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateRoleMenu(PlatformRoleMenu vo) {
        LambdaQueryWrapper<PlatformRoleMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformRoleMenu::getRoleId,vo.getRoleId());
        PlatformRoleMenu platformRoleMenu = this.getOne(queryWrapper);
        if(platformRoleMenu == null){
            //添加
            this.save(vo);
        }else{
            //修改
            vo.setId(platformRoleMenu.getId());
            this.updateById(vo);
        }
        return 1L;
    }
}