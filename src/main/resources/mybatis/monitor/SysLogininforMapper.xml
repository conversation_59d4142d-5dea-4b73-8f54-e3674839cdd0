<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.monitor.mapper.SysLogininforMapper">

	<resultMap type="SysLogininfor" id="SysLogininforResult">
		<id     property="infoId"        column="info_id"           />
		<result property="userName"      column="user_name"         />
		<result property="status"        column="status"            />
		<result property="ipaddr"        column="ipaddr"            />
		<result property="loginLocation" column="login_location"    />
		<result property="browser"       column="browser"           />
		<result property="os"            column="os"                />
		<result property="msg"           column="msg"               />
		<result property="loginTime"     column="login_time"        />
		<result property="platform"     column="platform"        />
	</resultMap>

	<insert id="insertLogininfor" parameterType="SysLogininfor">
		insert into t_d_user_logininfor (user_name, status, ipaddr, login_location, browser, os, msg, login_time, platform)
		values (#{userName}, #{status}, #{ipaddr}, #{loginLocation}, #{browser}, #{os}, #{msg}, sysdate(),#{platform})
	</insert>

	<select id="selectLogininforList" parameterType="SysLogininfor" resultMap="SysLogininforResult">
		select info_id, zsl.user_name, ipaddr, login_location, browser, os, zsl.status, msg, login_time, platform from t_d_user_logininfor zsl left join t_d_user_company su on zsl.user_name =su.user_name
		<where>
			<if test="ipaddr != null and ipaddr != ''">
				AND ipaddr like concat('%', #{ipaddr}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
			<if test="userName != null and userName != ''">
				AND user_name like concat('%', #{userName}, '%')
			</if>
			<if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
				and date_format(login_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
			</if>
			<if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
				and date_format(login_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
			</if>
			<if test="platform != null and platform != ''"><!-- 结束时间检索 -->
				AND platform = #{platform}
			</if>
		</where>
		<!-- 数据范围过滤 -->
		${dataScope}
		order by info_id desc
	</select>

	<delete id="deleteLogininforByIds" parameterType="Long">
 		delete from t_d_user_logininfor where info_id in
 		<foreach collection="array" item="infoId" open="(" separator="," close=")">
 			#{infoId}
        </foreach>
 	</delete>

    <update id="cleanLogininfor">
        truncate table t_d_user_logininfor
    </update>

</mapper>
