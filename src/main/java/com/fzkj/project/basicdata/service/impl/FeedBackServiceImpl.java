package com.fzkj.project.basicdata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.ServletUtils;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.framework.security.service.TokenService;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.basicdata.entity.NoticeEntity;
import com.fzkj.project.basicdata.entity.TFeedBack;
import com.fzkj.project.basicdata.request.TFeedBackRequest;
import com.fzkj.project.system.entity.Teacher;
import com.fzkj.project.system.entity.UserCompany;
import com.fzkj.project.basicdata.mapper.FeedBackMapper;
import com.fzkj.project.basicdata.service.FeedBackService;
import com.fzkj.project.system.service.UserCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-12 9:37
 * @Version: 1.0
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class FeedBackServiceImpl extends ServiceImpl<FeedBackMapper, TFeedBack> implements FeedBackService {

    @Autowired
    private FeedBackMapper requestMapper;

    @Resource
    UserCompanyService userCompanyService;

    @Autowired
    private TokenService tokenService;

    /**
     * 查询常见问题
     *
     * @param id 常见问题主键
     * @return 常见问题
     */
    @Override
    public TFeedBack selectTFeedBackById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询常见问题列表
     *
     * @param request 常见问题
     * @return 常见问题
     */
    @Override
    public PageUtils selectTFeedBackList(TFeedBackRequest request, PageEntity pageEntity)
    {
        TFeedBack entity = (TFeedBack) DataTransfer.transfer(request, TFeedBack.class);
        LambdaQueryWrapper<TFeedBack> queryWrapper = new LambdaQueryWrapper<>();
        if (!ObjectUtils.isEmpty(entity.getIsValid())) {
            queryWrapper.eq(TFeedBack::getIsValid, entity.getIsValid());
        }
        if(entity.getBackType() != null){
            queryWrapper.eq(TFeedBack::getBackType, entity.getBackType());
        }
        if (!ObjectUtils.isEmpty(entity.getContent())){
            queryWrapper.like(TFeedBack::getContent,entity.getContent());
        }
        if (!ObjectUtils.isEmpty(entity.getId())){
            queryWrapper.eq(TFeedBack::getId,entity.getId());
        }
        IPage<TFeedBack> page = this.page(
                new Query<TFeedBack>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    /**
     * 新增常见问题
     *
     * @param request 常见问题
     * @return 结果
     */
    @Override
    public Long insertTFeedBack(TFeedBackRequest request)
    {
        TFeedBack entity = (TFeedBack) DataTransfer.transfer(request, TFeedBack.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        entity.setUserName(userCompany.getUserName());
        entity.setUserCode(userCompany.getUserCode());
        entity.setBackType(3);
        entity.setIsValid(1);
        if (!ObjectUtils.isEmpty(entity.getReplyContent())) {
            entity.setReplyTime(new Date());
            entity.setIsReply(1);
        }
        entity.setCreationTime(new Date());
        this.save(entity);
        return request.getId();
    }

    /**
     * 修改常见问题
     *
     * @param request 常见问题
     * @return 结果
     */
    @Override
    public Boolean updateTFeedBack(TFeedBackRequest request)
    {
        TFeedBack entity = (TFeedBack) DataTransfer.transfer(request, TFeedBack.class);
        return this.saveOrUpdate(entity);
    }

    /**
     * 删除常见问题信息
     *
     * @param id 常见问题主键
     * @return 结果
     */
    @Override
    public Boolean deleteTFeedBackById(Long id)
    {
        TFeedBack byId = this.getById(id);
        if (byId.getIsValid().equals(0)){
            byId.setIsValid(1);
        }else {
            byId.setIsValid(0);
        }
        return this.saveOrUpdate(byId);
    }

    @Override
    public PageUtils getFeedBackAPP(TFeedBackRequest tFeedBack, PageEntity pageEntity) {
        if(tFeedBack.getFlag().equals("list")){
            LambdaQueryWrapper<TFeedBack> queryWrapper = new LambdaQueryWrapper<>();
            if (tFeedBack.getBackType() != null) {
                queryWrapper.eq(TFeedBack::getBackType, tFeedBack.getBackType());
            }
            queryWrapper.eq(TFeedBack::getIsValid,1);
            IPage<TFeedBack> page = this.page(
                    new Query<TFeedBack>().getPage(pageEntity), queryWrapper);
            return new PageUtils(page);
        }
        return null;
    }
}
