package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GetUserMsgCenterListVO {

    @JsonProperty("ID")
    @ApiModelProperty(value = "自增")
    private Long id;

    @JsonProperty("PushID")
    @ApiModelProperty(value = "推送ID")
    private Long pushId;

    @JsonProperty("UserID")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @JsonProperty("UserCode")
    @ApiModelProperty(value = "用户编号")
    private String userCode;

    @JsonProperty("Title")
    @ApiModelProperty(value = "标题")
    private String title;

    @JsonProperty("Content")
    @ApiModelProperty(value = "内容")
    private String content;

    @JsonProperty("PushState")
    @ApiModelProperty(value = "推送状态")
    private Integer pushState;

    @JsonProperty("TargetID")
    @ApiModelProperty(value = "目标ID")
    private Long targetId;

    @JsonProperty(value = "IsRead")
    @ApiModelProperty(value = "是否已读")
    private Integer isRead;

    @JsonProperty("Url")
    @ApiModelProperty(value = "URL")
    private String url;

    @JsonProperty("DeviceNumber")
    @ApiModelProperty(value = "设备号")
    private String deviceNumber;

    @JsonProperty("PushTime")
    @ApiModelProperty(value = "推送时间")
    private String pushTime;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("FunType")
    @ApiModelProperty(value = "功能类型")
    private Integer funType;

    @JsonProperty("FunTypeChild")
    @ApiModelProperty(value = "功能子类型")
    private String funTypeChild;

    @JsonProperty("ImgUrl")
    @ApiModelProperty(value = "图标")
    private String imgUrl="";
}
