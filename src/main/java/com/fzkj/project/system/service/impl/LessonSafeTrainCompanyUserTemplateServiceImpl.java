package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.service.CompanyLessonSafeTrainSetService;
import com.fzkj.project.system.service.CompanyService;
import com.fzkj.project.system.service.UserInfoService;
import com.fzkj.project.system.vo.AuthUserInfoVO;
import com.fzkj.project.system.vo.LessonSafeTrainCompanyUserTemplateVO;
import com.fzkj.project.system.request.LessonSafeTrainCompanyUserTemplateRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.LessonSafeTrainCompanyUserTemplateMapper;
import com.fzkj.project.system.service.LessonSafeTrainCompanyUserTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 企业下的用户安全培训课程用户模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class LessonSafeTrainCompanyUserTemplateServiceImpl extends ServiceImpl<LessonSafeTrainCompanyUserTemplateMapper, LessonSafeTrainCompanyUserTemplate> implements LessonSafeTrainCompanyUserTemplateService {

    private final CompanyService companyService;
    private final UserInfoService userInfoService;
    private final CompanyLessonSafeTrainSetService companyLessonSafeTrainSetService;

    @Override
    public PageUtils listLessonSafeTrainCompanyUserTemplate(LessonSafeTrainCompanyUserTemplateRequest request, PageEntity pageEntity) {
        Long departId = request.getDepartId();
        List<Long> subDeptIds = null;
        if (departId != null) {
            subDeptIds = companyService.getSubDeptIds(departId);
        }
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        switch (request.getFlag()) {
            case 1:
                if (departId != null && departId > 0) {
                    request.setDepartIds(subDeptIds);
                }
                return new PageUtils(pageEntity, getBaseMapper().selectLessonSafeTrainCompanyUserTemplate1(request));
            case 3:
                if (departId != null) {
                    request.setDepartIds(subDeptIds);
                }
                return new PageUtils(pageEntity, getBaseMapper().selectLessonSafeTrainCompanyUserTemplate3(request));
            default:
                break;
        }
        throw new CustomException("不支持的操作标识"+request.getFlag());
    }

    @Override
    public PageUtils listByPage(LessonSafeTrainCompanyUserTemplateRequest request, PageEntity pageEntity) {
        LessonSafeTrainCompanyUserTemplate entity = (LessonSafeTrainCompanyUserTemplate) DataTransfer.transfer(request, LessonSafeTrainCompanyUserTemplate.class);
        LambdaQueryWrapper<LessonSafeTrainCompanyUserTemplate> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<LessonSafeTrainCompanyUserTemplate> page = this.page(
                new Query<LessonSafeTrainCompanyUserTemplate>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public LessonSafeTrainCompanyUserTemplateVO findLessonSafeTrainCompanyUserTemplate(Long id) {
        LessonSafeTrainCompanyUserTemplate entity = this.getById(id);
        LessonSafeTrainCompanyUserTemplateVO vo = (LessonSafeTrainCompanyUserTemplateVO) DataTransfer.transfer(entity, LessonSafeTrainCompanyUserTemplateVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveLessonSafeTrainCompanyUserTemplate(LessonSafeTrainCompanyUserTemplateVO vo) {
        LessonSafeTrainCompanyUserTemplate entity = (LessonSafeTrainCompanyUserTemplate) DataTransfer.transfer(vo, LessonSafeTrainCompanyUserTemplate.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLessonSafeTrainCompanyUserTemplate(LessonSafeTrainCompanyUserTemplateVO vo) {
        LessonSafeTrainCompanyUserTemplate entity = (LessonSafeTrainCompanyUserTemplate) DataTransfer.transfer(vo, LessonSafeTrainCompanyUserTemplate.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeLessonSafeTrainCompanyUserTemplate(Long id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editLessonSafeTrainCompanyUserTemplate(LessonSafeTrainCompanyUserTemplateVO request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        switch (request.getFlag()) {
            case "add":
                if (null == request.getLessonCategoryId() || request.getLessonCategoryId() <= 0) {
                    throw new CustomException("请选择培训分类");
                }
                if (StringUtils.isEmpty(request.getUserCodes())) {
                    throw new CustomException("请选择要指定添加的用户");
                }
                String[] userCodeArray = request.getUserCodes().split(",");
                Company company = companyService.getById(request.getCompanyId());
                LambdaQueryWrapper<CompanyLessonSafeTrainSet> trainSetQueryWrapper = new LambdaQueryWrapper<>();
                trainSetQueryWrapper.eq(CompanyLessonSafeTrainSet::getCompanyId, request.getCompanyId());
                trainSetQueryWrapper.eq(CompanyLessonSafeTrainSet::getLessonCategoryId, request.getLessonCategoryId());
                trainSetQueryWrapper.eq(CompanyLessonSafeTrainSet::getIsValid, 1);
                List<CompanyLessonSafeTrainSet> list = companyLessonSafeTrainSetService.list(trainSetQueryWrapper);
                CompanyLessonSafeTrainSet companyLessonSafeTrainSet = list.get(0);
                List<LessonSafeTrainCompanyUserTemplate> addList =  new ArrayList<>();
                for (int i = 0; i < userCodeArray.length; i++) {
                    String userCode = userCodeArray[i];
                    LambdaQueryWrapper<LessonSafeTrainCompanyUserTemplate> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(LessonSafeTrainCompanyUserTemplate::getIsValid, 1);
                    queryWrapper.eq(LessonSafeTrainCompanyUserTemplate::getUserCode, userCode);
                    queryWrapper.eq(LessonSafeTrainCompanyUserTemplate::getCompanyId, request.getCompanyId());
                    queryWrapper.eq(LessonSafeTrainCompanyUserTemplate::getLessonCategoryId, request.getLessonCategoryId());
                    int count = this.count(queryWrapper);
                    if (count > 0) {
                        continue;
                    }
                    AuthUserInfoVO authUserInfoVO = userInfoService.selectUserByUserCode(userCode);
                    LessonSafeTrainCompanyUserTemplate entity = (LessonSafeTrainCompanyUserTemplate) DataTransfer.transfer(request, LessonSafeTrainCompanyUserTemplate.class);
                    entity.setCompanyName(company.getCompanyName());
                    entity.setCompanyLessonPayId(companyLessonSafeTrainSet.getId());
                    entity.setIsPayType(companyLessonSafeTrainSet.getIsPayType());
                    entity.setCompanyId(request.getCompanyId());
                    entity.setCompanyName(company.getCompanyName());
                    entity.setUserCode(userCode);
                    entity.setUserName(authUserInfoVO.getUserName());
                    entity.setHandMode(companyLessonSafeTrainSet.getHandMode());
                    entity.setIsTrusteeship(companyLessonSafeTrainSet.getIsTrusteeship());
                    entity.setCreatorCode(user.getCreatorCode());
                    entity.setCreationTime(now);
                    entity.setReviseCode(user.getUserCode());
                    entity.setReviseTime(now);
                    entity.setIsValid(1);
                    addList.add(entity);
                }
                if(addList.isEmpty()){
                    throw new CustomException("只能存在一个安全培训的类型");
                }
                return this.saveBatch(addList);
            case "del":
            case "delByID":
                LambdaUpdateWrapper<LessonSafeTrainCompanyUserTemplate> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(LessonSafeTrainCompanyUserTemplate::getIsValid,0);
                updateWrapper.set(LessonSafeTrainCompanyUserTemplate::getReviseTime,now);
                updateWrapper.eq(LessonSafeTrainCompanyUserTemplate::getIsValid,1);
                updateWrapper.in(LessonSafeTrainCompanyUserTemplate::getId,request.getUserCodes().split(","));
                return this.update(updateWrapper);
            case "edit":
                LessonSafeTrainCompanyUserTemplate entity = (LessonSafeTrainCompanyUserTemplate) DataTransfer.transfer(request, LessonSafeTrainCompanyUserTemplate.class);
                return this.updateById(entity);
            default:
                break;
        }
        throw new CustomException("不支持的操作标识"+request.getFlag());
    }
}