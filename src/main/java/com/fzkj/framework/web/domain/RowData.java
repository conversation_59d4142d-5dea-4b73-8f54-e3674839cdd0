package com.fzkj.framework.web.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.alibaba.fastjson2.annotation.JSONType;
import com.fzkj.framework.config.CustomRowSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@AllArgsConstructor
@JSONType(serializer = CustomRowSerialize.class)
public class RowData {
    @JSONField(name = "Num")
    private int num;
    @JSONField(serialize = false)
    private Object data;
}