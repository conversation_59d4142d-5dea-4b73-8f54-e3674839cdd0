<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.TeacherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.Teacher">
        <id column="id" property="id" />
        <result column="photo" property="photo" />
        <result column="real_name" property="realName" />
        <result column="phone" property="phone" />
        <result column="education" property="education" />
        <result column="graduation_school" property="graduationSchool" />
        <result column="be_good_at" property="beGoodAt" />
        <result column="unit" property="unit" />
        <result column="work_post" property="workPost" />
        <result column="technical_title" property="technicalTitle" />
        <result column="person_introduct" property="personIntroduct" />
        <result column="login_account" property="loginAccount" />
        <result column="login_pwd" property="loginPwd" />
        <result column="login_pwd_md5" property="loginPwdMd5" />
        <result column="audit_status" property="auditStatus" />
        <result column="audit_code" property="auditCode" />
        <result column="audit_time" property="auditTime" />
        <result column="audit_reason" property="auditReason" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="recycle_bin" property="recycleBin" />
        <result column="is_valid" property="isValid" />
        <result column="is_show" property="isShow" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, photo, real_name, phone, education, graduation_school, be_good_at, unit, work_post, technical_title, person_introduct, login_account, login_pwd, login_pwd_md5, audit_status, audit_code, audit_time, audit_reason, sort, creator_code, creation_time, revise_code, revise_time, remark, recycle_bin, is_valid, is_show
    </sql>
    <select id="selectTeacher" resultType="com.fzkj.project.system.entity.Teacher">
        select t.*,
        (select count(1) from t_d_course c where c.tearcher_code = t.id and c.is_valid = 1)as courseNum,
        (select count(1) from t_d_course c,t_d_comment co where c.tearcher_code = t.id and c.is_valid = 1 and co.target_id = t.id and co.is_valid = 1 and co.fun_type = 10 and co.grade=0 and co.status = 1)as commentNum
        from t_d_teacher t
        where t.is_valid = 1
        <if test="flag != null and flag == 'byId'">
            and  t.id = #{id}
        </if>
        <if test="flag != null and flag == 'list'">
            <if test="keyWord != null and keyWord != ''">
                and real_name like concat('%',#{keyWord},'%')
            </if>
            <if test="recycleBin != null">
                and recycle_bin = #{recycleBin}
            </if>
            <if test="isShow != null">
                and is_show = #{isShow}
            </if>
        </if>
        <if test="flag != null and flag == 'mobile'">
            <if test="keyWord != null and keyWord != ''">
                and real_name like concat('%',#{keyWord},'%')
            </if>
            <if test="recycleBin != null">
                and recycle_bin = #{recycleBin}
            </if>
            <if test="isShow != null">
                and is_show = #{isShow}
            </if>
        </if>
        ORDER BY
            t.sort ASC
    </select>

</mapper>