<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.LessonSafeTrainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.LessonSafeTrain">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="depart_id" property="departId" />
        <result column="lesson_date" property="lessonDate" />
        <result column="lesson_category_id" property="lessonCategoryId" />
        <result column="lesson_category_name" property="lessonCategoryName" />
        <result column="lesson_name" property="lessonName" />
        <result column="introduce" property="introduce" />
        <result column="lesson_summary" property="lessonSummary" />
        <result column="lesson_pic" property="lessonPic" />
        <result column="study_sign_state" property="studySignState" />
        <result column="study_face_state" property="studyFaceState" />
        <result column="study_count_down_time" property="studyCountDownTime" />
        <result column="study_fail_repeat_num" property="studyFailRepeatNum" />
        <result column="cours_face_num" property="coursFaceNum" />
        <result column="s_time" property="sTime" />
        <result column="e_time" property="eTime" />
        <result column="shape" property="shape" />
        <result column="total_time_count" property="totalTimeCount" />
        <result column="belong_plat" property="belongPlat" />
        <result column="hand_mode" property="handMode" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="reserved_field1" property="reservedField1" />
        <result column="reserved_field2" property="reservedField2" />
        <result column="reserved_field3" property="reservedField3" />
        <result column="reserved_field4" property="reservedField4" />
        <result column="reserved_field5" property="reservedField5" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, depart_id, lesson_date, lesson_category_id, lesson_category_name, lesson_name, introduce, lesson_summary, lesson_pic, study_sign_state, study_face_state, study_count_down_time, study_fail_repeat_num, cours_face_num, s_time, e_time, shape, total_time_count, belong_plat, hand_mode, source, sort, creator_code, creation_time, revise_code, revise_time, remark, is_valid, reserved_field1, reserved_field2, reserved_field3, reserved_field4, reserved_field5
    </sql>
    <select id="selectLessonSafeTrain" resultType="com.fzkj.project.system.vo.LessonSafeTrainVO">
        SELECT l.*,
        (SELECT COUNT(*) FROM t_d_lesson_safe_train_company WHERE is_valid = 1 AND lesson_id = l.id) company_num
        FROM t_d_lesson_safe_train l
        WHERE l.is_valid != 0
        <if test="isValid != null ">
            AND l.is_valid = #{isValid}
        </if>
        <if test="belongPlat != null ">
            AND l.belong_plat = #{belongPlat}
        </if>
        <if test="shape != null">
            AND l.shape = #{shape}
        </if>
        <if test="companyId != null">
            AND l.company_id = #{companyId}
        </if>
        <if test="keywords != null and keywords != ''">
            AND l.lesson_name LIKE CONCAT('%', #{keywords}, '%')
        </if>
        <if test="sTime != null and sTime != ''">
            AND l.lesson_date >= #{sTime}
        </if>
        <if test="eTime != null and eTime != ''">
            AND l.lesson_date  <![CDATA[ <= ]]> #{eTime}
        </if>
        <if test="lessonCategoryId != null">
            AND l.lesson_category_id = #{lessonCategoryId}
        </if>
        <if test="totalTimeCount != null">
            AND l.total_time_count = #{totalTimeCount}
        </if>
        <if test="handMode != null">
            AND l.hand_mode = #{handMode}
        </if>
        ORDER BY l.id DESC
    </select>
    <select id="getCourseByLessonSafeTrainId1" resultType="com.fzkj.project.system.vo.LessonSafeTrainCourseVO">
        SELECT
        l.sort,
        l.lesson_id,
        l.course_id,
        c.category_id,
        c.category_name,
        c.course_name,
        c.time_count,
        c.belong_plat,
        c.file_type,
        s.lesson_pic
        FROM
        t_d_lesson_course_safe_train l
        LEFT JOIN t_d_course c ON l.course_id = c.id
        LEFT JOIN t_d_lesson_safe_train s ON l.lesson_id = s.id
        WHERE
        l.is_valid = 1
        <if test="null != lessonId">
            AND l.lesson_id = #{lessonId}
        </if>
        ORDER BY
        l.sort ASC

    </select>
    <select id="getCourseByLessonSafeTrainId2" resultType="com.fzkj.project.system.vo.LessonSafeTrainCourseVO">
        SELECT * FROM (
        SELECT
        l.*,
        IFNULL(cr.encode_state, 0) AS encode_state
        FROM
        t_d_course l
        LEFT JOIN
        t_d_courseware_resources cr ON l.file_id = cr.file_id
        WHERE
        l.is_valid = 1
        <if test="null != fileType">
            AND l.file_type = #{fileType}
        </if>
        <if test="null != belongPlat">
            AND l.belong_plat = #{belongPlat}
        </if>
        <if test="null != isShow">
            AND l.is_show = #{isShow}
        </if>
        <if test="null != courseName and courseName != ''">
            AND
            (
            CASE
            WHEN REGEXP_LIKE(#{courseName}, '^0-9+$') THEN CAST(l.id AS CHAR)
            ELSE l.course_name
            END
            ) LIKE CONCAT('%', #{courseName}, '%')
        </if>
        AND l.id NOT IN (select l1.course_id
        from t_d_lesson_course_safe_train l1
        where l1.is_valid=1
        and l1.lesson_id=#{lessonId})
        ) t1
        WHERE
        t1.encode_state = 0
        ORDER BY
        t1.creation_time DESC
    </select>

    <select id="getCourseByLessonSafeTrainId3" resultType="com.fzkj.project.system.vo.LessonSafeTrainCourseVO">
        SELECT * FROM (
        SELECT
        l.*,
        IFNULL(cr.encode_state, 0) AS encode_state
        FROM
        t_d_course l
        LEFT JOIN
        t_d_courseware_resources cr ON l.file_id = cr.file_id
        WHERE
        l.is_valid = 1
        AND (
        l.belong_plat = 2 OR
        (l.belong_plat = 1 AND l.is_show = 1)
        )
        <if test="null != fileType">
            AND l.file_type = #{fileType}
        </if>
        <if test="null != belongPlat">
            AND l.belong_plat = #{belongPlat}
        </if>
        <if test="null != courseName and courseName != ''">
            AND
            (
            CASE
            WHEN REGEXP_LIKE(#{courseName}, '^0-9+$') THEN CAST(l.id AS CHAR)
            ELSE l.course_name
            END
            ) LIKE CONCAT('%', #{courseName}, '%')
        </if>
        AND l.id NOT IN (select l1.course_id
        from t_d_lesson_course_safe_train l1
        where l1.is_valid=1
        and l1.lesson_id=#{lessonId})
        ) t1
        WHERE
        t1.encode_state = 0
        ORDER BY
        t1.creation_time DESC
    </select>
    <select id="selectLessonSafeTrainListBus" resultType="com.fzkj.project.system.entity.LessonSafeTrain">
        SELECT l.*
        FROM t_d_lesson_safe_train l
        WHERE l.is_valid = 1
        <if test="null != lessonName and lessonName != ''">
            AND  l.lesson_name like CONCAT('%', #{lessonName}, '%')
        </if>
        <if test="null != lessonCategoryId">
            AND  l.lesson_category_id = #{lessonCategoryId}
        </if>
        AND  l.lesson_date >= #{lessonDate}
        and l.id not in ( select lesson_id from t_d_lesson_safe_train_plan_relationship where plan_id=#{planId})
        order by l.id desc
    </select>
    <select id="selectLessonSafeTrainListByPlanIdBus" resultType="com.fzkj.project.system.entity.LessonSafeTrain">
        SELECT l.*
        FROM t_d_lesson_safe_train_plan_relationship r
        left join t_d_lesson_safe_train l on l.id = r.lesson_id
        WHERE r.is_valid = 1
        and r.plan_id = #{planId}
        <if test="null != lessonName and lessonName != ''">
            AND  l.lesson_name like CONCAT('%', #{lessonName}, '%')
        </if>
        <if test="null != lessonCategoryId">
            AND  lesson_category_id = #{lessonCategoryId}
        </if>
        order by l.id desc
    </select>
    <select id="selectDistributeCompanyIds" resultType="java.lang.Long">
        select s.company_id
        from t_d_company_lesson_safe_train_set s
        left join t_d_company c on c.id=s.company_id
        left join t_d_company_extend ce on ce.company_id=s.company_id
        where s.is_valid=1 and s.lesson_category_id=#{lessonCategoryId} and s.total_time_count=#{totalTimeCount} and c.is_valid=1
        AND
        (
        CASE
        WHEN CHAR_LENGTH(s.e_time) > 9
        THEN TIMESTAMPDIFF(DAY, curdate(), STR_TO_DATE(s.e_time, '%Y-%m-%d'))
        ELSE TIMESTAMPDIFF(DAY, curdate(), LAST_DAY(STR_TO_DATE(CONCAT(s.e_time, '-01'), '%Y-%m-%d')))
        END
        ) >= 0
    </select>
    <select id="selectLessonSafeTrainListC" resultType="com.fzkj.project.system.vo.LessonSafeTrainVO">
        select * from (select l.*,
        (select staff_price from t_d_company_lesson_safe_train_set where company_id=l.company_id and lesson_category_id=l.lesson_category_id ) LessonPrice
        FROM t_d_lesson_safe_train l
        WHERE  l.is_valid !=0
        <if test="isValid != null">
            AND l.is_valid = #{isValid}
        </if>
        <if test="shape != null">
            AND l.shape = #{shape}
        </if>
        and l.belong_plat=2
        <if test="companyId != null">
            AND l.company_id = #{companyId}
        </if>
        <if test="keywords != null and keywords != ''">
            AND l.lesson_name LIKE CONCAT('%', #{keywords}, '%')
        </if>
        AND DATEDIFF(l.lesson_date,#{sTime})>=0
        AND DATEDIFF(#{eTime},l.lesson_date)>0
        union
        select l.*,
        (select staff_price from t_d_company_lesson_safe_train_set where company_id=l.company_id and lesson_category_id=l.lesson_category_id ) LessonPrice
        FROM t_d_lesson_safe_train l
        left join t_d_lesson_safe_train_company c on c.lesson_id=l.id
        WHERE  l.is_valid !=0
        <if test="isValid != null">
            AND l.is_valid = #{isValid}
        </if>
        <if test="shape != null">
            AND l.shape = #{shape}
        </if>
        and l.belong_plat=1
        and c.is_valid = 1
        <if test="companyId != null">
            AND c.company_id = #{companyId}
        </if>
        <if test="keywords != null and keywords != ''">
            AND l.lesson_name LIKE CONCAT('%', #{keywords}, '%')
        </if>
        AND DATEDIFF(l.lesson_date,#{sTime})>=0
        AND DATEDIFF(#{eTime},l.lesson_date)>0
        ) t WHERE 1=1
        <if test="null != belongPlat">
            AND belong_plat = #{belongPlat}
        </if>
        ORDER BY id DESC
    </select>

    <select id="selectLessonSafeTrainListAPP" resultType="com.fzkj.project.system.vo.LessonSafeTrainVO">
        select l.*
        FROM t_d_lesson_safe_train l
        left join t_d_lesson_safe_train_company c on c.lesson_id=l.id
        left join t_d_company c1 on c1.id=l.company_id
        WHERE  l.is_valid = 1 and ((l.belong_plat = 1 and c.is_valid=1) or l.belong_plat = 2)
        <if test="companyId != null">
            AND c.company_id = #{companyId}
        </if>
        <if test="lessonName != null and lessonName != ''">
            AND l.lesson_name LIKE CONCAT('%', #{lessonName}, '%')
        </if>
        ORDER BY l.id DESC
    </select>
    <select id="selectCourseByLessonSafeTrainId" resultType="com.fzkj.project.system.vo.CourseVO">
        select * from t_d_lesson_course_safe_train lc
        left join t_d_course c on c.id=lc.course_id
        where lc.lesson_id=#{lessonId} AND lc.is_valid=1
    </select>
    <select id="selectCourseAPP" resultType="com.fzkj.project.system.vo.GetCourseAPPVO">
        SELECT c.*,IFNULL(cr.encode_state,0) encodeState
        FROM t_d_course c LEFT JOIN t_d_courseware_resources cr ON c.file_id=cr.file_id
        WHERE c.is_valid=1
        <if test="tearcherCode != null">
            AND c.tearcher_code = #{tearcherCode}
        </if>
        <if test="categoryId != null">
            AND c.category_id = #{categoryId}
        </if>
        and c.company_id = 0
        <if test="keywords != null and keywords!=''">
            AND c.course_name LIKE CONCAT('%', #{keywords}, '%')
        </if>
        ORDER BY c.id DESC
    </select>
    <select id="selectCourseAPPById" resultType="com.fzkj.project.system.vo.GetCourseAPPVO">
        SELECT c.*
        ,(SELECT ROUND(AVG(COALESCE(grade, 0)), 0) FROM t_d_comment WHERE is_valid=1 AND target_id=#{id} AND fun_type='10' AND grade>0) grade
        ,(SELECT COUNT(1) FROM t_d_comment ct WHERE ct.fun_type='10' AND ct.target_id=#{id} AND ct.is_valid=1 AND ct.status=1 AND LENGTH(ct.content)>0) commentNum
        ,CASE WHEN (SELECT COUNT(1) FROM t_d_comment ct WHERE ct.fun_type='10' AND ct.target_id=c.id AND ct.is_valid=1 AND grade>0 AND ct.user_code=#{userCode})>0 THEN 1 ELSE 0 END isGrade
        ,IFNULL(cr.file_url_hls,c.file_url) fileUrl
        FROM t_d_course c LEFT JOIN t_d_courseware_resources cr ON c.file_id=cr.file_id WHERE c.id=#{id}
    </select>
    <select id="selectLessonSafeTrainUserTemplateList" resultType="com.fzkj.project.system.vo.LessonSafeTrainUserTemplateListVO">
        select u.id,u.lesson_category_id,u.lesson_category_name,u.company_lesson_pay_id,u.hand_mode,'学员免费' payTypeStr,c.is_trusteeship,c.s_time,c.e_time,c.total_time_count
        from t_d_lesson_safe_train_company_user_template u
        left join t_d_company_lesson_safe_train_set c on c.id = u.company_lesson_pay_id where u.is_valid=1 and u.user_code = #{userCode}
    </select>
    <select id="selectLessonSafeTrainByCompanyIDAndLessonName"
            resultType="com.fzkj.project.system.entity.LessonSafeTrain">
        SELECT t.*
        FROM
        (SELECT
        l.*
        FROM t_d_lesson_safe_train l
        WHERE l.is_valid=1
        and l.belong_plat=2
        <if test="companyId != -999">and l.company_id=#{companyId}
        </if>
        <if test="lessonName != null and lessonName != ''">
            and l.
            lesson_name like concat('%',#{lessonName},'%')
        </if>
          and datediff(CURDATE(),l.e_time) &lt; 0
        union
        SELECT
            l.*
        FROM t_d_lesson_safe_train l
        left join t_d_lesson_safe_train_company c on c.lesson_id=l.id
        WHERE c.is_valid = 1
          and l.belong_plat=1
          and l.is_valid = 1
        <if test="companyId != -999">
          and c.company_id=#{companyId}
        </if>
        <if test="lessonName != null and lessonName != ''">
            and l.lesson_name like concat('%',#{lessonName},'%')
        </if>
        and datediff(CURDATE(),l.e_time) &lt;
        0
        ) t
        WHERE
        t.lesson_category_id IN (
        SELECT
        lesson_category_id
        FROM
        t_d_lesson_safe_train_company_user_template
        WHERE
        is_valid = 1
        AND user_code = #{userCode})
    </select>
    <select id="selectLessonSafeTrainListC1" resultType="com.fzkj.project.system.vo.LessonSafeTrainVO">
        select * from (select l.*,
        (select staff_price from t_d_company_lesson_safe_train_set where company_id=l.company_id and lesson_category_id=l.lesson_category_id ) LessonPrice
        FROM t_d_lesson_safe_train l
        WHERE  l.is_valid =1
        <if test="shape != null">
            AND l.shape = #{shape}
        </if>
        and l.belong_plat=2
        <if test="companyId != null">
            AND l.company_id = #{companyId}
        </if>
        <if test="keywords != null and keywords != ''">
            AND l.lesson_name LIKE CONCAT('%', #{keywords}, '%')
        </if>
        AND DATEDIFF(l.e_time, CURDATE()) >= 0
        union
        select l.*,
        (select staff_price from t_d_company_lesson_safe_train_set where company_id=l.company_id and lesson_category_id=l.lesson_category_id ) LessonPrice
        FROM t_d_lesson_safe_train l
        left join t_d_lesson_safe_train_company c on c.lesson_id=l.id
        WHERE  l.is_valid =1
        <if test="shape != null">
            AND l.shape = #{shape}
        </if>
        and l.belong_plat=1
        and c.is_valid = 1
        <if test="companyId != null">
            AND c.company_id = #{companyId}
        </if>
        <if test="keywords != null and keywords != ''">
            AND l.lesson_name LIKE CONCAT('%', #{keywords}, '%')
        </if>
        AND DATEDIFF(l.e_time, CURDATE()) >= 0
        ) t WHERE 1=1
        <if test="null != belongPlat">
            AND belong_plat = #{belongPlat}
        </if>
        ORDER BY id DESC
    </select>
    <select id="selectLessonSafeTrainListCAll" resultType="com.fzkj.project.system.vo.LessonSafeTrainVO">
        select * from (select l.*,
        (select staff_price from t_d_company_lesson_safe_train_set where company_id=l.company_id and lesson_category_id=l.lesson_category_id ) LessonPrice
        FROM t_d_lesson_safe_train l
        WHERE  l.is_valid =1
        <if test="shape != null">
            AND l.shape = #{shape}
        </if>
        and l.belong_plat=2
        <if test="companyId != null">
            AND l.company_id = #{companyId}
        </if>
        <if test="keywords != null and keywords != ''">
            AND l.lesson_name LIKE CONCAT('%', #{keywords}, '%')
        </if>
        union
        select l.*,
        (select staff_price from t_d_company_lesson_safe_train_set where company_id=l.company_id and lesson_category_id=l.lesson_category_id ) LessonPrice
        FROM t_d_lesson_safe_train l
        left join t_d_lesson_safe_train_company c on c.lesson_id=l.id
        WHERE  l.is_valid =1
        <if test="shape != null">
            AND l.shape = #{shape}
        </if>
        and l.belong_plat=1
        and c.is_valid = 1
        <if test="companyId != null">
            AND c.company_id = #{companyId}
        </if>
        <if test="keywords != null and keywords != ''">
            AND l.lesson_name LIKE CONCAT('%', #{keywords}, '%')
        </if>
        ) t WHERE 1=1
        <if test="null != belongPlat">
            AND belong_plat = #{belongPlat}
        </if>
        ORDER BY id DESC
    </select>
    <select id="selectLessonSafeTrainIds" resultType="java.lang.Long">
        SELECT l.id
        FROM t_d_lesson_safe_train l
        WHERE l.is_valid != 0
          AND l.lesson_date = #{sTime}
        ORDER BY l.id DESC
    </select>
    <select id="selectLessonSafeTrainIdsNoMonth" resultType="java.lang.Long">
        SELECT l.id
        FROM t_d_lesson_safe_train l
        WHERE l.is_valid != 0
          AND l.lesson_date &lt;&gt; #{sTime}
        ORDER BY l.id DESC
    </select>
    <select id="selectIdByLessonName" resultType="java.lang.Long">
        SELECT l.id
        FROM t_d_lesson_safe_train l
        WHERE l.lesson_name = #{lessonName}
    </select>
    <select id="selectAllByLessonId" resultType="com.fzkj.project.system.entity.lessonSafeTrainTd">
        SELECT l.user_id,
               l.sign_url
        FROM t_d_exam_user_record l
        WHERE l.lesson_id = #{lessonId}
            AND l.score>l.pass_mark
    </select>
</mapper>