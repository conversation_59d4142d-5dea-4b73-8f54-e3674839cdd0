package com.fzkj.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "sms")
@Data
public class SmsProperties {
    private String url;

    private String userName;

    private String password;

    private String signName;

    private String templateCode;

    private String version;
}
