package com.fzkj.project.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.UserLimitService;
import com.fzkj.project.system.vo.UserLimitVO;
import com.fzkj.project.system.request.UserLimitRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 用户管理机构表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userPort")
public class UserLimitController extends BaseEntity {

    private final UserLimitService UserLimitServiceImpl;

    @PostMapping("GetUserLimit")
    @ApiOperation(value = "用户管理机构表下拉查询")
    public AjaxResult listUserLimit(@RequestBody CommonRequest commonRequest) {
        UserLimitRequest request = (UserLimitRequest) commonRequest.getRequest(UserLimitRequest.class);
        if(request.getFlag().equals("user")){
            return AjaxResult.success(UserLimitServiceImpl.listUserLimitPage(request,commonRequest.getPageEntity()));
        }else{
            return AjaxResult.success(UserLimitServiceImpl.listUserLimit(request));
        }

    }

//    @PostMapping("listByPage")
//    @ApiOperation(value = "用户管理机构表分页查询")
//    public AjaxResult listUserLimitByPage(@RequestBody CommonRequest commonRequest) {
//        UserLimitRequest request = (UserLimitRequest) commonRequest.getRequest(UserLimitRequest.class);
//        return AjaxResult.success(UserLimitServiceImpl.listByPage(request, commonRequest.getPageEntity()));
//    }
//
//    @GetMapping("{id}")
//    @ApiOperation(value = "用户管理机构表查询详情")
//    public AjaxResult findUserLimit(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(UserLimitServiceImpl.findUserLimit(request.getId()));
//    }
//
//    @PostMapping
//    @ApiOperation(value = "用户管理机构表新增数据")
//    public AjaxResult saveUserLimit(@RequestBody CommonRequest commonRequest) {
//        UserLimitVO vo = (UserLimitVO) commonRequest.getRequest(UserLimitVO.class);
//        return AjaxResult.success(UserLimitServiceImpl.saveUserLimit(vo));
//    }
//
    @PostMapping("EditUserLimit")
    @ApiOperation(value = "用户管理机构表修改数据")
    public AjaxResult updateUserLimit(@RequestBody CommonRequest commonRequest) {
        UserLimitVO vo = (UserLimitVO) commonRequest.getRequest(UserLimitVO.class);
        if(vo.getFlag().equals("cancel")){
            //删除管理员数据
            return AjaxResult.success(UserLimitServiceImpl.removeUserLimit(vo));
        }else if(vo.getFlag().equals("set")){
            //新增管理员数据
            return AjaxResult.success(UserLimitServiceImpl.saveUserLimit(vo));
        }
        return AjaxResult.error("类型异常");
    }
//
//    @PostMapping("delete")
//    @ApiOperation(value = "用户管理机构表删除数据")
//    public AjaxResult removeUserLimit(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(UserLimitServiceImpl.removeUserLimit(request.getId()));
//    }

}
