package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户管理机构表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户管理机构表Request")
public class UserLimitRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonProperty("UserCode")
    private String userCode;

    @JsonProperty("TargetID")
    private Long targetId;

    @JsonProperty("TargetType")
    private String targetType;

    @JsonProperty("PcRoleID")
    private String pcRoleId;

    @JsonProperty("MobileRoleID")
    private String mobileRoleId;

    @JsonProperty("OperPlatform")
    private Integer operPlatform;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("Sort")
    private Integer sort;

    @JsonProperty("CreatorCode")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("Keyword")
    private String keyword;

    @JsonProperty("Flag")
    private String flag;

    @JsonProperty("CompanyID")
    private String companyId;

}