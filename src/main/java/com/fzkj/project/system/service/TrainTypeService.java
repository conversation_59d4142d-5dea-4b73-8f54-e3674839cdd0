package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.TrainType;
import com.fzkj.project.system.vo.TrainTypeVO;
import com.fzkj.project.system.request.TrainTypeRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 安全培训课程分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface TrainTypeService extends IService<TrainType> {

    List<TrainType> listTrainType(TrainTypeRequest request);

    PageUtils listByPage(TrainTypeRequest request, PageEntity pageEntity);

    TrainTypeVO findTrainType(Long id);

    Long saveTrainType(TrainTypeVO vo);

    boolean updateTrainType(TrainTypeVO vo);

    boolean removeTrainType(Long id);
}
