package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GroupRollRequest {

    @ApiModelProperty(value = "用户ID")
    @JsonProperty("UserID")
    private String userId;

    @ApiModelProperty(value = "试卷ID")
    @JsonProperty("ExamID")
    private Integer examId;

    @ApiModelProperty(value = "用户补考次数")
    @JsonProperty("UserResitNumber")
    private Integer userResitNumber;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long LessonId;

    @ApiModelProperty(value = "操作标识")
    @JsonProperty("Flag")
    private Integer flag;

}
