package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AuthPortraitVerificationRequest {

    @ApiModelProperty(value = "比较模式", example = "1")
    @JsonProperty("ComparisonMode")
    private int comparisonMode;

    @ApiModelProperty(value = "比较照片URL")
    @JsonProperty("ComparisonPhotos")
    private String comparisonPhotos;

    @ApiModelProperty(value = "原始照片URL")
    @JsonProperty("OriginalPhotos")
    private String originalPhotos;
}
