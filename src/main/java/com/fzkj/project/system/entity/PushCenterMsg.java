package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_d_push_center_msg")
@ApiModel(value = "PushCenterMsg", description = "推送中心消息表")
public class PushCenterMsg {

    @TableId(value = "id", type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "自增")
    private Long id;

    @TableField("push_id")
    @JsonProperty("PushID")
    @ApiModelProperty(value = "推送ID")
    private Long pushId;

    @TableField("user_id")
    @JsonProperty("UserID")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @TableField("user_code")
    @JsonProperty("UserCode")
    @ApiModelProperty(value = "用户编号")
    private String userCode;

    @TableField("title")
    @JsonProperty("Title")
    @ApiModelProperty(value = "标题")
    private String title;

    @TableField("content")
    @JsonProperty("Content")
    @ApiModelProperty(value = "内容")
    private String content;

    @TableField("push_state")
    @JsonProperty("PushState")
    @ApiModelProperty(value = "推送状态")
    private Integer pushState;

    @TableField("target_id")
    @JsonProperty("TargetID")
    @ApiModelProperty(value = "目标ID")
    private Long targetId;

    @TableField("is_read")
    @JsonProperty(value = "IsRead")
    @ApiModelProperty(value = "是否已读")
    private Integer isRead;

    @TableField("url")
    @JsonProperty("Url")
    @ApiModelProperty(value = "URL")
    private String url;

    @TableField("device_number")
    @JsonProperty("DeviceNumber")
    @ApiModelProperty(value = "设备号")
    private String deviceNumber;

    @TableField("push_time")
    @JsonProperty("PushTime")
    @ApiModelProperty(value = "推送时间")
    private String pushTime;

    @TableField("creator_time")
    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @TableField("fun_type")
    @JsonProperty("FunType")
    @ApiModelProperty(value = "功能类型")
    private Integer funType;

    @TableField("fun_type_child")
    @JsonProperty("FunTypeChild")
    @ApiModelProperty(value = "功能子类型")
    private String funTypeChild;
}
