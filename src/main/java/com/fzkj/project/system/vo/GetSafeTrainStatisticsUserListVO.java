package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetSafeTrainStatisticsUserListVO {

    @ApiModelProperty(value = "用户编码")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "用户ID")
    @JsonProperty("UserID")
    private Long userId;

    @ApiModelProperty(value = "预留字段5")
    @JsonProperty("ReservedField5")
    private String reservedField5;

    @ApiModelProperty(value = "用户头像")
    @JsonProperty("UserPhoto")
    private String userPhoto;

    @ApiModelProperty(value = "用户名称")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "培训状态")
    @JsonProperty("StaticStatus")
    private Integer staticStatus;

    @ApiModelProperty(value = "考试ID")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "得分")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty(value = "课程类别名称")
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty(value = "部门名称")
    @JsonProperty("DepartName")
    private String departName;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课程名称")
    @JsonProperty("LessonName")
    private String lessonName;

}
