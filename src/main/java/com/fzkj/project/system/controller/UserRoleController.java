package com.fzkj.project.system.controller;

import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.UserRoleService;
import com.fzkj.project.system.vo.UserRoleVO;
import com.fzkj.project.system.request.UserRoleRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 用户关联角色 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userPort")
public class UserRoleController extends BaseEntity {

    private final UserRoleService UserRoleServiceImpl;

//    @PostMapping("list")
//    @ApiOperation(value = "用户关联角色列表查询")
//    public AjaxResult listUserRole(@RequestBody CommonRequest commonRequest) {
//        UserRoleRequest request = (UserRoleRequest) commonRequest.getRequest(UserRoleRequest.class);
//        return AjaxResult.success(UserRoleServiceImpl.listUserRole(request));
//    }

    @PostMapping("GetUserInner")
    @ApiOperation(value = "系统用户用户关联角色分页查询")
    public AjaxResult listUserRoleByPage(@RequestBody CommonRequest commonRequest) {
        UserRoleVO entity =(UserRoleVO) commonRequest.getRequest(UserRoleVO.class);
        return AjaxResult.success(UserRoleServiceImpl.listByPage(entity, commonRequest.getPageEntity()));
    }

//    @GetMapping("{id}")
//    @ApiOperation(value = "用户关联角色查询详情")
//    public AjaxResult findUserRole(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(UserRoleServiceImpl.findUserRole(request.getId()));
//    }
//
//    @PostMapping
//    @ApiOperation(value = "用户关联角色新增数据")
//    public AjaxResult saveUserRole(@RequestBody CommonRequest commonRequest) {
//        UserRoleVO vo = (UserRoleVO) commonRequest.getRequest(UserRoleVO.class);
//        return AjaxResult.success(UserRoleServiceImpl.saveUserRole(vo));
//    }
//
    @PostMapping("EditUserInner")
    @ApiOperation(value = "用户关联角色修改数据")
    public AjaxResult updateUserRole(@RequestBody CommonRequest commonRequest) {
        UserRoleVO vo = (UserRoleVO) commonRequest.getRequest(UserRoleVO.class);
        Integer count = UserRoleServiceImpl.updateUserRole(vo);
        if(count == null){
            return AjaxResult.error("当前用户已创建过系统用户！","");
        }else{
            return AjaxResult.success(count);
        }

    }

    @PostMapping("DeleteUserInner")
    @ApiOperation(value = "删除系统用户")
    public AjaxResult deleteUserInner(@RequestBody CommonRequest commonRequest) {
        UserRoleVO vo = (UserRoleVO) commonRequest.getRequest(UserRoleVO.class);
        return AjaxResult.success(UserRoleServiceImpl.removeUserRole(vo.getId()));
    }
//
//    @PostMapping("delete")
//    @ApiOperation(value = "用户关联角色删除数据")
//    public AjaxResult removeUserRole(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(UserRoleServiceImpl.removeUserRole(request.getId()));
//    }

}
