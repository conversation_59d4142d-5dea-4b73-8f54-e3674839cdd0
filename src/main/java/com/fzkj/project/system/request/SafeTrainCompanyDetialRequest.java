package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.security.handle.CustomParam;
import lombok.Data;

import java.util.List;

@Data
@CustomParam
public class SafeTrainCompanyDetialRequest {
    @JsonProperty("CompanyID")
    private Long companyId;

    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("DepartID")
    private Long departId;

    @JsonProperty("DepartIDs")
    private List<Long> departIds;

    @JsonProperty("LessonID")
    private Long lessonId;

    @JsonProperty("LearnFlag")
    private Integer learnFlag;

    @JsonProperty("LessonMonth")
    private String lessonMonth;

    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;
}
