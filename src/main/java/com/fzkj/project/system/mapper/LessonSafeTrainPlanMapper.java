package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.LessonSafeTrainPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.request.LessonSafeTrainCompanyStatisticRequest;
import com.fzkj.project.system.request.LessonSafeTrainPlanRequest;
import com.fzkj.project.system.vo.CompanyPlanTimeVO;
import com.fzkj.project.system.vo.LessonSafeTrainPlanVO;
import com.fzkj.project.system.vo.LessonTimeCountVO;
import com.fzkj.project.system.vo.SafeTrainCompanyStatisticsVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 公交培训计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface LessonSafeTrainPlanMapper extends BaseMapper<LessonSafeTrainPlan> {

    List<LessonSafeTrainPlanVO> getBusSafeTrainList(LessonSafeTrainPlanRequest request);

    List<LessonTimeCountVO> selectPlanLessonTotalTime(LessonSafeTrainPlanRequest request);

    List<CompanyPlanTimeVO> selectCompanyPlanTime(LessonSafeTrainPlanRequest request);

    List<SafeTrainCompanyStatisticsVO> getSafeTrainCompanyStatistics(LessonSafeTrainCompanyStatisticRequest request);

    List<SafeTrainCompanyStatisticsVO> getSafeTrainCompanyStatistics2(LessonSafeTrainCompanyStatisticRequest request);
}