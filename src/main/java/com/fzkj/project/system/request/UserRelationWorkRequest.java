package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户关联岗位
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户关联岗位Request")
public class UserRelationWorkRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private Long companyId;

    @ApiModelProperty(value = "")
    private Long workId;

    @ApiModelProperty(value = "")
    private String userCode;

    @ApiModelProperty(value = "")
    private String industryType;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "")
    private String creatorCode;

    @ApiModelProperty(value = "")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "")
    private String reviseCode;

    @ApiModelProperty(value = "")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty(value = "")
    private Integer isValid;

}