<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.olddata.mapper.OrganizationMapper">


    <select id="getSubDeptIds" resultType="java.lang.Long">
        SELECT id
        FROM t_g_organization
        where find_in_set(#{orgId}, Ancestors)
           or id = #{orgId}
    </select>
    <select id="getLessonLearnNumByCompanyDepartId"
            resultType="com.fzkj.project.system.vo.UserLessonRecordLearnNumVO">
        SELECT a.OrganizationID as companyId,
               LessonID         as lessonId,
               COUNT(*)         as learnNum,
               a.IsComplete     as staticStatus
        FROM `t_g_user_lesson_record` as a
                 LEFT JOIN t_g_organization as b ON a.OrganizationID = b.ID
        WHERE a.isValid = 1
          AND a.lessonID IN (SELECT id FROM t_g_train_lesson WHERE stagename = #{stageName})
        GROUP BY a.OrganizationID, a.IsComplete
    </select>
    <select id="getTwentyOneUserLessonRecordSafeTrainBus"
            resultType="com.fzkj.project.system.vo.UserLessonRecordSafeTrainUserVO">
        SELECT
        a.UserName AS userName,
        a.IDCard AS idCard,
        b.OrganizationName as departName,
        a.IsComplete as isComplete,
        a.CompleteTime as completeTime,
        a.TrainStaticStatus as  staticStatus,
        a.CreationTime as creationTime,
        c.Score as score,
        a.ExamID as examId,
        a.isValid as isValid,
        a.userCode as userCode,
        a.LessonID as lessonId
        FROM
        t_g_user_lesson_record AS a
        LEFT JOIN t_g_organization AS b ON a.OrganizationID = b.ID
        LEFT JOIN t_g_exam_user_record AS c ON a.ExamID = c.ID
        WHERE
        a.LessonID IN
        <foreach collection="lessonIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and a.OrganizationID IN
        <foreach collection="subDeptIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="staticStatusList  != null and staticStatusList.size() > 0">
            and a.IsComplete IN
            <foreach collection="staticStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="keyWord != null and keyWord !=''">
            and (a.UserCode like concat('%',#{keyWord},'%')
                or a.IDCard like concat('%',#{keyWord},'%')
                or a.UserName like concat('%',#{keyWord},'%'))
        </if>
    </select>
    <select id="getUserSafeTrainFilesDetail" resultType="com.fzkj.project.system.vo.LessonRecordVO">
        SELECT a.UserName AS userName,
               a.IDCard AS idCard,
               b.OrganizationName AS departName,
               a.IsComplete AS isComplete,
               a.CompleteTime AS completeTime,
               a.TrainStaticStatus AS staticStatus,
               a.CreationTime AS creationTime,
               c.Score AS score,
               a.ExamID AS examId,
               a.isValid AS isValid,
               a.userCode as userCode,
               a.LessonID as LessonId,
               a.SignImg as signImg,
               c.SignUrl as examSign,
               c.PhotoUrl as userPhoto
        FROM
            t_g_user_lesson_record AS a
                LEFT JOIN t_g_organization AS b ON a.OrganizationID = b.ID
                LEFT JOIN t_g_exam_user_record AS c ON a.ExamID = c.ExamID AND a.UserCode=c.UserCode
        WHERE
            a.LessonID=#{lessonId}
        and a.userCode=#{userCode}
    </select>
    <select id="getUserExamInfo" resultType="com.fzkj.project.system.vo.UserExamInfoVo">
        SELECT Score as score,
               PassMark as passMark,
               CreationTime as creationTime,
               UserExamCount as examCount
        FROM t_f_user_exam_record where userCode=#{userCode} and ExamID
        in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getUserCourse" resultType="com.fzkj.project.system.mongo.UserCourseRecordSafeTrain">
        select LessonID as lessonId,
               StartStudyTime as startStudyTime,
               IsComplete as courseIsComplete
        from t_g_user_lesson_record
        where LessonID = #{lessonId}
          and UserCode = #{userCode}
    </select>
    <select id="selectCourseRecord" resultType="com.fzkj.project.system.vo.CourseRecordVO">
        select LessonID as lessonId,
               CourseID as courseId,
               CourseName as courseName,
               Sort as name,
               FaceDistinguishImg as faceDistinguishImg,
               -3 course_is_complete,
               SignImg as signImg,
               '' start_study_time
        from t_g_user_course_record
        where LessonID = #{lessonId}
        and UserCode=#{userCode}
    </select>
</mapper>