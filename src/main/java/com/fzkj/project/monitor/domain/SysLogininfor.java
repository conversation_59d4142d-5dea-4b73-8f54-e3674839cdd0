package com.fzkj.project.monitor.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import com.fzkj.framework.aspectj.lang.annotation.Excel.ColumnType;
import com.fzkj.framework.web.domain.BaseEntity;
import lombok.Data;

/**
 * 系统访问记录表 logininfor
 *
 * <AUTHOR>
 */
@TableName("t_d_user_logininfor")
@Data
public class SysLogininfor{
    private static final long serialVersionUID = 1L;

    /** ID */
    @Excel(name = "访问编号", cellType = ColumnType.NUMERIC)
    @TableId
    private Long infoId;

    /** 用户账号 */
    @Excel(name = "用户名称")
    private String userName;

    /** 登录IP地址 */
    @Excel(name = "登录地址")
    private String ipaddr;

    /** 登录地点 */
    private String loginLocation;

    /** 浏览器类型 */
    @Excel(name = "浏览器")
    private String browser;

    /** 操作系统 */
    @Excel(name = "操作系统")
    private String os;

    /** 登录状态 0成功 1失败 */
    @Excel(name = "登录状态", readConverterExp = "0=成功,1=失败")
    private String status;

    /** 提示消息 */
    @Excel(name = "操作消息")
    private String msg;

    /** 访问时间 */
    @Excel(name = "登录日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date loginTime;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 平台:3-小程序,6-管理端,7-企业端*/
    private int platform;

    /** 开始时间 */
    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String beginTime;

    /** 结束时间 */
    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private int endTime;

}
