package com.fzkj.project.olddata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.olddata.dto.OrganizationEntity;
import com.fzkj.project.olddata.dto.TrainLessonEntity;
import com.fzkj.project.olddata.dto.request.NineteenFromTwentyOneRequest;
import com.fzkj.project.olddata.mapper.OrganizationMapper;
import com.fzkj.project.olddata.mapper.TrainLessonMapper;
import com.fzkj.project.olddata.service.NineteenFromTwentyOneService;
import com.fzkj.project.system.entity.Company;
import com.fzkj.project.system.entity.LessonSafeTrainPlanRelationship;
import com.fzkj.project.system.mongo.UserCourseRecordSafeTrain;
import com.fzkj.project.system.request.LessonSafeTrainPlanRequest;
import com.fzkj.project.system.request.UserSafeTrainFilesDetailRequest;
import com.fzkj.project.system.service.UserInfoService;
import com.fzkj.project.system.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class NineteenFromTwentyOneServiceImpl implements NineteenFromTwentyOneService {
    private final static List completeStatus = Arrays.asList(1, 2, 4);
    private final static List noBeginStatus = Arrays.asList(-3, 3, 4);
    private final static List noCompleteStatus = Arrays.asList(0, 2);
    private final static List completedStatus = Arrays.asList(2, 4);
    @Resource
    OrganizationMapper organizationMapper;
    @Resource
    TrainLessonMapper trainLessonMapper;


    @Override
    public PageUtils getOrgList(NineteenFromTwentyOneRequest request, PageEntity pageEntity) {
        List<Long> subDeptIds = organizationMapper.getSubDeptIds(request.getOrgId());
        LambdaQueryWrapper<OrganizationEntity> companyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyLambdaQueryWrapper.in(OrganizationEntity::getId, subDeptIds);
        List<OrganizationEntity> companyList = organizationMapper.selectList(companyLambdaQueryWrapper);
        LambdaQueryWrapper<TrainLessonEntity> lessonEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lessonEntityLambdaQueryWrapper.eq(TrainLessonEntity::getStageName, request.getStageName());
        List<TrainLessonEntity> list = trainLessonMapper.selectList(lessonEntityLambdaQueryWrapper);
        List<Long> lessonIds = list.stream().map(TrainLessonEntity::getId).collect(Collectors.toList());
        if (lessonIds.size() > 0) {
            request.setLessonIds(lessonIds);
            List<UserLessonRecordLearnNumVO> userLessonRecordLearnNumVOS = organizationMapper.getLessonLearnNumByCompanyDepartId(request.getStageName());
            DecimalFormat df = new DecimalFormat("#.##");
            if (CollectionUtils.isNotEmpty(userLessonRecordLearnNumVOS)) {
                List<OrganizationEntity> tempCompanyList = companyList.stream().filter(request.getChildren() == 0 ? company -> company.getId().equals(request.getOrgId()) : company -> company.getParentId().equals(request.getOrgId())).collect(Collectors.toList());
                List<BusSafeTrainOrgVO> result = new ArrayList<>();
                for (int i = 0; i < tempCompanyList.size(); i++) {
                    OrganizationEntity company = tempCompanyList.get(i);
                    BusSafeTrainOrgVO vo = new BusSafeTrainOrgVO();
                    vo.setId(company.getId());
                    vo.setName(company.getCompanyName());
                    vo.setFid(company.getParentId());
                    //根据id查找所有子部门id
                    List<Long> tempDepartIds = organizationMapper.getSubDeptIds(company.getId());
                    if (null == request.getLearnFlag()) {
                        vo.setSumNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId()))).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        vo.setLearnNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        vo.setCompleteNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && (item.getStaticStatus() == 1 || item.getStaticStatus() == 2 || item.getStaticStatus() == 4)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (request.getLearnFlag() == 0) {
                        vo.setSumNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && item.getStaticStatus() != 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        vo.setLearnNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && item.getStaticStatus() != 1 && item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        vo.setCompleteNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && (item.getStaticStatus() == 2 || item.getStaticStatus() == 4)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (request.getLearnFlag() == 1) {
                        vo.setSumNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId()))).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        vo.setLearnNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && (item.getStaticStatus() == 0 || item.getStaticStatus() == 1 || item.getStaticStatus() == 2)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        vo.setCompleteNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && item.getStaticStatus() == 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    }
                    vo.setCompleteRate(vo.getSumNumber() > 0 ? (df.format(vo.getCompleteNumber() * 1.00 / vo.getSumNumber() * 100)) + "%" : "0%");
                    vo.setLearnRate(vo.getSumNumber() > 0 ? (df.format(vo.getLearnNumber() * 1.00 / vo.getSumNumber() * 100)) + "%" : "0%");
                    vo.setIsNext(tempDepartIds.size() > 1 ? "1" : "0");
                    result.add(vo);
                }
                return new PageUtils(pageEntity, result);
            }
        }
        return null;
    }

    @Override
    public PageUtils getTwentyOneSafeTrainUserList(NineteenFromTwentyOneRequest request, PageEntity pageEntity) {
        List<Long> subDeptIds = organizationMapper.getSubDeptIds(request.getOrgId());
        if (null != request.getOrgId()) {
            request.setOrgIds(organizationMapper.getSubDeptIds(request.getOrgId()));
        }
        LambdaQueryWrapper<TrainLessonEntity> lessonEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lessonEntityLambdaQueryWrapper.eq(TrainLessonEntity::getStageName, request.getStageName());
        List<TrainLessonEntity> lessonEntities = trainLessonMapper.selectList(lessonEntityLambdaQueryWrapper);
        List<Long> lessonIds = lessonEntities.stream().map(TrainLessonEntity::getId).collect(Collectors.toList());
        if (lessonIds.size() > 0) {
            request.setLessonIds(lessonIds);
            request.setOrgIds(subDeptIds);
            List<Integer> staticStatusList = new ArrayList<>();
            if (null != request.getStaticStatus()) {
                if (request.getStaticStatus() == 0) {
                    if (null == request.getLearnFlag() ||request.getLearnFlag() != 1) {
                        staticStatusList = Arrays.asList(0, 3);
                    }else {
                        staticStatusList = Arrays.asList(0, 2);
                    }
                } else if (request.getStaticStatus() == 1) {
                    if (null == request.getLearnFlag()) {
                        staticStatusList = Arrays.asList(1, 2, 4);
                    } else if (request.getLearnFlag() == 1) {
                        staticStatusList = Arrays.asList(1);
                    } else if (request.getLearnFlag() == 0) {
                        staticStatusList = Arrays.asList(2, 4);
                    }
                } else if (request.getStaticStatus() == -3) {
                    if (null == request.getLearnFlag() ||request.getLearnFlag() != 1) {
                        staticStatusList = Arrays.asList(request.getStaticStatus());
                    }else {
                        staticStatusList = Arrays.asList(-3, 3, 4);
                    }
                }
            }
            List<UserLessonRecordSafeTrainUserVO> list = organizationMapper.getTwentyOneUserLessonRecordSafeTrainBus(staticStatusList,lessonIds,subDeptIds,request.getKeyWords());
            int size = list.size();
            if (null != pageEntity) {
                int startIndex = (pageEntity.getPageIndex() - 1) * pageEntity.getPageSize();
                int endIndex = (pageEntity.getPageIndex()) * pageEntity.getPageSize();
                if (size > 0 && startIndex < size) {
                    if (endIndex > size) {
                        endIndex = size;
                    }
                    list = list.subList(startIndex, endIndex);
                }
            }
            PageUtils pageUtils = new PageUtils(pageEntity, list);
            pageUtils.setTotalCount(size);
            int tempLearnFlag = null != request.getLearnFlag() ? request.getLearnFlag() : -999;
            list.forEach(item -> {
                item.setStaticStatus(tempLearnFlag == 1 && noBeginStatus.contains(item.getStaticStatus()) ? -3 : tempLearnFlag == 1 && noCompleteStatus.contains(item.getStaticStatus()) ? 0 : tempLearnFlag == 0 && completedStatus.contains(item.getStaticStatus()) ? 1 : item.getStaticStatus());
                item.setCompleteTime(completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0 ? item.getCompleteTime() : completeStatus.contains(item.getStaticStatus()) ? item.getCompleteTime() : "");
                item.setTrainStatusStr(completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0 ? "已完成(" + item.getScore() + "分)" : completeStatus.contains(item.getStaticStatus()) ? "已完成" : item.getStaticStatus() == -3 ? "未开始" : null != item.getCompleteTime() ? "未考试" : "培训中");
                item.setIsValidStr(item.getIsValid() == 1 ? "正常" : "已收回");
            });
            return pageUtils;
        }
        return null;
    }

    @Override
    public UserSafeTrainFilesDetailVO getTwentyOneSafeTrainFilesDetail(UserSafeTrainFilesDetailRequest request) {
        UserSafeTrainFilesDetailVO result = new UserSafeTrainFilesDetailVO();
        LessonRecordVO lessonRecordVO = organizationMapper.getUserSafeTrainFilesDetail(request.getUserCode(),request.getLessonId());
        result.setLessonRecord(lessonRecordVO);
        if (null != lessonRecordVO) {
            if (lessonRecordVO.getExamId() > 0) {
                UserExamInfoVo userExamInfoVo = organizationMapper.getUserExamInfo(request.getUserCode(),Arrays.asList(request.getLessonId()));
                if (null != userExamInfoVo) {
                    lessonRecordVO.setScore(userExamInfoVo.getScore());
                    lessonRecordVO.setExamStateStr(userExamInfoVo.getScore() == -1 ? "未考试" : userExamInfoVo.getScore() >= userExamInfoVo.getPassMark() ? "合格" : "不合格");
                    lessonRecordVO.setExamCompleteTime(userExamInfoVo.getScore() >= userExamInfoVo.getPassMark() ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", userExamInfoVo.getCreationTime()) : "--");
                    lessonRecordVO.setExamSign(userExamInfoVo.getScore() >= userExamInfoVo.getPassMark() ? userExamInfoVo.getSignUrl() : "");
                    int resitNum = lessonRecordVO.getResitNumber() - userExamInfoVo.getExamCount() + 1;
                    lessonRecordVO.setResitNumber(Math.max(resitNum, 0));
                }
            }
            List<UserCourseRecordSafeTrain> userCourse = organizationMapper.getUserCourse(request.getLessonId(),request.getUserCode());
            List<CourseRecordVO> courses = organizationMapper.selectCourseRecord(request.getLessonId(),request.getUserCode());
            if (CollectionUtils.isNotEmpty(userCourse)) {
                courses.forEach(item -> {
                    List<UserCourseRecordSafeTrain> courseRecordSafeTrains = userCourse.stream().filter(a -> a.getLessonId().equals(item.getLessonId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(courseRecordSafeTrains)) {
                        UserCourseRecordSafeTrain userCourseRecordSafeTrain = courseRecordSafeTrains.get(0);
                        item.setCourseIsComplete(String.valueOf(userCourseRecordSafeTrain.getCourseIsComplete()));
                        item.setStartStudyTime(String.valueOf(userCourseRecordSafeTrain.getStartStudyTime()));
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(courses)) {
                courses.forEach(item -> {
                    if (null == item.getCourseIsComplete()) {
                        item.setCourseIsComplete("-3");
                    }
                    if (null == item.getStartStudyTime()) {
                        item.setStartStudyTime("");
                    }
                    if (null == item.getFaceDistinguishImg()) {
                        item.setFaceDistinguishImg("");
                    }
                });
                result.setCourseRecord(courses);
            }
        }
        return result;
    }
}
