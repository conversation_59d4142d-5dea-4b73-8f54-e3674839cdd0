package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.security.handle.CustomParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@CustomParam
public class SafeTrainFilesRequest {

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("lessonid")
    private Long lessonId;

    @ApiModelProperty(value = "公司名称")
    @JsonProperty("companyname")
    private String companyName;
}
