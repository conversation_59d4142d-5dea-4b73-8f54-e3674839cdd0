package com.fzkj.project.basicdata.controller;

import com.fzkj.common.constant.Constants;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.basicdata.request.NoticeRequest;
import com.fzkj.project.basicdata.service.NoticeService;
import com.fzkj.project.system.request.GetNoticeAPPRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 11:21
 * @Version: 1.0
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
//@RequestMapping("/basicdata")
public class NoticeController {

    private final NoticeService noticeService;

    @PostMapping("Get_Notice")
    @ApiOperation(value = "公告表分页查询")
    public AjaxResult listTrainTypeByPage(@RequestBody CommonRequest commonRequest) {
        NoticeRequest request = (NoticeRequest) commonRequest.getRequest(NoticeRequest.class);
        return AjaxResult.success(noticeService.listByPage(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_NoticeAPP")
    @ApiOperation(value = "小程序-获取最新一条公告")
    public AjaxResult GetNoticeAPPRequest(@RequestBody CommonRequest commonRequest) {
        GetNoticeAPPRequest request = (GetNoticeAPPRequest) commonRequest.getRequest(GetNoticeAPPRequest.class);
        return AjaxResult.success(noticeService.getNoticeAPP(request));
    }

    /**
     * 新增保存常见问题
     */
    @PostMapping("/Edit_Notice")
    @ApiOperation(value = "新增保存公告")
    @ResponseBody
    public AjaxResult addSave(@RequestBody CommonRequest commonRequest) {
        NoticeRequest request = (NoticeRequest) commonRequest.getRequest(NoticeRequest.class);
        switch (request.getFlag()) {
            case Constants.ADD_DATA:
                return AjaxResult.success(noticeService.insertNotice(request));
            case Constants.EDIT_DATA:
                return AjaxResult.success(noticeService.updateNotice(request));
            case Constants.DEL_DATA:
                return AjaxResult.success(noticeService.deleteNotice(request.getId()));
            default:
                return AjaxResult.error("操作异常！");
        }
    }
}
