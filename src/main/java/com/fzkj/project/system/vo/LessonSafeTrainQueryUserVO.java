package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 安全培训课程
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "安全培训课程查看已分发学员VO")
public class LessonSafeTrainQueryUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "身份证号")
    @JsonProperty("IDCard")
    private String idCard;

    @ApiModelProperty(value = "部门")
    @JsonProperty("DepartName")
    private String departName;

    @ApiModelProperty(value = "岗位")
    @JsonProperty("WorkName")
    private String workName;

    @ApiModelProperty(value = "用户编码")
    @JsonProperty("UserCode")
    private String userCode;

}

