# 项目相关配置
app:
    # 名称
    name: 在线教育平台
    # 版本
    version: 2.1.0
    # 版权年份
    copyrightYear: 2019
    # 实例演示开关
    demoEnabled: true
    # 文件路径 示例（ Windows配置D:/fzkj/uploadPath，Linux配置 /home/<USER>/uploadPath）
    profile: /home/<USER>/apps/html2pdf
    # 获取ip地址开关
    addressEnabled: false

# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: *****************************************************************************************************************************************************************
                username: jypxpro
                password: sLSp&*34sfp59

            # 从库数据源,企业端端
            slave:
                url: *****************************************************************************************************************************************************************
                username: jypxpro
                password: sLSp&*34sfp59
        # 初始连接数
        initialSize: 5
        # 最小连接池数量
        minIdle: 10
        # 最大连接池数量
        maxActive: 200
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000
        # 配置检测连接是否有效
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        webStatFilter:
            enabled: true
        statViewServlet:
            enabled: true
            # 设置白名单，不填则允许所有访问
            allow:
            url-pattern: /druid/*
            # 控制台管理用户名和密码
            login-username:
            login-password:
        filter:
            stat:
                enabled: true
                # 慢SQL记录
                log-slow-sql: true
                slow-sql-millis: 10000
                merge-sql: true
            wall:
                config:
                    multi-statement-allow: true
    data:
        mongodb:
            repositories:
                type: none
            master:
                uri: mongodb://teach:md#<EMAIL>:3717,dds-bp178ef5c489aa843592-pub.mongodb.rds.aliyuncs.com:3717,dds-bp178ef5c489aa841850-pub.mongodb.rds.aliyuncs.com:3717/teach?replicaSet=mgset-75676160
            history:
                uri: mongodb://teach:md#<EMAIL>:3717,dds-bp1d6689fbc41b742346-pub.mongodb.rds.aliyuncs.com:3717,dds-bp1d6689fbc41b743381-pub.mongodb.rds.aliyuncs.com:3717/teach?replicaSet=mgset-78272601
#    redis:
#        host: **************
#        port: 6379
#        password: redis@cache
#        database: 5
#        jedis:
#            pool:
#                min-idle: 0
#                # 连接池中的最大空闲连接
#                max-idle: 100
#                # 连接池的最大数据库连接数
#                max-active: 8
#                # #连接池最大阻塞等待时间（使用负值表示没有限制）
#                max-wait: -1ms
#        timeout: 60
    redis:
        host: localhost
        port: 6379
        password:
        database: 5
use:
    captcha: false


patrol:
    url: https://fkm.cqphx.cn:4443/
    userName: admin
    password: fzkj@123

scheduled:
    run: false
wechat:
    config:
        enable: true
        app-id: wx252535d88d3b0c09
        secret: 67a441db1e1c04bede54befaa6dcb560
        public-app-id: wx04523bfa23286316
        public-secret: f5a272f356b1b051265f16df0cdba3eb
        access-token-url: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${wechat.config.app-id}&secret=${wechat.config.secret}
        public-access-token-url: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&&appid=${wechat.config.public-app-id}&secret=${wechat.config.public-secret}
        public-subscribe-send-url: https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=
        session-get-url: https://api.weixin.qq.com/sns/jscode2session?appid=${wechat.config.app-id}&secret=${wechat.config.secret}&grant_type=authorization_code&js_code=
        wxacodeunlimit-get-url: https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=
        openid-get-url: https://api.weixin.qq.com/cgi-bin/user/info?access_token=
        fansi-get-url: https://api.weixin.qq.com/cgi-bin/user/get?access_token=