package com.fzkj.common.utils;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Collection;
import java.util.List;

/**
 * double型数据的加减乘除
 */
public class MathUtil {
    private static final int DEF_DIV_SCALE = 10;
    private final static double dmax=999;//Double.MAX_VALUE;//Double类型的最大值，太大的double值，相乘会达到无穷大
    private final static double dmin=Double.MIN_VALUE;//Double类型的最小值

    /**
     * 两个Double数相加
     * @param v1
     * @param v2
     * @return Double
     */
    public static Double add(Double v1,Double v2){
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.add(b2).doubleValue();
    }

    /**
     * 两个Double数相减
     * @param v1
     * @param v2
     * @return Double
     */
    public static Double sub(Double v1,Double v2){
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.subtract(b2).doubleValue();
    }

    /**
     * 两个Double数相乘
     * @param v1
     * @param v2
     * @return Double
     */
    public static Double mul(Double v1,Double v2){
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.multiply(b2).doubleValue();
    }

    /**
     * 两个Double数相除
     * @param v1
     * @param v2
     * @return Double
     */
    public static Double div(Double v1,Double v2){
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.divide(b2,DEF_DIV_SCALE,BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 两个Double数相除，并保留scale位小数
     * @param v1
     * @param v2
     * @param scale
     * @return Double
     */
    public static Double div(Double v1,Double v2,int scale){
        if(scale<0){
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.divide(b2,scale,BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static double avg(Collection<Double> ds, boolean skipZeroOrNull, int digitNum) {
        if (CollectionUtils.isEmpty(ds)) {
            return 0;
        }
        if (skipZeroOrNull) {
            double res = 0.0;
            int size = 0;
            for (Double d : ds) {
                if (d != null && d > 0) {
                    ++size;
                    res += d;
                }
            }
            if (size > 0) {
                double v = res / size;
                return formatDouble(v, digitNum);
            } else {
                return 0.0;
            }
        } else {
            double res = 0.0;
            int size = 0;
            for (Double d : ds) {
                ++size;
                if (d != null && d > 0) {
                    res += d;
                }
            }
            if (size > 0) {
                double v = res / size;
                return formatDouble(v, digitNum);
            } else {
                return 0.0;
            }
        }
    }

    public static double formatDouble(double v, int digitNum) {
        BigDecimal bd = new BigDecimal(v);
        bd = bd.setScale(digitNum, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }

    /**
     * 分段,根据最小和最大值
     *
     * @param min
     * @param max
     * @return
     */
    public static List<Pair<Integer, Integer>> seg2sevenDistributes(int min, int max, Pair<Integer, Integer> first, Pair<Integer, Integer> last) {
        if (first.getLeft() == first.getRight()) {
            first = Pair.of(first.getLeft(), first.getLeft() + 1);
            min = first.getLeft() + 1;
        }
        if (last.getLeft() == last.getRight()) {
            last = Pair.of(last.getLeft(), last.getLeft() + 1);
        }

        int t = max - min;
        if (t < 5) {
            t = 5 + 1;
            min = min - (5 + 1 - t);
        }
        int ll = min + (int) (Math.ceil(t));
        if (last.getLeft() < ll) {
            last = Pair.of(ll, last.getRight());
        }
        if (min != first.getRight()) {
            min = first.getRight();
        }
        if (max != last.getLeft()) {
            max = last.getLeft();
        }

        List<Pair<Integer, Integer>> res = Lists.newArrayList(first, Pair.of(min, min + (int) (Math.ceil(t * 0.2))),
                Pair.of(min + (int) (Math.ceil(t * 0.2)), min + (int) (Math.ceil(t * 0.4))),
                Pair.of(min + (int) (Math.ceil(t * 0.4)), min + (int) (Math.ceil(t * 0.6))),
                Pair.of(min + (int) (Math.ceil(t * 0.6)), min + (int) (Math.ceil(t * 0.8))), Pair.of(min + (int) (Math.ceil(t * 0.8)), max), last);
        // 处理最后两个区间可能类似于 [17,17),[17,27] 的这种情况
        if (res.get(5).getLeft() == res.get(5).getRight()) {
            // 处理倒数第一个数据
            Pair<Integer, Integer> e = res.get(5);
            res.remove(5);
            res.add(5, Pair.of(e.getLeft(), e.getLeft() + 2));
            // 处理最后一个
            e = res.get(6);
            res.remove(6);
            res.add(6, Pair.of(e.getLeft() + 2, e.getRight() + 2));
        }
        return res;
    }

    /**
     * 分段,根据最小和最大值,分出7段
     *
     * @param min
     * @param max
     * @return
     */
    public static List<Pair<Integer, Integer>> seg2sevenDistributes(int min, int max) {
        int t = max - min;
        if (t < 7) {
            t = 7 + 1;
            min = min - (7 + 1 - t);
        }
        return Lists.newArrayList(Pair.of(min, min + (int) (Math.ceil(t * 0.2))), Pair.of(min + (int) (Math.ceil(t * 0.2)), min + (int) (Math.ceil(t * 0.3))),
                Pair.of(min + (int) (Math.ceil(t * 0.3)), min + (int) (Math.ceil(t * 0.4))),
                Pair.of(min + (int) (Math.ceil(t * 0.4)), min + (int) (Math.ceil(t * 0.5))),
                Pair.of(min + (int) (Math.ceil(t * 0.5)), min + (int) (Math.ceil(t * 0.6))),
                Pair.of(min + (int) (Math.ceil(t * 0.6)), min + (int) (Math.ceil(t * 0.7))), Pair.of(min + (int) (Math.ceil(t * 0.7)), max));
    }

    /**
     * 算术求和-数组
     *
     * @param c
     * @return
     */
    public static <T> T sumArr(T[] c) {
        if (ArrayUtils.isEmpty(c))
            return null;
        if (c[0] instanceof Integer) {
            Integer count = 0;
            for (T i : c) {
                count += ((Integer) i).intValue();
            }
            return (T) count;
        }

        if (c[0] instanceof Long) {
            Long count = 0l;
            for (T i : c) {
                count += ((Long) i).longValue();
            }
            return (T) count;
        }

        if (c[0] instanceof Double) {
            Double count = 0.00;
            for (T i : c) {
                count += ((Double) i).doubleValue();
            }
            return (T) count;
        }

        if (c[0] instanceof Float) {
            Float count = 0f;
            for (T i : c) {
                count += ((Float) i).floatValue();
            }
            return (T) count;
        }

        return null;
    }

    /**
     * 算术求和-集合
     *
     * @param c
     * @return
     */
    public static <T> T sumCollection(Collection<T> c) {
        if (CollectionUtils.isEmpty(c))
            return null;
        if (c.toArray()[0] instanceof Integer) {
            Integer count = 0;
            for (T i : c) {
                count += ((Integer) i).intValue();
            }
            return (T) count;
        }

        if (c.toArray()[0] instanceof Long) {
            Long count = 0l;
            for (T i : c) {
                count += ((Long) i).longValue();
            }
            return (T) count;
        }

        if (c.toArray()[0] instanceof Double) {
            Double count = 0.00;
            for (T i : c) {
                count += ((Double) i).doubleValue();
            }
            return (T) count;
        }

        if (c.toArray()[0] instanceof Float) {
            Float count = 0f;
            for (T i : c) {
                count += ((Float) i).floatValue();
            }
            return (T) count;
        }

        return null;
    }

    /*
        计算变异系数
     */
    public static double cv(Double[] x) {
        int m=x.length;
        double sum=0D;
        for(int i=0;i<m;i++){//求和
            sum = MathUtil.add(sum, x[i]);
        }
        double avg= MathUtil.div(sum, (double)m, 4);//求平均值
        double sd = standardDiviation(x);//标准差
        return MathUtil.div(sd, avg, 4);
    }

    //方差s^2=[(x1-x)^2 +...(xn-x)^2]/n
    public static double variance(Double[] x) {
        int m=x.length;
        double sum=0;
        for(int i=0;i<m;i++){//求和
            sum+=x[i];
        }
        double dAve=sum/m;//求平均值
        double dVar=0;
        for(int i=0;i<m;i++){//求方差
            dVar+=(x[i]-dAve)*(x[i]-dAve);
        }
        return dVar/m;
    }

    //标准差σ=sqrt(s^2)
    public static double standardDiviation(Double[] x) {
        int m=x.length;
        double sum=0D;
        for(int i=0;i<m;i++){//求和
            sum = MathUtil.add(sum, x[i]);
        }
        double dAve= MathUtil.div(sum, (double)m, 4);//求平均值
        double dVar=0;
        for(int i=0;i<m;i++){//求方差
            dVar = MathUtil.add(dVar, MathUtil.mul((x[i]-dAve), (x[i]-dAve)));
            //dVar+=(x[i]-dAve)*(x[i]-dAve);
        }
        return Math.sqrt(MathUtil.div(dVar, (double)m));
    }

    /**
     * Description: 转百分数
     *
     * @param number
     * @param scale
     * @return:
     *
     * @auther: cxhuan
     * @date: 2018/8/16 15:13
     */
    public static String formatPercent(Double number, int scale) {
        NumberFormat nf = NumberFormat.getPercentInstance();
        nf.setMinimumFractionDigits(scale);
        return nf.format(number);
    }

}
