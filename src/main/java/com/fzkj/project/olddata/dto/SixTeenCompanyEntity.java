package com.fzkj.project.olddata.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("t_f_company")
public class SixTeenCompanyEntity {
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField("CompanyName")
    @JsonProperty("CompanyName")
    @ApiModelProperty(value = "主键ID")
    private String companyName;

    @TableField("FID")
    @JsonProperty("fId")
    @ApiModelProperty(value = "主键ID")
    private Long fId;

    @TableField("IsValid")
    @JsonProperty("fId")
    @ApiModelProperty(value = "主键ID")
    private Integer isValid;
}
