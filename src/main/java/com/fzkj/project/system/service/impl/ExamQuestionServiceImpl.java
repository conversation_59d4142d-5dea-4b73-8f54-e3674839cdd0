package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.ExamQuestion;
import com.fzkj.project.system.vo.ExamQuestionVO;
import com.fzkj.project.system.request.ExamQuestionRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ExamQuestionMapper;
import com.fzkj.project.system.service.ExamQuestionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 考试试题表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExamQuestionServiceImpl extends ServiceImpl<ExamQuestionMapper, ExamQuestion> implements ExamQuestionService {

    @Override
    public List<ExamQuestion> listExamQuestion(ExamQuestionRequest request) {
        ExamQuestion entity = (ExamQuestion) DataTransfer.transfer(request, ExamQuestion.class);
        LambdaQueryWrapper<ExamQuestion> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ExamQuestionRequest request, PageEntity pageEntity) {
        ExamQuestion entity = (ExamQuestion) DataTransfer.transfer(request, ExamQuestion.class);
        LambdaQueryWrapper<ExamQuestion> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<ExamQuestion> page = this.page(
                new Query<ExamQuestion>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ExamQuestionVO findExamQuestion(Long id) {
        ExamQuestion entity = this.getById(id);
        ExamQuestionVO vo = (ExamQuestionVO) DataTransfer.transfer(entity, ExamQuestionVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExamQuestion(ExamQuestionVO vo) {
        ExamQuestion entity = (ExamQuestion) DataTransfer.transfer(vo, ExamQuestion.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamQuestion(ExamQuestionVO vo) {
        ExamQuestion entity = (ExamQuestion) DataTransfer.transfer(vo, ExamQuestion.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExamQuestion(Long id) {
        return this.removeById(id);
    }
}