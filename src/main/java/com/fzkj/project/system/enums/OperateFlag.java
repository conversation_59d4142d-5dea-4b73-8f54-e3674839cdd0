package com.fzkj.project.system.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 *
 */
@Getter
public enum OperateFlag implements IEnum<String> {
    ADD("add", "新增"),
    DEL("del", "删除"),
    EDIT("edit", "修改"),
    RECOVERY("recovery","恢复");

    OperateFlag(String code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final String code;
    @JsonValue
    private final String descp;

    @Override
    public String getValue() {
        return this.code;
    }

}
