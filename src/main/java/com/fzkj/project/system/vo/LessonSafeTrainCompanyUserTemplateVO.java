package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 企业下的用户安全培训课程用户模板
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="企业下的用户安全培训课程用户模板VO")
public class LessonSafeTrainCompanyUserTemplateVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "企业安全培训课程设置表T_D_CompanyLessonSafeTrainSet")
    @NotNull(message = "企业安全培训课程设置表T_D_CompanyLessonSafeTrainSet不能为空")
    @JsonProperty("CompanyLessonPayID")
    private Long companyLessonPayId;

    @ApiModelProperty(value = "课程支付类型：1：余额支付；2：包干（学员免费）；3:员工支付（学员付费）")
    @NotNull(message = "课程支付类型：1：余额支付；2：包干（学员免费）；3:员工支付（学员付费）不能为空")
    @JsonProperty("IsPayType")
    private Integer isPayType;

    @ApiModelProperty(value = "课程支付类型名称：1：余额支付；2：包干；3:员工支付")
    @NotNull(message = "课程支付类型名称：1：余额支付；2：包干；3:员工支付不能为空")
    @JsonProperty("PayTypeName")
    private String payTypeName;

    @ApiModelProperty(value = "企业ID")
    @NotNull(message = "企业ID不能为空")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    @NotNull(message = "企业名称不能为空")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "用户编码")
    @NotNull(message = "用户编码不能为空")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "用户名称")
    @NotNull(message = "用户名称不能为空")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "课程父级分类ID")
    @NotNull(message = "课程父级分类ID不能为空")
    @JsonProperty("LessonCategoryFID")
    private Long lessonCategoryFid;

    @ApiModelProperty(value = "课程分类")
    @NotNull(message = "课程分类不能为空")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课程分类名称")
    @NotNull(message = "课程分类名称不能为空")
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty(value = "参培课程（1：（通用课程）自动分发 2：（定制课程）手动分发）")
    @NotNull(message = "参培课程（1：（通用课程）自动分发 2：（定制课程）手动分发）不能为空")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "是否托管：1：开启0：不开启")
    @NotNull(message = "是否托管：1：开启0：不开启不能为空")
    @JsonProperty("IsTrusteeship")
    private Integer isTrusteeship;

    @ApiModelProperty(value = "是否免费：1：免费 0：不免费")
    @NotNull(message = "是否免费：1：免费 0：不免费不能为空")
    @JsonProperty("IsFree")
    private Integer isFree;

    @ApiModelProperty(value = "统计分组：1：管理人员 0：普通学员")
    @NotNull(message = "统计分组：1：管理人员 0：普通学员不能为空")
    @JsonProperty("StatisticalGroup")
    private Integer statisticalGroup;

    @ApiModelProperty(value = "来源")
    @NotNull(message = "来源不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @NotNull(message = "创建人ID不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @NotNull(message = "修改人ID不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    @NotNull(message = "备注信息不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否")
    @NotNull(message = "是否有效 1：是， 0：否不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "用户编码")
    @NotNull(message = "用户编码不能为空")
    @JsonProperty("UserCodes")
    private String userCodes;

    @ApiModelProperty(value = "操作标识")
    @NotNull(message = "操作标识不能为空")
    @JsonProperty("Flag")
    private String flag;

}