<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CompanyWorkMapper">

    <select id="selectCompanyWorkList" resultType="com.fzkj.project.system.entity.CompanyWork">
        SELECT
        *
        FROM
        t_d_company_work
        WHERE
        is_valid = 1
        <if test="companyId != null">
            AND depart_id= #{companyId}
        </if>
    </select>

    <select id="selectCompanyWorkPage" resultType="com.fzkj.project.system.entity.CompanyWork">
        SELECT
        *
        FROM
        t_d_company_work
        WHERE
        (
        company_id in (SELECT id FROM t_d_company WHERE FIND_IN_SET ( #{companyId}, ancestors ) UNION ALL
        SELECT #{companyId})
        OR depart_id in (SELECT id FROM t_d_company WHERE FIND_IN_SET ( #{companyId}, ancestors ) UNION ALL
        SELECT #{companyId}))
        AND is_valid = 1
        <if test="workName != null and workName != ''">AND work_name like concat('%', #{workName}, '%')</if>
    </select>
    <select id="selectCompanyDriver" resultType="java.lang.Long">
        select id from t_d_company_work where depart_id=#{id} and work_name='营运驾驶员'
    </select>
    <select id="selectCompanySystem" resultType="java.lang.Long">
        select id from t_d_company_work where depart_id=#{id} and work_name='管理人员'
    </select>
</mapper>