package com.fzkj.project.basicdata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.ServletUtils;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.framework.security.service.TokenService;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.basicdata.entity.SubStanceLabel;
import com.fzkj.project.basicdata.entity.SubStanceLabelExportVo;
import com.fzkj.project.basicdata.entity.SubStanceManage;
import com.fzkj.project.basicdata.mapper.SubStanceLabelMapper;
import com.fzkj.project.basicdata.mapper.SubStanceManageMapper;
import com.fzkj.project.basicdata.service.SubStanceLabelService;
import com.fzkj.project.system.entity.UserCompany;
import com.fzkj.project.system.service.UserCompanyService;
import com.fzkj.project.system.vo.UserInfoVO;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 13:53
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class SubStanceLabelServiceImpl extends ServiceImpl<SubStanceLabelMapper, SubStanceLabel> implements SubStanceLabelService {

    @Resource
    UserCompanyService userCompanyService;

    @Autowired
    private TokenService tokenService;
    @Resource
    SubStanceManageMapper subStanceManageMapper;
    @Override
    public PageUtils listByPage(SubStanceLabel request, PageEntity pageEntity) {
        SubStanceLabel entity = (SubStanceLabel) DataTransfer.transfer(request, SubStanceLabel.class);
        LambdaQueryWrapper<SubStanceLabel> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtils.isEmpty(entity.getId())) {
            queryWrapper.eq(SubStanceLabel::getIsValid, entity.getIsValid());
        }
        if (!ObjectUtils.isEmpty(entity.getLabelName())){
            queryWrapper.like(SubStanceLabel::getLabelName,entity.getLabelName());
        }
        if (!ObjectUtils.isEmpty(entity.getId())){
            queryWrapper.eq(SubStanceLabel::getId,entity.getId());
        }
        if (!ObjectUtils.isEmpty(entity.getType())){
            queryWrapper.eq(SubStanceLabel::getType,entity.getType());
        }
        IPage<SubStanceLabel> page = this.page(
                new Query<SubStanceLabel>().getPage(pageEntity), queryWrapper);
        page.getRecords().forEach(a->{
            a.setSubstanceNum(subStanceManageMapper.selectCount(new LambdaQueryWrapper<SubStanceManage>()
                    .eq(SubStanceManage::getIsValid,1)
                    .like(SubStanceManage::getSubStanceLabelQuery,","+a.getId()+",")));
        });
        return new PageUtils(page);
    }

    @Override
    public Long insertSubStanceLabel(SubStanceLabel request) {
        SubStanceLabel entity = (SubStanceLabel) DataTransfer.transfer(request, SubStanceLabel.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        entity.setCreatorName(userCompany.getUserName());
        entity.setCreatorCode(userCompany.getUserCode());
        entity.setCreationTime(DateUtils.dateTimeNew(new Date()));
        entity.setType(1);
        entity.setIsValid(1);
        this.save(entity);
        return request.getId();
    }

    @Override
    public Boolean updateSubStanceLabel(SubStanceLabel request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        SubStanceLabel entity = (SubStanceLabel) DataTransfer.transfer(request, SubStanceLabel.class);
        entity.setReviseCode(userCompany.getUserCode());
        entity.setReviseName(userCompany.getUserName());
        entity.setReviseTime(DateUtils.dateTimeNew(new Date()));
        return this.saveOrUpdate(entity);
    }

    @Override
    public Boolean deleteSubStanceLabel(Long id) {

        SubStanceLabel byId = this.getById(id);
        if (byId.getIsValid().equals(0)){
            byId.setIsValid(1);
        }else {
            byId.setIsValid(0);
        }
        return this.saveOrUpdate(byId);
    }

    @Override
    public String exportSubstanceLabel(SubStanceLabel entity,PageEntity pageEntity) {
        List<SubStanceLabelExportVo> labelExportVos=new ArrayList<>();
        pageEntity.setPageSize(9999);
        pageEntity.setPageIndex(1);
        LambdaQueryWrapper<SubStanceLabel> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtils.isEmpty(entity.getId())) {
            queryWrapper.eq(SubStanceLabel::getIsValid, entity.getIsValid());
        }
        if (!ObjectUtils.isEmpty(entity.getLabelName())){
            queryWrapper.like(SubStanceLabel::getLabelName,entity.getLabelName());
        }
        if (!ObjectUtils.isEmpty(entity.getId())){
            queryWrapper.eq(SubStanceLabel::getId,entity.getId());
        }
        if (!ObjectUtils.isEmpty(entity.getType())){
            queryWrapper.eq(SubStanceLabel::getType,entity.getType());
        }
        IPage<SubStanceLabel> page = this.page(
                new Query<SubStanceLabel>().getPage(pageEntity), queryWrapper);
        page.getRecords().forEach(a->{
            SubStanceLabelExportVo subStanceLabelExportVo=new SubStanceLabelExportVo();
            subStanceLabelExportVo.setName(a.getLabelName());
            subStanceLabelExportVo.setType(a.getType().equals(1) ? "自定义" : "系统");
            subStanceLabelExportVo.setNum(subStanceManageMapper.selectCount(new LambdaQueryWrapper<SubStanceManage>()
                    .like(SubStanceManage::getSubStanceLabelQuery,","+a.getId()+",")));
            subStanceLabelExportVo.setColor(a.getColorCode());
            labelExportVos.add(subStanceLabelExportVo);
        });
        try {
            ExcelUtil<SubStanceLabelExportVo> util = new ExcelUtil<SubStanceLabelExportVo>(SubStanceLabelExportVo.class);
            String fileName = util.encodingDateFilename("内容标签列表");
            InputStream inputStream = util.exportExcelFile(labelExportVos, fileName);
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "basicdata", fileName);
            return fileName + ".xlsx," + uploadFile.getUrl();
        } catch (Exception e) {
            log.error("内容标签导出上传异常");
            return "内容标签导出上传异常";
        }
    }
}
