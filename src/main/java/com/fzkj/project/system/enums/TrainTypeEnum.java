package com.fzkj.project.system.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 *
 */
@Getter
public enum TrainTypeEnum{
    SAFETY_TRAIN(1, "安全培训");

    TrainTypeEnum(Integer trainType, String descp) {
        this.trainType = trainType;
        this.descp = descp;
    }

    @EnumValue
    private final Integer trainType;
    @JsonValue
    private final String descp;

    public Integer getValue() {
        return this.trainType;
    }

    public String getName() {
        return this.descp;
    }

    public String getNameByType(Integer trainType) {
        for (TrainTypeEnum type : TrainTypeEnum.values()) {
            if (type.getValue().equals(trainType)) {
                return type.getDescp();
            }
        }
        return null;
    }

}
