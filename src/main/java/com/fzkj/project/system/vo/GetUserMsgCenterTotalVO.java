package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GetUserMsgCenterTotalVO {
    @ApiModelProperty("模块类型")
    @JsonProperty("FunType")
    private Integer funType;

    @ApiModelProperty("模块类型名称")
    @JsonProperty("FunTypeName")
    private String funTypeName;

    @ApiModelProperty("标题")
    @JsonProperty("Title")
    private String title;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "用户ID")
    @JsonProperty("UserID")
    private Long userId;

    @ApiModelProperty(value = "消息数量")
    @JsonProperty("RedNum")
    private int redNum;

    @ApiModelProperty(value = "图标")
    @JsonProperty("iCON")
    private String icon;

}
