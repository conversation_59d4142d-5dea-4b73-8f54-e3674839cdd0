package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class LessonSafeTrainCompanyUserTemplateQueryVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "用户编码")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "用户名称")
    @JsonProperty("UserName")
    @Excel(name = "姓名", order = 1)
    private String userName;

    @ApiModelProperty(value = "身份证号")
    @JsonProperty("IDCard")
    @Excel(name = "身份证号", order = 2)
    private String idCard;

    @ApiModelProperty(value = "所属公司")
    @JsonProperty("CompanyName")
    @Excel(name = "企业名称", order = 4)
    private String companyName;

    @ApiModelProperty(value = "所属部门")
    @JsonProperty("DepartName")
    @Excel(name = "部门", order = 5)
    private String departName;

    @ApiModelProperty(value = "所属岗位")
    @JsonProperty("WorkName")
    @Excel(name = "岗位", order = 6)
    private String workName;

    @ApiModelProperty(value = "所属区域")
    @JsonProperty("AreaName")
    private String areaName;

    @ApiModelProperty(value = "课程类别")
    @JsonProperty("LessonCategoryName")
    @Excel(name = "参培分类", order = 7)
    private String lessonCategoryName;

    @ApiModelProperty(value = "学时")
    @JsonProperty("TotalTimeCount")
    @Excel(name = "参培学时", order = 8, suffix = "学时")
    private Integer totalTimeCount;

    @ApiModelProperty(value = "是否付费")
    @JsonProperty("IsPayType")
    private Integer isPayType;

    @ApiModelProperty(value = "学员付费")
    @JsonProperty("StaffPrice")
    private BigDecimal staffPrice;

    @ApiModelProperty(value = "通用/定制")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "是否托管")
    @JsonProperty("IsTrusteeship")
    private Integer isTrusteeship;

    @ApiModelProperty(value = "手机号")
    @JsonProperty("Phone")
    @Excel(name = "手机号", order = 3)
    private String phone;

    @ApiModelProperty(value = "手模式")
    @JsonProperty("HandModeStr")
    private String handModeStr;

    @ApiModelProperty(value = "是否付费")
    @JsonProperty("IsPayTypeStr")
    private String isPayTypeStr;

    @ApiModelProperty(value = "学时")
    @JsonProperty("TotalTimeCountStr")
    private String totalTimeCountStr;

    @ApiModelProperty(value = "是否免费")
    @JsonProperty("IsFreeStr")
    private String isFreeStr;

    @ApiModelProperty(value = "统计分组")
    @JsonProperty("StatisticalGroupStr")
    private String statisticalGroupStr;

    @ApiModelProperty(value = "统计分组")
    @JsonProperty("StatisticalGroup")
    @Excel(name = "统计分组", order = 9, readConverterExp = "1=管理人员,0=普通学员")
    private Integer statisticalGroup;

    public void setTotalTimeCount(Integer totalTimeCount) {
        this.totalTimeCount = totalTimeCount;
        this.totalTimeCountStr = totalTimeCount + "学时";
    }

    public void setStatisticalGroup(Integer statisticalGroup) {
        this.statisticalGroup = statisticalGroup;
        this.statisticalGroupStr = this.statisticalGroup == 1 ? "管理人员" : "普通学员";
    }
}
