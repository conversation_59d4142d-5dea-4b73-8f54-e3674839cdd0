package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("t_d_log_send_email_info")
@ApiModel(value = "LogSendEmailInfo", description = "发送邮件信息日志表")
public class LogSendEmailInfo {

    @TableId(value = "id", type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField("company_id")
    @JsonProperty("CompanyID")
    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    @TableField("company_name")
    @JsonProperty("CompanyName")
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @TableField("lesson_month")
    @JsonProperty("LessonMonth")
    @ApiModelProperty(value = "课程月份")
    private String lessonMonth;

    @TableField("email")
    @JsonProperty("Email")
    @ApiModelProperty(value = "邮箱地址")
    private String email;

    @TableField("is_send")
    @JsonProperty("IsSend")
    @ApiModelProperty(value = "是否发送")
    private Integer isSend;

    @TableField("send_time")
    @JsonProperty("SendTime")
    @ApiModelProperty(value = "发送时间")
    private LocalDateTime sendTime;

    @TableField("archive_url")
    @JsonProperty("ArchiveURL")
    @ApiModelProperty(value = "归档URL")
    private String archiveUrl;

    @TableField("send_address")
    @JsonProperty("SendAddress")
    @ApiModelProperty(value = "发送地址")
    private String sendAddress;
}
