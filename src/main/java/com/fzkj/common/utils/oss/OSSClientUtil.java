package com.fzkj.common.utils.oss;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.vod.model.v20170321.*;
import com.fzkj.framework.manager.AsyncManager;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import org.apache.commons.codec.binary.Base64;
import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.TimerTask;

/**
 * 阿里云服务器文件服务器工具类
 *
 * @author: LuoChongwei
 * @date:2019/4/22
 */
public class OSSClientUtil {

    /**
     * 上传本地文件file到仓库bucketName 命名fileName
     *
     * @param bucketName
     * @param fileName
     * @param file
     */
    public static void uploadLocalFile(String bucketName, String fileName, File file) {
        OSSClient ossClient = new OSSClient(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        ossClient.putObject(bucketName, fileName, file);
        ossClient.shutdown();
    }

    /**
     * 上传网络流url到仓库bucketName 命名fileName
     *
     * @param bucketName
     * @param fileName
     * @param url
     */
    public static void uploadUrlFile(String bucketName, String fileName, String url) {
        OSSClient ossClient = new OSSClient(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        InputStream inputStream = null;
        try {
            inputStream = new URL(url).openStream();
            ossClient.putObject(bucketName, fileName, inputStream);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null){
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        ossClient.shutdown();
    }

    /**
     * 上传文件流inputStream到仓库bucketName 命名fileName
     *
     * @param bucketName
     * @param fileName
     * @param inputStream
     */
    public static void uploadUrlFile(String bucketName, String fileName, InputStream inputStream) {
        OSSClient ossClient = new OSSClient(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        ossClient.putObject(bucketName, fileName, inputStream);
        ossClient.shutdown();
    }

    /**
     * 上传文件流inputStream到仓库bucketName 命名fileName
     *
     * @param bucketName
     * @param fileName
     * @param inputStream
     */
    public static void uploadTempUrlFile(String bucketName, String fileName, InputStream inputStream) {
        OSSClient ossClient = new OSSClient(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        //设置过期时间为10分钟
        Date now = new Date();
        now.setTime(now.getTime() + 2 * 60 * 1000);
        objectMetadata.setExpirationTime(now);
        ossClient.putObject(bucketName, fileName, inputStream, objectMetadata);
        ossClient.shutdown();
    }

    /**
     * 上传文件Byte数组到仓库bucketName 命名fileName
     *
     * @param bucketName
     * @param fileName
     * @param bytes
     */
    public static void uploadByteFile(String bucketName, String fileName, byte[] bytes) {
        OSSClient ossClient = new OSSClient(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        ossClient.putObject(bucketName, fileName, new ByteArrayInputStream(bytes));
        ossClient.shutdown();
    }

    /**
     * 删除库bucketName中fileName文件
     *
     * @param bucketName
     * @param fileName
     */
    public static void deleteFile(String bucketName, String fileName) {
        OSSClient ossClient = new OSSClient(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        ossClient.deleteObject(bucketName, fileName);
        ossClient.shutdown();
    }

    /**
     * 文件流形式下载库bucketName中fileName文件
     *
     * @param bucketName
     * @param fileName
     * @return
     */
    public static InputStream downloadFile(String bucketName, String fileName) {
        OSSClient ossClient = new OSSClient(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        OSSObject ossObject = ossClient.getObject(bucketName, fileName);
        ossClient.shutdown();
        return ossObject.getObjectContent();
    }

    /**
     * 获取阿里云文件访问地址
     *
     * @param path
     * @return
     */
    public static String getFileUrl(String buketName, String path) {
        if (StringUtils.isEmpty(path)) {
            return null;
        }
        return new StringBuffer("https://").append(buketName).append(".").append(OSSConst.URL).append("/").append(path).toString();
    }

    /**
     * 获取凤筑文件
     *
     * @param path
     * @return
     */
    public static String getFileUrl(String path) {
        return getFileUrl(OSSConst.BUCKET_NAME, path);
    }

    public static UploadFile uploadVideoFile(String bucketName, String fileName, InputStream inputStream) throws ClientException {
        try {
            // 初始化VOD客户端并获取上传地址和凭证
            CreateUploadVideoResponse createUploadVideoResponse = createUploadVideo(fileName);
            // 执行成功会返回VideoId、UploadAddress和UploadAuth
            String videoId = createUploadVideoResponse.getVideoId();
            JSONObject uploadAuth = JSONObject.parseObject(decodeBase64(createUploadVideoResponse.getUploadAuth()));
            JSONObject uploadAddress = JSONObject.parseObject(decodeBase64(createUploadVideoResponse.getUploadAddress()));
            // 使用UploadAuth和UploadAddress初始化OSS客户端
            OSSClient ossClient = initOssClient(uploadAuth, uploadAddress);
            // 上传文件，注意是同步上传会阻塞等待，耗时与文件大小和网络上行带宽有关
            uploadToOSSAsync(ossClient, uploadAddress, inputStream);
            UploadFile file = new UploadFile();
            file.setFileId(videoId);
            return file;
        } catch (Exception e) {
            System.out.println("Put local file fail, ErrorMessage : " + e.getLocalizedMessage());
        }
        return null;
    }

    public static DefaultAcsClient initVodClient() throws ClientException {
        // 根据点播接入服务所在的Region填写，例如：接入服务在上海，则填cn-shanghai；其他区域请参见媒体上传概述。
        DefaultProfile profile = DefaultProfile.getProfile(OSSConst.REGION, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        return client;
    }

    public static CreateUploadVideoResponse createUploadVideo(String fileName) throws ClientException {
        CreateUploadVideoRequest request = new CreateUploadVideoRequest();
        request.setFileName(fileName);
        request.setTitle(fileName);
        //request.setDescription("this is desc");
        //request.setTags("tag1,tag2");
        //CoverURL示例：http://example.aliyundoc.com/test_cover_****.jpg
        //request.setCoverURL("<your CoverURL>");
        //request.setCateId(-1L);
        request.setTemplateGroupId(OSSConst.VIDEO_TEMPLATE_ID);
        //request.setWorkflowId("");
        //request.setStorageLocation("");
        //request.setAppId("app-1000000");
        //设置请求超时时间
        request.setSysReadTimeout(3000);
        request.setSysConnectTimeout(3000);
        return initVodClient().getAcsResponse(request);
    }

    public static RefreshUploadVideoResponse refreshCreateUploadVideo(String videoId) throws ClientException {
        RefreshUploadVideoRequest request = new RefreshUploadVideoRequest();
        request.setVideoId(videoId);
        request.setSysReadTimeout(3000);
        request.setSysConnectTimeout(3000);
        return initVodClient().getAcsResponse(request);
    }

    public static OSSClient initOssClient(JSONObject uploadAuth, JSONObject uploadAddress) {
        String endpoint = uploadAddress.getString("Endpoint");
        String accessKeyId = uploadAuth.getString("AccessKeyId");
        String accessKeySecret = uploadAuth.getString("AccessKeySecret");
        String securityToken = uploadAuth.getString("SecurityToken");
        return new OSSClient(endpoint, accessKeyId, accessKeySecret, securityToken);
    }

    public static void uploadLocalFile(OSSClient ossClient, JSONObject uploadAddress, InputStream inputStream) {
        String bucketName = uploadAddress.getString("Bucket");
        String objectName = uploadAddress.getString("FileName");
        ossClient.putObject(bucketName, objectName, inputStream);
    }

    private static void uploadToOSSAsync(OSSClient ossClient, JSONObject uploadAddress, InputStream inputStream) {
        AsyncManager.me().execute(new TimerTask() {
            @Override
            public void run() {
                String bucketName = uploadAddress.getString("Bucket");
                String objectName = uploadAddress.getString("FileName");
                ossClient.putObject(bucketName, objectName, inputStream);
            }
        });
    }

    private static String decodeBase64(String data) {
        return new String(Base64.decodeBase64(data));
    }

    public static String getPlayAuth(String videoId) throws ClientException {
        GetVideoPlayAuthRequest getVideoPlayAuthRequest = new GetVideoPlayAuthRequest();
        getVideoPlayAuthRequest.setVideoId(videoId);
        GetVideoPlayAuthResponse getVideoPlayAuthResponse = initVodClient().getAcsResponse(getVideoPlayAuthRequest);
        String playAuth = getVideoPlayAuthResponse.getPlayAuth();
        return playAuth;
    }

    public static String getPlayUrl(String videoId) throws ClientException {
        GetPlayInfoRequest getPlayInfoRequest = new GetPlayInfoRequest();
        getPlayInfoRequest.setVideoId(videoId);
        GetPlayInfoResponse getPlayInfoResponse = initVodClient().getAcsResponse(getPlayInfoRequest);
        String playURL = getPlayInfoResponse.getPlayInfoList().get(0).getPlayURL();
        return playURL;
    }

    public static String getPreviewUrl(String objectName) throws ClientException {
        int page = 1;
        String[] split = objectName.split(",");
        if (split.length > 1) {
            page = Integer.parseInt(split[1]);
            objectName = split[0];
        }
        OSSClient ossClient = new OSSClient(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        String style = "doc/snapshot,target_png,page_" + page;
        // 指定签名URL过期时间为10分钟。
        Date expiration = new Date(new Date().getTime() + 1000 * 60 * 10);
        GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(OSSConst.BUCKET_NAME, objectName, HttpMethod.GET);
        req.setExpiration(expiration);
        req.setProcess(style);
        URL signedUrl = ossClient.generatePresignedUrl(req);
        String url = signedUrl.toString();
        StringBuffer domain = new StringBuffer(OSSConst.BUCKET_NAME).append(".").append(OSSConst.URL);
        url = url.replace(domain, OSSConst.CUSTOM_URL);
        url = url.replace("http://", "https://");
        return url;
    }

    public static int GetTotalPage(String objectName) throws Exception {
        int page = 1;
        try {
            page = GetTotalPageLocal(objectName);
        } catch (Exception e) {
            for (int i = 1; i < 1000; i++) {
                String previewUrl = getPreviewUrl(objectName + "," + i);
                int contentLength = getContentLength(previewUrl);
                if (contentLength == 0) {
                    page = Math.max(1, i - 1);
                    break;
                }
            }
        }
        return page;
    }

    public static int GetTotalPageLocal(String objectName) throws Exception {
        OSS ossClient = new OSSClientBuilder().build(OSSConst.ENDPOINT, OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
        OSSObject ossObject = ossClient.getObject(OSSConst.BUCKET_NAME, objectName);
        BufferedInputStream inputStream = new BufferedInputStream(ossObject.getObjectContent());
        int page = 1;
        if (objectName.endsWith(".ppt")) {
            HSLFSlideShow ppt = new HSLFSlideShow(inputStream);
            page = ppt.getSlides().size();
        } else if (objectName.endsWith(".pptx")) {
            XMLSlideShow pptx = new XMLSlideShow(inputStream);
            page = pptx.getSlides().size();
        } else if (objectName.endsWith(".doc")) {
            HWPFDocument doc = new HWPFDocument(inputStream);
            page = doc.getSummaryInformation().getPageCount();
        } else if (objectName.endsWith(".docx")) {
            XWPFDocument docx = new XWPFDocument(inputStream);
            page = docx.getProperties().getExtendedProperties().getUnderlyingProperties().getPages();
        } else if (objectName.endsWith(".pdf")) {
            PdfDocument document = new PdfDocument(new PdfReader(inputStream));
            page = document.getNumberOfPages();
        } else {
            throw new Exception("不支持的类型");
        }
        return Math.max(1, page);
    }

    private static int getContentLength(String previewUrl) throws IOException {
        URL url = new URL(previewUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        int contentLength = connection.getContentLength();
        connection.disconnect();
        return contentLength;
    }
}
