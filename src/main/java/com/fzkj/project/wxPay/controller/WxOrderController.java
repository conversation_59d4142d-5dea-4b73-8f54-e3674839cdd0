package com.fzkj.project.wxPay.controller;


import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.basicdata.request.TFeedBackRequest;
import com.fzkj.project.wxPay.service.WxPayService;
import com.fzkj.project.wxPay.dao.WxPayDto;
import com.fzkj.project.wxPay.utils.PayUtil;
import com.fzkj.project.wxPay.utils.WXConfigUtil;
import com.github.wxpay.sdk.WXPayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping("app/wx")
public class WxOrderController {

    @Autowired
    public WxPayService wxPayService;

    @PostMapping("/pay")
    public AjaxResult wxAdd(HttpServletRequest request, @RequestBody CommonRequest commonRequest) throws Exception {
        WxPayDto wxPayDto = (WxPayDto) commonRequest.getRequest(WxPayDto.class);
        return AjaxResult.success(wxPayService.doUnifiedOrder(request,wxPayDto));
    }

    @PostMapping("/notify")
//   ("微信回调")
    public String wxPayNotify(HttpServletRequest request) {
        String resXml = "";
        try {
            InputStream inputStream = request.getInputStream();
            //将InputStream转换成xmlString
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder sb = new StringBuilder();
            String line = null;
            try {
                while ((line = reader.readLine()) != null) {
                    sb.append(line + "\n");
                }
            } catch (IOException e) {
                System.out.println(e.getMessage());
            } finally {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.info(e.getMessage());
                }
            }
            resXml = sb.toString();
            String result = wxPayService.payBack(resXml);
            return result;
        } catch (Exception e) {
            System.out.println("微信手机支付失败:" + e.getMessage());
            String result = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
            return result;
        }
    }

    @PostMapping("refundNotifyResult")
    public Object refundNotifyResult(@RequestBody String xmlData) throws Exception {
        System.out.println("进入回调");
        //回调接收到的是xml格式的数据
        WXConfigUtil config = new WXConfigUtil();
        Map<String, String> xmlMap =  WXPayUtil.xmlToMap(xmlData); //转为map格式
        //退款成功后返回一个加密字段req_info,以下为解密
        //解密的方法在上述准备工作的AesUtil类里
        String req_info = xmlMap.get("req_info");
        String resultStr = PayUtil.aesDecrypt(req_info,config.getKey());
        Map<String, String> reqInfo = WXPayUtil.xmlToMap(resultStr);
        String out_trade_no = reqInfo.get("out_trade_no");
        String return_code = xmlMap.get("return_code");

        Map<String,Object> parm = new HashMap<>();
        if (StringUtils.isNotBlank(return_code) && StringUtils.equals(return_code, "success")) {

            //你自己的业务操作

            parm.put("return_code","success");
            parm.put("req_info",reqInfo);
        }
        System.out.println(parm);
        return null;
//        return parm;  //返回给前端的参数
    }

    @PostMapping("/payOk")
    public AjaxResult payOk(@RequestBody CommonRequest commonRequest) throws Exception {
        WxPayDto wxPayDto = (WxPayDto) commonRequest.getRequest(WxPayDto.class);
        Boolean aBoolean = wxPayService.payOk(wxPayDto);
        if (aBoolean){
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }
    @PostMapping("/refund")
    public AjaxResult wxRefund(@RequestBody CommonRequest commonRequest) throws Exception {
        WxPayDto wxPayDto = (WxPayDto) commonRequest.getRequest(WxPayDto.class);
        return AjaxResult.success(wxPayService.wxRefund(wxPayDto));
    }
}

