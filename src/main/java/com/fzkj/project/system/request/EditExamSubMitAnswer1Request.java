package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fzkj.common.utils.JsonUtil;
import com.fzkj.framework.web.domain.CommonRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.List;

@Data
public class EditExamSubMitAnswer1Request {
    private static Log logger = LogFactory.getLog(EditExamSubMitAnswer1Request.class);
    @ApiModelProperty(value = "答案XML")
    @JsonProperty("AnswerXML")
    private String answerXML;

    @ApiModelProperty(value = "答案对象列表")
    @JsonProperty("Answers")
    private List<AnswerSubmitRequest> answers;

    @ApiModelProperty(value = "组织ID", example = "49")
    @JsonProperty("OrganizationID")
    private Long organizationId;

    @ApiModelProperty(value = "部门ID", example = "49")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "身份证号", example = "500228199306271018")
    @JsonProperty("IDCard")
    private String idCard;

    @ApiModelProperty(value = "用户ID", example = "b135339037ae410a87ad42c872c9353f")
    @JsonProperty("UserID")
    private String userId;

    @ApiModelProperty(value = "用户名", example = "测试账号")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "考试ID", example = "25607")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "使用时间", example = "30")
    @JsonProperty("UseTimeCount")
    private int useTimeCount;

    @ApiModelProperty(value = "课程ID", example = "4412")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "准确率", example = "0")
    @JsonProperty("Accuracy")
    private BigDecimal accuracy;

    @ApiModelProperty(value = "照片URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2024-4/image/171195570410472447.jpg")
    @JsonProperty("PhotoUrl")
    private String photoUrl;

    @ApiModelProperty(value = "签名URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2023-11/image/169960956171731996.png")
    @JsonProperty("SignUrl")
    private String signUrl;

    @ApiModelProperty(value = "分数", example = "30")
    @JsonProperty("Score")
    private int score;

    @ApiModelProperty(value = "补考次数", example = "2")
    @JsonProperty("UserResitNumber")
    private Integer userResitNumber;

    @ApiModelProperty(value = "是否统计", example = "1")
    @JsonProperty("IsStatic")
    private String isStatic;

    @ApiModelProperty(value = "错误次数", example = "2")
    @JsonProperty("Errorcount")
    private int errorCount;

    @ApiModelProperty(value = "及格分数", example = "80")
    @JsonProperty("PassMark")
    private int passMark;

    @ApiModelProperty(value = "标志", example = "1")
    @JsonProperty("Flag")
    private Integer flag;

    @ApiModelProperty(value = "计划ID", example = "-999")
    @JsonProperty("PlanID")
    private Long planId;

    public List<AnswerSubmitRequest> getAnswers() {
        try {
            // 数据转换
            this.answers = JsonUtil.objectMapper().readValue(this.answerXML, new TypeReference<List<AnswerSubmitRequest>>(){});
        } catch (Exception e) {
            logger.error("数据转换失败", e);
        }
        return this.answers;
    }
}
