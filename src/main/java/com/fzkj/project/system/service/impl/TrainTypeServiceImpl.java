package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.CompanyLessonSafeTrainSet;
import com.fzkj.project.system.entity.LessonSafeTrainCompany;
import com.fzkj.project.system.entity.TrainType;
import com.fzkj.project.system.mapper.TrainTypeMapper;
import com.fzkj.project.system.request.TrainTypeRequest;
import com.fzkj.project.system.service.CompanyLessonSafeTrainSetService;
import com.fzkj.project.system.service.TrainTypeService;
import com.fzkj.project.system.vo.TrainTypeVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 安全培训课程分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class TrainTypeServiceImpl extends ServiceImpl<TrainTypeMapper, TrainType> implements TrainTypeService {

    private final CompanyLessonSafeTrainSetService companyLessonSafeTrainSetService;

    @Override
    public List<TrainType> listTrainType(TrainTypeRequest request) {
        TrainType entity = (TrainType) DataTransfer.transfer(request, TrainType.class);
        if(StringUtils.isEmpty(entity.getTrainTypeName())){
            entity.setTrainTypeName(null);
        }
        LambdaQueryWrapper<TrainType> queryWrapper = new LambdaQueryWrapper<>(entity);
        if(null != request.getCompanyId()){
            //获取企业参培设置
            LambdaQueryWrapper<CompanyLessonSafeTrainSet> companyLessonSafeTrainSetLambdaQueryWrapper = new LambdaQueryWrapper<CompanyLessonSafeTrainSet>();
            companyLessonSafeTrainSetLambdaQueryWrapper.eq(CompanyLessonSafeTrainSet::getCompanyId, request.getCompanyId());
            companyLessonSafeTrainSetLambdaQueryWrapper.eq(CompanyLessonSafeTrainSet::getIsValid,1);
            List<CompanyLessonSafeTrainSet> list = companyLessonSafeTrainSetService.list(companyLessonSafeTrainSetLambdaQueryWrapper);
            if(CollectionUtils.isEmpty(list)){
                return new ArrayList<>();
            }
            queryWrapper.in(TrainType::getId, list.stream().map(CompanyLessonSafeTrainSet::getLessonCategoryId).collect(Collectors.toList()));
        }
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(TrainTypeRequest request, PageEntity pageEntity) {
        TrainType entity = (TrainType) DataTransfer.transfer(request, TrainType.class);
        LambdaQueryWrapper<TrainType> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<TrainType> page = this.page(
                new Query<TrainType>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public TrainTypeVO findTrainType(Long id) {
        TrainType entity = this.getById(id);
        TrainTypeVO vo = (TrainTypeVO) DataTransfer.transfer(entity, TrainTypeVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveTrainType(TrainTypeVO vo) {
        TrainType entity = (TrainType) DataTransfer.transfer(vo, TrainType.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTrainType(TrainTypeVO vo) {
        TrainType entity = (TrainType) DataTransfer.transfer(vo, TrainType.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTrainType(Long id) {
        return this.removeById(id);
    }
}