package com.fzkj.project.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.project.system.entity.Company;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.entity.UserLimit;
import com.fzkj.project.system.entity.UserRole;
import com.fzkj.project.system.mapper.CompanyMapper;
import com.fzkj.project.system.mapper.UserRoleMapper;
import com.fzkj.project.system.vo.AuthTargetVO;
import com.fzkj.project.system.vo.UserLimitVO;
import com.fzkj.project.system.request.UserLimitRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.UserLimitMapper;
import com.fzkj.project.system.service.UserLimitService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户管理机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserLimitServiceImpl extends ServiceImpl<UserLimitMapper, UserLimit> implements UserLimitService {

    private final UserRoleMapper userRoleMapper;
    private final CompanyMapper companyMapper;
    @Override
    public String listUserLimit(UserLimitRequest request) {
        JSONObject jsonObject = new JSONObject();
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        List<UserLimitVO> userLimitVOS = getBaseMapper().selectUserLimitListByUserCode(request.getKeyword());
        for (UserLimitVO userLimitVO : userLimitVOS) {
            if (userLimitVO.getUserCode().equals(user.getUserCode()) || "admin".equals(userLimitVO.getUserName())){
                userLimitVO.setIsMy(1);
            }else {
                userLimitVO.setIsMy(0);
            }
        }
        jsonObject.put("DataInfo", userLimitVOS);
        return jsonObject.toString();
    }

    @Override
    public PageUtils listUserLimitPage(UserLimitRequest request, PageEntity pageEntity){
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        PageHelper.startPage(pageEntity.getPageIndex(),pageEntity.getPageSize());
        List<UserLimitVO> list = getBaseMapper().selectUserLimitListByCompanyId(Long.parseLong(request.getCompanyId()));
        for (UserLimitVO userLimitVO : list) {
            if (userLimitVO.getUserCode().equals(user.getUserCode()) || "admin".equals(userLimitVO.getUserName())){
                userLimitVO.setIsMy(1);
            }else {
                userLimitVO.setIsMy(0);
            }
        }
        return new PageUtils(pageEntity,list);
    }

    @Override
    public PageUtils listByPage(UserLimitRequest request, PageEntity pageEntity) {
        UserLimit entity = (UserLimit) DataTransfer.transfer(request, UserLimit.class);
        LambdaQueryWrapper<UserLimit> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<UserLimit> page = this.page(
                new Query<UserLimit>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public UserLimitVO findUserLimit(Long id) {
        UserLimit entity = this.getById(id);
        UserLimitVO vo = (UserLimitVO) DataTransfer.transfer(entity, UserLimitVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveUserLimit(UserLimitVO vo) {
        String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
/*        Integer i = this.baseMapper.selectUserLimitCountByUserCode(vo.getUserCode(), vo.getTargetId());
        if (i>0){
            throw new CustomException("请勿重复设置!");
        }*/
        //新增用户角色关联表数据(PC)
        UserRole userRolePc = new UserRole();
        userRolePc.setTargetId(vo.getTargetId());
        userRolePc.setRoleId(Long.parseLong(vo.getPcRoleId()));
        userRolePc.setUserCode(vo.getUserCode());
        userRolePc.setOperPlatform(16);
        userRolePc.setCreatorCode(userCode);
        userRolePc.setCreatorTime(LocalDateTime.now());
        userRolePc.setIsValid(1);
        userRoleMapper.insert(userRolePc);
        //新增用户角色关联表数据(小程序)
        UserRole userRoleMobile = new UserRole();
        userRoleMobile.setTargetId(vo.getTargetId());
        userRoleMobile.setRoleId(Long.parseLong(vo.getMobileRoleId()));
        userRoleMobile.setUserCode(vo.getUserCode());
        userRoleMobile.setOperPlatform(256);
        userRoleMobile.setCreatorCode(userCode);
        userRoleMobile.setCreatorTime(LocalDateTime.now());
        userRoleMobile.setIsValid(1);
        userRoleMapper.insert(userRoleMobile);
        //新增本表数据
        UserLimit entity = (UserLimit) DataTransfer.transfer(vo, UserLimit.class);
        entity.setCreatorCode(userCode);
        entity.setCreatorTime(LocalDateTime.now());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserLimit(UserLimitVO vo) {
        UserLimit entity = (UserLimit) DataTransfer.transfer(vo, UserLimit.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeUserLimit(UserLimitVO userLimitVO) {
        //删除用户角色关联表数据(PC)
        LambdaQueryWrapper<UserRole> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(UserRole::getUserCode,userLimitVO.getUserCode());
        queryWrapper1.eq(UserRole::getRoleId,userLimitVO.getPcRoleId());
        queryWrapper1.eq(UserRole::getTargetId,userLimitVO.getTargetId());
        userRoleMapper.delete(queryWrapper1);
        //删除用户角色关联表数据(小程序)
        LambdaQueryWrapper<UserRole> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(UserRole::getUserCode,userLimitVO.getUserCode());
        queryWrapper2.eq(UserRole::getRoleId,userLimitVO.getMobileRoleId());
        queryWrapper2.eq(UserRole::getTargetId,userLimitVO.getTargetId());
        userRoleMapper.delete(queryWrapper2);
        //删除本表数据
        LambdaQueryWrapper<UserLimit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLimit::getUserCode,userLimitVO.getUserCode());
        queryWrapper.eq(UserLimit::getTargetId,userLimitVO.getTargetId());
        this.remove(queryWrapper);
        return true;
    }

    @Override
    public List<AuthTargetVO> loginAuthTargetVO(String targetId){
        String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
        List<AuthTargetVO> authTargetVOS = baseMapper.selectLoginAuthTargetVO(userCode,targetId);

        //TODO:修改返回值
        for (AuthTargetVO authTargetVO : authTargetVOS) {
            Company company = companyMapper.selectById(authTargetVO.getFid());
            if (!company.getParentId().equals(543L) && !company.getParentId().equals(0L)){
                String[] split = company.getAncestors().split(",");
                authTargetVO.setFid(Long.valueOf(split[2]));
                authTargetVO.setFName(companyMapper.selectById(Long.valueOf(split[2])).getCompanyName());
            }else if (company.getParentId().equals(0L)){
                authTargetVO.setFid(authTargetVO.getId());
                authTargetVO.setFName(authTargetVO.getCompanyName());
            }
        }
        return authTargetVOS;
    }
}