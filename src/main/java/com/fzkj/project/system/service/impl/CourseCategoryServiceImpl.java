package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.project.system.entity.Course;
import com.fzkj.project.system.entity.CourseCategory;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.enums.OperateFlag;
import com.fzkj.project.system.mapper.CourseMapper;
import com.fzkj.project.system.request.CourseCategoryQueryRequest;
import com.fzkj.project.system.service.CourseService;
import com.fzkj.project.system.vo.CourseCategoryVO;
import com.fzkj.project.system.request.CourseCategoryRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.mapper.CourseCategoryMapper;
import com.fzkj.project.system.service.CourseCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 平台课件分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CourseCategoryServiceImpl extends ServiceImpl<CourseCategoryMapper, CourseCategory> implements CourseCategoryService {

    private final CourseService courseService;

    @Override
    public List<CourseCategory> listCourseCategory(CourseCategoryRequest request) {
        CourseCategory entity = (CourseCategory) DataTransfer.transfer(request, CourseCategory.class);
        LambdaQueryWrapper<CourseCategory> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public CourseCategoryVO findCourseCategory(Long id) {
        CourseCategory entity = this.getById(id);
        CourseCategoryVO vo = (CourseCategoryVO) DataTransfer.transfer(entity, CourseCategoryVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCourseCategory(CourseCategoryVO vo) {
        CourseCategory entity = (CourseCategory) DataTransfer.transfer(vo, CourseCategory.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCourseCategory(CourseCategoryVO vo) {
        CourseCategory entity = (CourseCategory) DataTransfer.transfer(vo, CourseCategory.class);
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        if (vo.getFlag().equals(OperateFlag.ADD.getCode())) {
            //查询是否有相同名称的分类
            LambdaQueryWrapper<CourseCategory> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(CourseCategory::getCategoryName, vo.getCategoryName());
            int count = this.count(checkWrapper);
            if (count > 0) {
                throw new CustomException(1003, "新增失败," + vo.getCategoryName() + "已存在");
            }
            entity.setCreatorName(user.getUserName());
            entity.setCreationTime(now);
            entity.setReviseCode(user.getUserCode());
            entity.setReviseName(user.getUserName());
            entity.setCreatorCode(user.getUserCode());
            entity.setReviseTime(now);
            return this.save(entity);
        } else if (vo.getFlag().equals(OperateFlag.EDIT.getCode())) {
            //查询是否有相同名称的分类
            LambdaQueryWrapper<CourseCategory> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(CourseCategory::getCategoryName, vo.getCategoryName());
            checkWrapper.ne(CourseCategory::getId, vo.getId());
            int count = this.count(checkWrapper);
            if (count > 0) {
                throw new CustomException(1003, "修改失败," + vo.getCategoryName() + "已存在");
            }
            entity.setReviseCode(user.getUserCode());
            entity.setReviseName(user.getUserName());
            entity.setReviseTime(now);
            return this.saveOrUpdate(entity);
        } else if (vo.getFlag().equals(OperateFlag.DEL.getCode())) {
            LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Course::getCategoryId, entity.getId());
            queryWrapper.eq(Course::getBelongPlat, 1);
            int count = courseService.count(queryWrapper);
            if (count > 0) {
                throw new CustomException(1004, "删除失败，该分类已使用");
            }
            LambdaUpdateWrapper<CourseCategory> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CourseCategory::getIsValid, 0);
            updateWrapper.set(CourseCategory::getReviseCode, user.getUserCode());
            updateWrapper.set(CourseCategory::getReviseName, user.getUserName());
            updateWrapper.set(CourseCategory::getReviseTime, now);
            updateWrapper.eq(CourseCategory::getId, entity.getId());
            return this.update(updateWrapper);
        } else if (vo.getFlag().equals(OperateFlag.RECOVERY.getCode())) {
            LambdaUpdateWrapper<CourseCategory> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CourseCategory::getIsValid, 1);
            updateWrapper.set(CourseCategory::getReviseCode, user.getUserCode());
            updateWrapper.set(CourseCategory::getReviseName, user.getUserName());
            updateWrapper.set(CourseCategory::getReviseTime, now);
            updateWrapper.eq(CourseCategory::getId, entity.getId());
            return this.update(updateWrapper);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCourseCategory(Long id) {
        return this.removeById(id);
    }

    @Override
    public PageUtils listCourseCategory(CourseCategoryQueryRequest request, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List list = null;
        if ("list".equals(request.getFlag())) {
            list = this.baseMapper.selectCourseCategory(request);
        } else if ("byId".equals(request.getFlag())) {
            list = new ArrayList<>();
            CourseCategory byId = getById(request.getId());
            if(null!=byId){
                list.add(byId);
            }
        }
        return new PageUtils(pageEntity, list);
    }
}