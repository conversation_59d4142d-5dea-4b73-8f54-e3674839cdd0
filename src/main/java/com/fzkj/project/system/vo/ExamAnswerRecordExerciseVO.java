package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户练习记录
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value="用户练习记录VO")
public class ExamAnswerRecordExerciseVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "组织ID")
    @NotNull(message = "组织ID不能为空")
    @JsonProperty("OrganizationID")
    private String organizationId;

    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不能为空")
    @JsonProperty("UserID")
    private String userId;

    @ApiModelProperty(value = "考试ID")
    @NotNull(message = "考试ID不能为空")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "题目ID")
    @NotNull(message = "题目ID不能为空")
    @JsonProperty("QuestionID")
    private Long questionId;

    @ApiModelProperty(value = "用户选择的答案")
    @NotNull(message = "用户选择的答案不能为空")
    @JsonProperty("SubOption")
    private String subOption;

    @ApiModelProperty(value = "正确答案")
    @NotNull(message = "正确答案不能为空")
    @JsonProperty("RightOption")
    private String rightOption;

    @ApiModelProperty(value = "得分")
    @NotNull(message = "得分不能为空")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @NotNull(message = "创建人ID不能为空")
    @JsonProperty("CreatorID")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @NotNull(message = "修改人ID不能为空")
    @JsonProperty("ReviseID")
    private String reviseId;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效：0：无效，1：有效")
    @NotNull(message = "是否有效：0：无效，1：有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("Flag")
    private String flag;
}