package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 企业自建课件分类
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "企业自建课件分类Request")
public class CourseCategoryOneselfRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "分类名称(企业自建课件分类)")
    private String categoryName;

    @ApiModelProperty(value = "是否显示（1：显示，0：未显示）")
    private Integer isShow;

    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    @ApiModelProperty(value = "修改人姓名")
    private String reviseName;

    @ApiModelProperty(value = "来源（1：行业版）")
    private String source;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否有效 1是 0否 默认有效")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    private String reservedField5;

}