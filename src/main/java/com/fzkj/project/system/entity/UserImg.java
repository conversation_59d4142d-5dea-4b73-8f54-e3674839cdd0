package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户签名照片表
 *
 */
@Data
@TableName("t_d_user_img")
@ApiModel(value = "用户签名照片表")
public class UserImg implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("UserCode")
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @JsonProperty("Type")
    @ApiModelProperty(value = "照片类型（1：人脸识别，2：签名）")
    private Integer type;

    @JsonProperty("PhotoUrl")
    @ApiModelProperty(value = "照片地址")
    private String photoUrl;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @TableField(exist = false)
    @JsonProperty("Flag")
    String flag;
}