package com.fzkj.api.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fzkj.api.entity.vo.TrainingYesCourse;
import com.fzkj.api.utils.SM4;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PlatHttpUtil;
import com.fzkj.common.utils.aliyun.SmsUtil;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.project.system.vo.UserLessonRecordSafeTrainUserVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import static com.fzkj.common.utils.MongoQueryUtil.*;

@Component
@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
@Slf4j
public class SyncFkmTask {
    private final static String FKM_EDU_KEY = "fkm_edu_task";
    @Autowired
    @Qualifier("mongoTemplateMaster")
    private MongoTemplate mongoTemplateMaster;

    @Autowired
    @Qualifier("mongoTemplateHistory")
    private MongoTemplate mongoTemplateHistory;

    @Resource
    private  RedisCache redisCache;

    @Scheduled(cron = "00 01 00 * * ?")
    public void  syncTranEdu (){
        Boolean lock = redisCache.getLock(FKM_EDU_KEY, "true", 1200);
        if (lock) {
            try {
                log.info("开始同步培训学习数据");
                Date now = new Date();
                SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd");
                String time = DateUtils.getBeforeDay(format.format(now), 1);
                List<Integer> staticStatusList = new ArrayList<>();
                staticStatusList.add(1);
                staticStatusList.add(2);
                staticStatusList.add(4);
                Aggregation aggregation = Aggregation.newAggregation(
                        Aggregation.match(Criteria.where("isValid").is(1)),
                        getConditionalStage("statisticalGroup", 0),
                        getConditionalStageOrLike(time,"completeTime"),
                        getConditionalStage("staticStatus", staticStatusList));
                List<UserLessonRecordSafeTrainUserVO> mappedResults = mongoTemplateMaster.aggregate(aggregation, "_tableSafeTrainUserLesson", UserLessonRecordSafeTrainUserVO.class).getMappedResults();
                List<UserLessonRecordSafeTrainUserVO> mappedResultsHistory = mongoTemplateHistory.aggregate(aggregation, "_tableSafeTrainUserLesson", UserLessonRecordSafeTrainUserVO.class).getMappedResults();
                List<UserLessonRecordSafeTrainUserVO> list = mergeList(mappedResults, mappedResultsHistory);
                String encrypt = null;
                List<TrainingYesCourse> trainingYesCourses = new ArrayList<>();
                for (UserLessonRecordSafeTrainUserVO mappedResult : list) {
                    String month = null;
                    try {
                        month = DateUtils.getMonthNow(format.parse(mappedResult.getLessonDate()));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    TrainingYesCourse trainingYesCourse=new TrainingYesCourse();
                    trainingYesCourse.setName(mappedResult.getUserName());
                    trainingYesCourse.setIdno(mappedResult.getIdCard());
                    trainingYesCourse.setPhone(mappedResult.getPhone());
                    trainingYesCourse.setJob("驾驶员");
                    trainingYesCourse.setStudentImg(mappedResult.getUserPhoto());
                    trainingYesCourse.setMonth(month);
                    trainingYesCourse.setCourseName(mappedResult.getLessonName());
                    trainingYesCourse.setCourseStat(3);
                    trainingYesCourse.setLearnStat(3);
                    trainingYesCourse.setCompeleteTime(mappedResult.getCompleteTime());
                    trainingYesCourse.setDuration(mappedResult.getTrainTimeCount());
                    trainingYesCourse.setExamineStat(1);
                    String score = mappedResult.getScore();
                    if (score.contains(".")){
                        String[] split = score.split("\\.");
                        score = split[0];
                    }
                    trainingYesCourse.setScore(Long.valueOf(score));
                    trainingYesCourse.setTestTime(mappedResult.getTrainEndTime());
                    trainingYesCourse.setCompName(mappedResult.getDepartName());
                    trainingYesCourse.setCompCode(String.valueOf(mappedResult.getDepartId()));
                    trainingYesCourses.add(trainingYesCourse);
                }
                try {
                    encrypt = SM4.encrypt(JSONObject.toJSONString(trainingYesCourses), SM4.KEY);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                PlatHttpUtil.sendPostParams("https://fkm.cqphx.cn:4443/prod-api/api/training/saveTrain",encrypt,null,
                        "UTF-8",null);
                log.info("日期："+time);
                log.info("条数："+trainingYesCourses.size()+"条");
                log.info("结束同步培训学习数据");
                //短信通知
                if (trainingYesCourses.size()<10){
                    //短信通知
                    SmsUtil.sendSMS("15213769431", "9"+ trainingYesCourses.size() +"99");
                }else if (trainingYesCourses.size()<100){
                    SmsUtil.sendSMS("15213769431", "9"+ trainingYesCourses.size() +"9");
                }else if (trainingYesCourses.size()<1000){
                    SmsUtil.sendSMS("15213769431", "9"+ trainingYesCourses.size());
                }else {
                    SmsUtil.sendSMS("15213769431", String.valueOf(trainingYesCourses.size()));
                }
            }catch (Exception e){
                e.printStackTrace();
            }finally {
                redisCache.releaseLock(FKM_EDU_KEY, "true");
            }
        }
    }


    public static List<UserLessonRecordSafeTrainUserVO> mergeList(List<UserLessonRecordSafeTrainUserVO> list1, List<UserLessonRecordSafeTrainUserVO> list2) {
        if (null == list1) {
            list1 = new ArrayList<>();
        }
        List<UserLessonRecordSafeTrainUserVO> result = new ArrayList<>(list1);
        if (CollectionUtils.isNotEmpty(list2)) {
            result.addAll(list2);
        }
        return result;
    }
}
