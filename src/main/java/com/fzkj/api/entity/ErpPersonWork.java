package com.fzkj.api.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class ErpPersonWork {
    /**
     * code 工号
     */
    private String empCode;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idno;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 生日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private String birthday;

    /**
     * 任职机构
     */
    private String orgCode;

    /**
     * 任职部门
     */
    private String deptCode;

    /**
     * 任职部门
     */
    private String deptName;

    /**
     * 任职岗位
     */
    private String postName;

    /**
     * 机构编码
     */
    private String compCode;

    /**
     * 职务名称
     */
    private String jobName;
}
