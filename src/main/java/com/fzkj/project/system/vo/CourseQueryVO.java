package com.fzkj.project.system.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

@Data
public class CourseQueryVO {

    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "课程名称")
    @JsonProperty("CourseName")
    private String courseName;

    @ApiModelProperty(value = "时长")
    @JsonProperty("TimeCount")
    private Integer timeCount;

    @ApiModelProperty(value = "分类名称")
    @JsonProperty("CategoryName")
    private String categoryName;

    @ApiModelProperty(value = "文件类型")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(value = "文件ID")
    @JsonProperty("FileID")
    private String fileId;

    @ApiModelProperty(value = "是否显示")
    @JsonProperty("IsShow")
    private Integer isShow;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private String creationTime;

    @ApiModelProperty(value = "文件URL")
    @JsonProperty("FileUrlHLS")
    private String fileUrlHLS;

    @ApiModelProperty(value = "编码状态")
    @JsonProperty("EncodeState")
    private Integer encodeState;

    @ApiModelProperty(value = "评论数")
    @JsonProperty("CommentNum")
    private Integer commentNum;

    @ApiModelProperty(value = "评分")
    @JsonProperty("Grade")
    private Double grade;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTimes")
    private LocalDateTime creationTimes;

    @ApiModelProperty(value = "学习人数")
    @JsonProperty("LearnNum")
    private String learnNum;

    @ApiModelProperty(value = "教师名称")
    @JsonProperty("TearcherName")
    private String tearcherName;

    @ApiModelProperty(value = "创建者名称")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "编码")
    @JsonProperty("courseCode")
    private String courseCode;
}
