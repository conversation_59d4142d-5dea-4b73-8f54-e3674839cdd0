package com.fzkj.project.monitor.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.monitor.domain.SysLogininfor;
import com.fzkj.project.monitor.mapper.SysLogininforMapper;
import com.fzkj.project.monitor.service.ISysLogininforService;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 系统访问日志情况信息 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysLogininforServiceImpl extends ServiceImpl<SysLogininforMapper, SysLogininfor> implements ISysLogininforService {
    @Override
    public List selectLogininforList(SysLogininfor logininfor) {
        return baseMapper.selectLogininforList(logininfor);
    }
}
