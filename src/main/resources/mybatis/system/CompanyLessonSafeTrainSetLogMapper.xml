<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CompanyLessonSafeTrainSetLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.CompanyLessonSafeTrainSetLog">
        <id column="id" property="id" />
        <result column="safe_train_set_id" property="safeTrainSetId" />
        <result column="operation_name" property="operationName" />
        <result column="object_name" property="objectName" />
        <result column="object_values" property="objectValues" />
        <result column="lesson_category_name" property="lessonCategoryName" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="creator_name" property="creatorName" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, safe_train_set_id, operation_name, object_name, object_values, lesson_category_name, creator_code, creation_time, creator_name, remark, is_valid
    </sql>

</mapper>