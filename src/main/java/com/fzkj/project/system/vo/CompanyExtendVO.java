package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 企业扩展表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="企业扩展表VO")
public class CompanyExtendVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("TaxpayerNumber")
    private String taxpayerNumber;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LicenseKey")
    private String licenseKey;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Liaison")
    private String liaison;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LiaisonPhone")
    private String liaisonPhone;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IndustryType")
    private String industryType;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Nature")
    private String nature;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Scale")
    private String scale;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("BusinessScope")
    private String businessScope;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("RegisterSource")
    private String registerSource;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsEnable")
    private Integer isEnable;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("EnableSummary")
    private String enableSummary;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("OperPlatform")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("OpenSign")
    private Integer openSign;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CompanyLabel")
    private String companyLabel;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("MerchantName")
    private String merchantName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("MerchantCode")
    private String merchantCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("TaxpayerUrl")
    private String taxpayerUrl;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("TaxpayerFileName")
    private String taxpayerFileName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("RegisterTime")
    private String registerTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("SiteArea")
    private String siteArea;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("RegisterCapital")
    private String registerCapital;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("EconomicType")
    private Integer economicType;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsOpenAlbum")
    private Integer isOpenAlbum;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsSafeStatus")
    private Integer isSafeStatus;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("UplusTime")
    private String uplusTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CompRange")
    private Integer compRange;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsAddress")
    private Integer isAddress;

}