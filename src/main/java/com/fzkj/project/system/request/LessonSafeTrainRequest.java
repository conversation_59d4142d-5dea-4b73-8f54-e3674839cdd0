package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全培训课程
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "安全培训课程Request")
public class LessonSafeTrainRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "部门ID")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "课件分类ID")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课件信息")
    @JsonProperty("LessonName")
    private String lessonName;

    @ApiModelProperty(value = "查询关键字")
    @JsonProperty("Keywords")
    private String keywords;

    @ApiModelProperty(value = "课程时长")
    @JsonProperty("TotalTimeCount")
    private Integer totalTimeCount;

    @ApiModelProperty(value = "有效期开始时间")
    @JsonProperty("STime")
    private String sTime;

    @ApiModelProperty(value = "有效期结束时间")
    @JsonProperty("ETime")
    private String eTime;

    @ApiModelProperty(value = "来源（1：行业版2.0）")
    @JsonProperty("Source")
    private Integer source;

    @ApiModelProperty(value = "参培课程（1：（通用课程）自动分发 2：（定制课程）手动分发）")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "查询标识 list:查询全部  byId:查询单条记录")
    @JsonProperty("Flag")
    private String flag;

    @ApiModelProperty(value = "课程状态 1：(发布)， 0：（删除） -1：未发布")
    @JsonProperty("IsValid")
    private String isValid;

    @ApiModelProperty(value = "课程形式 1：课件+考试 2：只有课件 3：只有考试")
    @JsonProperty("Shape")
    private String shape;

    @ApiModelProperty(value = "培训计划ID")
    @JsonProperty("PlanID")
    private String planId;

    @ApiModelProperty(value = "课程月份")
    @JsonProperty("LessonDate")
    private String lessonDate;

    @ApiModelProperty(value = "课件所属来源")
    @JsonProperty("BelongPlat")
    private Integer belongPlat;

    @ApiModelProperty(value = "是否小于结束日期")
    private Integer beforeEndTime;

    @ApiModelProperty(value = "是否在课程日期之间")
    private Integer betweenLessonDate;

    @ApiModelProperty(value = "课程状态")
    private Integer state;

    @ApiModelProperty(value = "是否删除")
    private Integer isDel;

    @ApiModelProperty(value = "是否删除")
    private List<Long> lessonIds;

    @ApiModelProperty(value = "企业名称")
    private String CompanyName;

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("UserCode")
    private String userCode;
}

