<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.ReminderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.Reminder">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="val" property="val" />
        <result column="creator_code" property="creatorCode" />
        <result column="creator_time" property="creatorTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, val, creator_code, creator_time, revise_code, revise_time
    </sql>

</mapper>