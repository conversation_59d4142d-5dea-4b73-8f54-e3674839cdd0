package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 安全培训课程课件关系
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_lesson_course_safe_train")
@ApiModel(value = "安全培训课程课件关系实体")
public class LessonCourseSafeTrain implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("LessonID")
    @ApiModelProperty(value = "课程ID")
    private Long lessonId;

    @JsonProperty("CourseID")
    @ApiModelProperty(value = "课件ID")
    private Long courseId;

    @JsonProperty("BelongPlat")
    @ApiModelProperty(value = "所属平台")
    private Integer belongPlat;

    @JsonProperty("Source")
    @ApiModelProperty(value = "来源")
    private String source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人")
    private String creatorCode;

    @JsonProperty("CreationTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;
}