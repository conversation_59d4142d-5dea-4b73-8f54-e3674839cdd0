package com.fzkj.project.basicdata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.basicdata.entity.SubStanceType;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2024-03-13 13:52
 * @Version: 1.0
 */
public interface SubStanceTypeService extends IService<SubStanceType> {
    PageUtils listByPage(SubStanceType request, PageEntity pageEntity);

    /**
     * 新增内容管理
     *
     * @param subStanceType 内容管理
     * @return 结果
     */
    public Long insertSubStanceType(SubStanceType subStanceType);

    /**
     * 修改内容管理
     *
     * @param subStanceType 内容管理
     * @return 结果
     */
    public Boolean updateSubStanceType(SubStanceType subStanceType);

    /**
     * 删除内容管理
     *
     * @param id 内容管理主键
     * @return 结果
     */
    public Boolean deleteSubStanceType(Long id);

    String exportSubStanceType(SubStanceType request, PageEntity pageEntity);

    PageUtils getSubStanceTypeAPP(SubStanceType request, PageEntity pageEntity);
}
