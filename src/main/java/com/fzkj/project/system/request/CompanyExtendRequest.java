package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 企业扩展表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "企业扩展表Request")
public class CompanyExtendRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private Long companyId;

    @ApiModelProperty(value = "")
    private String taxpayerNumber;

    @ApiModelProperty(value = "")
    private String licenseKey;

    @ApiModelProperty(value = "")
    private String liaison;

    @ApiModelProperty(value = "")
    private String liaisonPhone;

    @ApiModelProperty(value = "")
    private String industryType;

    @ApiModelProperty(value = "")
    private String nature;

    @ApiModelProperty(value = "")
    private String scale;

    @ApiModelProperty(value = "")
    private String businessScope;

    @ApiModelProperty(value = "")
    private String registerSource;

    @ApiModelProperty(value = "")
    private Integer isEnable;

    @ApiModelProperty(value = "")
    private String enableSummary;

    @ApiModelProperty(value = "")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "")
    private String creatorCode;

    @ApiModelProperty(value = "")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "")
    private String reviseCode;

    @ApiModelProperty(value = "")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty(value = "")
    private Integer isValid;

    @ApiModelProperty(value = "")
    private Integer openSign;

    @ApiModelProperty(value = "")
    private String companyLabel;

    @ApiModelProperty(value = "")
    private String merchantName;

    @ApiModelProperty(value = "")
    private String merchantCode;

    @ApiModelProperty(value = "")
    private String taxpayerUrl;

    @ApiModelProperty(value = "")
    private String taxpayerFileName;

    @ApiModelProperty(value = "")
    private String registerTime;

    @ApiModelProperty(value = "")
    private String siteArea;

    @ApiModelProperty(value = "")
    private String registerCapital;

    @ApiModelProperty(value = "")
    private Integer economicType;

    @ApiModelProperty(value = "")
    private Integer isOpenAlbum;

    @ApiModelProperty(value = "")
    private Integer isSafeStatus;

    @ApiModelProperty(value = "")
    private String uplusTime;

    @ApiModelProperty(value = "")
    private Integer compRange;

    @ApiModelProperty(value = "")
    private Integer isAddress;

}