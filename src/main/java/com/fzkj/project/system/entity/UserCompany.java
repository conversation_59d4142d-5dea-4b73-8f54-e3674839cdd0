package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_user_company")
@ApiModel(value = "用户信息实体")
public class UserCompany implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("UserCode")
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @JsonProperty("CompanyID")
    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    @JsonProperty("DepartID")
    @ApiModelProperty(value = "部门ID")
    private Long departId;

    @JsonProperty("WorkID")
    @ApiModelProperty(value = "工号")
    private Long workId;

    @JsonProperty("UserName")
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @JsonProperty("UserPhoto")
    @ApiModelProperty(value = "用户头像")
    private String userPhoto;

    @JsonProperty("Sex")
    @ApiModelProperty(value = "性别")
    private Integer sex;

    @JsonProperty("BirthDay")
    @ApiModelProperty(value = "出生日期")
    private LocalDate birthDay;

    @JsonProperty("HomeAddress")
    @ApiModelProperty(value = "家庭住址")
    private String homeAddress;

    @JsonProperty("IsEnable")
    @ApiModelProperty(value = "是否启用")
    private Integer isEnable;

    @JsonProperty("IsOut")
    @ApiModelProperty(value = "是否离职")
    private Integer isOut;

    @JsonProperty("IsActivate")
    @ApiModelProperty(value = "是否激活")
    private Integer isActivate;

    @JsonProperty("PlateNumber")
    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    @JsonProperty("SignUrl")
    @ApiModelProperty(value = "签名地址")
    private String signUrl;

    @JsonProperty("TrainTypeIdStr")
    @ApiModelProperty(value = "培训类型")
    private String trainTypeIdStr;

    @JsonProperty("AccountCode")
    @ApiModelProperty(value = "账号")
    private String accountCode;

    @JsonProperty("OperPlatform")
    @ApiModelProperty(value = "操作平台")
    private Integer operPlatform;

    @JsonProperty("Source")
    @ApiModelProperty(value = "来源")
    private String source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("Identity")
    @ApiModelProperty(value = "身份")
    private Integer identity;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @JsonProperty("IndustryType")
    @ApiModelProperty(value = "行业类型")
    private String industryType;

    @JsonProperty("JoinTime")
    @ApiModelProperty(value = "加入时间")
    private String joinTime;

    @JsonProperty("NoAccidentCertificate")
    @ApiModelProperty(value = "无事故证明")
    private String noAccidentCertificate;

    @JsonProperty("NoAccidentFileName")
    @ApiModelProperty(value = "无事故证明文件名")
    private String noAccidentFileName;

    @JsonProperty("XattrsVal")
    @ApiModelProperty(value = "扩展属性")
    private String xattrsVal;

    @JsonProperty("EduLevel")
    @ApiModelProperty(value = "学历")
    private String eduLevel;

    @JsonProperty("IDCardJson")
    @ApiModelProperty(value = "身份证信息")
    private String idCardJson;

    @JsonProperty("SosName")
    @ApiModelProperty(value = "紧急联系人姓名")
    private String sosName;

    @JsonProperty("SosPhone")
    @ApiModelProperty(value = "紧急联系人电话")
    private String sosPhone;

    @JsonProperty("LearnType")
    @ApiModelProperty(value = "学习形式")
    private String learnType;

    @JsonProperty("CompanyLabel")
    @ApiModelProperty(value = "公司标签")
    private String companyLabel;
}