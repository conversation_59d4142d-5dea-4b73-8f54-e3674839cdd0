package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EditUserCourseFirstRequest {

    @ApiModelProperty(value = "用户编码", example = "b135339037ae410a87ad42c872c9353f")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "课件ID", example = "4958")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty(value = "课程ID", example = "3822")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "培训类型", example = "aqpx")
    @JsonProperty("TrainType")
    private String trainType;

    @ApiModelProperty(value = "人脸图片URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2024-4/image/171195752252360318.jpg")
    @JsonProperty("FaceImgUrl")
    private String faceImgUrl;

    @ApiModelProperty(value = "培训计划ID", example = "-999")
    @JsonProperty("PlanID")
    private String planId;

    @ApiModelProperty(value = "人脸识别", example = "44,44")
    @JsonProperty("FaceRecognitionString")
    private String faceRecognitionString;
}
