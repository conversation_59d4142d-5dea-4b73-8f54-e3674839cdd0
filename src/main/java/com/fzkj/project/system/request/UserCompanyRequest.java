package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户信息Request")
public class UserCompanyRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private String userCode;

    @ApiModelProperty(value = "")
    private Long companyId;

    @ApiModelProperty(value = "")
    private Long departId;

    @ApiModelProperty(value = "")
    private Long workId;

    @ApiModelProperty(value = "")
    private String userName;

    @ApiModelProperty(value = "")
    private String userPhoto;

    @ApiModelProperty(value = "")
    private Integer sex;

    @ApiModelProperty(value = "")
    private LocalDate birthDay;

    @ApiModelProperty(value = "")
    private String homeAddress;

    @ApiModelProperty(value = "")
    private Integer isEnable;

    @ApiModelProperty(value = "")
    private Integer isOut;

    @ApiModelProperty(value = "")
    private Integer isActivate;

    @ApiModelProperty(value = "")
    private String plateNumber;

    @ApiModelProperty(value = "")
    private String signUrl;

    @ApiModelProperty(value = "")
    private String trainTypeIdStr;

    @ApiModelProperty(value = "")
    private String accountCode;

    @ApiModelProperty(value = "")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "")
    private String creatorCode;

    @ApiModelProperty(value = "")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "")
    private String reviseCode;

    @ApiModelProperty(value = "")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty(value = "")
    private Integer identity;

    @ApiModelProperty(value = "")
    private Integer isValid;

    @ApiModelProperty(value = "")
    private String industryType;

    @ApiModelProperty(value = "")
    private String joinTime;

    @ApiModelProperty(value = "")
    private String noAccidentCertificate;

    @ApiModelProperty(value = "")
    private String noAccidentFileName;

    @ApiModelProperty(value = "")
    private String xattrsVal;

    @ApiModelProperty(value = "")
    private String eduLevel;

    @ApiModelProperty(value = "")
    private String idCardJson;

    @ApiModelProperty(value = "")
    private String sosName;

    @ApiModelProperty(value = "")
    private String sosPhone;

    @ApiModelProperty(value = "")
    private String learnType;

    @ApiModelProperty(value = "")
    private String companyLabel;

}