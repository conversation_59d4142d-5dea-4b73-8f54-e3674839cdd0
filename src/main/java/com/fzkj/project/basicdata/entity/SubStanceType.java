package com.fzkj.project.basicdata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 内容管理对象 t_d_sub_stance_type
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
@Data
@TableName("t_d_sub_stance_type")
@ApiModel(value = "内容管理实体")
public class SubStanceType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    /**
     * 类型名称
     */
    @ApiModelProperty(name = "分类名称")
    @JsonProperty("TypeName")
    private String typeName;

    /**
     * 是否展示
     */
    @ApiModelProperty(name = "栏目状态")
    @JsonProperty("IsShow")
    private Integer isShow;

    /**
     * 排序
     */
    @ApiModelProperty(name = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    /**
     * 创建人code
     */
    @ApiModelProperty(name = "创建人code")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    /**
     * 新建时间
     */
    @ApiModelProperty(name = "新建时间")
    @JsonProperty("CreationTime")
    private String creationTime;

    /**
     * 修改人code
     */
    @ApiModelProperty(name = "修改人code")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "修改时间")
    @JsonProperty("ReviseTime")
    private String reviseTime;

    /**
     * 是否有效（0：无效，1：有效）
     */
    @ApiModelProperty(name = "是否有效0=：无效，1：有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    @JsonProperty("CreatorName")
    private String creatorName;

    /**
     * 修改人
     */
    @ApiModelProperty(name = "修改人")
    @JsonProperty("ReviseName")
    private String reviseName;

    /**
     * 来源
     */
    @ApiModelProperty(name = "来源")
    @JsonProperty("Source")
    private String source;

    /**
     * 平台
     */
    @ApiModelProperty(name = "平台")
    @JsonProperty("PutInPlatform")
    private Integer putInPlatform;

    /**
     * 平台名称
     */
    @ApiModelProperty(name = "平台名称")
    @JsonProperty("PutInPlatformStr")
    private String putInPlatformStr;

    /**
     *
     */
    @ApiModelProperty(name = "分类标签")
    @JsonProperty("MarkID")
    private Integer markId;

    /**
     *
     */
    @ApiModelProperty(name = "排序")
    @JsonProperty("HomeSortRule")
    private Integer homeSortRule;

    /**
     *
     */
    @ApiModelProperty(name = "换一换")
    @JsonProperty("IsShowChange")
    private Integer isShowChange;

    /**
     *
     */
    @ApiModelProperty(name = "solgan")
    @JsonProperty("Summary")
    private String summary;

    /**
     *
     */
    @ApiModelProperty(name = "首页是否展示")
    @JsonProperty("IsShowHome")
    private Integer isShowHome;

    /**
     *
     */
    @ApiModelProperty(name = "首页数量")
    @JsonProperty("HomeNum")
    private String homeNum;

    @ApiModelProperty(name = "")
    @JsonProperty("Flag")
    @TableField(exist = false)
    private String flag;

    @ApiModelProperty(name = "")
    @JsonProperty("SubStanceNum")
    @TableField(exist = false)
    private Integer substanceNum;
}
