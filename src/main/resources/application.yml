
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 30003
#  port: 8001
  servlet:    # 应用的访问路径
    context-path: /edu
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30
  compression:
    enabled: true
    mime-types: application/javascript,text/css,application/json,application/xml,text/html,text/xml,text/plain

# 日志配置
logging:
  level:
    com.fzkj: debug
    org.springframework: warn
# Spring配置
spring:
  profiles:
    active: uat
  main:
    allow-circular-references: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 1GB
      # 设置总上传的文件大小
      max-request-size: 1GB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
#  jackson:
#    date-format: yyyy-MM-dd HH:mm:ss
#    time-zone: GMT+8
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    mode: HTML
    encoding: UTF-8
    cache: false
weixin:
  private-key-path: D:\ectocyst\fkxt-edu\src\main\resources\weixin\apiclient_cert.p12
#  private-key-path: /data/edu/pem/apiclient_cert.p12
# token配置
token:
  # 令牌自定义标识
  header: Token
  # 令牌秘钥
  secret: 62c0f15059527d4d
  # 令牌有效期（默认30分钟）
  expireTime: 1440

  # MyBatis Plus配置
mybatis-plus:
    # 搜索指定包别名
    typeAliasesPackage: com.fzkj.project.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mybatis/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 代码生成
gen:
  # 作者
  author: fzkj
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.fzkj.project.system
  # 自动去除表前缀，默认是true
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_
