package com.fzkj.framework.web.domain;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fzkj.framework.security.filter.SQLFilter;
import org.apache.commons.lang3.StringUtils;

/**
 * 查询参数
 */
public class Query<T> {

    public static final String ASC = "asc";

    public IPage<T> getPage(PageEntity pageEntity) {
        return this.getPage(pageEntity, null, false);
    }

/*    public IPage<T> getPage(Map<String, Object> params) {
        PageEntity pageEntity = new PageEntity();
        BeanUtils.copyProperties(params, pageEntity);
        return this.getPage(pageEntity, null, false);
    }*/

    public IPage<T> getPage(PageEntity pageEntity, String defaultOrderField, boolean isAsc) {
        int curPage = pageEntity.getPageIndex();
        int limit = pageEntity.getPageSize();

        //分页对象
        Page<T> page = new Page<>(curPage, limit);

        //排序字段
        //防止SQL注入（因为sidx、order是通过拼接SQL实现排序的，会有SQL注入风险）
        String orderField = SQLFilter.sqlInject(pageEntity.getSidx());
        String order = pageEntity.getOrder();


        //前端字段排序
        if (StringUtils.isNotEmpty(orderField) && StringUtils.isNotEmpty(order)) {
            if (ASC.equalsIgnoreCase(order)) {
                return page.addOrder(OrderItem.asc(orderField));
            } else {
                return page.addOrder(OrderItem.desc(orderField));
            }
        }

        //没有排序字段，则不排序
        if (StringUtils.isBlank(defaultOrderField)) {
            return page;
        }

        //默认排序
        if (isAsc) {
            page.addOrder(OrderItem.asc(defaultOrderField));
        } else {
            page.addOrder(OrderItem.desc(defaultOrderField));
        }

        return page;
    }
}
