package com.fzkj.project.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.common.utils.aliyun.FaceUtil;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.mapper.ExamMapper;
import com.fzkj.project.system.mapper.ExamUserRecordMapper;
import com.fzkj.project.system.mapper.ExamUserRecordSimulationMapper;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.*;
import com.fzkj.project.system.vo.*;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 考试信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExamServiceImpl extends ServiceImpl<ExamMapper, Exam> implements ExamService {
    private final ExaminationPaperRuleService examinationPaperRuleService;
    private final ExamQuestionService examQuestionService;
    private final ExamLessonService examLessonService;
    private final ExamUserRecordMapper examUserRecordMapper;
    private final ExamUserRecordSimulationMapper examUserRecordSimulationMapper;
    private final ExamAnswerRecordService examAnswerRecordService;
    private final ExamAnswerRecordSimulationService examAnswerRecordSimulationService;
    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;
    private final UserCourseRecordSafeTrainMongoService userCourseRecordSafeTrainMongoService;
    private final RestTemplate restTemplate;

    @Override
    public PageUtils getExamInfo(ExamRequest request, PageEntity pageEntity) {
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        List<ExamVO> list = getBaseMapper().selectExamInfo(request);
        return new PageUtils(pageEntity, list);
    }

    @Override
    public ExamVO findExam(Long id) {
        Exam entity = this.getById(id);
        ExamVO vo = (ExamVO) DataTransfer.transfer(entity, ExamVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExam(ExamVO vo) {
        Exam entity = (Exam) DataTransfer.transfer(vo, Exam.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExam(ExamVO vo) {
        Exam entity = (Exam) DataTransfer.transfer(vo, Exam.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExam(Long id) {
        return this.removeById(id);
    }

    @Override
    public PageUtils getExamInfoQuestion(ExamRequest request, PageEntity pageEntity) {
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        request.setIsValid(1);
        List<ExamVO> list = getBaseMapper().selectExamInfo(request);
        if (CollectionUtils.isNotEmpty(list)) {
            ExamRequest request2 = new ExamRequest();
            request2.setExamId(list.get(0).getId());
            //遍历list查询paperRule
            list.forEach(item -> {
                item.setExaminationPaperRuleList(getBaseMapper().selectExamPaperRule(request2));
            });
            //遍历list查询试题
            list.forEach(item -> {
                item.setExamQuestionList(getBaseMapper().selectExamQuestion(request2));
            });
        }
        return new PageUtils(pageEntity, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long editExamInfo(ExamVO request) {
        Exam entity = (Exam) DataTransfer.transfer(request, Exam.class);
        LocalDateTime now = LocalDateTime.now();
        switch (request.getFlag()) {
            case "add": //新增
                entity.setOrgId(String.valueOf(request.getCompanyId()));
                entity.setCreationTime(now);
                this.save(entity);
                if (request.getGroupRollType() == 3) {
                    List<ExaminationPaperRule> groupRollTypeStrsXML = request.getExaminationPaperRules();
                    int sum = 0;
                    if (CollectionUtils.isNotEmpty(groupRollTypeStrsXML)) {
                        for (int i = 0; i < groupRollTypeStrsXML.size(); i++) {
                            sum += groupRollTypeStrsXML.get(i).getNum() * groupRollTypeStrsXML.get(i).getSorce();
                        }
                        if (null == request.getScore() || request.getScore() - sum != 0) {
                            throw new CustomException(1005, "题目分数异常");
                        }
                        examinationPaperRuleService.saveBatch(groupRollTypeStrsXML);
                    }
                }
                return entity.getId();
            case "edit": //修改
                if (request.getScore() <= 0 || request.getScore() < request.getPassMark() || request.getQuestionCount() <= 0) {
                    throw new CustomException(1005, "总分或题数目为0或者总分小于合格分数，操作失败！");
                }
                if (request.getGroupRollType() == 1) {
                    LambdaQueryWrapper<ExamQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(ExamQuestion::getExamId, entity.getId());
                    lambdaQueryWrapper.eq(ExamQuestion::getIsValid, 1);
                    List<ExamQuestion> examQuestions = examQuestionService.list(lambdaQueryWrapper);
                    int questionCount = examQuestions.size();
                    entity.setQuestionCount(questionCount);
                    entity.setScore(examQuestions.stream().mapToInt(ExamQuestion::getScore).sum());
                    entity.setReviseTime(now);
                    entity.setRemark("0");
                    boolean updateById = this.updateById(entity);
                    if (!updateById) {
                        throw new CustomException("操作失败");
                    }
                } else if (request.getGroupRollType() == 3) {
                    examinationPaperRuleService.remove(new LambdaQueryWrapper<ExaminationPaperRule>().eq(ExaminationPaperRule::getExamId, entity.getId()));
                    this.update(entity, new LambdaQueryWrapper<Exam>().eq(Exam::getId, entity.getId()).eq(Exam::getIsValid, 1));
                    List<ExaminationPaperRule> groupRollTypeStrsXML = request.getExaminationPaperRules();
                    int sum = 0;
                    if (CollectionUtils.isNotEmpty(groupRollTypeStrsXML)) {
                        for (int i = 0; i < groupRollTypeStrsXML.size(); i++) {
                            sum += groupRollTypeStrsXML.get(i).getNum() * groupRollTypeStrsXML.get(i).getSorce();
                            groupRollTypeStrsXML.get(i).setExamId(entity.getId());
                        }
                        if (null == request.getScore() || request.getScore() - sum != 0) {
                            throw new CustomException(1005, "题目分数异常");
                        }
                        examinationPaperRuleService.saveBatch(groupRollTypeStrsXML);
                        for (int i = 0; i < groupRollTypeStrsXML.size(); i++) {
                            ExaminationPaperRule examinationPaperRule = groupRollTypeStrsXML.get(i);
                            LambdaUpdateWrapper<ExamQuestion> examQuestionLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                            examQuestionLambdaUpdateWrapper.set(ExamQuestion::getScore, examinationPaperRule.getSorce());
                            examQuestionLambdaUpdateWrapper.eq(ExamQuestion::getExamId, request.getId());
                            examQuestionLambdaUpdateWrapper.eq(ExamQuestion::getIsValid, 1);
                            examQuestionLambdaUpdateWrapper.eq(ExamQuestion::getQuestionTypeId, examinationPaperRule.getQuestionTypeId());
                            examQuestionService.update(examQuestionLambdaUpdateWrapper);
                        }
                    }
                }
                return entity.getId();
            case "del": //删除
                examLessonService.remove(Wrappers.<ExamLesson>lambdaQuery().eq(ExamLesson::getExamId, entity.getId()));
                LambdaQueryWrapper<ExamQuestion> examQuestionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                examQuestionLambdaQueryWrapper.eq(ExamQuestion::getExamId, entity.getId());
                examQuestionLambdaQueryWrapper.eq(ExamQuestion::getIsValid, 1);
                int count = examQuestionService.count(examQuestionLambdaQueryWrapper);
                if (count > 0) {
                    throw new CustomException(1005, "该试卷下有关联的试题，操作失败！");
                }
                LambdaUpdateWrapper<Exam> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Exam::getId, entity.getId());
                updateWrapper.set(Exam::getReviseTime, now);
                updateWrapper.set(Exam::getReviseId, entity.getReviseId());
                updateWrapper.set(Exam::getIsValid, 0);
                this.update(updateWrapper);
                return entity.getId();
            default:
                throw new CustomException("不支持的操作标识: " + request.getFlag());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editExamLesson(ExamVO request) {
        LocalDateTime now = LocalDateTime.now();
        switch (request.getFlag()) {
            case "add":
                LambdaQueryWrapper<ExamLesson> examLessonLambdaQueryWrapper = new LambdaQueryWrapper<>();
                examLessonLambdaQueryWrapper.eq(ExamLesson::getExamId, request.getExamId())
                        .or()
                        .eq(ExamLesson::getLessonId, request.getLessonId());
                int count = examLessonService.count(examLessonLambdaQueryWrapper);
                if (count > 0) {
                    throw new CustomException("该类型、课程已关联试卷或该试卷已被其他类型、课程关联，操作失败！");
                }
                ExamLesson entity = new ExamLesson();
                entity.setExamId(request.getExamId());
                entity.setLessonId(request.getLessonId());
                entity.setSource(request.getSource());
                return examLessonService.save(entity);
            case "del":
                LambdaQueryWrapper<ExamUserRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(ExamUserRecord::getExamId, request.getExamId());
                int existRecordCount = examUserRecordMapper.selectCount(lambdaQueryWrapper);
                if (existRecordCount > 0) {
                    throw new CustomException(1003, "该试卷已有考试记录，操作失败！");
                }
                LambdaUpdateWrapper<ExamLesson> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ExamLesson::getExamId, request.getExamId());
                return examLessonService.remove(updateWrapper);
            default:
                throw new CustomException("不支持的操作标识: " + request.getFlag());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editExamQuestion(ExamQuestionVO request) {
        ExamQuestion entity = (ExamQuestion) DataTransfer.transfer(request, ExamQuestion.class);
        Exam exam = this.getById(request.getExamId());
        boolean result = true;
        if (exam == null) {
            throw new CustomException("试卷不存在");
        }
        LocalDateTime now = LocalDateTime.now();
        switch (request.getFlag()) {
            case "add":
                if (exam.getGroupRollType() == 3) {
                    LambdaQueryWrapper<ExaminationPaperRule> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(ExaminationPaperRule::getExamId, request.getExamId());
                    lambdaQueryWrapper.eq(ExaminationPaperRule::getQuestionTypeId, request.getQuestionTypeId());
                    ExaminationPaperRule examinationPaperRule = examinationPaperRuleService.getOne(lambdaQueryWrapper);
                    if (examinationPaperRule == null) {
                        throw new CustomException("试卷规则不存在");
                    }
                    entity.setScore(examinationPaperRule.getSorce());
                }
                entity.setCreatorId(request.getCreatorId());
                entity.setCreationTime(now);
                result = examQuestionService.save(entity);
                //更新总分数
                if (result && exam.getGroupRollType() == 1) {
                    LambdaQueryWrapper<ExamQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(ExamQuestion::getExamId, request.getExamId());
                    lambdaQueryWrapper.eq(ExamQuestion::getIsValid, 1);
                    List<ExamQuestion> questions = examQuestionService.list(lambdaQueryWrapper);
                    LambdaUpdateWrapper<Exam> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(Exam::getId, request.getExamId());
                    updateWrapper.eq(Exam::getIsValid, 1);
                    updateWrapper.set(Exam::getQuestionCount, questions.size());
                    updateWrapper.set(Exam::getScore, questions.stream().mapToInt(ExamQuestion::getScore).sum());
                    updateWrapper.set(Exam::getRemark, 0);
                    updateWrapper.set(Exam::getReviseId, request.getCreatorId());
                    updateWrapper.set(Exam::getReviseTime, now);
                    result = this.update(updateWrapper);
                }
                if (!result) {
                    throw new CustomException("操作失败");
                }
                return true;
            case "edit":
                if (exam.getGroupRollType() == 1) {
                    entity.setReviseTime(now);
                    result = examQuestionService.updateById(entity);
                    if (result) {
                        LambdaQueryWrapper<ExamQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                        lambdaQueryWrapper.eq(ExamQuestion::getExamId, request.getExamId());
                        lambdaQueryWrapper.eq(ExamQuestion::getIsValid, 1);
                        List<ExamQuestion> questions = examQuestionService.list(lambdaQueryWrapper);
                        LambdaUpdateWrapper<Exam> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(Exam::getId, request.getExamId());
                        updateWrapper.eq(Exam::getIsValid, 1);
                        updateWrapper.set(Exam::getQuestionCount, questions.size());
                        updateWrapper.set(Exam::getScore, questions.stream().mapToInt(ExamQuestion::getScore).sum());
                        updateWrapper.set(Exam::getRemark, 0);
                        updateWrapper.set(Exam::getReviseId, request.getCreatorId());
                        updateWrapper.set(Exam::getReviseTime, now);
                        result = this.update(updateWrapper);
                    }
                } else {
                    entity.setScore(null);
                    entity.setReviseTime(now);
                    result = examQuestionService.updateById(entity);
                    if (result) {
                        LambdaUpdateWrapper<Exam> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(Exam::getId, request.getExamId());
                        updateWrapper.eq(Exam::getIsValid, 1);
                        updateWrapper.set(Exam::getRemark, 0);
                        result = this.update(updateWrapper);
                    }
                    if (!result) {
                        throw new CustomException("操作失败!");
                    }
                }
                return result;

            case "del":
                LambdaUpdateWrapper<ExamQuestion> examQuestionLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                examQuestionLambdaUpdateWrapper.eq(ExamQuestion::getId, request.getId());
                examQuestionLambdaUpdateWrapper.set(ExamQuestion::getIsValid, 0);
                result = examQuestionService.update(examQuestionLambdaUpdateWrapper);
                if (result) {
                    if (exam.getGroupRollType() == 1) {
                        LambdaQueryWrapper<ExamQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                        lambdaQueryWrapper.eq(ExamQuestion::getExamId, request.getExamId());
                        lambdaQueryWrapper.eq(ExamQuestion::getIsValid, 1);
                        List<ExamQuestion> questions = examQuestionService.list(lambdaQueryWrapper);
                        LambdaUpdateWrapper<Exam> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(Exam::getId, request.getExamId());
                        updateWrapper.eq(Exam::getIsValid, 1);
                        updateWrapper.set(Exam::getQuestionCount, questions.size());
                        updateWrapper.set(Exam::getScore, questions.stream().mapToInt(ExamQuestion::getScore).sum());
                        updateWrapper.set(Exam::getRemark, 0);
                        updateWrapper.set(Exam::getReviseId, request.getCreatorId());
                        updateWrapper.set(Exam::getReviseTime, now);
                        result = this.update(updateWrapper);
                    } else {
                        LambdaUpdateWrapper<Exam> examUpdateWrapper = new LambdaUpdateWrapper<>();
                        examUpdateWrapper.eq(Exam::getId, request.getExamId());
                        examUpdateWrapper.eq(Exam::getIsValid, 1);
                        examUpdateWrapper.set(Exam::getRemark, 0);
                        result = this.update(examUpdateWrapper);
                    }
                }
                if (!result) {
                    throw new CustomException("操作失败!");
                }
                return true;
            default:
                throw new CustomException("不支持的操作标识: " + request.getFlag());
        }
    }

    @Override
    public int importExamQuestion(ExamQuestionImportVO request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        switch (request.getFlag()) {
            case "add":
                int readCount = 0;
                try {
                    String excelFilePath = request.getExcelFilePath();
                    byte[] bytes = downloadExcelFile(excelFilePath);
                    List<ExamQuestionVO> examQuestionVOS = readExcelFile(bytes);
                    Exam exam = this.getById(request.getExamId());
                    Set<Integer> questionTypeIds = new HashSet();
                    String totalError = StringUtils.EMPTY;
                    for (int i = 0; i < examQuestionVOS.size(); i++) {
                        ExamQuestionVO examQuestionVO = examQuestionVOS.get(i);
                        questionTypeIds.add(examQuestionVO.getQuestionTypeId());
                        if ("单选题".equals(examQuestionVO.getQuestionTypeName())) {
                            examQuestionVO.setQuestionTypeId(1);
                            examQuestionVO.setSubjectType(1);
                        } else if ("多选题".equals(examQuestionVO.getQuestionTypeName())) {
                            examQuestionVO.setQuestionTypeId(2);
                            examQuestionVO.setSubjectType(2);
                        } else if ("判断题".equals(examQuestionVO.getQuestionTypeName())) {
                            examQuestionVO.setQuestionTypeId(3);
                            examQuestionVO.setSubjectType(3);
                        }
                        List<QuestionOption> options = new ArrayList<>();
                        addOption(examQuestionVO.getChoiceA(), options);
                        addOption(examQuestionVO.getChoiceB(), options);
                        addOption(examQuestionVO.getChoiceC(), options);
                        addOption(examQuestionVO.getChoiceD(), options);
                        addOption(examQuestionVO.getChoiceE(), options);
                        addOption(examQuestionVO.getChoiceF(), options);
                        examQuestionVO.setQuestionOption(JSON.toJSONString(options));
                        examQuestionVO.setExamId(request.getExamId());
                        examQuestionVO.setCreatorId(user.getUserCode());
                        examQuestionVO.setCreationTime(now);
                        //校验有效性
                        String errorMsg = StringUtils.EMPTY;
                        if (examQuestionVO.getSubjectType() == null) {
                            errorMsg = "试题类型有误，";
                        }
                        Integer questionTypeId = examQuestionVO.getQuestionTypeId();
                        if (StringUtils.isBlank(examQuestionVO.getRightOption()) || ((questionTypeId == 1 || questionTypeId == 3) && examQuestionVO.getRightOption().trim().length() > 1)) {
                            errorMsg += "正确答案有误，";
                        }
                        if (questionTypeId == 2) {
                            String[] rightOptions = examQuestionVO.getRightOption().split("\\|");
                            Arrays.sort(rightOptions);
                            examQuestionVO.setRightOption(String.join("|", rightOptions));
                            if (rightOptions.length <= 1 || options.size() < rightOptions.length) {
                                errorMsg += "正确答案有误，";
                            }
                        }
                        if (errorMsg.length() > 0) {
                            errorMsg = "第" + (i + 1) + "题导入失败，" + errorMsg;
                            totalError += errorMsg;
                        }
                        examQuestionVO.setRemark(errorMsg);
                    }
                    if (StringUtils.isNotBlank(totalError)) {
                        throw new CustomException(2003, "导入失败，请检查文件内容:" + totalError.substring(0, totalError.length() - 1));
                    }
                    if (CollectionUtils.isEmpty(examQuestionVOS)) {
                        throw new CustomException(2003, "导入失败，未读取到题目数据");
                    }
                    List<ExamQuestion> examQuestions = DataTransfer.transferList(examQuestionVOS, ExamQuestion.class);
                    if (exam.getGroupRollType() == 3) {
                        LambdaQueryWrapper<ExaminationPaperRule> paperRuleLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        paperRuleLambdaQueryWrapper.eq(ExaminationPaperRule::getExamId, request.getExamId());
                        paperRuleLambdaQueryWrapper.in(ExaminationPaperRule::getQuestionTypeId, questionTypeIds);
                        List<ExaminationPaperRule> examinationPaperRules = examinationPaperRuleService.list(paperRuleLambdaQueryWrapper);
                        if (CollectionUtils.isEmpty(examinationPaperRules)) {
                            throw new CustomException("试卷规则不存在");
                        }
                        Map<Integer, Integer> ruleMap = examinationPaperRules.stream().collect(Collectors.toMap(ExaminationPaperRule::getQuestionTypeId, ExaminationPaperRule::getSorce));
                        for (ExamQuestion examQuestion : examQuestions) {
                            examQuestion.setScore(ruleMap.get(examQuestion.getQuestionTypeId()));
                        }
                    }

                    boolean result = examQuestionService.saveBatch(examQuestions);
                    //更新总分数
                    if (result && exam.getGroupRollType() == 1) {
                        LambdaQueryWrapper<ExamQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                        lambdaQueryWrapper.eq(ExamQuestion::getExamId, request.getExamId());
                        lambdaQueryWrapper.eq(ExamQuestion::getIsValid, 1);
                        List<ExamQuestion> questions = examQuestionService.list(lambdaQueryWrapper);
                        LambdaUpdateWrapper<Exam> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(Exam::getId, request.getExamId());
                        updateWrapper.eq(Exam::getIsValid, 1);
                        updateWrapper.set(Exam::getQuestionCount, questions.size());
                        updateWrapper.set(Exam::getScore, questions.stream().mapToInt(ExamQuestion::getScore).sum());
                        updateWrapper.set(Exam::getRemark, 0);
                        updateWrapper.set(Exam::getReviseId, user.getUserCode());
                        updateWrapper.set(Exam::getReviseTime, now);
                        result = this.update(updateWrapper);
                    }
                    if (!result) {
                        throw new CustomException("操作失败");
                    }
                    return examQuestions.size();
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new CustomException("读取文件失败" + e.getMessage());
                }
            default:
                throw new CustomException("不支持的操作标识: " + request.getFlag());
        }
    }

    @Override
    public String editExamInfoIssueExam(ExamVO request) {
        request.setFlag("edit");
        Long id = this.editExamInfo(request);
        if (id <= 0) {
            throw new CustomException("试卷发布失败");
        }
        Exam exam = getById(request.getId());
        LambdaQueryWrapper<ExamQuestion> examQuestionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        examQuestionLambdaQueryWrapper.eq(ExamQuestion::getExamId, request.getId());
        examQuestionLambdaQueryWrapper.eq(ExamQuestion::getIsValid, 1);
        List<ExamQuestion> examQuestions = examQuestionService.list(examQuestionLambdaQueryWrapper);
        String result = "";
        if (exam.getGroupRollType() == 1) {
            if (exam.getQuestionCount() != examQuestions.size()) {
                result += "题目总数异常|";
            }
            if (exam.getScore() != examQuestions.stream().mapToInt(ExamQuestion::getScore).sum()) {
                result += "题目总分异常|";
            }
            if (exam.getPassMark() > examQuestions.stream().mapToInt(ExamQuestion::getScore).sum()) {
                result += "及格分数异常，及格分数大于试卷总分|";
            }
        } else if (exam.getGroupRollType() == 3) {
            if (exam.getQuestionCount() > examQuestions.size()) {
                result += "题目总数异常|";
            }
            LambdaQueryWrapper<ExaminationPaperRule> examinationPaperRuleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            examinationPaperRuleLambdaQueryWrapper.eq(ExaminationPaperRule::getExamId, request.getId());
            List<ExaminationPaperRule> examinationPaperRules = examinationPaperRuleService.list(examinationPaperRuleLambdaQueryWrapper);

            int questionNum = examinationPaperRules.stream().mapToInt(ExaminationPaperRule::getNum).sum();
            if (questionNum != exam.getQuestionCount()) {
                result += "题目总数异常|";
            } else {
                for (int i = 0; i < examinationPaperRules.size(); i++) {
                    ExaminationPaperRule examinationPaperRule = examinationPaperRules.get(i);
                    if (examinationPaperRule.getNum() > examQuestions.stream().filter(e -> e.getQuestionTypeId().equals(examinationPaperRule.getQuestionTypeId())).count()) {
                        result += examinationPaperRule.getQuestionTypeName() + "题目总数异常|";
                    }
                }
            }
        }
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isEmpty(result)) {
            LambdaUpdateWrapper<Exam> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(Exam::getReviseTime, now);
            updateWrapper.set(Exam::getReviseId, user.getUserCode());
            updateWrapper.set(Exam::getRemark, 1);
            updateWrapper.eq(Exam::getId, request.getId());
            updateWrapper.eq(Exam::getIsValid, 1);
            boolean update = this.update(updateWrapper);
            if (!update) {
                return "发布失败";
            }
        }
        if (StringUtils.isNotEmpty(result)) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }

    @Override
    public List<ExamQuestion> getGroupRoll(GroupRollRequest request) {
        Exam exam = getById(request.getExamId());
        if (null == exam || exam.getIsValid() != 1 || exam.getGroupRollType() == null) {
            throw new CustomException("试卷不存在");
        }
        int bkcs = exam.getResitNumber();//补考次数
        if (request.getUserResitNumber() != null) {
            bkcs = request.getUserResitNumber();
        }
        LambdaQueryWrapper<ExamUserRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamUserRecord::getExamId, request.getExamId());
        queryWrapper.eq(ExamUserRecord::getUserId, request.getUserId());
        queryWrapper.eq(ExamUserRecord::getIsValid, 1);
        int ksjl = examUserRecordMapper.selectCount(queryWrapper);
        if (ksjl > bkcs) {
            throw new CustomException("没有足够的考试次数！");
        }
        List<ExamQuestion> examQuestions = new ArrayList<>();
        if (exam.getGroupRollType() == 1) {
            LambdaQueryWrapper<ExamQuestion> examQuestionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            examQuestionLambdaQueryWrapper.eq(ExamQuestion::getExamId, request.getExamId());
            examQuestionLambdaQueryWrapper.eq(ExamQuestion::getIsValid, 1);
            examQuestionLambdaQueryWrapper.orderByAsc(ExamQuestion::getSort, ExamQuestion::getId);
            examQuestions = examQuestionService.list(examQuestionLambdaQueryWrapper);
        } else if (exam.getGroupRollType() == 3) {
            // 查出该试卷下所有的随机类型信息
            LambdaQueryWrapper<ExaminationPaperRule> examPaperRuleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            examPaperRuleLambdaQueryWrapper.eq(ExaminationPaperRule::getExamId, request.getExamId());
            List<ExaminationPaperRule> examinationPaperRules = examinationPaperRuleService.list(examPaperRuleLambdaQueryWrapper);
            for (int i = 0; i < examinationPaperRules.size(); i++) {
                ExaminationPaperRule examinationPaperRule = examinationPaperRules.get(i);
                Integer questionTypeId = examinationPaperRule.getQuestionTypeId();
                int num = examinationPaperRule.getNum();
                if (num == 0) {
                    continue;
                }
                List<ExamQuestion> examQuestionsByQuestionType = examQuestionService.list(new LambdaQueryWrapper<ExamQuestion>()
                        .eq(ExamQuestion::getExamId, request.getExamId())
                        .eq(ExamQuestion::getQuestionTypeId, questionTypeId)
                        .eq(ExamQuestion::getIsValid, 1));
                // 打乱所有题目的顺序
                List<ExamQuestion> tempList = new ArrayList<>();
                while (tempList.size() < num) {
                    int randomIndex = (int) getRandomIndex(examQuestionsByQuestionType.size());
                    ExamQuestion tempQuestion = examQuestionsByQuestionType.remove(randomIndex);
                    tempList.add(tempQuestion);
                }
                examQuestions.addAll(tempList);
            }
        }
        return examQuestions;
    }

    @Override
    public List<GetUserExamInfoVO> getUserExamInfo(GetUserExamInfoRequest request) {
        return getBaseMapper().selectUserExamInfo(request);
    }

    @Override
    public List<GetUserExamInfoRecordVO> getUserExamInfoRecord(GetUserExamInfoRecordRequest request) {
        switch (request.getFlag()) {
            case 1:
                return examUserRecordMapper.selectUserExamInfoRecord1(request);
            default:
                return examUserRecordMapper.selectUserExamInfoRecord2(request);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editExamSubMitAnswer1(EditExamSubMitAnswer1Request request) {
        int isComplete = -3;
        int shape = 1;
        //正式考试需要查询课程是否已完成，安全培训只有正式考试，没有模拟
        ExamUserRecord record = new ExamUserRecord();
        if (request.getFlag() == 1) {
            UserLessonRecordSafeTrain userLessonInfo = getUserLessonInfo(request.getLessonId(), request.getUserId());
            if (null != userLessonInfo) {
                request.setOrganizationId(userLessonInfo.getCompanyId());
                request.setDepartId(userLessonInfo.getDepartId());
                shape = userLessonInfo.getShape();
                isComplete = userLessonInfo.getIsComplete();
            }
        } else {
            isComplete = 1;
        }
        if (isComplete == 1 || isComplete == 2 || isComplete == 4 || shape == 3) {
            int examScore = -1;
            if (request.getFlag() == 1) {
                GetUserExamInfoRequest getUserExamInfoRequest = new GetUserExamInfoRequest();
                getUserExamInfoRequest.setLessonIds(String.valueOf(request.getLessonId()));
                getUserExamInfoRequest.setUserId(request.getUserId());
                List<GetUserExamInfoVO> getUserExamInfoVOS = getBaseMapper().selectUserExamInfo(getUserExamInfoRequest);
                if (CollectionUtils.isNotEmpty(getUserExamInfoVOS)) {
                    examScore = getUserExamInfoVOS.get(0).getScore();
                }
            }
            if (request.getPassMark() > examScore) {
                AjaxResult ajaxResult = editExamSubMitAnswer(request);
                if ((boolean) ajaxResult.get(AjaxResult.CODE_TAG)) {
                    if (request.getFlag() == 1) {
                        int score = Integer.valueOf((String) ajaxResult.get(AjaxResult.DATA_TAG));
                        if (shape != 3 && request.getPassMark() <= score) {
                            boolean editStatus = editUserLessonRecordStaticStatus(request.getUserId(), request.getPlanId(), request.getLessonId(), score);
                            if (!editStatus) {
                                log.error("试卷内课程状态修改失败：课程id" + request.getLessonId() + "用户code:" + request.getUserId());
                                throw new CustomException(2010, "课程状态修改失败");
                            }
                        } else if (shape == 3) {
                            if (request.getPassMark() <= score) {//考试及格直接修改学习课程状态为1
                                boolean editStatus = editUserLessonRecordCompleteStaticStatus(request.getUserId(), request.getPlanId(), request.getLessonId(), score);
                                if (!editStatus) {
                                    log.error("单考试课程提交答案课程状态修改失败：课程id" + request.getLessonId() + "用户code:" + request.getUserId());
                                    throw new CustomException(2010, "课程状态修改失败");
                                }
                            } else if (isComplete == -3) {//不及格，状态为未开始时，修改学习状态为1，总的课程状态为0
                                boolean editStatus = editUserRecordStaticStatus(request.getUserId(), request.getPlanId(), request.getLessonId(), score);
                                if (!editStatus) {
                                    log.error("单考试课程提交答案修改课程状态为学习中失败：课程id" + request.getLessonId() + "用户code:" + request.getUserId());
                                    throw new CustomException(2010, "课程状态修改失败");
                                }
                            }
                        }
                    }
                }
            } else {
                if (request.getFlag() == 1) {
                    if (shape != 3) {
                        boolean editStatus = editUserLessonRecordStaticStatus(request.getUserId(), request.getPlanId(), request.getLessonId(), examScore);
                        if (!editStatus) {
                            log.error("试卷内课程状态修改失败：课程id" + request.getLessonId() + "用户code:" + request.getUserId());
                            throw new CustomException(2010, "课程状态修改失败");
                        }
                    } else {
                        boolean editStatus = editUserLessonRecordCompleteStaticStatus(request.getUserId(), request.getPlanId(), request.getLessonId(), examScore);
                        if (!editStatus) {
                            log.error("单考试课程提交答案课程状态修改失败：课程id" + request.getLessonId() + "用户code:" + request.getUserId());
                            throw new CustomException(2010, "课程状态修改失败");
                        }
                    }
                }
            }
        } else {
            throw new CustomException("课程未学习完，无法提交答案");
        }
        return true;
    }

    private boolean editUserLessonRecordCompleteStaticStatus(String userId, Long planId, Long lessonId, int score) {
        String now = DateUtils.getCurrentTime();
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCode(userId);
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(lessonId);
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrains)) {
            UserLessonRecordSafeTrain userLesson = userLessonRecordSafeTrains.get(0);
            int isComplete = 1;
            if (userLesson.getIsComplete() == 0 && (userLesson.getTrainEndTime() + " 23:59:59").compareTo(now) < 0) {
                isComplete = 2;
            } else if (userLesson.getIsComplete() == 3) {
                isComplete = 4;
            }
            if (userLesson.getIsComplete() == -3) {
                userLesson.setCompleteTime(now);
                userLesson.setStartStudyTime(now);
            }
            userLesson.setIsComplete(isComplete);
            userLesson.setStaticStatus(isComplete);
            userLesson.setScore(score);
            userLesson.setReservedField4(now);
            return userLessonRecordSafeTrainMongoService.updateUserLessonById(userLesson);
        }
        return false;
    }

    private boolean editUserLessonRecordStaticStatus(String userId, Long planId, Long lessonId, int score) {
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getCompleteUserLessonRecord(userId, lessonId);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrains)) {
            UserLessonRecordSafeTrain userLesson = userLessonRecordSafeTrains.get(0);
            int isComplete = 1;
            String now = DateUtils.getCurrentTime();
            if (userLesson.getIsComplete() == 1 && (userLesson.getTrainEndTime() + " 23:59:59").compareTo(now) < 0) {
                isComplete = 2;
            } else {
                isComplete = userLesson.getIsComplete();
            }
            userLesson.setStaticStatus(isComplete);
            userLesson.setScore(score);
            userLesson.setReservedField4(now);
            return userLessonRecordSafeTrainMongoService.updateUserLessonById(userLesson);
        }
        return false;
    }

    private boolean editUserRecordStaticStatus(String userId, Long planId, Long lessonId, int score) {
        String now = DateUtils.getCurrentTime();
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCode(userId);
        queryRequest.setIsValid(1);
        queryRequest.setIsComplete(-3);
        queryRequest.setLessonId(lessonId);
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrains)) {
            UserLessonRecordSafeTrain userLesson = userLessonRecordSafeTrains.get(0);
            int isComplete = 0;
            if ((userLesson.getTrainEndTime() + " 23:59:59").compareTo(now) < 0) {
                isComplete = 3;
            }
            userLesson.setIsComplete(1);
            userLesson.setStaticStatus(isComplete);
            userLesson.setCompleteTime(now);
            userLesson.setStartStudyTime(now);
            return userLessonRecordSafeTrainMongoService.updateUserLessonById(userLesson);
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    public AjaxResult editExamSubMitAnswer(EditExamSubMitAnswer1Request request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        List<AnswerSubmitRequest> answers = request.getAnswers();
        if (CollectionUtils.isNotEmpty(answers)) {
            int totalScore = 0;
            for (AnswerSubmitRequest answer : answers) {
                if (answer.getSubOption().equals(answer.getRightOption())) {
                    totalScore += answer.getScore();
                }
            }
            if (request.getScore() != totalScore) {
                request.setScore(totalScore);
            }
            //执行存储过程
            Exam exam = getById(request.getExamId());
            //已考次数
            LambdaQueryWrapper<ExamUserRecord> examUserRecordLambdaQueryWrapper = Wrappers.lambdaQuery(ExamUserRecord.class).eq(ExamUserRecord::getExamId, request.getExamId()).eq(ExamUserRecord::getUserId, request.getUserId()).eq(ExamUserRecord::getIsValid, 1);
            int examCount = examUserRecordMapper.selectCount(examUserRecordLambdaQueryWrapper);
            int resitNumber = exam.getResitNumber();
            if (null != request.getUserResitNumber()) {
                resitNumber = request.getUserResitNumber();
            }
            Long recordId = 0L;
            if (examCount <= resitNumber) {
                resitNumber = resitNumber - examCount;
                if (request.getFlag() == 1) {
                    ExamUserRecord record = (ExamUserRecord) DataTransfer.transfer(request, ExamUserRecord.class);
                    record.setPassMark(exam.getPassMark());
                    record.setTotalScore(exam.getScore());
                    record.setResitNumber(resitNumber);
                    record.setUserExamCount(exam.getQuestionCount());
                    record.setSource(exam.getSource());
                    record.setCreationTime(LocalDateTime.now());
                    examUserRecordMapper.insert(record);
                    recordId = record.getId();
                } else {
                    ExamUserRecordSimulation record = (ExamUserRecordSimulation) DataTransfer.transfer(request, ExamUserRecordSimulation.class);
                    record.setPassMark(exam.getPassMark());
                    record.setTotalScore(exam.getScore());
                    record.setResitNumber(resitNumber);
                    record.setUserExamCount(exam.getQuestionCount());
                    record.setSource(exam.getSource());
                    record.setCreationTime(LocalDateTime.now());
                    examUserRecordSimulationMapper.insert(record);
                    recordId = record.getId();
                }
                if (recordId != 0) {
                    if (request.getFlag() == 1) {
                        List<ExamAnswerRecord> answerRecords = DataTransfer.transferList(answers, ExamAnswerRecord.class);
                        for (ExamAnswerRecord answerRecord : answerRecords) {
                            answerRecord.setRecordId(recordId);
                            answerRecord.setCreationTime(LocalDateTime.now());
                            answerRecord.setCreatorId(user.getUserCode());
                            answerRecord.setReviseTime(LocalDateTime.now());
                            answerRecord.setReviseId(user.getUserCode());
                        }
                        boolean saved = examAnswerRecordService.saveBatch(answerRecords);
                        if (!saved) {
                            throw new CustomException("操作失败");
                        }
                    } else {
                        List<ExamAnswerRecordSimulation> answerRecords = DataTransfer.transferList(answers, ExamAnswerRecordSimulation.class);
                        for (ExamAnswerRecordSimulation answerRecord : answerRecords) {
                            answerRecord.setRecordId(recordId);
                            answerRecord.setCreationTime(LocalDateTime.now());
                            answerRecord.setCreatorId(user.getUserCode());
                            answerRecord.setReviseTime(LocalDateTime.now());
                            answerRecord.setReviseId(user.getUserCode());
                        }
                        boolean saved = examAnswerRecordSimulationService.saveBatch(answerRecords);
                        if (!saved) {
                            throw new CustomException("操作失败");
                        }
                    }
                }
            } else {
                throw new CustomException(1002, "考试次数不足");
            }
        }
        return AjaxResult.success(request.getScore());
    }

    private UserLessonRecordSafeTrain getUserLessonInfo(Long lessonId, String userCode) {
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserCode(userCode);
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(lessonId);
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrains)) {
            return userLessonRecordSafeTrains.get(0);
        }
        return null;
    }

    @Override
    public boolean authPortraitVerification(AuthPortraitVerificationRequest request) {
        return FaceUtil.detectLivingFace(request.getComparisonPhotos()) && FaceUtil.compareFace(request.getComparisonPhotos(), request.getOriginalPhotos());
    }

    @Override
    public List<GetUserExamDetailRecordVO> getUserExamDetailRecord(GetUserExamDetailRecordRequest request) {
        switch (request.getFlag()) {
            case 1:
                return examUserRecordMapper.selectUserExamDetailRecord1(request);
            default:
                return examUserRecordMapper.selectUserExamDetailRecord2(request);
        }
    }

    @Override
    public List<ExamFaceRecordVO> getExamFaceRecord(GetCompanySafeTrainFaceRecordRequest request) {
        return getBaseMapper().selectExamFaceRecord(request);
    }

    @Override
    public PageUtils getExamInfoQuestionByPage(ExamRequest request, PageEntity pageEntity) {
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        return new PageUtils(pageEntity, getBaseMapper().selectExamQuestion(request));
    }

    private static void addOption(String choice, List<QuestionOption> options) {
        if (StringUtils.isNotEmpty(choice)) {
            char answerKey = (char) ('A' + options.size());
            QuestionOption option = new QuestionOption();
            option.setAnswer(answerKey + "");
            option.setTitle(choice);
            options.add(option);
        }
    }

    public byte[] downloadExcelFile(String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM));
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.GET, entity, byte[].class);
        return response.getBody();
    }

    public List<ExamQuestionVO> readExcelFile(byte[] fileBytes) throws Exception {
        InputStream inputStream = new ByteArrayInputStream(fileBytes);
        ExcelUtil<ExamQuestionVO> util = new ExcelUtil<ExamQuestionVO>(ExamQuestionVO.class);
        List<ExamQuestionVO> examQuestionVOS = util.importExcel(inputStream);
        return examQuestionVOS;
    }

    private long getRandomIndex(int size) {
        return Math.round(Math.random() * (size - 1));
    }
}