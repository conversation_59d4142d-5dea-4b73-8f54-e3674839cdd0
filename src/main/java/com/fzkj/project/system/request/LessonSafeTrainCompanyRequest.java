package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 给企业分发的安全培训课程关系表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "给企业分发的安全培训课程关系表Request")
public class LessonSafeTrainCompanyRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private Long companyId;

    @ApiModelProperty(value = "")
    private String companyName;

    @ApiModelProperty(value = "")
    private Long lessonId;

    @ApiModelProperty(value = "")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "")
    private String lessonCategoryName;

    @ApiModelProperty(value = "")
    private Integer handMode;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "")
    private String creatorCode;

    @ApiModelProperty(value = "")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "")
    private String reviseCode;

    @ApiModelProperty(value = "")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty(value = "")
    private Integer isValid;

}