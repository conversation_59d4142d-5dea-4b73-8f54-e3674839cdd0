package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 企业表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "企业表Request")
public class CompanyRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @JsonProperty("FID")
    private Long fid;

    @ApiModelProperty(value = "")
    @JsonProperty("ParentID")
    private Long parentId;

    @ApiModelProperty(value = "")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "")
    @JsonProperty("AreaCode")
    private String areaCode;

    @ApiModelProperty(value = "")
    @JsonProperty("AreaName")
    private String areaName;

    @ApiModelProperty(value = "")
    @JsonProperty("AbbrName")
    private String abbrName;

    @ApiModelProperty(value = "")
    @JsonProperty("Address")
    private String address;

    @ApiModelProperty(value = "")
    @JsonProperty("Email")
    private String email;

    @ApiModelProperty(value = "")
    @JsonProperty("CompanyLabel")
    private String companyLabel;

    @ApiModelProperty(value = "")
    @JsonProperty("OperPlatform")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @JsonProperty("IsEnable")
    private Integer isValid;

    @ApiModelProperty(value = "")
    @JsonProperty("Flag")
    private String flag;

    @ApiModelProperty(value = "")
    @JsonProperty("TargetID")
    private Long targetId;

    @ApiModelProperty(value = "")
    @JsonProperty("IsSelf")
    private Long isSelf;

}