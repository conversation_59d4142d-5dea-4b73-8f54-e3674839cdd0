package com.fzkj.project.system.queue;

import com.alibaba.fastjson2.JSON;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.project.system.entity.PushCenterMsg;
import com.fzkj.project.system.entity.WxPushTab;
import com.fzkj.project.system.service.PushCenterMsgService;
import com.fzkj.project.system.service.WxPushTabService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 消息推送状态更新队列
 */
@Component
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class WxPushTabStateHandler implements MessageListener {
    private final WxPushTabService wxPushTabService;
    private final PushCenterMsgService pushCenterMsgService;
    private final RedisCache redisCache;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(Message message, byte[] pattern) {
        System.out.println("Received message for wxPushTabState: " + message.toString());
        WxPushTab wxPushTab = JSON.parseObject(new String(message.getBody()), WxPushTab.class);
        if (null == wxPushTab) {
            return;
        }
        wxPushTabService.updateById(wxPushTab);
        pushCenterMsgService.lambdaUpdate().eq(PushCenterMsg::getTargetId, wxPushTab.getLessonId()).eq(PushCenterMsg::getUserCode, wxPushTab.getUserCode()).set(PushCenterMsg::getPushState, wxPushTab.getIsWxSend()).update();
        redisCache.removeElementFromList("distribute_ids", wxPushTab.getId());
    }
}
