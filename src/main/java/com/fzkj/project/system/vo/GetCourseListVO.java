package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetCourseListVO {

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课件ID")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty(value = "课件名称")
    @JsonProperty("CourseName")
    private String courseName;

    @ApiModelProperty(value = "课件时长")
    @JsonProperty("TimeCount")
    private Integer timeCount;

    @ApiModelProperty(value = "课件类型")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(value = "课程文件URL")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "课件排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "课件是否完成")
    @JsonProperty("IsComplete")
    private String isComplete;
}
