package com.fzkj.api.entity.vo;

import com.fzkj.framework.aspectj.lang.annotation.Excel;
import com.fzkj.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 安全培训数据对象 training_yes_course
 *
 * <AUTHOR>
 * @date 2022-10-19
 */
public class TrainingYesCourse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idno;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 岗位 */
    @Excel(name = "岗位")
    private String job;

    /** 学员照片 */
    @Excel(name = "学员照片")
    private String studentImg;

    /** 学习照片 */
    @Excel(name = "学习照片")
    private String learnImg;

    /** 学习月份 */
    @Excel(name = "学习月份")
    private String month;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 课程状态 */
    @Excel(name = "课程状态")
    private Integer courseStat;

    /** 学习状态 */
    @Excel(name = "学习状态")
    private Integer learnStat;

    /** 学习完成时间 */
    @Excel(name = "学习完成时间")
    private String compeleteTime;

    /** 学习时长 */
    @Excel(name = "学习时长")
    private Long duration;

    /** 考试状态 */
    @Excel(name = "考试状态")
    private Integer examineStat;

    /** 考试成绩 */
    @Excel(name = "考试成绩")
    private Long score;

    /** 考试完成时间 */
    @Excel(name = "考试完成时间")
    private String testTime;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String compName;

    /** 公司编码 */
    @Excel(name = "公司编码")
    private String compCode;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setIdno(String idno)
    {
        this.idno = idno;
    }

    public String getIdno()
    {
        return idno;
    }
    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getPhone()
    {
        return phone;
    }
    public void setJob(String job)
    {
        this.job = job;
    }

    public String getJob()
    {
        return job;
    }
    public void setStudentImg(String studentImg)
    {
        this.studentImg = studentImg;
    }

    public String getStudentImg()
    {
        return studentImg;
    }
    public void setLearnImg(String learnImg)
    {
        this.learnImg = learnImg;
    }

    public String getLearnImg()
    {
        return learnImg;
    }
    public void setMonth(String month)
    {
        this.month = month;
    }

    public String getMonth()
    {
        return month;
    }
    public void setCourseName(String courseName)
    {
        this.courseName = courseName;
    }

    public String getCourseName()
    {
        return courseName;
    }
    public void setCourseStat(Integer courseStat)
    {
        this.courseStat = courseStat;
    }

    public Integer getCourseStat()
    {
        return courseStat;
    }
    public void setLearnStat(Integer learnStat)
    {
        this.learnStat = learnStat;
    }

    public Integer getLearnStat()
    {
        return learnStat;
    }
    public void setCompeleteTime(String compeleteTime)
    {
        this.compeleteTime = compeleteTime;
    }

    public String getCompeleteTime()
    {
        return compeleteTime;
    }
    public void setDuration(Long duration)
    {
        this.duration = duration;
    }

    public Long getDuration()
    {
        return duration;
    }
    public void setExamineStat(Integer examineStat)
    {
        this.examineStat = examineStat;
    }

    public Integer getExamineStat()
    {
        return examineStat;
    }
    public void setScore(Long score)
    {
        this.score = score;
    }

    public Long getScore()
    {
        return score;
    }
    public void setTestTime(String testTime)
    {
        this.testTime = testTime;
    }

    public String getTestTime()
    {
        return testTime;
    }
    public void setCompName(String compName)
    {
        this.compName = compName;
    }

    public String getCompName()
    {
        return compName;
    }
    public void setCompCode(String compCode)
    {
        this.compCode = compCode;
    }

    public String getCompCode()
    {
        return compCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("idno", getIdno())
            .append("phone", getPhone())
            .append("job", getJob())
            .append("studentImg", getStudentImg())
            .append("learnImg", getLearnImg())
            .append("month", getMonth())
            .append("courseName", getCourseName())
            .append("courseStat", getCourseStat())
            .append("learnStat", getLearnStat())
            .append("compeleteTime", getCompeleteTime())
            .append("duration", getDuration())
            .append("examineStat", getExamineStat())
            .append("score", getScore())
            .append("testTime", getTestTime())
            .append("compName", getCompName())
            .append("compCode", getCompCode())
            .append("createTime", getCreateTime())
            .toString();
    }
}
