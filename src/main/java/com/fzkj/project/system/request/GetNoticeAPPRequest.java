package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetNoticeAPPRequest {

    @ApiModelProperty(value = "平台", example = "4")
    @JsonProperty("Platform")
    private Integer platform;

    @ApiModelProperty(value = "组织ID", example = "49")
    @JsonProperty("OrganizationID")
    private Integer organizationId;

    @ApiModelProperty(value = "组织类型", example = "2")
    @JsonProperty("OrgType")
    private Integer orgType;
}
