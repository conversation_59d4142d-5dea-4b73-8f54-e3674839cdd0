package com.fzkj.project.olddata.service;

import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.olddata.dto.SixTeenCompanyEntity;
import com.fzkj.project.olddata.dto.SixTeenLessonEntity;
import com.fzkj.project.olddata.dto.request.NineteenFromTwentyOneRequest;
import com.fzkj.project.olddata.dto.request.SixteenFromEighteenOneRequest;

import java.util.List;

public interface SixteenFromEighteenService {
    List<SixTeenCompanyEntity> getSixteenOrgList(SixteenFromEighteenOneRequest request);

    List<SixTeenLessonEntity> getSixteenLesson(SixteenFromEighteenOneRequest request);

    PageUtils getSixteenLessonUser(SixteenFromEighteenOneRequest request, PageEntity pageEntity);
}
