package com.fzkj.project.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fzkj.common.constant.Constants;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.PlatformMenuService;
import com.fzkj.project.system.vo.PlatformMenuVO;
import com.fzkj.project.system.request.PlatformMenuRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 平台菜单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userPort")
public class PlatformMenuController extends BaseEntity {

    private final PlatformMenuService PlatformMenuServiceImpl;

    @PostMapping("list-all")
    @ApiOperation(value = "平台菜单列表查询-不带权限")
    public AjaxResult listPlatformMenuAll(@RequestBody CommonRequest commonRequest) {
        Integer request = (Integer) commonRequest.getRequest(Integer.class);
        return AjaxResult.success(JSONObject.toJSONString(PlatformMenuServiceImpl.listPlatformMenuAll(request), SerializerFeature.WriteMapNullValue));
    }

    @PostMapping("GetMenuList")
    @ApiOperation(value = "平台菜单列表查询-带权限")
    public AjaxResult listPlatformMenu(@RequestBody CommonRequest commonRequest) {
        Integer request = (Integer) commonRequest.getRequest(Integer.class);
        return AjaxResult.success(JSONObject.toJSONString(PlatformMenuServiceImpl.listPlatformMenu(request), SerializerFeature.WriteMapNullValue));
    }

    @PostMapping("GetRoleMenu")
    @ApiOperation(value = "平台菜单列表查询-角色权限部分")
    public AjaxResult GetRoleMenu(@RequestBody CommonRequest commonRequest) {
        Integer request = (Integer) commonRequest.getRequest(Integer.class);
        return AjaxResult.success(JSONObject.toJSONString(PlatformMenuServiceImpl.listPlatformMenu(request), SerializerFeature.WriteMapNullValue));
    }

    @PostMapping("EditMenu")
    @ApiOperation(value = "edit公共方法")
    public AjaxResult savePlatformMenu(@RequestBody CommonRequest commonRequest) {
        PlatformMenuVO vo = (PlatformMenuVO) commonRequest.getRequest(PlatformMenuVO.class);
        if(vo.getFlag().equals(Constants.ADD_DATA)){
            return AjaxResult.success(PlatformMenuServiceImpl.savePlatformMenu(vo));
        }else if(vo.getFlag().equals(Constants.EDIT_DATA)){
            return AjaxResult.success(PlatformMenuServiceImpl.updatePlatformMenu(vo));
        }else if(vo.getFlag().equals(Constants.DEL_DATA)){
            Boolean isSuccess = PlatformMenuServiceImpl.removePlatformMenu(vo.getId());
            if(isSuccess == null){
                return AjaxResult.error("存在子级菜单不可删除！");
            }else{
                return AjaxResult.success(isSuccess.booleanValue());
            }
        }else{
            return AjaxResult.error("类型异常");
        }
    }

}
