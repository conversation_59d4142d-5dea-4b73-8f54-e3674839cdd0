package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 安全培训课程
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "安全培训课程查看已分发学员Request")
public class LessonSafeTrainQueryUserRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业设置ID")
    @JsonProperty("CompanyLessonPayID")
    private Long companyLessonPayId;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "部门ID")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "部门IDs")
    @JsonProperty("DepartIDs")
    private List<Long> departIds;

    @ApiModelProperty(value = "岗位ID")
    @JsonProperty("WorkID")
    private Long workId;

    @ApiModelProperty(value = "关键字")
    @JsonProperty("KeyWords")
    private String keyWords;

    @ApiModelProperty(value = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "统计分组")
    @JsonProperty("StaticStatus")
    private Integer staticStatus;

    @ApiModelProperty(value = "开始日期")
    @JsonProperty("StartDate")
    private String startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonProperty("EndDate")
    private String endDate;

}

