package com.fzkj.project.monitor.controller;

import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.aspectj.lang.enums.BusinessType;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.monitor.domain.SysOperLog;
import com.fzkj.project.monitor.service.ISysOperLogService;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 操作日志记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/operlog")
public class SysOperlogController {
    @Autowired
    private ISysOperLogService operLogService;

    @PostMapping("/list")
    public AjaxResult list(@RequestBody CommonRequest request) {
        SysOperLog operLog = (SysOperLog) request.getRequest(SysOperLog.class);
        PageHelper.startPage(request.getPageIndex(), request.getPageSize());
        List list = operLogService.selectOperLogList(operLog);
        return AjaxResult.success(new PageUtils(request.getPageEntity(), list));
    }

    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(@RequestBody CommonRequest request) {
        SysOperLog operLog = (SysOperLog) request.getRequest(SysOperLog.class);
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        ExcelUtil<SysOperLog> util = new ExcelUtil<SysOperLog>(SysOperLog.class);
        return util.exportExcel(list, "操作日志");
    }
}
