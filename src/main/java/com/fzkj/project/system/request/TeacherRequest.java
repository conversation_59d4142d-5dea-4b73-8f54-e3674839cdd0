package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 讲师
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "讲师Request")
public class TeacherRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private String photo;

    @ApiModelProperty(value = "")
    private String realName;

    @ApiModelProperty(value = "")
    private String phone;

    @ApiModelProperty(value = "")
    private String education;

    @ApiModelProperty(value = "")
    private String graduationSchool;

    @ApiModelProperty(value = "")
    private String beGoodAt;

    @ApiModelProperty(value = "")
    private String unit;

    @ApiModelProperty(value = "")
    private String workPost;

    @ApiModelProperty(value = "")
    private String technicalTitle;

    @ApiModelProperty(value = "")
    private String personIntroduct;

    @ApiModelProperty(value = "")
    private String loginAccount;

    @ApiModelProperty(value = "")
    private String loginPwd;

    @ApiModelProperty(value = "")
    private String loginPwdMd5;

    @ApiModelProperty(value = "")
    private Integer auditStatus;

    @ApiModelProperty(value = "")
    private String auditCode;

    @ApiModelProperty(value = "")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "")
    private String auditReason;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "")
    private String creatorCode;

    @ApiModelProperty(value = "")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "")
    private String reviseCode;

    @ApiModelProperty(value = "")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty(value = "")
    private Integer recycleBin;

    @ApiModelProperty(value = "")
    private Integer isValid;

    @ApiModelProperty(value = "")
    private Integer isShow;

}