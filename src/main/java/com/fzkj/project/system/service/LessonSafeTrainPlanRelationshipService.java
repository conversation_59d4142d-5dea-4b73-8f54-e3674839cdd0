package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.LessonSafeTrainPlanRelationship;
import com.fzkj.project.system.vo.LessonSafeTrainPlanRelationshipVO;
import com.fzkj.project.system.request.LessonSafeTrainPlanRelationshipRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 给公交分发的培训课程关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface LessonSafeTrainPlanRelationshipService extends IService<LessonSafeTrainPlanRelationship> {

    List<LessonSafeTrainPlanRelationship> listLessonSafeTrainPlanRelationship(LessonSafeTrainPlanRelationshipRequest request);

    PageUtils listByPage(LessonSafeTrainPlanRelationshipRequest request, PageEntity pageEntity);

    LessonSafeTrainPlanRelationshipVO findLessonSafeTrainPlanRelationship(Long id);

    Long saveLessonSafeTrainPlanRelationship(LessonSafeTrainPlanRelationshipVO vo);

    boolean updateLessonSafeTrainPlanRelationship(LessonSafeTrainPlanRelationshipVO vo);

    boolean removeLessonSafeTrainPlanRelationship(Long id);

    boolean editLessonSafeTrainPlanRelationship(LessonSafeTrainPlanRelationshipVO request);
}
