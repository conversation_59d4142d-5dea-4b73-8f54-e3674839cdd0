package com.fzkj.project.system.service;

import com.fzkj.framework.security.LoginUser;
import com.fzkj.project.system.entity.UserCompany;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.vo.UserCompanyVO;
import com.fzkj.project.system.request.UserCompanyRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface UserCompanyService extends IService<UserCompany> {

    List<UserCompany> listUserCompany(UserCompanyRequest request);

    PageUtils listByPage(UserCompanyRequest request, PageEntity pageEntity);

    UserCompanyVO findUserCompany(Long id);

    UserCompany selectUserCompany(UserInfo loginUser);

    Long saveUserCompany(UserCompanyVO vo);

    boolean updateUserCompany(UserCompanyVO vo);

    boolean removeUserCompany(Long id);
}
