package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 安全培训课程
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "安全培训课程VO")
public class LessonSafeTrainVO implements Serializable {
    //月份	课程名称	课程分类	学时	开始日期	结束日期	课程来源	总人数	参学人数	完成人数	参学率	完成率	创建时间	课程单价（元）

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LessonDate")
    @Excel(name = "月份", order = 1)
    private String lessonDate;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LessonCategoryName")
    @Excel(name = "课程分类", order = 3)
    private String lessonCategoryName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LessonName")
    @Excel(name = "课程名称", order = 2)
    private String lessonName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Introduce")
    private String introduce;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LessonSummary")
    private String lessonSummary;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LessonPic")
    private String lessonPic;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("StudySignState")
    private Integer studySignState;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("StudyFaceState")
    private Integer studyFaceState;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("StudyCountDownTime")
    private Integer studyCountDownTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("StudyFailRepeatNum")
    private Integer studyFailRepeatNum;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CoursFaceNum")
    private Integer coursFaceNum;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("STime")
    @Excel(name = "开始日期", order = 5)
    private String sTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ETime")
    @Excel(name = "结束日期", order = 6)
    private String eTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Shape")
    private Integer shape;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("TotalTimeCount")
    @Excel(name = "学时", order = 4, suffix = "学时")
    private Integer totalTimeCount;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("BelongPlat")
    private Integer belongPlat;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreationTime")
    @Excel(name = "创建时间", order = 13, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReservedField5")
    private String reservedField5;

    @ApiModelProperty(value = "企业数量")
    @JsonProperty("CompanyNum")
    private int companyNum;

    @ApiModelProperty(value = "操作标识")
    @JsonProperty("Flag")
    private String flag;

    @ApiModelProperty(value = "课程IDs")
    @JsonProperty("LessonIDs")
    private String lessonIDs;

    @ApiModelProperty(value = "分发企业Ids")
    @JsonProperty("CompanyIDs")
    private String companyIds;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "分发总人数")
    @JsonProperty("TotalUserNum")
    @Excel(name = "总人数", order = 8)
    private int totalUserNum;

    @ApiModelProperty(value = "参学人数")
    @JsonProperty("CXUserNum")
    @Excel(name = "参学人数", order = 9)
    private int cxUserNum;

    @ApiModelProperty(value = "完成人数")
    @JsonProperty("WCUserNum")
    @Excel(name = "完成人数", order = 10)
    private int wcUserNum;

    @ApiModelProperty(value = "参学率")
    @JsonProperty("CXL")
    @Excel(name = "参学率", order = 11)
    private String cxl = "0%";

    @ApiModelProperty(value = "完成率")
    @JsonProperty("WCL")
    @Excel(name = "完成率", order = 12)
    private String wcl = "0%";

    @ApiModelProperty(value = "参学人员数据")
    @JsonProperty("CXUserNumStr")
    private String cxUserNumStr = "0(0%)";

    @ApiModelProperty(value = "完成人员数据")
    @JsonProperty("WCUserNumStr")
    private String wcUserNumStr = "0(0%)";

    @ApiModelProperty(value = "未完成人员数据")
    @JsonProperty("NoCompleteUserNumStr")
    private String noCompleteUserNumStr = "0(0%)";

    @ApiModelProperty(value = "台账文件名称")
    @JsonProperty("LedgerFileName")
    private String ledgerFileName;

    @ApiModelProperty(value = "档案文件名称")
    @JsonProperty("FileName")
    private String fileName;

    @ApiModelProperty(value = "档案文件下载地址")
    @JsonProperty("FilesUrl")
    private String filesUrl;

    @ApiModelProperty(value = "台账文件下载地址")
    @JsonProperty("LedgerUrl")
    private String ledgerUrl;

    @ApiModelProperty(value = "学时数据")
    @JsonProperty("TotalTimeCountStr")
    private String totalTimeCountStr;

    @ApiModelProperty(value = "所属平台数据")
    @JsonProperty("BelongPlatStr")
    @Excel(name = "课程来源", order = 7)
    private String belongPlatStr;

    @ApiModelProperty(value = "人脸识别照片下载地址")
    @JsonProperty("FaceLederUrl")
    private String faceLederUrl;

    @ApiModelProperty(value = "人脸识别照片文件名称")
    @JsonProperty("FaceLederFileName")
    private String faceLederFileName;

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "分发人数")
    @JsonProperty("RecordNum")
    private int recordNum;
}