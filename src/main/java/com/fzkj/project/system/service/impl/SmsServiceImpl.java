package com.fzkj.project.system.service.impl;

import com.fzkj.common.utils.VerifyCodeUtils;
import com.fzkj.common.utils.aliyun.SmsUtil;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.project.system.service.ISmsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class SmsServiceImpl implements ISmsService {
    private final RedisCache redisCache;

    @Override
    public String sendVerificationCode(String mobile, String smsType) {
        // 生成6位随机验证码
        String verificationCode = VerifyCodeUtils.generateVerifyCode(6);
        // 调用短信接口发送短信
        boolean isSMSSent = SmsUtil.sendSMS(mobile, verificationCode);
        if (isSMSSent) {
            // 将验证码缓存至redis，key为phone+smsType，value为验证码，有效期为5分钟
            String key = mobile + "_" + smsType;
            String value = verificationCode;
            redisCache.setCacheObject(key, value, 5 * 60, TimeUnit.SECONDS);
            // 返回验证码
            return verificationCode;
        }
        return null; // 发送短信失败
    }

    @Override
    public boolean verify(String mobile, String smsType, String verificationCode) {
        String code = redisCache.getCacheObject(mobile + "_" + smsType);
        if (null != code && code.equals(verificationCode)) {
            return true;
        }
        return false;
    }

    @Override
    public void removeVerificationCode(String mobile, String smsType) {
        redisCache.deleteObject(mobile + "_" + smsType);
    }

}
