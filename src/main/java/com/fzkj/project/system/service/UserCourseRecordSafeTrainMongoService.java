package com.fzkj.project.system.service;

import com.fzkj.project.system.mongo.UserCourseRecordSafeTrain;
import com.fzkj.project.system.request.EditCompleteCourseRequest;
import com.fzkj.project.system.request.EditUserLessonRecordSignRequest;
import com.fzkj.project.system.request.GetCompanySafeTrainFaceRecordRequest;
import com.fzkj.project.system.request.GetUserCourseDetailRequest;
import com.fzkj.project.system.vo.GetUserCourseDetailVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 安全培训课件分发学员学习记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface UserCourseRecordSafeTrainMongoService{

    void delUserCourse(Long lessonId, List<String> userCodes);
    void delUserCourses(String userCode);
    List<UserCourseRecordSafeTrain> getUserCourse(UserCourseRecordSafeTrain request);
    int completeUserCourse(EditCompleteCourseRequest request);
    long selectCompleteNum(EditCompleteCourseRequest request);
    int selectLearnNum(Long courseId);
    boolean updateUserCourse(UserCourseRecordSafeTrain userCourseRecordSafeTrain);
    void recoveryUserCourse(Long lessonId, String userCodes);
    void save(UserCourseRecordSafeTrain courseRecordSafeTrain);
    List<UserCourseRecordSafeTrain> getUserCourseFaceMap(GetCompanySafeTrainFaceRecordRequest queryRequest2);
    long countCourse(EditUserLessonRecordSignRequest request);
    void updateCourseRecord(Long companyId, Long departId, String companyName, String userCode);
    long getLearnNum(List<Long> id);
}
