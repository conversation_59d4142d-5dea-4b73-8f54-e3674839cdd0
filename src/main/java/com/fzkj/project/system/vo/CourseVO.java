package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 课件信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="课件信息表VO")
public class CourseVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "企业ID")
    @NotNull(message = "企业ID不能为空")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "课件名称")
    @NotNull(message = "课件名称不能为空")
    @JsonProperty("CourseName")
    private String courseName;

    @ApiModelProperty(value = "课件分类ID")
    @NotNull(message = "课件分类ID不能为空")
    @JsonProperty("CategoryID")
    private Integer categoryId;

    @ApiModelProperty(value = "分类名称")
    @NotNull(message = "分类名称不能为空")
    @JsonProperty("CategoryName")
    private String categoryName;

    @ApiModelProperty(value = "讲师编号")
    @NotNull(message = "讲师编号不能为空")
    @JsonProperty("TearcherCode")
    private String tearcherCode;

    @ApiModelProperty(value = "讲师名称")
    @NotNull(message = "讲师名称不能为空")
    @JsonProperty("TearcherName")
    private String tearcherName;

    @ApiModelProperty(value = "课件介绍")
    @NotNull(message = "课件介绍不能为空")
    @JsonProperty("Summary")
    private String summary;

    @ApiModelProperty(value = "课件内容")
    @NotNull(message = "课件内容不能为空")
    @JsonProperty("Content")
    private String content;

    @ApiModelProperty(value = "音频内容 0:输入音频内容(默认) 1:上传音频内容")
    @NotNull(message = "音频内容 0:输入音频内容(默认) 1:上传音频内容不能为空")
    @JsonProperty("AudioType")
    private Integer audioType;

    @ApiModelProperty(value = "视频内容 （上传音频内容 当audio_type=1时 改字段写入值）")
    @NotNull(message = "视频内容 （上传音频内容 当audio_type=1时 改字段写入值）不能为空")
    @JsonProperty("VideoContent")
    private String videoContent;

    @ApiModelProperty(value = "课件封面图")
    @NotNull(message = "课件封面图不能为空")
    @JsonProperty("ImgUrl")
    private String imgUrl;

    @ApiModelProperty(value = "课件URL")
    @NotNull(message = "课件URL不能为空")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "课件大小")
    @NotNull(message = "课件大小不能为空")
    @JsonProperty("FileSize")
    private Long fileSize;

    @ApiModelProperty(value = "课件时长")
    @NotNull(message = "课件时长不能为空")
    @JsonProperty("TimeCount")
    private Long timeCount;

    @ApiModelProperty(value = "课件类型（1：视频，2：文件，3：图文，4：音频）")
    @NotNull(message = "课件类型（1：视频，2：文件，3：图文，4：音频）不能为空")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(value = "课件类型（1：视频，2：文件，3：图文，4：音频）")
    @NotNull(message = "课件类型（1：视频，2：文件，3：图文，4：音频）不能为空")
    @JsonIgnore
    private String fileTypeStr;

    @ApiModelProperty(value = "文件ID")
    @NotNull(message = "文件ID不能为空")
    @JsonProperty("FileID")
    private String fileId;

    @ApiModelProperty(value = "课件所属来源 （1：平台 2：企业）")
    @NotNull(message = "课件所属来源 （1：平台 2：企业）不能为空")
    @JsonProperty("BelongPlat")
    private Integer belongPlat;

    @ApiModelProperty(value = "是否显示给企业查看（1：显示 2：不显示）")
    @NotNull(message = "是否显示给企业查看（1：显示 2：不显示）不能为空")
    @JsonProperty("IsShow")
    private Integer isShow;

    @ApiModelProperty(value = "创建人姓名")
    @NotNull(message = "创建人姓名不能为空")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "修改人姓名")
    @NotNull(message = "修改人姓名不能为空")
    @JsonProperty("ReviseName")
    private String reviseName;

    @ApiModelProperty(value = "来源（1：行业版）")
    @NotNull(message = "来源（1：行业版）不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    @NotNull(message = "创建人编码不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    @NotNull(message = "修改人编码不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1是 0否 默认有效")
    @NotNull(message = "是否有效 1是 0否 默认有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1（文件名称：上传文件的文件名称）")
    @NotNull(message = "预留字段1（文件名称：上传文件的文件名称）不能为空")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2（学习人次）")
    @NotNull(message = "预留字段2（学习人次）不能为空")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3 （上传音频内容 当audio_type=1时 改字段写上传的文件名）")
    @NotNull(message = "预留字段3 （上传音频内容 当audio_type=1时 改字段写上传的文件名）不能为空")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @NotNull(message = "预留字段4不能为空")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @NotNull(message = "预留字段5不能为空")
    @JsonProperty("ReservedField5")
    private String reservedField5;

    @ApiModelProperty(value = "二级标题")
    @NotNull(message = "二级标题不能为空")
    @JsonProperty("SecondTitle")
    private String secondTitle;

    @ApiModelProperty(value = "转码状态")
    @JsonProperty("EncodeState")
    private String encodeState;

    @ApiModelProperty(value = "公司名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "文件地址")
    @JsonProperty("FileUrlHLS")
    private String fileUrlHLS;

}