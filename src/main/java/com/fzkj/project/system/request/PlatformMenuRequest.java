package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 平台菜单
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "平台菜单Request")
public class PlatformMenuRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private Long fid;

    @ApiModelProperty(value = "")
    private String name;

    @ApiModelProperty(value = "")
    private String url;

    @ApiModelProperty(value = "")
    private String authority;

    @ApiModelProperty(value = "")
    private String icon;

    @ApiModelProperty(value = "")
    private String menuClass;

    @ApiModelProperty(value = "")
    private String menuType;

    @ApiModelProperty(value = "")
    @JsonProperty("platform")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    private Integer isShow;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "")
    private String creatorCode;

    @ApiModelProperty(value = "")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "")
    private String reviseCode;

    @ApiModelProperty(value = "")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty(value = "")
    private Integer isValid;

}