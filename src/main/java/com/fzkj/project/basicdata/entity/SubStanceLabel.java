package com.fzkj.project.basicdata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 内容标签对象 t_d_sub_stance_label
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Data
@TableName("t_d_sub_stance_label")
@ApiModel(value = "内容标签实体")
public class SubStanceLabel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    /**
     * 标签名称
     */
    @ApiModelProperty(name = "标签名称")
    @JsonProperty("LabelName")
    private String labelName;

    /**
     * 标签分类
     */
    @ApiModelProperty(name = "标签分类")
    @JsonProperty("Type")
    private Integer type;

    /**
     * 排序
     */
    @ApiModelProperty(name = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    /**
     * 创建人code
     */
    @ApiModelProperty(name = "创建人code")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间")
    @JsonProperty("CreationTime")
    private String creationTime;

    /**
     * 修改人code
     */
    @ApiModelProperty(name = "修改人code")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "修改时间")
    @JsonProperty("ReviseTime")
    private String reviseTime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "备注")
    @JsonProperty("Remark")
    private String remark;

    /**
     * 是否有效
     */
    @ApiModelProperty(name = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    @JsonProperty("CreatorName")
    private String creatorName;

    /**
     * 修改人
     */
    @ApiModelProperty(name = "修改人")
    @JsonProperty("ReviseName")
    private String reviseName;

    /**
     * 来源
     */
    @ApiModelProperty(name = "来源")
    @JsonProperty("Source")
    private String source;

    /**
     * 颜色
     */
    @ApiModelProperty(name = "颜色")
    @JsonProperty("ColorCode")
    private String colorCode;

    @ApiModelProperty(name = "")
    @JsonProperty("Flag")
    @TableField(exist = false)
    private String flag;


    @ApiModelProperty(name = "")
    @JsonProperty("SubStanceNum")
    @TableField(exist = false)
    private Integer substanceNum;
}


