package com.fzkj.project.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.entity.CompanyWork;
import com.fzkj.project.system.request.CompanyRequest;
import com.fzkj.project.system.service.CompanyService;
import com.fzkj.project.system.service.CompanyWorkService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 岗位 前端控制器
 * </p>
 *
 */
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userPort")
public class CompanyWorkController extends BaseEntity {

    private final CompanyWorkService companyWorkService;

    @PostMapping(value = {"GetWork"})
    @ApiOperation(value = "岗位查询下拉列表")
    public AjaxResult getWork(@RequestBody CommonRequest commonRequest) {
        CompanyWork request = (CompanyWork) commonRequest.getRequest(CompanyWork.class);
        return AjaxResult.success(companyWorkService.listPageCompanyWork(request,commonRequest.getPageEntity()));
    }

    @PostMapping(value = {"GetWorkPage"})
    @ApiOperation(value = "岗位查询分页列表")
    public AjaxResult getWorkPage(@RequestBody CommonRequest commonRequest) {
        CompanyWork request = (CompanyWork) commonRequest.getRequest(CompanyWork.class);
        return AjaxResult.success(companyWorkService.getWorkPage(request,commonRequest.getPageEntity()));
    }

}
