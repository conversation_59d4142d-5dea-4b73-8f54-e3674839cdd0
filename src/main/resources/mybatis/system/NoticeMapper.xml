<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.basicdata.mapper.NoticeMapper">

    <select id="selectNoticeAPP" resultType="com.fzkj.project.basicdata.entity.NoticeEntity">
        select *
        from t_d_notice
        where is_valid = 1
        and put_in_platform &amp; #{platform}=#{platform}
        and ((#{orgType}=1 and organization_id=#{organizationId})
        or (#{orgType}=2 and (organization_id = #{organizationId} or organization_id is null))
        or (#{orgType}=3 and organization_id is null )
        )
        ORDER BY id DESC LIMIT 1
    </select>
</mapper>