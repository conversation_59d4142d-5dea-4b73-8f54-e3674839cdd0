package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 公交培训计划
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "公交培训计划Request")
public class LessonSafeTrainPlanRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "培训月份")
    private String lessonMonth;

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "统计分组")
    private Integer statisticalGroup;

    @ApiModelProperty(value = "统计范围1-按时学习 0-补学数据")
    private Integer learnFlag;

    @ApiModelProperty(value = "子部门")
    private Integer children;

    @ApiModelProperty(value = "培训状态")
    private Integer staticStatus;

    @ApiModelProperty(value = "组织ID")
    private Long orgId;

    @ApiModelProperty(value = "计划ID")
    private Long planId;

    @ApiModelProperty(value = "企业/部门IDs")
    private List<Long> orgIds;

    @ApiModelProperty(value = "课程IDs")
    private List<Long> lessonIds;

    @ApiModelProperty(value = "企业ID")
    private Long companyId;

    @ApiModelProperty(value = "部门ID")
    private Long departId;
    @ApiModelProperty(value = "部门IDs")
    private List<Long> departIds;

    @ApiModelProperty(value = "父级部门IDs")
    private List<Long> parentIds;

    @ApiModelProperty(value = "是否删除")
    private Integer isDel;

    @ApiModelProperty(value = "1-按时学习，0-补学数据")
    private Integer state;

    private String orgName;

    private String planName;

    private String lessonName;

}