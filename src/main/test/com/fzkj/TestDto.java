package com.fzkj;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fzkj.common.utils.*;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.mapper.*;
import com.fzkj.project.system.service.UserInfoService;
import com.fzkj.project.system.service.impl.UserInfoServiceImpl;
import com.fzkj.project.system.vo.*;
import com.fzkj.project.wxPay.dao.WxPayDto;
import com.fzkj.project.wxPay.service.impl.WxPayServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-10-20 16:44
 * @Version: 1.0
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class TestDto {
    @Resource
    WxPayServiceImpl wxPayService;
    @Resource
    CompanyMapper companyMapper;
    @Resource
    CompanyWorkMapper companyWorkMapper;
    @Resource
    UserInfoService userInfoService;
    @Resource
    UserInfoMapper userInfoMapper;
    @Resource
    UserRoleMapper userRoleMapper;
    @Resource
    UserLimitMapper userLimitMapper;
    @Autowired
    private UserCompanyMapper userCompanyMapper;


    @Test
    public void sss() {
        WxPayDto wxPayDto1 = new WxPayDto();
        wxPayDto1.setId(1800777084969172996L);
        WxPayDto wxPayDto2 = new WxPayDto();
        wxPayDto2.setId(1800777084969172997L);
        WxPayDto wxPayDto3 = new WxPayDto();
        wxPayDto3.setId(1800777084969172998L);
        wxPayService.wxRefund(wxPayDto1);
        wxPayService.wxRefund(wxPayDto2);
        wxPayService.wxRefund(wxPayDto3);
    }

    @Test
    public void exportUser() {
        List<ImportTestVo> importTests = new ArrayList<>();
        String fileName = "E:\\工作文件\\风筑\\培训平台\\人员导入表\\2024-07-04\\北部.xlsx";
        try {
            File file = new File(fileName);
            InputStream inputStream = Files.newInputStream(file.toPath());
            ExcelUtil<ImportTestVo> util = new ExcelUtil<ImportTestVo>(ImportTestVo.class);
            importTests = util.importExcel(inputStream);
        } catch (Exception e) {
            log.error("批量添加数据流操作异常：" + e.getMessage());
        }
        int successNum = 0;
        int errorNum = 0;
        List<ImportTestVo> importTestVosRes = new ArrayList<>();
        for (ImportTestVo importTestVo : importTests) {
            if (fileName.contains("西")) {
                importTestVo.setEnterprise("西部公交");
            } else if (fileName.contains("南")) {
                importTestVo.setEnterprise("南部公交");
            } else if (fileName.contains("北")) {
                importTestVo.setEnterprise("北部公交");
            } else {
                importTestVo.setEnterprise("两江公交");
            }
            QueryWrapper<Company> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("company_name", importTestVo.getEnterprise());
            Company companyName = companyMapper.selectOne(queryWrapper);
            //获取部门
            QueryWrapper<Company> queryWrapperTwo = new QueryWrapper<>();
            queryWrapperTwo.eq("company_name", importTestVo.getDepart());
            queryWrapperTwo.like("org_code", companyName.getOrgCode());
            Company selectOne = companyMapper.selectOne(queryWrapperTwo);
            UserInfoVO userInfoVO = new UserInfoVO();
            userInfoVO.setUserName(importTestVo.getName());
            userInfoVO.setIdCard(importTestVo.getIdCard());
            userInfoVO.setJobNo(importTestVo.getJobNo());
            userInfoVO.setPhone(importTestVo.getPhone());
            userInfoVO.setCompanyId(Integer.parseInt(companyName.getId() + ""));
            userInfoVO.setDepartId(selectOne.getId());
            Long l = companyWorkMapper.selectCompanyDriver(selectOne.getId());
            userInfoVO.setWorkId(l);

            if (importTestVo.getJob().equals("管理")) {
                userInfoVO.setTrainTypeIDStr("46");
                userInfoVO.setStatisticalGroup(1);
            } else {
                userInfoVO.setTrainTypeIDStr("43");
                userInfoVO.setStatisticalGroup(0);
            }
            //日期处理后添加
            AjaxResult ajaxResult = userInfoService.saveUserInfo(userInfoVO);
            if (!ajaxResult.get("code").equals(1000)) {
                //添加失败
                ImportTestVo userBatchAddExcelResVO = (ImportTestVo) DataTransfer.transfer(importTestVo, ImportTestVo.class);
                importTestVosRes.add(userBatchAddExcelResVO);
                errorNum++;
            } else {
                successNum++;
            }
        }
        String message = "操作成功" + successNum + "人，操作失败" + errorNum + "人！";
        //判断当前导入是否有失败
        if (errorNum == 0) {
            System.out.println(message);
        } else {
            try {
                ExcelUtil<ImportTestVo> util = new ExcelUtil<ImportTestVo>(ImportTestVo.class);
                String name = util.encodingDateFilename("添加用户失败信息");
                InputStream inputStream = util.exportExcelFile(importTestVosRes, name);
                UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "user", name);
                System.out.println(message + ";url:" + uploadFile.getUrl());
            } catch (Exception e) {
                log.error("用户批量添加错误报告上传异常：" + e.getMessage());
                System.out.println(message);
            }
        }
    }

    @Test
    public void exportUserTwo() {
        List<ImportTestVo> importTests = new ArrayList<>();
        String fileName = "E:\\工作文件\\风筑\\培训平台\\人员导入表\\2024-07-04\\北部.xlsx";
        try {
            File file = new File(fileName);
            InputStream inputStream = Files.newInputStream(file.toPath());
            ExcelUtil<ImportTestVo> util = new ExcelUtil<ImportTestVo>(ImportTestVo.class);
            importTests = util.importExcel(inputStream);
        } catch (Exception e) {
            log.error("批量添加数据流操作异常：" + e.getMessage());
        }
        int successNum = 0;
        int errorNum = 0;
        List<ImportTestVo> importTestVosRes = new ArrayList<>();
        for (ImportTestVo importTestVo : importTests) {
            if (fileName.contains("西")) {
                importTestVo.setEnterprise("西部公交");
            } else if (fileName.contains("南")) {
                importTestVo.setEnterprise("南部公交");
            } else if (fileName.contains("北")) {
                importTestVo.setEnterprise("北部公交");
            } else {
                importTestVo.setEnterprise("两江公交");
            }
            QueryWrapper<Company> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("company_name", importTestVo.getEnterprise());
            Company companyName = companyMapper.selectOne(queryWrapper);
            //获取部门
            QueryWrapper<Company> queryWrapperTwo = new QueryWrapper<>();
            queryWrapperTwo.eq("company_name", importTestVo.getDepart());
            queryWrapperTwo.like("org_code", companyName.getOrgCode());
            Company selectOne = companyMapper.selectOne(queryWrapperTwo);
            UserInfoVO userInfoVO = new UserInfoVO();
            userInfoVO.setUserName(importTestVo.getName());
            userInfoVO.setIdCard(importTestVo.getIdCard());
            userInfoVO.setJobNo(importTestVo.getJobNo());
            userInfoVO.setPhone(importTestVo.getPhone());
            userInfoVO.setCompanyId(Integer.parseInt(companyName.getId() + ""));
            userInfoVO.setDepartId(selectOne.getId());
            Long l = companyWorkMapper.selectCompanyDriver(selectOne.getId());
            userInfoVO.setWorkId(l);

            if (importTestVo.getJob().equals("管理人员")) {
                userInfoVO.setTrainTypeIDStr("46");
                userInfoVO.setStatisticalGroup(1);
            } else {

                userInfoVO.setTrainTypeIDStr("43");
                userInfoVO.setStatisticalGroup(0);
            }
            //日期处理后添加
            AjaxResult ajaxResult = userInfoService.saveUserInfo(userInfoVO);
            if (!ajaxResult.get("code").equals(1000)) {
                //添加失败
                ImportTestVo userBatchAddExcelResVO = (ImportTestVo) DataTransfer.transfer(importTestVo, ImportTestVo.class);
                importTestVosRes.add(userBatchAddExcelResVO);
                errorNum++;
            } else {
                successNum++;
            }
        }
        String message = "操作成功" + successNum + "人，操作失败" + errorNum + "人！";
        //判断当前导入是否有失败
        if (errorNum == 0) {
            System.out.println(message);
        } else {
            try {
                ExcelUtil<ImportTestVo> util = new ExcelUtil<ImportTestVo>(ImportTestVo.class);
                String name = util.encodingDateFilename("添加用户失败信息");
                InputStream inputStream = util.exportExcelFile(importTestVosRes, name);
                UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "user", name);
                System.out.println(message + ";url:" + uploadFile.getUrl());
            } catch (Exception e) {
                log.error("用户批量添加错误报告上传异常：" + e.getMessage());
                System.out.println(message);
            }
        }
    }

    @Test
    public void updatePassword() {
        List<UserInfoVO> userInfoVOS = userInfoMapper.getUserSelect(null, null, null, null);
        for (UserInfoVO work : userInfoVOS) {
            String pass = null;
            if (work.getUserName().equals("admin") || work.getJobNo().length() > 6) {
                continue;
            }
            if (StringUtils.isNotEmpty(work.getJobNo())) {
                pass = PinYinHeadUtils.getPinYinHeadChar(work.getUserName()) + work.getJobNo();
            } else {
                pass = "Gj2024";//默认密码

            }
            UserInfo userCode = userInfoMapper.selectOne(new QueryWrapper<UserInfo>().eq("user_code", work.getUserCode()));
            userCode.setPassWord(SecurityUtils.encryptPassword(pass));
            userInfoMapper.updateById(userCode);
        }
    }

    @Test
    public void updateUserPassword(){
        List<ImportPassTestVo> importTests = new ArrayList<>();
        String fileName = "D:\\app\\OneDrive\\桌面\\重置密码E.xlsx";
        try {
            File file = new File(fileName);
            InputStream inputStream = Files.newInputStream(file.toPath());
            ExcelUtil<ImportPassTestVo> util = new ExcelUtil<ImportPassTestVo>(ImportPassTestVo.class);
            importTests = util.importExcel(inputStream);
        } catch (Exception e) {
            log.error("批量添加数据流操作异常：" + e.getMessage());
        }
        List<String> list=new ArrayList<>();
        for (ImportPassTestVo importTest : importTests) {
            AuthUserInfoVO userInfo = userInfoMapper.selectUserByIdCard(importTest.getIdCard());
            Integer i = userInfoService.restPassword(userInfo.getUserCode());
            if (i==0){
//            if(ObjectUtils.isEmpty(userInfo)){
                log.error(importTest.getIdCard()+"修改密码失败！");
                list.add(importTest.getIdCard()+"修改密码失败！");
            }
        }
        System.out.println("失败用户集合："+list);
    }
    @Test
    public void pass() {
        String pass = "Gj202407";
        System.out.println(PinYinHeadUtils.getPinYinHeadChar("吴 疆"));
        System.out.println(SecurityUtils.encryptPassword(pass));
//        List<UserLessonRecordSafeTrain> companyId = userLessonRecordSafeTrainMapper.selectList(new QueryWrapper<UserLessonRecordSafeTrain>()
//                .eq("company_id", 664L).eq("lesson_id", 142));
//        companyId.forEach(
//                a -> {
//                    UserCompany userCode = userCompanyMapper.selectOne(new QueryWrapper<UserCompany>()
//                            .eq("user_code", a.getUserCode()));
//                    a.setDepartId(userCode.getDepartId());
//                    a.setDepartName(companyMapper.selectById(userCode.getDepartId()).getCompanyName());
//                    userLessonRecordSafeTrainMapper.updateById(a);
//                }
//        );
    }

    @Test
    public void updateUserLimit() {
        List<ImportTestVo> importTests = new ArrayList<>();
        String fileName = "E:\\工作文件\\风筑\\培训平台\\人员导入表\\2024-07-04\\两江.xlsx";
        try {
            File file = new File(fileName);
            InputStream inputStream = Files.newInputStream(file.toPath());
            ExcelUtil<ImportTestVo> util = new ExcelUtil<ImportTestVo>(ImportTestVo.class);
            importTests = util.importExcel(inputStream);
        } catch (Exception e) {
            log.error("批量添加数据流操作异常：" + e.getMessage());
        }
        for (ImportTestVo importTestVo : importTests) {
            if (importTestVo.getJob().contains("管理")) {
                AuthUserInfoVO userInfo = userInfoMapper.selectUserByIdCard(importTestVo.getIdCard());
                UserLimit userLimit = new UserLimit();
                UserLimit oldLimit = userLimitMapper.selectOne(new QueryWrapper<UserLimit>().eq("target_id", userInfo.getDepartID())
                        .eq("user_code", userInfo.getUserCode()));
                if (userInfo.getUserCode().equals("afd7b9f228b09617a9e2df345117edcd")) {
                    System.out.println(1111);
                }
                if (ObjectUtils.isEmpty(oldLimit)) {
                    userLimit.setTargetId(userInfo.getDepartID());
                    userLimit.setUserCode(userInfo.getUserCode());
                    userLimit.setPcRoleId("82");
                    userLimit.setMobileRoleId("81");
                    userLimitMapper.insert(userLimit);
                } else {
                    Integer userRole = userRoleMapper.selectUserCodeByTargetId(userInfo.getDepartID(), userInfo.getUserCode(), 16);
                    if (userRole < 1) {
                        UserRole userRolePc = new UserRole();
                        userRolePc.setTargetId(userInfo.getDepartID());
                        userRolePc.setRoleId(Long.parseLong("82"));
                        userRolePc.setUserCode(userInfo.getUserCode());
                        userRolePc.setOperPlatform(16);
                        userRolePc.setIsValid(1);
                        userRoleMapper.insert(userRolePc);
                    }
                    Integer code = userRoleMapper.selectUserCodeByTargetId(userInfo.getDepartID(), userInfo.getUserCode(), 256);
                    if (code < 1) {
                        //新增用户角色关联表数据(小程序)
                        UserRole userRoleMobile = new UserRole();
                        userRoleMobile.setTargetId(userInfo.getDepartID());
                        userRoleMobile.setRoleId(Long.parseLong("81"));
                        userRoleMobile.setUserCode(userInfo.getUserCode());
                        userRoleMobile.setOperPlatform(256);
                        userRoleMobile.setIsValid(1);
                        userRoleMapper.insert(userRoleMobile);
                    }
                }
            }
        }
    }

    @Test
    public void updateUserWork() {
        List<ImportTestVo> importTests = new ArrayList<>();
        String fileName = "E:\\工作文件\\风筑\\培训平台\\人员导入表\\2024-07-04\\西部.xlsx";
        try {
            File file = new File(fileName);
            InputStream inputStream = Files.newInputStream(file.toPath());
            ExcelUtil<ImportTestVo> util = new ExcelUtil<ImportTestVo>(ImportTestVo.class);
            importTests = util.importExcel(inputStream);
        } catch (Exception e) {
            log.error("批量添加数据流操作异常：" + e.getMessage());
        }
        for (ImportTestVo importTestVo : importTests) {
            if (importTestVo.getIdCard().equals("500104200008140012")
                    || importTestVo.getIdCard().equals("500221199805097316")
                    || importTestVo.getIdCard().equals("500107198602077019")
                    || importTestVo.getIdCard().equals("500104198706020018")) {
                System.out.println(importTestVo);
            }
//            if (importTestVo.getJob().contains("管理")) {
//                AuthUserInfoVO authUserInfoVO = userInfoMapper.selectUserByIdCard(importTestVo.getIdCard());
//                UserCompany userCode = userCompanyMapper.selectOne(new QueryWrapper<UserCompany>()
//                        .eq("user_code", authUserInfoVO.getUserCode()));
//                if (!ObjectUtils.isEmpty(userCode)){
//                    userCode.setWorkId(companyWorkMapper.selectCompanySystem(authUserInfoVO.getDepartID()));
//                    userCompanyMapper.updateById(userCode);
//                }
//            }
        }
    }
}
