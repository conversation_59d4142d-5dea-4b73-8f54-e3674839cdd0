package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 安全培训课件分发学员学习记录
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="安全培训课件分发学员学习记录VO")
public class UserCourseRecordSafeTrainVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private String id;

    @ApiModelProperty(value = "课程ID")
    @NotNull(message = "课程ID不能为空")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课程分类")
    @NotNull(message = "课程分类不能为空")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课件ID")
    @NotNull(message = "课件ID不能为空")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty(value = "课件名称")
    @NotNull(message = "课件名称不能为空")
    @JsonProperty("CourseName")
    private String courseName;

    @ApiModelProperty(value = "文件类型")
    @NotNull(message = "文件类型不能为空")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(value = "文件地址")
    @NotNull(message = "文件地址不能为空")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "课件完成状态：（-3,未开始，0学习中，1已完成，2过期学习，默认-3）")
    @NotNull(message = "课件完成状态：（-3,未开始，0学习中，1已完成，2过期学习，默认-3）不能为空")
    @JsonProperty("CourseIsComplete")
    private Integer courseIsComplete;

    @ApiModelProperty(value = "企业ID")
    @NotNull(message = "企业ID不能为空")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    @NotNull(message = "企业名称不能为空")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "部门ID")
    @NotNull(message = "部门ID不能为空")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "岗位ID")
    @NotNull(message = "岗位ID不能为空")
    @JsonProperty("WorkID")
    private Long workId;

    @ApiModelProperty(value = "用户编号")
    @NotNull(message = "用户编号不能为空")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "是否统计")
    @NotNull(message = "是否统计不能为空")
    @JsonProperty("IsStatic")
    private Integer isStatic;

    @ApiModelProperty(value = "学习时长")
    @NotNull(message = "学习时长不能为空")
    @JsonProperty("StudyTimeCount")
    private Long studyTimeCount;

    @ApiModelProperty(value = "课件时长")
    @NotNull(message = "课件时长不能为空")
    @JsonProperty("TimeCount")
    private Long timeCount;

    @ApiModelProperty(value = "人脸识别弹框的时间（562,2256）")
    @NotNull(message = "人脸识别弹框的时间（562,2256）不能为空")
    @JsonProperty("FaceRecognitionString")
    private String faceRecognitionString;

    @ApiModelProperty(value = "人脸识别图片地址")
    @NotNull(message = "人脸识别图片地址不能为空")
    @JsonProperty("FaceDistinguishImg")
    private String faceDistinguishImg;

    @ApiModelProperty(value = "开始学习时间")
    @NotNull(message = "开始学习时间不能为空")
    @JsonProperty("StartStudyTime")
    private String startStudyTime;

    @ApiModelProperty(value = "完成学习时间")
    @NotNull(message = "完成学习时间不能为空")
    @JsonProperty("CompleteTime")
    private String completeTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @NotNull(message = "创建人ID不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @NotNull(message = "修改人ID不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    @NotNull(message = "是否有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "分发来源 1：售后服务端分发的， 0：用户自己购买分发的")
    @NotNull(message = "分发来源 1：售后服务端分发的， 0：用户自己购买分发的不能为空")
    @JsonProperty("DisSource")
    private Integer disSource;

    @ApiModelProperty(value = "参培课程（1：（通用课程） 2：（定制课程））")
    @NotNull(message = "参培课程（1：（通用课程） 2：（定制课程））不能为空")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "预留字段1")
    @NotNull(message = "预留字段1不能为空")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    @NotNull(message = "预留字段2不能为空")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    @NotNull(message = "预留字段3不能为空")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @NotNull(message = "预留字段4不能为空")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @NotNull(message = "预留字段5不能为空")
    @JsonProperty("ReservedField5")
    private String reservedField5;

}