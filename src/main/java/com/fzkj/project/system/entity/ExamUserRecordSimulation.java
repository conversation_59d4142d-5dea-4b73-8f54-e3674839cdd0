package com.fzkj.project.system.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 用户考试记录表（模拟考试）
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@TableName("t_d_exam_user_record_simulation")
@ApiModel(value = "用户考试记录表（模拟考试）实体")
public class ExamUserRecordSimulation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "组织ID")
    @JsonProperty("OrganizationID")
    private String organizationId;

    @ApiModelProperty(value = "用户ID")
    @JsonProperty("UserID")
    private String userId;

    @ApiModelProperty(value = "用户姓名")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "身份证号")
    @JsonProperty("IdCard")
    private String idCard;

    @ApiModelProperty(value = "考试ID")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "及格分")
    @JsonProperty("PassMark")
    private Integer passMark;

    @ApiModelProperty(value = "得分")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty(value = "总分")
    @JsonProperty("TotalScore")
    private Integer totalScore;

    @ApiModelProperty(value = "考试次数")
    @JsonProperty("ResitNumber")
    private Integer resitNumber;

    @ApiModelProperty(value = "用时")
    @JsonProperty("UseTimeCount")
    private Integer useTimeCount;

    @ApiModelProperty(value = "正确率")
    @JsonProperty("Accuracy")
    private BigDecimal accuracy;

    @ApiModelProperty(value = "用户考试次数")
    @JsonProperty("UserExamCount")
    private Integer userExamCount;

    @ApiModelProperty(value = "是否静态")
    @JsonProperty("IsStatic")
    private Integer isStatic;

    @ApiModelProperty(value = "错题数")
    @JsonProperty("ErrorCount")
    private Integer errorCount;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "人脸识别照片")
    @JsonProperty("PhotoUrl")
    private String photoUrl;

    @ApiModelProperty(value = "签字照片")
    @JsonProperty("SignUrl")
    private String signUrl;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "来源")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "用户电话")
    @JsonProperty("UserPhone")
    private String userPhone;

    @ApiModelProperty(value = "部门ID")
    @JsonProperty("DepartID")
    private Long departId;


}