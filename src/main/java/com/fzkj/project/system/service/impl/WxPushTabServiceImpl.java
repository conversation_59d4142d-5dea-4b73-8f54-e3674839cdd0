package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.project.system.entity.WxPushTab;
import com.fzkj.project.system.mapper.WxPushTabMapper;
import com.fzkj.project.system.service.WxPushTabService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 微信推送列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class WxPushTabServiceImpl extends ServiceImpl<WxPushTabMapper, WxPushTab> implements WxPushTabService {
}
