package com.fzkj.project.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.monitor.domain.SysOperLog;
import com.fzkj.project.monitor.mapper.SysOperLogMapper;
import com.fzkj.project.system.entity.UserRole;
import com.fzkj.project.system.mapper.UserRoleMapper;
import com.fzkj.project.system.request.UserRoleRequest;
import com.fzkj.project.system.service.UserOperLogService;
import com.fzkj.project.system.service.UserRoleService;
import com.fzkj.project.system.service.WxService;
import com.fzkj.project.system.vo.UserOperLogVO;
import com.fzkj.project.system.vo.UserRoleVO;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserOperLogServiceImpl implements UserOperLogService {

    private final SysOperLogMapper sysOperLogMapper;

    @Override
    public PageUtils getOperUserLog(UserOperLogVO userOperLogVO, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<UserOperLogVO> userOperLogVOS = sysOperLogMapper.selectUserOperLogPage(userOperLogVO.getPlatform(),
                                                                                    userOperLogVO.getOperName(),
                                                                                    userOperLogVO.getCompanyId(),
                                                                                    userOperLogVO.getKeyWord(),
                                                                                    userOperLogVO.getStartTime(),
                                                                                    userOperLogVO.getEndTime());
        userOperLogVOS.forEach(item->{
            String operParam = item.getOperParam();
            if(StringUtils.isNotEmpty(operParam) && operParam.startsWith("{")){
                JSONObject operParamJs = JSONObject.parseObject(operParam);
                String request = operParamJs.getString("request");
                if(StringUtils.isNotEmpty(request) && request.startsWith("{")){
                    JSONObject requestJs = JSONObject.parseObject(request);
                    item.setOperContent(requestJs.getString("OperContent"));
                }
            }
        });
        return new PageUtils(pageEntity,userOperLogVOS);
    }
}