package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetUserCourseStudyTimeRequest {

    @ApiModelProperty(value = "用户编码", example = "b135339037ae410a87ad42c872c9353f")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "组织ID", example = "50")
    @JsonProperty("OrganizationID")
    private Long organizationId;

    @ApiModelProperty(value = "课程ID", example = "4958")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty(value = "课程ID", example = "3822")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "培训类型", example = "aqpx")
    @JsonProperty("TrainType")
    private String trainType;
}
