package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业安全培训课程设置表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_company_lesson_safe_train_set_log")
@ApiModel(value = "企业安全培训课程设置表实体")
public class CompanyLessonSafeTrainSetLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "企业安全培训课程设置表ID（T_D_CompanyLessonSafeTrainSet）")
    @JsonProperty("SafeTrainSetID")
    private Long safeTrainSetId;

    @ApiModelProperty(value = "操作名称（修改，创建，增减）")
    @JsonProperty("OperationName")
    private String operationName;

    @ApiModelProperty(value = "操作列名称")
    @JsonProperty("ObjectName")
    private String objectName;

    @ApiModelProperty(value = "操作列值")
    @JsonProperty("ObjectValues")
    private String objectValues;

    @ApiModelProperty(value = "参培分类名称")
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty(value = "创建人ID")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "创建人名称")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "备注信息")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否 默认有效")
    @JsonProperty("IsValid")
    private Integer isValid;


}