package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.ExamAnswerRecord;
import com.fzkj.project.system.vo.ExamAnswerRecordVO;
import com.fzkj.project.system.request.ExamAnswerRecordRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ExamAnswerRecordMapper;
import com.fzkj.project.system.service.ExamAnswerRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户答题记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExamAnswerRecordServiceImpl extends ServiceImpl<ExamAnswerRecordMapper, ExamAnswerRecord> implements ExamAnswerRecordService {

    @Override
    public List<ExamAnswerRecord> listExamAnswerRecord(ExamAnswerRecordRequest request) {
        ExamAnswerRecord entity = (ExamAnswerRecord) DataTransfer.transfer(request, ExamAnswerRecord.class);
        LambdaQueryWrapper<ExamAnswerRecord> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ExamAnswerRecordRequest request, PageEntity pageEntity) {
        ExamAnswerRecord entity = (ExamAnswerRecord) DataTransfer.transfer(request, ExamAnswerRecord.class);
        LambdaQueryWrapper<ExamAnswerRecord> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<ExamAnswerRecord> page = this.page(
                new Query<ExamAnswerRecord>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ExamAnswerRecordVO findExamAnswerRecord(Long id) {
        ExamAnswerRecord entity = this.getById(id);
        ExamAnswerRecordVO vo = (ExamAnswerRecordVO) DataTransfer.transfer(entity, ExamAnswerRecordVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExamAnswerRecord(ExamAnswerRecordVO vo) {
        ExamAnswerRecord entity = (ExamAnswerRecord) DataTransfer.transfer(vo, ExamAnswerRecord.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamAnswerRecord(ExamAnswerRecordVO vo) {
        ExamAnswerRecord entity = (ExamAnswerRecord) DataTransfer.transfer(vo, ExamAnswerRecord.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExamAnswerRecord(Long id) {
        return this.removeById(id);
    }
}