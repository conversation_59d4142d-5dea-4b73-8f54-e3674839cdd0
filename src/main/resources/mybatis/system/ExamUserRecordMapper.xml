<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.ExamUserRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.ExamUserRecord">
        <id column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="id_card" property="idCard" />
        <result column="exam_id" property="examId" />
        <result column="pass_mark" property="passMark" />
        <result column="score" property="score" />
        <result column="total_score" property="totalScore" />
        <result column="resit_number" property="resitNumber" />
        <result column="use_time_count" property="useTimeCount" />
        <result column="accuracy" property="accuracy" />
        <result column="user_exam_count" property="userExamCount" />
        <result column="is_static" property="isStatic" />
        <result column="error_count" property="errorCount" />
        <result column="lesson_id" property="lessonId" />
        <result column="photo_url" property="photoUrl" />
        <result column="sign_url" property="signUrl" />
        <result column="sort" property="sort" />
        <result column="creation_time" property="creationTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="source" property="source" />
        <result column="user_phone" property="userPhone" />
        <result column="depart_id" property="departId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, organization_id, user_id, user_name, id_card, exam_id, pass_mark, score, total_score, resit_number, use_time_count, accuracy, user_exam_count, is_static, error_count, lesson_id, photo_url, sign_url, sort, creation_time, remark, is_valid, source, user_phone, depart_id
    </sql>
    <select id="selectSafeTrainCompanyDetialUserExamRecord" resultType="com.fzkj.project.system.vo.SafeTrainCompanyDetialUserExamRecordVO">
        select
        ex.user_id,
        ex.lesson_id,
        ex.score,
        (
        select
        e.pass_mark
        from
        t_d_exam e
        left join t_d_exam_lesson el on
        el.exam_id = e.id
        where
        el.lesson_id = #{lessonId})pass_mark,
        DATE_FORMAT(ex.creation_time, '%Y-%m-%d %H:%i:%s') as examCompleteTime,sign_url,
        (
        select
        case
        when plate_number = '' then '--'
        when plate_number = null then '--'
        else plate_number
        end
        from
        t_d_user_company t
        where
        user_code = ex.user_id)plate_number
        from
        t_d_exam_user_record ex
        where
        ex.id in (
        select
        max(t.id)
        from
        t_d_exam_user_record t
        where
        t.is_valid = 1
        and t.lesson_id = #{lessonId}
        <if test="companyId != null">
            and t.organization_id = #{companyId}
        </if>
        <if test="departIds != null and departIds.size > 0">
            AND t.depart_id IN
            <foreach item="id" collection="departIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by
        user_id , lesson_id )
    </select>
    <select id="selectUserExamInfoRecord1" resultType="com.fzkj.project.system.vo.GetUserExamInfoRecordVO">
        select *
        from
        t_d_exam_user_record
        where
        user_id = #{userId}
        <if test="lessonId != null">
            and lesson_id = #{lessonId}
        </if>
        <if test="examId != null">
            and exam_id = #{examId}
        </if>
        <if test="sDate != null">
            and creation_time >= #{sDate}
        </if>
        <if test="eDate != null">
            and creation_time &lt;= #{eDate}
        </if>
        and is_valid = 1
        order by creation_time desc
    </select>

    <select id="selectUserExamInfoRecord2" resultType="com.fzkj.project.system.vo.GetUserExamInfoRecordVO">
        select *
        from
        t_d_exam_user_record_simulation
        where
        user_id = #{userId}
        <if test="lessonId != null">
            and lesson_id = #{lessonId}
        </if>
        <if test="examId != null">
            and exam_id = #{examId}
        </if>
        <if test="sDate != null">
            and creation_time >= #{sDate}
        </if>
        <if test="eDate != null">
            and creation_time &lt;= #{eDate}
        </if>
        and is_valid = 1
        order by creation_time desc
    </select>
    <select id="selectUserExamDetailRecord1" resultType="com.fzkj.project.system.vo.GetUserExamDetailRecordVO">
        SELECT
        tdear.id,
        tdear.record_id,
        tdear.question_id,
        tdear.sub_option,
        tdear.right_option,
        tdear.score,
        tdear.serial_number,
        tdeq.subject_type,
        tdeq.subject_name,
        tdeq.question_type_id,
        tdeq.question_option,
        tdeq.question_type_name,
        tdeq.img,
        tdeq.score as question_score,
        tdeq.problem_analysis
        FROM
        t_d_exam_answer_record AS tdear
        JOIN (
        SELECT
        tdeur.id
        FROM
        t_d_exam_user_record AS tdeur
        WHERE
        tdeur.is_valid = 1
        <if test="userId != null">
            AND tdeur.id = (
            SELECT
            IFNULL(MAX(t.id), 0) AS id
            FROM
            t_d_exam_user_record t
            WHERE
            t.user_id = #{userId}
            AND t.lesson_id = #{lessonId}
            )
        </if>
        ) t ON tdear.record_id = t.id
        LEFT JOIN t_d_exam_question AS tdeq ON tdear.question_id = tdeq.id
        where tdear.is_valid = 1
        ORDER BY tdear.serial_number
    </select>
    <select id="selectUserExamDetailRecord2" resultType="com.fzkj.project.system.vo.GetUserExamDetailRecordVO">
        SELECT
        tdear.id,
        tdear.record_id,
        tdear.question_id,
        tdear.sub_option,
        tdear.right_option,
        tdear.score,
        tdear.serial_number,
        tdeq.subject_type,
        tdeq.subject_name,
        tdeq.question_type_id,
        tdeq.question_option,
        tdeq.question_type_name,
        tdeq.img,
        tdeq.score as question_score,
        tdeq.problem_analysis
        FROM
        t_d_exam_answer_record_simulation AS tdear
        JOIN (
        SELECT
        tdeur.id
        FROM
        t_d_exam_user_record_simulation AS tdeur
        WHERE
        tdeur.is_valid = 1
        AND tdeur.id = (
            SELECT
            IFNULL(MAX(t.id), 0) AS id
            FROM
            t_d_exam_user_record_simulation t
            WHERE
            t.user_id = #{userId}
            AND t.lesson_id = #{lessonId}
            )
        ) t ON tdear.record_id = t.id
        LEFT JOIN t_d_exam_question AS tdeq ON tdear.question_id = tdeq.id
        where tdear.is_valid = 1
        ORDER BY tdear.serial_number
    </select>
    <select id="selectSignUrlById" resultType="java.lang.String">
        SELECT t.sign_url
        FROM t_d_exam_user_record t
        WHERE  t.lesson_id = #{lessonId}
    </select>


</mapper>