package com.fzkj.project.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.entity.CourseCategory;
import com.fzkj.project.system.request.CourseCategoryQueryRequest;
import com.fzkj.project.system.vo.CourseCategoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 平台课件分类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface CourseCategoryMapper extends BaseMapper<CourseCategory> {
    List<CourseCategoryVO> selectCourseCategory(CourseCategoryQueryRequest request);
}