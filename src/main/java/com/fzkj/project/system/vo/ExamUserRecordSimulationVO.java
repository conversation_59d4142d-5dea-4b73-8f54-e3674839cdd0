package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户考试记录表（模拟考试）
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value="用户考试记录表（模拟考试）VO")
public class ExamUserRecordSimulationVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "组织ID")
    @NotNull(message = "组织ID不能为空")
    @JsonProperty("OrganizationID")
    private String organizationId;

    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不能为空")
    @JsonProperty("UserID")
    private String userId;

    @ApiModelProperty(value = "用户姓名")
    @NotNull(message = "用户姓名不能为空")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "身份证号")
    @NotNull(message = "身份证号不能为空")
    @JsonProperty("IdCard")
    private String idCard;

    @ApiModelProperty(value = "考试ID")
    @NotNull(message = "考试ID不能为空")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "及格分")
    @NotNull(message = "及格分不能为空")
    @JsonProperty("PassMark")
    private Integer passMark;

    @ApiModelProperty(value = "得分")
    @NotNull(message = "得分不能为空")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty(value = "总分")
    @NotNull(message = "总分不能为空")
    @JsonProperty("TotalScore")
    private Integer totalScore;

    @ApiModelProperty(value = "考试次数")
    @NotNull(message = "考试次数不能为空")
    @JsonProperty("ResitNumber")
    private Integer resitNumber;

    @ApiModelProperty(value = "用时")
    @NotNull(message = "用时不能为空")
    @JsonProperty("UseTimeCount")
    private Integer useTimeCount;

    @ApiModelProperty(value = "正确率")
    @NotNull(message = "正确率不能为空")
    @JsonProperty("Accuracy")
    private BigDecimal accuracy;

    @ApiModelProperty(value = "用户考试次数")
    @NotNull(message = "用户考试次数不能为空")
    @JsonProperty("UserExamCount")
    private Integer userExamCount;

    @ApiModelProperty(value = "是否静态")
    @NotNull(message = "是否静态不能为空")
    @JsonProperty("IsStatic")
    private Integer isStatic;

    @ApiModelProperty(value = "错题数")
    @NotNull(message = "错题数不能为空")
    @JsonProperty("ErrorCount")
    private Integer errorCount;

    @ApiModelProperty(value = "课程ID")
    @NotNull(message = "课程ID不能为空")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "人脸识别照片")
    @NotNull(message = "人脸识别照片不能为空")
    @JsonProperty("PhotoUrl")
    private String photoUrl;

    @ApiModelProperty(value = "签字照片")
    @NotNull(message = "签字照片不能为空")
    @JsonProperty("SignUrl")
    private String signUrl;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    @NotNull(message = "是否有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "来源")
    @NotNull(message = "来源不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "用户电话")
    @NotNull(message = "用户电话不能为空")
    @JsonProperty("UserPhone")
    private String userPhone;

    @ApiModelProperty(value = "部门ID")
    @NotNull(message = "部门ID不能为空")
    @JsonProperty("DepartID")
    private Long departId;

    @JsonProperty("Flag")
    private String flag;
}