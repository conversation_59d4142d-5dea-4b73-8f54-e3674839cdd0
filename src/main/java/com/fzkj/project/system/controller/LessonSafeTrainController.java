package com.fzkj.project.system.controller;

import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.annotation.ListNoArray;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.entity.LessonSafeTrain;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.LessonSafeTrainService;
import com.fzkj.project.system.service.UserLessonRecordSafeTrainMongoService;
import com.fzkj.project.system.service.WxService;
import com.fzkj.project.system.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 * 安全培训课程 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "lessonApi")
@ThirdPart
@Api(tags = "安全培训课程管理")
@Log
public class LessonSafeTrainController extends BaseEntity {

    private final LessonSafeTrainService LessonSafeTrainServiceImpl;
    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;

    private final WxService wxService;

    @PostMapping("Get_LessonSafeTrainList")
    @ApiOperation(value = "安全培训课程管理列表查询")
    public AjaxResult Get_LessonSafeTrainList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainRequest request = (LessonSafeTrainRequest) commonRequest.getRequest(LessonSafeTrainRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.listLessonSafeTrain(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_LessonSafeTrainList_C")
    @ApiOperation(value = "安全培训课程管理列表查询-企业端")
    public AjaxResult Get_LessonSafeTrainList_C(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainRequest request = (LessonSafeTrainRequest) commonRequest.getRequest(LessonSafeTrainRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLessonSafeTrainListC(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Export_SafeTrainCompanyLesson")
    @ApiOperation(value = "安全培训课程管理列表下载-企业端")
    public AjaxResult Export_SafeTrainCompanyLesson(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainRequest request = (LessonSafeTrainRequest) commonRequest.getRequest(LessonSafeTrainRequest.class);
        String sTime = request.getSTime();
        String eTime = request.getETime();
        request.setFlag("list");
        PageUtils pageUtils = LessonSafeTrainServiceImpl.getLessonSafeTrainListC(request, null);
        List list = pageUtils.getList();
        ExcelUtil<LessonSafeTrainVO> util = new ExcelUtil<>(LessonSafeTrainVO.class);
        //重庆国泰出租汽车（集团）有限公司安全培训报表全部数据（2023-03~2024-03）
        String title = "";
        if (null == request.getState()) {
            title = request.getCompanyName() + "安全培训报表全部数据" + "(" + sTime + "~" + eTime + ")";
        } else if (1 == request.getState()) {
            title = request.getCompanyName() + "安全培训报表数据" + "(" + sTime + "~" + eTime + ")";
        } else if (0 == request.getState()) {
            title = request.getCompanyName() + "安全培训报表补学数据" + "(" + sTime + "~" + eTime + ")";
        }
        return util.exportExcel(list, title, title, true);
    }

    @PostMapping("Get_CourseByLessonSafeTrainID")
    @ApiOperation(value = "安全培训课程-关联课件查询")
    public AjaxResult Get_CourseByLessonSafeTrainID(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainCourseRequest request = (LessonSafeTrainCourseRequest) commonRequest.getRequest(LessonSafeTrainCourseRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getCourseByLessonSafeTrainID(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Edit_LessonSafeTrain")
    @ApiOperation(value = "安全培训课程-编辑课程")
    public AjaxResult Edit_LessonSafeTrain(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainVO request = (LessonSafeTrainVO) commonRequest.getRequest(LessonSafeTrainVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.editLessonSafeTrain(request));
    }

    @PostMapping("Edit_LessonSafeTrain_Del")
    @ApiOperation(value = "安全培训课程-编辑课程")
    public AjaxResult Edit_LessonSafeTrain_Del(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainVO request = (LessonSafeTrainVO) commonRequest.getRequest(LessonSafeTrainVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.editLessonSafeTrainDel(request));
    }

    @PostMapping("Edit_LessonSafeTrainChoiceDistribute")
    @ApiOperation(value = "安全培训课程-设置企业-分发企业")
    public AjaxResult Edit_LessonSafeTrainChoiceDistribute(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainDisVO request = (LessonSafeTrainDisVO) commonRequest.getRequest(LessonSafeTrainDisVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.editLessonSafeTrainChoiceDistribute(request));
    }

    @PostMapping("Get_LessonSafeTrainDisCompanyList")
    @ApiOperation(value = "安全培训课程-设置企业-查看已分发企业")
    public AjaxResult Get_LessonSafeTrainDisCompanyList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainQueryCompanyRequest request = (LessonSafeTrainQueryCompanyRequest) commonRequest.getRequest(LessonSafeTrainQueryCompanyRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLessonSafeTrainDisCompanyList(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_LessonDisedUserList")
    @ApiOperation(value = "安全培训课程-设置企业-查看已分发学员")
    public AjaxResult Get_LessonDisedUserList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainQueryUserRequest request = (LessonSafeTrainQueryUserRequest) commonRequest.getRequest(LessonSafeTrainQueryUserRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLessonDisedUserList(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_LessonNoDisUserList")
    @ApiOperation(value = "安全培训课程-设置企业-查看待分发学员")
    public AjaxResult Get_LessonNoDisUserList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainQueryUserRequest request = (LessonSafeTrainQueryUserRequest) commonRequest.getRequest(LessonSafeTrainQueryUserRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLessonNoDisUserList(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Edit_LessonSafeTrainChoiceUserDistribute")
    @ApiOperation(value = "安全培训课程-设置企业-分发学员")
    public AjaxResult Edit_LessonSafeTrainChoiceUserDistribute(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainDisVO request = (LessonSafeTrainDisVO) commonRequest.getRequest(LessonSafeTrainDisVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.editLessonSafeTrainChoiceUserDistribute(request));
    }

    @PostMapping("Edit_LessonSafeTrainChoiceUserDistribute_C")
    @ApiOperation(value = "安全培训课程-企业端-分发学员")
    public AjaxResult Edit_LessonSafeTrainChoiceUserDistribute_C(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainDisVO request = (LessonSafeTrainDisVO) commonRequest.getRequest(LessonSafeTrainDisVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.editLessonSafeTrainChoiceUserDistributeC(request));
    }

    @PostMapping("Get_LessonNoDisUserList_C")
    @ApiOperation(value = "安全培训课程-企业端-查询待分发学员")
    public AjaxResult Get_LessonNoDisUserList_C(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainQueryUserRequest request = (LessonSafeTrainQueryUserRequest) commonRequest.getRequest(LessonSafeTrainQueryUserRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLessonNoDisUserListC(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Edit_LessonSafeTrainOnekeyDistribute")
    @ApiOperation(value = "安全培训课程-设置企业-一键分发")
    public AjaxResult Edit_LessonSafeTrainOnekeyDistribute(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainDisVO request = (LessonSafeTrainDisVO) commonRequest.getRequest(LessonSafeTrainDisVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.editLessonSafeTrainOnekeyDistribute(request));
    }

    @PostMapping("Edit_LessonSafeTrain_Copy")
    @ApiOperation(value = "安全培训课程-复制课程")
    public AjaxResult Edit_LessonSafeTrain_Copy(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainVO request = (LessonSafeTrainVO) commonRequest.getRequest(LessonSafeTrainVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.editLessonSafeTrainCopy(request));
    }

    @PostMapping("Get_CopyExamInfo")
    @ApiOperation(value = "安全培训课程-复制课程试卷")
    public AjaxResult Get_CopyExamInfo(@RequestBody CommonRequest commonRequest) {
        GetCopyExamInfoRequest request = (GetCopyExamInfoRequest) commonRequest.getRequest(GetCopyExamInfoRequest.class);
        boolean copyExamInfo = LessonSafeTrainServiceImpl.getCopyExamInfo(request);
        if (copyExamInfo) {
            return AjaxResult.success("复制成功!");
        }
        return AjaxResult.error(1003, "复制失败");
    }

    @PostMapping("Del_UserLessonRecordSafeTrain")
    @ApiOperation(value = "安全培训课程-回收分发学员")
    public AjaxResult Del_UserLessonRecordSafeTrain(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainDisVO request = (LessonSafeTrainDisVO) commonRequest.getRequest(LessonSafeTrainDisVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.delUserLessonRecordSafeTrain(request));
    }

    @PostMapping("Recovery_UserLessonRecordSafeTrain")
    @ApiOperation(value = "安全培训课程-恢复分发学员")
    public AjaxResult Recovery_UserLessonRecordSafeTrain(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainDisVO request = (LessonSafeTrainDisVO) commonRequest.getRequest(LessonSafeTrainDisVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.recoveryUserLessonRecordSafeTrain(request));
    }

    @PostMapping("Edit_LessonSafeTrainCompany_Del")
    @ApiOperation(value = "安全培训课程-删除分发企业")
    public AjaxResult Edit_LessonSafeTrainCompany_Del(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainDisVO request = (LessonSafeTrainDisVO) commonRequest.getRequest(LessonSafeTrainDisVO.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.editLessonSafeTrainCompanyDel(request));
    }

    @PostMapping("Get_LessonSafeTrainList_Bus")
    @ApiOperation(value = "安全培训课程-查询培训计划可添加课程")
    public AjaxResult Get_LessonSafeTrainList_Bus(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainRequest request = (LessonSafeTrainRequest) commonRequest.getRequest(LessonSafeTrainRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLessonSafeTrainListBus(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_LessonSafeTrainListByPlanID_Bus")
    @ApiOperation(value = "安全培训课程-查询培训计划已添加课程")
    public AjaxResult Get_LessonSafeTrainListByPlanID_Bus(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainRequest request = (LessonSafeTrainRequest) commonRequest.getRequest(LessonSafeTrainRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLessonSafeTrainListByPlanIdBus(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Export_LessonSafeTrainUserList")
    @ApiOperation(value = "安全培训课程-企业端-导出课程学员")
    public AjaxResult Export_LessonSafeTrainUserList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainQueryUserRequest request = (LessonSafeTrainQueryUserRequest) commonRequest.getRequest(LessonSafeTrainQueryUserRequest.class);
        LessonSafeTrain lessonSafeTrain = LessonSafeTrainServiceImpl.getById(request.getLessonId());
        PageUtils pageUtils = LessonSafeTrainServiceImpl.getLessonDisedUserList(request, null);
        List list = pageUtils.getList();
        ExcelUtil<UserLessonRecordSafeTrainVO> util = new ExcelUtil<>(UserLessonRecordSafeTrainVO.class);
        return util.exportExcel(list, "安全培训学员管理", lessonSafeTrain.getLessonName() + "学员数据", true);
    }

    @PostMapping("Get_WeiXinCode")
    @ApiOperation(value = "安全培训课程-小程序二维码")
    public AjaxResult Get_WeiXinCode(@RequestBody CommonRequest commonRequest) {
        HashMap request = (HashMap) commonRequest.getRequest(HashMap.class);
        byte[] qrCodeBytes = wxService.generateMiniProgramQRCode(request);
        String base64QRCode = Base64.getEncoder().encodeToString(qrCodeBytes);
        return AjaxResult.success(base64QRCode);
    }

    @PostMapping("Get_SmallProgramCode")
    @ApiOperation(value = "安全培训课程-根据小程序二维码获取参数")
    public AjaxResult Get_SmallProgramCode(@RequestBody CommonRequest commonRequest) {
        String sceneId = (String) commonRequest.getRequest(String.class);
        Object params = wxService.getParamsBySceneId(sceneId);
        if (null != params) {
            return AjaxResult.success(Arrays.asList(params));
        }
        return AjaxResult.success();
    }

    @PostMapping("Get_UserNotDisSafeLessonList")
    @ApiOperation(value = "添加用户获取分发课程")
    public AjaxResult Get_UserNotDisSafeLessonList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainRequest lessonSafeTrainRequest = (LessonSafeTrainRequest) commonRequest.getRequest(LessonSafeTrainRequest.class);
        PageUtils list =LessonSafeTrainServiceImpl.getUserNotDisSafeLessonList(lessonSafeTrainRequest,commonRequest.getPageEntity());
        return AjaxResult.success(list);
    }

    @PostMapping("Get_HomeLessonInfo")
    @ApiOperation(value = "小程序-获取主页课程列表")
    public AjaxResult Get_HomeLessonInfo(@RequestBody CommonRequest commonRequest) {
        GetHomeLessonInfoRequest request = (GetHomeLessonInfoRequest) commonRequest.getRequest(GetHomeLessonInfoRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getHomeLessonInfo(request));
    }

    @PostMapping("Get_LearnRedNum")
    @ApiOperation(value = "小程序-获取主页红点数")
    public AjaxResult Get_LearnRedNum(@RequestBody CommonRequest commonRequest) {
        GetLearnRedNumRequest request = (GetLearnRedNumRequest) commonRequest.getRequest(GetLearnRedNumRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLearnRedNum(request));
    }

    @PostMapping("Get_LessonInfoDetail")
    @ApiOperation(value = "小程序-获取课程详情")
    public AjaxResult Get_LessonInfoDetail(@RequestBody CommonRequest commonRequest) {
        GetLessonInfoDetailRequest request = (GetLessonInfoDetailRequest) commonRequest.getRequest(GetLessonInfoDetailRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLessonInfoDetail(request));
    }

    @PostMapping("GetLessonLearnNum")
    @ApiOperation(value = "小程序-获取课程已学习人数")
    public AjaxResult GetLessonLearnNum(@RequestBody CommonRequest commonRequest) {
        GetLessonLearnNumRequest request = (GetLessonLearnNumRequest) commonRequest.getRequest(GetLessonLearnNumRequest.class);
        return AjaxResult.success(userLessonRecordSafeTrainMongoService.getLessonLearnNum(request));
    }

    @PostMapping("Edit_UserLessonRecordSign")
    @ApiOperation(value = "小程序-课程签名")
    public AjaxResult Edit_UserLessonRecordSign(@RequestBody CommonRequest commonRequest) {
        EditUserLessonRecordSignRequest request = (EditUserLessonRecordSignRequest) commonRequest.getRequest(EditUserLessonRecordSignRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.editUserLessonRecordSign(request));
    }

    @PostMapping("Get_MyLessonInfo")
    @ApiOperation(value = "小程序-我的课程列表")
    public AjaxResult Get_MyLessonInfo(@RequestBody CommonRequest commonRequest) {
        GetMyLessonInfoRequest request = (GetMyLessonInfoRequest) commonRequest.getRequest(GetMyLessonInfoRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getMyLessonInfo(request));
    }

    @PostMapping("Get_UserTrainType")
    @ApiOperation(value = "小程序-获取用户培训类型")
    public AjaxResult Get_UserTrainType(@RequestBody CommonRequest commonRequest) {
        GetUserTrainTypeRequest request = (GetUserTrainTypeRequest) commonRequest.getRequest(GetUserTrainTypeRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getUserTrainType(request));
    }

    @PostMapping("Auth_FaceRecognition")
    @ApiOperation(value = "小程序-课件学习人脸识别")
    public AjaxResult Auth_FaceRecognition(@RequestBody CommonRequest commonRequest) {
        AuthFaceRecognitionRequest request = (AuthFaceRecognitionRequest) commonRequest.getRequest(AuthFaceRecognitionRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.authFaceRecognition(request));
    }

    @PostMapping("Get_UserRedNum")
    @ApiOperation(value = "小程序-获取当前用户红点信息")
    public AjaxResult Get_UserRedNum(@RequestBody CommonRequest commonRequest) {
        GetUserRedNumRequest request = (GetUserRedNumRequest) commonRequest.getRequest(GetUserRedNumRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getUserRedNum(request));
    }

    @PostMapping("Get_UserLessonTrainDetail")
    @ApiOperation(value = "小程序-获取课程用户学习信息")
    @ListNoArray
    public AjaxResult Get_UserLessonTrainDetail(@RequestBody CommonRequest commonRequest) {
        GetUserLessonTrainDetailRequest request = (GetUserLessonTrainDetailRequest) commonRequest.getRequest(GetUserLessonTrainDetailRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getUserLessonTrainDetail(request));
    }

    @PostMapping("Get_LearnCertificate_Pic")
    @ApiOperation(value = "小程序-获取结业证书")
    public AjaxResult Get_LearnCertificate_Pic(@RequestBody CommonRequest commonRequest) {
        GetLearnCertificatePicRequest request = (GetLearnCertificatePicRequest) commonRequest.getRequest(GetLearnCertificatePicRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLearnCertificatePic(request));
    }

    @PostMapping("Get_LessonSafeTrainList_APP")
    @ApiOperation(value = "小程序-工具-企业课程列表")
    public AjaxResult Get_LessonSafeTrainList_APP(@RequestBody CommonRequest commonRequest) {
        GetLessonSafeTrainListAPPRequest request = (GetLessonSafeTrainListAPPRequest) commonRequest.getRequest(GetLessonSafeTrainListAPPRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getLessonSafeTrainListAPP(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_CompanyLessonTrainTypeList_APP")
    @ApiOperation(value = "小程序-工具-企业课程培训类型列表")
    public AjaxResult Get_CompanyLessonTrainTypeList_APP(@RequestBody CommonRequest commonRequest) {
        GetCompanyLessonTrainTypeListAPPRequest request = (GetCompanyLessonTrainTypeListAPPRequest) commonRequest.getRequest(GetCompanyLessonTrainTypeListAPPRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getCompanyLessonTrainTypeListAPP(request));
    }

    @PostMapping("Get_CourseAPP")
    @ApiOperation(value = "小程序-工具-查询课件详情")
    public AjaxResult Get_CourseAPP(@RequestBody CommonRequest commonRequest) {
        GetCourseAPPRequest request = (GetCourseAPPRequest) commonRequest.getRequest(GetCourseAPPRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getCourseAPP(request));
    }

    @PostMapping("Get_UserSurvey")
    @ApiOperation(value = "企业端-查询用户概况信息-培训记录")
    public AjaxResult Get_UserSurvey(@RequestBody CommonRequest commonRequest) {
        GetUserSurveyRequest request = (GetUserSurveyRequest) commonRequest.getRequest(GetUserSurveyRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getUserSurvey(request));
    }

    @PostMapping("Get_UserLessonRecordByUserCode")
    @ApiOperation(value = "企业端-查询用户概况信息-安全培训分页查询")
    public AjaxResult Get_UserLessonRecordByUserCode(@RequestBody CommonRequest commonRequest) {
        GetUserSurveyRequest request = (GetUserSurveyRequest) commonRequest.getRequest(GetUserSurveyRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getUserLessonRecordByUserCode(request,commonRequest.getPageEntity()));
    }

    @PostMapping("Get_UserSafeTrainLessonRecordExport")
    @ApiOperation(value = "企业端-查询用户概况信息-安全培训导出报表")
    public AjaxResult getUserSafeTrainLessonRecordExport(@RequestBody CommonRequest commonRequest) {
        GetUserSurveyRequest request = (GetUserSurveyRequest) commonRequest.getRequest(GetUserSurveyRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.getUserSafeTrainLessonRecordExport(request));
    }

    @PostMapping("UserToCompanyIsEnable")
    @ApiOperation(value = "小程序-工具-查询课件详情")
    public AjaxResult UserToCompanyIsEnable(@RequestBody CommonRequest commonRequest) {
        UserToCompanyIsEnableRequest request = (UserToCompanyIsEnableRequest) commonRequest.getRequest(UserToCompanyIsEnableRequest.class);
        return AjaxResult.success(LessonSafeTrainServiceImpl.userToCompanyIsEnable(request));
    }


}
