package com.fzkj.project.olddata.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("t_g_organization")
@ApiModel(value = "企业表实体")
public class OrganizationEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField("OrganizationName")
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private String companyName;

    @TableField("FID")
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long parentId;

}
