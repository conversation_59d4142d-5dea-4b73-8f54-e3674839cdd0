package com.fzkj.project.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.entity.Comment;
import com.fzkj.project.system.entity.Exam;
import com.fzkj.project.system.request.ExamRequest;
import com.fzkj.project.system.vo.CommentVO;
import com.fzkj.project.system.vo.ExamVO;
import com.fzkj.project.system.vo.GetMyCommentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 评论 Mapper 接口
 * </p>
 *
 */
@Mapper
public interface CommentMapper extends BaseMapper<Comment> {

    List<CommentVO> getCommentSubStanceList(@Param(value = "funType") String funType,
                                            @Param(value = "status") Integer status,
                                            @Param(value = "isValid") Integer isValid,
                                            @Param(value = "targetId") Long targetId,
                                            @Param(value = "userName") String userName,
                                            @Param(value = "keywords") String keywords,
                                            @Param(value = "isReply") Integer isReply,
                                            @Param(value = "teacherId") Long teacherId);

    List<GetMyCommentVO> getMyCommentSubStanceList(String userCode);

    List<GetMyCommentVO> getCommentAppList(Long targetId);


}