<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CompanyLessonSafeTrainSetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.CompanyLessonSafeTrainSet">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="company_name" property="companyName" />
        <result column="lesson_category_id" property="lessonCategoryId" />
        <result column="lesson_category_name" property="lessonCategoryName" />
        <result column="hand_mode" property="handMode" />
        <result column="is_pay_type" property="isPayType" />
        <result column="staff_price" property="staffPrice" />
        <result column="total_time_count" property="totalTimeCount" />
        <result column="is_trusteeship" property="isTrusteeship" />
        <result column="person_num" property="personNum" />
        <result column="s_time" property="sTime" />
        <result column="e_time" property="eTime" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, company_name, lesson_category_id, lesson_category_name, hand_mode, is_pay_type, staff_price, total_time_count, is_trusteeship, person_num, s_time, e_time, source, sort, creator_code, creation_time, revise_code, revise_time, remark, is_valid
    </sql>
    <select id="selectCompanyLessonSafeTrainSetList1" resultType="com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO">
        SELECT
        c.*,
        c.is_valid state,
        c1.area_code AS areaCode,
        c1.area_name AS areaName,
        (SELECT COUNT(1) FROM t_d_lesson_safe_train_company_user_template WHERE is_valid=1 AND company_lesson_pay_id=c.id) AS userTemplate
        FROM
        t_d_company_lesson_safe_train_set c
        LEFT JOIN t_d_company c1 ON c1.id=c.company_id
        <where>
            <if test="companyId != null">
                AND c.company_id = #{companyId}
            </if>
            <if test="state != null">
                AND c.is_valid = #{state}
            </if>
            <if test="lessonCategoryId != null">
                AND c.lesson_category_id = #{lessonCategoryId}
            </if>
        </where>
        ORDER BY c.creation_time DESC
    </select>
    <select id="selectCompanyLessonSafeTrainSetList3" resultType="com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO">
        SELECT
        s.*,
        c.area_code,
        c.area_name,
        COALESCE(
        (
        SELECT GROUP_CONCAT(l.lesson_name SEPARATOR ',')
        FROM t_d_lesson_safe_train_company lsc
        JOIN t_d_lesson_safe_train l ON lsc.lesson_id = l.id
        WHERE lsc.company_id = s.company_id
        AND lsc.is_valid = 1
        AND lsc.lesson_category_id = s.lesson_category_id
        AND SUBSTR(l.lesson_date, 1, 7) = SUBSTR(CURDATE(), 1, 7)
        ),
        ''
        ) AS lessonNames
        FROM
        t_d_company_lesson_safe_train_set s
        JOIN
        (
        SELECT lesson_category_id, total_time_count
        FROM t_d_lesson_safe_train
        WHERE id = #{lessonId}
        ) AS temp ON s.total_time_count = temp.total_time_count AND s.lesson_category_id = temp.lesson_category_id
        left join t_d_company c on c.id=s.company_id
        left join t_d_company_extend ce on ce.company_id=c.id
        WHERE
        s.is_valid = 1
        and c.is_valid = 1
        AND s.company_id NOT IN
        (
        SELECT company_id
        FROM t_d_lesson_safe_train_company
        WHERE lesson_id = #{lessonId} AND is_valid = 1
        )
        <if test="companyId != null">
            AND s.company_id = #{companyId}
        </if>
        AND
        (
        CASE
        WHEN CHAR_LENGTH(s.e_time) > 9
        THEN TIMESTAMPDIFF(DAY, curdate(), STR_TO_DATE(s.e_time, '%Y-%m-%d'))
        ELSE TIMESTAMPDIFF(DAY, curdate(), LAST_DAY(STR_TO_DATE(CONCAT(s.e_time, '-01'), '%Y-%m-%d')))
        END
        ) >= 0
        ORDER BY s.Sort ASC
    </select>
    <select id="selectCompanyLessonSafeTrainSetList2" resultType="com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO">
        select c.*, c1.area_code,
        c1.area_name,ls.is_trusteeship,
        (SELECT COUNT(1) FROM t_d_lesson_safe_train_company_user_template WHERE is_valid=1 AND company_lesson_pay_id=ls.id) AS person_num,
        ls.id companyLessonPayId
        from t_d_lesson_safe_train_company c
        left join t_d_company_lesson_safe_train_set ls on ls.company_id=c.company_id and ls.lesson_category_id=c.lesson_category_id AND ls.is_valid=1
        left join t_d_company c1 on c1.id=c.company_id
        where c.is_valid=1
        <if test="companyId != null">
            AND c.company_id = #{companyId}
        </if>
        <if test="lessonId != null">
            AND c.lesson_id = #{lessonId}
        </if>
        <if test="isTrusteeship != null">
            AND ls.is_trusteeship = #{isTrusteeship}
        </if>
        ORDER BY c.sort ASC
    </select>
    <select id="selectCompanyLessonSafeTrainSetList4" resultType="com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO">
        select c.company_id,c.company_name
        from t_d_company_lesson_safe_train_set c
        where c.is_valid=1
        group by company_id,company_name
    </select>


</mapper>