package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 企业证照
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "企业证照Request")
public class CompanyLicenceRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private Long companyId;

    @ApiModelProperty(value = "")
    private String licenceType;

    @ApiModelProperty(value = "")
    private String licenceUrl;

    @ApiModelProperty(value = "")
    private String fileType;

    @ApiModelProperty(value = "")
    private Integer isLongTerm;

    @ApiModelProperty(value = "")
    private LocalDate expirTime;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "")
    private String creatorCode;

    @ApiModelProperty(value = "")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "")
    private String reviseCode;

    @ApiModelProperty(value = "")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty(value = "")
    private Integer isValid;

    @ApiModelProperty(value = "")
    private String licenceName;

}