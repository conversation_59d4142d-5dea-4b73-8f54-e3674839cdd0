package com.fzkj.common.utils;

import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class DataTransfer {
    /**
     * 可转换支持的特殊类型
     */
    private static final Set<Class<?>> wrapperTypeSet = new HashSet<>();
    /**
     * 字符串分割符
     */
    public static final String MULTIPLE_DELIM = "&&&";
    /**
     * JSON数组判断标识
     */
    public static final String JSON_ARRAY_PREFIX = "[";

    static {
        wrapperTypeSet.add(LocalDateTime.class);
        wrapperTypeSet.add(String.class);
        wrapperTypeSet.add(List.class);
    }

    /**
     * 參考org.springframework.beans.BeanUtils.copyProperties方法进行属性赋值，并扩展支持不同类型属性之间的转换
     *
     * @param source    待转换数据
     * @param classType 转换目标的类型
     * @return 转换目标的类型的对象
     */
    public static Object transfer(Object source, Class classType) {
        if (null == source) {
            return null;
        }
        Object target = null;
        try {
            target = classType.newInstance();
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(target.getClass());
        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            PropertyDescriptor sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), targetPd.getName());
            if (null == writeMethod || null == sourcePd || null == sourcePd.getReadMethod()) {
                continue;
            }
            Method readMethod = sourcePd.getReadMethod();
            Class sourcePropertyClass = readMethod.getReturnType();
            Class targetPropertyClass = writeMethod.getParameterTypes()[0];
            try {
                if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                    readMethod.setAccessible(true);
                }
                Object value = readMethod.invoke(source);
                if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                    writeMethod.setAccessible(true);
                }
                if (ClassUtils.isAssignable(sourcePropertyClass, targetPropertyClass) || null == value) {
                    writeMethod.invoke(target, value);
                } else if (wrapperTypeSet.contains(sourcePropertyClass)) {
                    if (sourcePropertyClass.equals(String.class)) {
                        String sourceString = (String) value;
                        if (targetPropertyClass.equals(List.class)) {
                            List list = null;
                            if (sourceString.startsWith(JSON_ARRAY_PREFIX)) {
                                list = JSONArray.parseArray(sourceString, Class.forName(((ParameterizedTypeImpl) writeMethod.getGenericParameterTypes()[0]).getActualTypeArguments()[0].getTypeName()));
                            } else {
                                list = Arrays.asList(StringUtils.delimitedListToStringArray(sourceString, MULTIPLE_DELIM));
                            }
                            writeMethod.invoke(target, list);
                        } else if (targetPropertyClass.equals(LocalDateTime.class)) {
                            String formatPattern = "yyyy-MM-dd";
                            if(sourceString.length() == 7){
                                formatPattern = "yyyy-MM";
                            }else if (sourceString.length() == 19){
                                formatPattern = "yyyy-MM-dd HH:mm:ss";
                            }
                            Date parse = new SimpleDateFormat(formatPattern).parse(sourceString);
                            LocalDateTime dateTime = LocalDateTime.ofInstant(parse.toInstant(), ZoneId.systemDefault());
                            writeMethod.invoke(target, dateTime);
                        }
                    } else if (sourcePropertyClass.equals(List.class)) {
                        List list = (List) value;
                        String targetString;
                        if (null == list || list.isEmpty()) {
                            targetString = "";
                        }else if (!list.isEmpty() && String.class.equals(((List<?>) value).get(0).getClass())) {
                            targetString = StringUtils.arrayToDelimitedString(list.toArray(), MULTIPLE_DELIM);
                        } else {
                            targetString = JSONArray.toJSONString(list);
                        }
                        writeMethod.invoke(target, targetString);
                    } else if (sourcePropertyClass.equals(LocalDateTime.class)) {
                        LocalDateTime dateTime = (LocalDateTime) value;
                        String targetString = dateTime.toString();
                        writeMethod.invoke(target, targetString);
                    }
                }else if(String.class.equals(targetPropertyClass)){
                    writeMethod.invoke(target, String.valueOf(value));
                }
            } catch (Throwable ex) {
                throw new FatalBeanException(
                        "Could not copy property '" + targetPd.getName() + "' from source to target", ex);
            }
        }
        return target;
    }
    
    /**
     * 循环遍历调用transfer方法，使数据转换支持集合类型。
     *
     * @param sources   待转换数据集合list
     * @param classType 转换目标的类型
     * @return 转换目标的类型的对象list
     */
    public static List transferList(List sources, Class classType) {
        if (null == sources) {
            return null;
        }
        List targetList = new ArrayList();
        for (Object source : sources) {
            Object target = transfer(source, classType);
            targetList.add(target);
        }
        return targetList;
    }
}
