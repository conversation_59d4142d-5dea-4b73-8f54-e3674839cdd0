package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.UserLimit;
import com.fzkj.project.system.vo.AuthTargetVO;
import com.fzkj.project.system.vo.UserLimitVO;
import com.fzkj.project.system.request.UserLimitRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户管理机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface UserLimitService extends IService<UserLimit> {

    String listUserLimit(UserLimitRequest request);

    PageUtils listUserLimitPage(UserLimitRequest request, PageEntity pageEntity);

    PageUtils listByPage(UserLimitRequest request, PageEntity pageEntity);

    UserLimitVO findUserLimit(Long id);

    Long saveUserLimit(UserLimitVO vo);

    boolean updateUserLimit(UserLimitVO vo);

    Boolean removeUserLimit(UserLimitVO userLimitVO);

    public List<AuthTargetVO> loginAuthTargetVO(String targetId);
}
