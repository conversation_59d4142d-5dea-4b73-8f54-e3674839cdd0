package com.fzkj.project.basicdata.entity;

import com.fzkj.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2024-04-01 15:12
 * @Version: 1.0
 */
@Data
public class SubStanceLabelExportVo {
    @Excel(name = "标签名称")
    public String name;
    @Excel(name = "类型")
    public String type;
    @Excel(name = "文章数量")
    public int num;
    @Excel(name = "标签颜色")
    private String color;
}
