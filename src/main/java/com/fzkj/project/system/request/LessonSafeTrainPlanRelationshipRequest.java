package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 给公交分发的培训课程关系表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "给公交分发的培训课程关系表Request")
public class LessonSafeTrainPlanRelationshipRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "计划ID（t_d_lessonsafetrainplan）")
    private Long planId;

    @ApiModelProperty(value = "轨道课程ID（t_d_lessonsafetrain）")
    private Long lessonId;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否 默认有效")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    private String reservedField5;

}