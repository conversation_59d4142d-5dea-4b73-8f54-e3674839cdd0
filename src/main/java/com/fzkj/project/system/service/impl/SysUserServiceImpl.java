package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.aspectj.lang.annotation.DataSource;
import com.fzkj.framework.aspectj.lang.enums.DataSourceType;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.domain.SysUser;
import com.fzkj.project.system.mapper.SysUserMapper;
import com.fzkj.project.system.service.ISysUserService;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);


    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        LambdaQueryWrapper <SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUserName, userName);
        return getOne(queryWrapper);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return getBaseMapper().selectById(userId);
    }

    @Override
    public List<SysUser> list(SysUser user) {
        QueryWrapper <SysUser> queryWrapper = new QueryWrapper<>(user);
        return list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(SysUser user, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        QueryWrapper <SysUser> queryWrapper = new QueryWrapper<>(user);
        return new PageUtils(pageEntity, list(queryWrapper));
    }

}
