<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.ExamQuestionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.ExamQuestion">
        <id column="id" property="id" />
        <result column="exam_id" property="examId" />
        <result column="subject_type" property="subjectType" />
        <result column="subject_name" property="subjectName" />
        <result column="question_type_id" property="questionTypeId" />
        <result column="question_type_name" property="questionTypeName" />
        <result column="score" property="score" />
        <result column="question_option" property="questionOption" />
        <result column="right_option" property="rightOption" />
        <result column="sort" property="sort" />
        <result column="creator_id" property="creatorId" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_id" property="reviseId" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="img" property="img" />
        <result column="problem_analysis" property="problemAnalysis" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, exam_id, subject_type, subject_name, question_type_id, question_type_name, score, question_option, right_option, sort, creator_id, creation_time, revise_id, revise_time, remark, is_valid, img, problem_analysis
    </sql>

</mapper>