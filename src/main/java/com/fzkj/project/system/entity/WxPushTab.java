package com.fzkj.project.system.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("t_d_wx_push_tab")
@ApiModel(value = "WxPushTab", description = "微信推送表")
public class WxPushTab {

    @TableId(value = "id", type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "自增")
    private Long id;

    @JsonProperty("UserName")
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @TableField("user_code")
    @JsonProperty("UserCode")
    @ApiModelProperty(value = "用户编号")
    private String userCode;

    @TableField("open_id")
    @JsonProperty("OpenID")
    @ApiModelProperty(value = "用户微信标识")
    private String openId;

    @TableField("phone")
    @JsonProperty("Phone")
    @ApiModelProperty(value = "手机号")
    private String phone;

    @TableField("reg_id")
    @JsonProperty("RegID")
    @ApiModelProperty(value = "推送设备ID")
    private String regId;

    @TableField("month_str")
    @JsonProperty("MonthStr")
    @ApiModelProperty(value = "月份")
    private String monthStr;

    @TableField("lesson_id")
    @JsonProperty("LessonId")
    @ApiModelProperty(value = "课程ID")
    private Long lessonId;

    @TableField("lesson_name")
    @JsonProperty("LessonName")
    @ApiModelProperty(value = "课程名称")
    private String lessonName;

    @TableField("lesson_category_id")
    @JsonProperty("LessonCategoryID")
    @ApiModelProperty(value = "课程分类ID")
    private Long lessonCategoryId;

    @TableField("lesson_category_name")
    @JsonProperty("LessonCategoryName")
    @ApiModelProperty(value = "课程分类名称")
    private String lessonCategoryName;

    @TableField("train_s_time")
    @JsonProperty("TrainSTime")
    @ApiModelProperty(value = "开始时间")
    private String trainSTime;

    @TableField("train_e_time")
    @JsonProperty("TrainETime")
    @ApiModelProperty(value = "结束时间")
    private String trainETime;

    @TableField("is_pay")
    @JsonProperty("IsPay")
    @ApiModelProperty(value = " 1：已支付(小程序) 0：未支付(H5)")
    private Integer isPay;

    @TableField("template_code")
    @JsonProperty("TemplateCode")
    @ApiModelProperty(value = "模板编号")
    private Integer templateCode;

    @TableField("is_wx_send")
    @JsonProperty("IsWxSend")
    @ApiModelProperty(value = "微信推送结果 0待推送 1成功 -1失败")
    private Integer isWxSend;

    @TableField("is_mobile")
    @JsonProperty("IsMobile")
    @ApiModelProperty(value = "移动推送结果 0待推送 1成功 -1失败")
    private Integer isMobile;

    @TableField("is_sms")
    @JsonProperty("IsSms")
    @ApiModelProperty(value = "短信推送结果 0待推送 1成功 -1失败")
    private Integer isSms;

    @TableField("is_del")
    @JsonProperty("IsDel")
    @ApiModelProperty(value = "是否删除 1删除 0有效")
    private Integer isDel;

    @TableField("type")
    @JsonProperty("Type")
    @ApiModelProperty(value = "类型（0：课程，1：运管文件）")
    private Integer type;

    @TableField("plan_id")
    @JsonProperty("PlanID")
    private Long planId;

    @TableField("url_type")
    @JsonProperty("UrlType")
    private String urlType;

    @TableField(exist = false)
    @JsonIgnore
    private Long userId;

    @TableField(exist = false)
    @JsonIgnore
    private String trainType;
}
