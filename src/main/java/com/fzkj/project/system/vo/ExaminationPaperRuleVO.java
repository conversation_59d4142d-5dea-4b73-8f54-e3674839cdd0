package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 随机组卷规则表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value="随机组卷规则表VO")
public class ExaminationPaperRuleVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "考试ID")
    @NotNull(message = "考试ID不能为空")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "试题类型ID")
    @NotNull(message = "试题类型ID不能为空")
    @JsonProperty("QuestionTypeId")
    private Integer questionTypeId;

    @ApiModelProperty(value = "试题类型名称")
    @NotNull(message = "试题类型名称不能为空")
    @JsonProperty("QuestionTypeName")
    private String questionTypeName;

    @ApiModelProperty(value = "题目数")
    @NotNull(message = "题目数不能为空")
    @JsonProperty("Num")
    private Integer num;

    @ApiModelProperty(value = "分数")
    @NotNull(message = "分数不能为空")
    @JsonProperty("Sorce")
    private Integer sorce;

    @JsonProperty("Flag")
    private String flag;
}