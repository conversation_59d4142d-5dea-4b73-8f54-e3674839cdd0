package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CourseRecordVO {

    @ApiModelProperty("课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty("课程分类ID")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty("课件ID")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty("课件名称")
    @JsonProperty("CourseName")
    private String courseName;

    @ApiModelProperty("文件类型")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty("文件URL")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty("课程完成状态")
    @JsonProperty("CourseIsComplete")
    private String courseIsComplete;

    @ApiModelProperty("企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty("企业名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty("部门ID")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty("岗位ID")
    @JsonProperty("WorkID")
    private Long workId;

    @ApiModelProperty("用户编码")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty("是否静态")
    @JsonProperty("IsStatic")
    private Integer isStatic;

    @ApiModelProperty("学习时间计数")
    @JsonProperty("StudyTimeCount")
    private int studyTimeCount;

    @ApiModelProperty("时间计数")
    @JsonProperty("TimeCount")
    private int timeCount;

    @ApiModelProperty("人脸识别字符串")
    @JsonProperty("FaceRecognitionString")
    private String faceRecognitionString;

    @ApiModelProperty("人脸识别图片")
    @JsonProperty("FaceDistinguishImg")
    private String faceDistinguishImg;

    @ApiModelProperty("开始学习时间")
    @JsonProperty("StartStudyTime")
    private String startStudyTime;

    @ApiModelProperty("完成时间")
    @JsonProperty("CompleteTime")
    private String completeTime;

    @ApiModelProperty("来源")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty("排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty("创建者编码")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty("创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty("修订者编码")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty("修订时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty("备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty("是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty("来源")
    @JsonProperty("DisSource")
    private Integer disSource;

    @ApiModelProperty("模式")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty("预留字段1")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty("预留字段2")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty("预留字段3")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty("预留字段4")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty("预留字段5")
    @JsonProperty("ReservedField5")
    private String reservedField5;

}
