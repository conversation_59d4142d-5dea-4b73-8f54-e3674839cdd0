package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetUserLessonTrainDetailVO {


    @ApiModelProperty(value = "课程ID", example = "4423")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课程名称", example = "市租投标演示课程")
    @JsonProperty("LessonName")
    private String lessonName;

    @ApiModelProperty(value = "用户ID", example = "90802")
    @JsonProperty("UserID")
    private Long userId;

    @ApiModelProperty(value = "身份证号", example = "500228199306271018")
    @JsonProperty("IDCard")
    private String idCard;

    @ApiModelProperty(value = "用户名", example = "测试账号")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "考试ID", example = "25621")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "培训类型", example = "aqpx")
    @JsonProperty("TrainType")
    private String trainType;

    @ApiModelProperty(value = "培训类型名称", example = "安全培训")
    @JsonProperty("TrainTypeName")
    private String trainTypeName;

    @ApiModelProperty(value = "开始学习时间", example = "2024-03-31 15:40:32")
    @JsonProperty("StartStudyTime")
    private String startStudyTime;

    @ApiModelProperty(value = "完成时间", example = "2024-03-31 15:44:35")
    @JsonProperty("CompleteTime")
    private String completeTime;

    @ApiModelProperty(value = "是否完成", example = "1")
    @JsonProperty("IsComplete")
    private Integer isComplete;

    @ApiModelProperty(value = "是否完成字符串", example = "已完成")
    @JsonProperty("IsCompleteStr")
    private String isCompleteStr;

    @ApiModelProperty(value = "签名URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2023-11/image/169960956171731996.png")
    @JsonProperty("SignUrl")
    private String signUrl;

    @ApiModelProperty(value = "人脸图片URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2024-3/image/171187083158501075.jpg,https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2024-3/image/171187589616663090.jpg")
    @JsonProperty("FaceImgUrls")
    private String faceImgUrls;

    @ApiModelProperty(value = "分数", example = "100")
    @JsonProperty("Score")
    private int score;

    @ApiModelProperty(value = "考试状态", example = "1")
    @JsonProperty("ExamStatus")
    private int examStatus;

    @ApiModelProperty(value = "考试状态字符串", example = "合格（100分）")
    @JsonProperty("ExamStatusStr")
    private String examStatusStr;

    @ApiModelProperty(value = "考试签名URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2023-11/image/169960956171731996.png")
    @JsonProperty("ExamSignUrl")
    private String examSignUrl;

    @ApiModelProperty(value = "考试结束时间", example = "2024-03-31 17:05:45")
    @JsonProperty("ExamEndTime")
    private String examEndTime;

    @ApiModelProperty(value = "使用时间", example = "43")
    @JsonProperty("UseTimeCount")
    private int useTimeCount;

    @ApiModelProperty(value = "保留字段1", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/pc/file/913591709191800617.png")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "静态状态", example = "1")
    @JsonProperty("StaticStatus")
    private String staticStatus;

    @ApiModelProperty(value = "形状", example = "1")
    @JsonProperty("Shape")
    private String shape;

    @ApiModelProperty(value = "是否在线考试", example = "0")
    @JsonProperty("IsOnlineExam")
    private int isOnlineExam;

    @ApiModelProperty(value = "计划ID", example = "null")
    @JsonProperty("PlanID")
    private String planID;

    @ApiModelProperty(value = "计划名称", example = "null")
    @JsonProperty("PlanName")
    private String planName;

    @ApiModelProperty(value = "计划级别", example = "null")
    @JsonProperty("PlanLevel")
    private String planLevel;
}
