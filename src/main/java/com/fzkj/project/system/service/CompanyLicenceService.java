package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.CompanyLicence;
import com.fzkj.project.system.vo.CompanyLicenceVO;
import com.fzkj.project.system.request.CompanyLicenceRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 企业证照 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CompanyLicenceService extends IService<CompanyLicence> {

    List<CompanyLicence> listCompanyLicence(CompanyLicenceRequest request);

    PageUtils listByPage(CompanyLicenceRequest request, PageEntity pageEntity);

    CompanyLicenceVO findCompanyLicence(Long id);

    Long saveCompanyLicence(CompanyLicenceVO vo);

    boolean updateCompanyLicence(CompanyLicenceVO vo);

    boolean removeCompanyLicence(Long id);
}
