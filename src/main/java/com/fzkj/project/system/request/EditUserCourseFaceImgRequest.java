package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EditUserCourseFaceImgRequest {
    @ApiModelProperty(value = "用户编码")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课件ID")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty(value = "课件ID")
    @JsonProperty("FaceImgUrl")
    private String faceImgUrl;
}
