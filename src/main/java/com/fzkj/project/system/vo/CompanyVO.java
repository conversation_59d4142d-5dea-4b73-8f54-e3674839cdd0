package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 企业表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="企业表VO")
public class CompanyVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("FID")
    private Long fid;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ParentID")
    private Long parentId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Ancestors")
    private String ancestors;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("OrgCode")
    private String orgCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("DeptType")
    @ApiModelProperty(value = "部门类型（1主营公司，2部门，3分公司，4其他，0集团）")
    private String deptType;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("AreaCode")
    private String areaCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("AreaName")
    private String areaName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("AbbrName")
    private String abbrName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Address")
    private String address;

    @JsonProperty("Leader")
    @ApiModelProperty(value = "负责人")
    private String leader;

    @JsonProperty("Phone")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Email")
    private String email;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CompanyLabel")
    private String companyLabel;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("OperPlatform")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("Name")
    private String name;

    @JsonProperty("UpCompanyName")
    private String upCompanyName;

    @JsonProperty("UserNumber")
    private Integer userNumber;

    @JsonProperty("WorkNumber")
    private Integer workNumber;

    @JsonProperty("CompanyID")
    private Long companyId;

    @JsonProperty("DepartID")
    private Long departId;

    @JsonProperty("Flag")
    private String flag;

    private int isNext;

    List<CompanyVO> children;
}