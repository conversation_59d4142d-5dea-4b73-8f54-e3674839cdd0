// service实现类
package com.fzkj.project.wxPay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.project.basicdata.mapper.OrderMapper;
import com.fzkj.project.system.service.UserLessonRecordSafeTrainMongoService;
import com.fzkj.project.wxPay.config.WxPayConfig;
import com.fzkj.project.wxPay.dao.WxPayDto;
import com.fzkj.project.wxPay.dto.Order;
import com.fzkj.project.wxPay.service.WxPayService;
import com.fzkj.project.wxPay.utils.*;
import com.github.wxpay.sdk.WXPay;
import com.github.wxpay.sdk.WXPayUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class WxPayServiceImpl implements WxPayService {
    public static final String NOTIFY_URL = WxPayConfig.getNoticeUrl();//必须是外网地址，带域名的
    public static final String TRADE_TYPE_APP = "JSAPI";//类型 APP、JSAPI......
    @Value("${weixin.private-key-path}")
    public String url;

    private final OrderMapper orderMapper;
    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;

    @Override
    public String payBack(String resXml) {
        WXConfigUtil config = null;
        try {
            config = new WXConfigUtil();
        } catch (Exception e) {
            e.printStackTrace();
        }
        WXPay wxpay = new WXPay(config);
        String xmlBack = "";
        Map<String, String> notifyMap = null;
        try {
            notifyMap = WXPayUtil.xmlToMap(resXml);         // 调用官方SDK转换成map类型数据
            if (wxpay.isPayResultNotifySignatureValid(notifyMap)) {//验证签名是否有效，有效则进一步处理

                String return_code = notifyMap.get("return_code");//状态
                String out_trade_no = notifyMap.get("out_trade_no");//商户订单号
                if (return_code.equals("SUCCESS")) {
                    if (out_trade_no != null) {
                        // 注意特殊情况：订单已经退款，但收到了支付结果成功的通知，不应把商户的订单状态从退款改成支付成功
                        // 注意特殊情况：微信服务端同样的通知可能会多次发送给商户系统，所以数据持久化之前需要检查是否已经处理过了，处理了直接返回成功标志
                        //业务数据持久化
                        System.err.println("微信手机支付回调成功订单号:{}");
                        System.err.println(out_trade_no);
                        Order order = orderMapper.selectOne(new QueryWrapper<Order>().eq("order_num", out_trade_no));
                        if (!order.getStatus().equals(2)) {
                            order.setStatus(1);
                            orderMapper.updateById(order);
                        }
                        xmlBack = "<xml>" + "<return_code><![CDATA[SUCCESS]]></return_code>" + "<return_msg><![CDATA[OK]]></return_msg>" + "</xml> ";
                    } else {
                        System.err.println("微信手机支付回调失败订单号:{}");
                        System.err.println(out_trade_no);
                        xmlBack = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
                    }
                }
                return xmlBack;
            } else {
                // 签名错误，如果数据里没有sign字段，也认为是签名错误
                //失败的数据要不要存储？
                System.err.println("手机支付回调通知签名错误");
                xmlBack = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
                return xmlBack;
            }
        } catch (Exception e) {
            System.err.println("手机支付回调通知签名错误");
            System.err.println(e);
            xmlBack = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
        }
        return xmlBack;
    }

    @Override
    public Map doUnifiedOrder(HttpServletRequest request, WxPayDto wxPayDto) throws Exception {
        String body = "课程购买";
        double price = 1;
        String openId = wxPayDto.getOpenId();
        //商品名称
        String total_fee = String.valueOf(new BigDecimal(price).multiply(new BigDecimal(1)).intValue());
        //组装参数，用户生成统一下单接口的签名
        WXConfigUtil config = new WXConfigUtil();
        Map<String, String> packageParams = new HashMap<String, String>();
        packageParams.put("appid", config.getAppID());
        packageParams.put("mch_id", config.getMchID());
        String s = WXPayUtil.generateNonceStr();
        packageParams.put("nonce_str", s);
        packageParams.put("body", body);
        Random random = new Random();
        String s1 = System.currentTimeMillis() + ""+random.nextInt(20);
        packageParams.put("out_trade_no", s1);//商户订单号
        packageParams.put("total_fee", total_fee);//支付金额，这边需要转成字符串类型，否则后面的签名会失败
        packageParams.put("notify_url", NOTIFY_URL);//支付成功后的回调地址
        packageParams.put("trade_type", TRADE_TYPE_APP);//支付方式
        packageParams.put("openid", openId);

        String prestr = PayUtil.createLinkString(packageParams); // 把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串

        //MD5运算生成签名，这里是第一次签名，用于调用统一下单接口
        String mysign = PayUtil.sign(prestr, config.getKey(), "utf-8").toUpperCase();

        //拼接统一下单接口使用的xml数据，要将上一步生成的签名一起拼接进去
        String xml = "<xml>" + "<appid>" + config.getAppID() + "</appid>"
                + "<body><![CDATA[" + body + "]]></body>"
                + "<mch_id>" + config.getMchID() + "</mch_id>"
                + "<nonce_str>" + s + "</nonce_str>"
                + "<notify_url>" + NOTIFY_URL + "</notify_url>"
                + "<openid>" + openId + "</openid>"
                + "<out_trade_no>" + s1 + "</out_trade_no>"
                + "<total_fee>" + total_fee + "</total_fee>"
                + "<trade_type>" + TRADE_TYPE_APP + "</trade_type>"
                + "<sign>" + mysign + "</sign>"
                + "</xml>";

        System.out.println("调试模式_统一下单接口 请求XML数据：" + xml);
        //调用统一下单接口，并接受返回的结果
        String res = PayUtil.httpRequest("https://api.mch.weixin.qq.com/pay/unifiedorder", "POST", xml);

        System.out.println("调试模式_统一下单接口 返回XML数据：" + res);

        // 将解析结果存储在HashMap中   
        Map map = PayUtil.doXMLParse(res);

        String return_code = (String) map.get("return_code");//返回状态码

        Map<String, String> result = new HashMap<String, String>();//返回给小程序端需要的参数
        String prepay_id = null;
        if (return_code == "SUCCESS" || return_code.equals(return_code)) {
            //创建订单
            Order order = new Order();
            order.setOpenId(openId);
            order.setUserCode(wxPayDto.getUserCode());
            order.setOrderNum(s1);
            order.setType(0);
            order.setMoney(price);
            if (!ObjectUtils.isEmpty(wxPayDto.getDataId())) {
                order.setDataId(wxPayDto.getDataId());
            }
            order.setStatus(0);
            order.setCreateTime(new Date());
            orderMapper.insert(order);
            prepay_id = (String) map.get("prepay_id");//返回的预付单信息   

            result.put("nonceStr", s);
            result.put("package", "prepay_id=" + prepay_id);
            Long timeStamp = System.currentTimeMillis() / 1000;
            result.put("timeStamp", timeStamp + "");//这边要将返回的时间戳转化成字符串，不然小程序端调用wx.requestPayment方法会报签名错误
            //拼接签名需要的参数
            String stringSignTemp = "appId=" + config.getAppID() + "&nonceStr=" + s + "&package=prepay_id" +
                    "=" + prepay_id + "&signType=MD5&timeStamp=" + timeStamp;
            //再次签名，这个签名用于小程序端调用wx.requesetPayment方法
            String paySign = PayUtil.sign(stringSignTemp, config.getKey(), "utf-8").toUpperCase();
            result.put("signType", "MD5");
            result.put("paySign", paySign);
            result.put("out_trade_no", s1);
        }
        result.put("appid", config.getAppID());
        return result;
    }

    @Override
    public Boolean payOk(WxPayDto wxPayDto) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>()
                .eq("order_num", wxPayDto.getOrderNum()));
        if (order.getStatus().equals(1)) {
            userLessonRecordSafeTrainMongoService.updateIsPayById(0, order.getDataId());
            return true;
        }
        return false;
    }

    @Override
    public AjaxResult wxRefund(WxPayDto wxPayDto) {
        System.out.println(wxPayDto);
        Order order = orderMapper.selectById(wxPayDto.getId());
        SortedMap<String, String> params = new TreeMap<>();
        // 公众账号ID
        params.put("appid", WXConfigUtil.APP_ID);
        // 商户号
        params.put("mch_id", WXConfigUtil.MCH_ID);
        // 随机字符串
        String s = WXPayUtil.generateNonceStr();
        params.put("nonce_str", s);
        // 商户订单号
        params.put("out_trade_no", order.getOrderNum());
        //退款单号
        Random random = new Random();
        String s1 = System.currentTimeMillis() + "" + random.nextInt(20);
        params.put("out_refund_no", s1);
        BigDecimal bigDecimal = new BigDecimal("1");
        String format = String.format("%.0f", bigDecimal);
        //总金额
        params.put("total_fee", format);
        //退款金额
        params.put("refund_fee", format);
        //
        params.put("notify_url", NOTIFY_URL);
        // 签名
        String prestr = PayUtil.createLinkString(params); // 把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串

        //MD5运算生成签名，这里是第一次签名，用于调用统一下单接口
        String mysign = PayUtil.sign(prestr, WXConfigUtil.KEY, "utf-8").toUpperCase();
        params.put("sign", mysign);
        String data = PaymentUtils.mapToXml(params);
        System.out.println("xml数据格式：" + data);
        // 微信退款需要证书
        CloseableHttpClient httpClient = null;
        try {
            httpClient = HttpUtil.sslHttpsClient(url,
                    WXConfigUtil.MCH_ID);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 向微信发起退款
        String responseXml = HttpUtil.sendSslXmlPost("https://api.mch.weixin.qq.com/secapi/pay/refund", data, null, httpClient);
        Map<String, String> responseMap = PaymentUtils.xmlToMap(responseXml);
        Assert.notNull(responseMap, ExceptionMessage.XML_DATA_INCORRECTNESS.getMessage());
        // return_code为微信返回的状态码，SUCCESS表示申请退款成功，return_msg 如非空，为错误原因 签名失败 参数格式校验错误
        if ("SUCCESS".equalsIgnoreCase(responseMap.get("return_code"))) {
            System.out.println(1111111);
        }
//            log.info("wx refund success response:{}", responseMap);
//            // 修改订单状态为退款保存退款订单等操作
//            order.setStatus(2);
//            orderMapper.updateById(order);
//            if (order.getType().equals(1)) {
//                userLessonRecordSafeTrainMapper.updateIsPayById(0,order.getDataId());
//            }else {
//                userLessonRecordSafeTrainHistoryMapper.updateIsPayById(0,order.getDataId());
//            }
//            return AjaxResult.success();
//        }
        log.error("wx refund error response:{}", responseMap);
        return AjaxResult.success();
    }

}

