package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 企业自建课件分类
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="企业自建课件分类VO")
public class CourseCategoryOneselfVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "分类名称(企业自建课件分类)")
    @NotNull(message = "分类名称(企业自建课件分类)不能为空")
    @JsonProperty("CategoryName")
    private String categoryName;

    @ApiModelProperty(value = "是否显示（1：显示，0：未显示）")
    @NotNull(message = "是否显示（1：显示，0：未显示）不能为空")
    @JsonProperty("IsShow")
    private Integer isShow;

    @ApiModelProperty(value = "创建人姓名")
    @NotNull(message = "创建人姓名不能为空")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "修改人姓名")
    @NotNull(message = "修改人姓名不能为空")
    @JsonProperty("ReviseName")
    private String reviseName;

    @ApiModelProperty(value = "来源（1：行业版）")
    @NotNull(message = "来源（1：行业版）不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    @NotNull(message = "创建人编码不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    @NotNull(message = "修改人编码不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1是 0否 默认有效")
    @NotNull(message = "是否有效 1是 0否 默认有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1")
    @NotNull(message = "预留字段1不能为空")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    @NotNull(message = "预留字段2不能为空")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    @NotNull(message = "预留字段3不能为空")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @NotNull(message = "预留字段4不能为空")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @NotNull(message = "预留字段5不能为空")
    @JsonProperty("ReservedField5")
    private String reservedField5;

}