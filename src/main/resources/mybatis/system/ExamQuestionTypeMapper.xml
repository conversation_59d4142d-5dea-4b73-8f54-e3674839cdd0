<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.ExamQuestionTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.ExamQuestionType">
        <id column="question_type_id" property="questionTypeId" />
        <result column="type_name" property="typeName" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_name" property="creatorName" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
        <result column="is_valid" property="isValid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        question_type_id, type_name, creator_id, creator_name, create_date, update_date, is_valid
    </sql>

</mapper>