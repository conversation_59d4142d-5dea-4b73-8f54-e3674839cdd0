package com.fzkj.project.system.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 课件信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "课件分类信息表查询Request")
public class CourseCategoryQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "关键字")
    private String keywords;

    @ApiModelProperty("是否展示")
    private Long isShow;

    @ApiModelProperty("是否有效")
    private Long isValid;

    @ApiModelProperty("是否有效")
    private Long isVaild;

    @ApiModelProperty("操作标识")
    private String flag;

}