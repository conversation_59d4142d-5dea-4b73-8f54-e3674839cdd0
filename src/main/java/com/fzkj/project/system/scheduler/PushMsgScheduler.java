package com.fzkj.project.system.scheduler;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import com.fzkj.project.system.request.LessonSafeTrainCompanyStatisticRequest;
import com.fzkj.project.system.service.*;
import com.fzkj.project.system.vo.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Configuration
@RequiredArgsConstructor
@Transactional
public class PushMsgScheduler {
    @Value("${app.profile}")
    private String uploadPath;
    @Value("${scheduled.run}")
    private Boolean run;
    private final Logger log = LoggerFactory.getLogger(PushMsgScheduler.class);
    private final static String DISTRIBUTE_KEY = "distribute_task";
    private final static String EXPIRE_KEY = "expire_task";
    private final static String MONTH_KEY = "month_task";
    private final static String WEEK_KEY = "week_task";
    private final static String HISTORY_KEY = "history_task";
    private final ReminderService reminderService;
    private final WxPushTabService wxPushTabService;
    private final PushCenterMsgService pushCenterMsgService;
    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;
    private final UserInfoService userInfoService;
    private final LessonSafeTrainCompanyService lessonSafeTrainCompanyService;
    private final LessonSafeTrainPlanService lessonSafeTrainPlanService;
    private final LogSendEmailInfoService logSendEmailInfoService;
    private final WxService wxService;
    private final RedisCache redisCache;
    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 课程分发消息提醒 实时提醒
     *
     * @return
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void distribute() {
        if (!run){
            return;
        }
        Boolean lock = redisCache.getLock(DISTRIBUTE_KEY, "true", 600);
        if (lock) {
            try {
                //是否有分发提醒设置
                Reminder reminder = reminderService.findReminderByTrainTypAndRemindType(1, 2);
                //如果有推送提醒，且值的长度为2,则标记当前时间段可允许推送，否则默认10-22点可以推送
                int start = 10;
                int end = 22;
                if (reminder != null) {
                    String[] split = reminder.getVal().split(",|，");
                    if (split.length == 2) {
                        start = Integer.valueOf(split[0]);
                        end = Integer.valueOf(split[1]);
                    }
                }
                //如果当前时间不在start，end之间，则直接return
                int hour = LocalDateTime.now().getHour();
                if (hour < start || hour >= end) {
                    return;
                }
                //如果当前时间在start，end之间，则推送消息
                //获取待推送的消息
                LambdaQueryWrapper<WxPushTab> queryWrapper = Wrappers.lambdaQuery(WxPushTab.class)
                        .eq(WxPushTab::getIsDel, 0)
                        .and(wrapper -> wrapper.eq(WxPushTab::getIsWxSend, 0).or().eq(WxPushTab::getIsMobile, 0))
                        .le(WxPushTab::getTrainSTime, DateUtils.parseDateToStr("yyyy-MM-dd", new Date()));
                List<WxPushTab> list = wxPushTabService.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    log.info("课程分发消息提醒-无课程分发内容");
                } else {
                    //从redis中获取已经在处理的ids
                    List<Object> distributeIds = redisCache.getCacheList("distribute_ids");
                    if (CollectionUtils.isNotEmpty(distributeIds)) {
                        list = list.stream().filter(item -> !distributeIds.contains(item.getId())).collect(Collectors.toList());
                    }
                    if (CollectionUtils.isEmpty(list)) {
                        return;
                    }
                    redisCache.setCacheList("distribute_ids", list.stream().map(WxPushTab::getId).collect(Collectors.toList()));
                    List<String> userCodes = list.stream().map(WxPushTab::getUserCode).collect(Collectors.toList());
                    LambdaQueryWrapper<UserInfo> userInfoLambdaQueryWrapper = Wrappers.lambdaQuery(UserInfo.class);
                    userInfoLambdaQueryWrapper.in(UserInfo::getUserCode, userCodes);
                    List<UserInfo> userInfos = userInfoService.list(userInfoLambdaQueryWrapper);
                    Map<String, Long> userIdMap = userInfos.stream().collect(Collectors.toMap(UserInfo::getUserCode, UserInfo::getId));
                    List<PushCenterMsg> msgs = new ArrayList<>();
                    list.forEach(item -> {
                        PushCenterMsg msg = new PushCenterMsg();
                        msg.setUserId(userIdMap.get(item.getUserCode()));
                        msg.setUserCode(item.getUserCode());
                        msg.setTitle("课程分发提醒");
                        msg.setContent("温馨提示：您的课程《" + item.getLessonName() + "》已分发" + (StringUtils.isNotEmpty(item.getTrainETime()) ? "，结束日期：" +
                                DateUtils.formatDate(item.getTrainETime(), "yyyy-MM-dd", "yyyy年MM月dd日") : "") + "，请尽快完成学习和考试。");
                        msg.setPushState(1);
                        msg.setTargetId(item.getLessonId());
                        msg.setIsRead(0);
                        msg.setUrl("src/pages/learn/learn?id=" + item.getLessonId() + "&type=" + "aqpx" + "&planid=" + item.getPlanId());
                        msg.setDeviceNumber("");
                        msg.setPushTime(StringUtils.isNotEmpty(item.getTrainSTime()) ? item.getTrainSTime() : DateUtils.dateTime(new Date()));
                        msg.setFunType(4);
                        msg.setFunTypeChild("401");
                        msg.setCreatorTime(LocalDateTime.now());
                        msgs.add(msg);
                        if (item.getIsWxSend() == 0) {//加入推送队列
                            redisCache.sendMessage("wxPushTabTrain", item);
                        }
                    });
                    //插入消息中心
                    pushCenterMsgService.saveBatch(msgs);
                }
            } finally {
                redisCache.releaseLock(DISTRIBUTE_KEY, "true");
            }
        }
    }

    /**
     * 课程临期消息提醒 每天早上10点执行
     *
     * @return
     */
    @Scheduled(cron = "0 0 10 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void expireSoon() {
        if (!run){
            return;
        }
        Boolean lock = redisCache.getLock(EXPIRE_KEY, "true", 600);
        if (lock) {
            try {
                String expireDays = "1,2,3";
                Reminder reminder = reminderService.findReminderByTrainTypAndRemindType(1, 1);
                if (null != reminder && StringUtils.isNotEmpty(reminder.getVal().trim())) {
                    expireDays = reminder.getVal().replace("，", ",");
                }
                // 查询并返回结果
                List<UserLessonRecordSafeTrain> list = userLessonRecordSafeTrainMongoService.getExpireLesson(expireDays);

                if (CollectionUtils.isEmpty(list)) {
                    log.info("课程临期消息提醒-无临期提醒内容");
                } else {
                    List<WxPushTab> wxPushTabs = new ArrayList<>();
                    list.forEach(item -> {
                        WxPushTab wxPushTab = new WxPushTab();
                        wxPushTab.setUserId(item.getUserId());
                        wxPushTab.setUserCode(item.getUserCode());
                        wxPushTab.setUserName(item.getUserName());
                        wxPushTab.setOpenId("");
                        wxPushTab.setLessonId(item.getLessonId());
                        wxPushTab.setLessonName(item.getLessonName());
                        wxPushTab.setLessonCategoryId(item.getLessonCategoryId());
                        wxPushTab.setLessonCategoryName(item.getLessonCategoryName());
                        wxPushTab.setTrainETime(DateUtils.formatDate(item.getTrainEndTime(), "yyyy-MM-dd", "yyyy年MM月dd日"));
                        wxPushTab.setTrainType("aqpx");
                        wxPushTab.setPlanId(0L);
                        wxPushTabs.add(wxPushTab);
                    });
                    //根据userCode列表查询对应OpenId
                    List<String> userCodes = wxPushTabs.stream().map(WxPushTab::getUserCode).collect(Collectors.toList());
                    LambdaQueryWrapper<UserInfo> userInfoLambdaQueryWrapper = Wrappers.lambdaQuery(UserInfo.class);
                    userInfoLambdaQueryWrapper.in(UserInfo::getUserCode, userCodes);
                    List<UserInfo> userInfos = userInfoService.list(userInfoLambdaQueryWrapper);
                    Map<String, String> openIdMap = userInfos.stream().filter(item -> StringUtils.isNotBlank(item.getPublicOpenId())).collect(Collectors.toMap(UserInfo::getUserCode, UserInfo::getPublicOpenId));
                    List<PushCenterMsg> msgs = new ArrayList<>();
                    wxPushTabs.forEach(item -> {
                        String openId = openIdMap.get(item.getUserCode());
                        PushCenterMsg msg = new PushCenterMsg();
                        msg.setPushId(0L);
                        msg.setIsRead(0);
                        msg.setDeviceNumber("");
                        msg.setFunType(4);
                        msg.setFunTypeChild("402");
                        msg.setCreatorTime(LocalDateTime.now());
                        msg.setUserId(item.getUserId());
                        msg.setUserCode(item.getUserCode());
                        msg.setTitle("课程临期提醒");
                        msg.setContent("温馨提示：您的课程《" + item.getLessonName() + "》即将到期，结束日期：" + item.getTrainETime() + "，请尽快完成学习或考试。");
                        msg.setTargetId(item.getLessonId());
                        msg.setUrl("src/pages/learn/learn?id=" + item.getLessonId() + "&type=" + item.getTrainType() + "&planid=" + item.getPlanId());
                        msg.setPushTime(DateUtils.dateTime(new Date()));
                        msgs.add(msg);
                        if (StringUtils.isNotEmpty(openId)) {
                            item.setOpenId(openId);
                            String pushInfo = item.getOpenId() + "|温馨提示：" + item.getUserName() + "，您有一个课程即将到期|" + com.fzkj.common.utils.StringUtils.getStringInTwenty(item.getLessonName()) + "|" + item.getTrainETime()
                                    + "|点击详情进入学习|小程序Appid|" + "src/pages/learn/learn?id=" + item.getLessonId() + "&type=" + item.getTrainType() + "&planid=" + item.getPlanId() + "|";
                            WxMessage wxMessage = new WxMessage();
                            wxMessage.setCode("2");
                            wxMessage.setData(pushInfo);
                            wxMessage.setOpenId(item.getOpenId());
                            boolean sent = wxService.sendWxMessage(wxMessage);
                            if (sent) {
                                msg.setPushState(1);
                            } else {
                                msg.setPushState(0);
                            }
                        } else {//没有微信标识，不发送
                            msg.setPushState(2);
                        }
                    });
                    //插入消息中心
                    pushCenterMsgService.saveBatch(msgs);

                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                redisCache.releaseLock(EXPIRE_KEY, "true");
            }
        }
    }

    /**
     * 每月统计数据消息提醒 每月1、2号早上10点执行
     *
     * @return
     */
    @Scheduled(cron = "0 0 10,11,12,13,14 1 * ?")
    @Transactional(rollbackFor = Exception.class)
    public void monthStatistics() {
        if (!run){
            return;
        }
        Boolean lock = redisCache.getLock(MONTH_KEY, "true", 600);
        if (lock) {
            try {
                String lastMonth = DateUtils.getLastMonth();
                List<UserManageVO> users = userInfoService.getSendUserInfo(lastMonth);
                if (CollectionUtils.isEmpty(users)) {
                    log.info("每月统计数据消息提醒-不存在可推送的用户");
                    return;
                }
                List<LessonInfoVO> totalLessons = lessonSafeTrainCompanyService.getLessonInfo(lastMonth);
                String monthStr = DateUtils.formatDate(lastMonth, "yyyy-MM", "yyyy年MM月");
                List<StatisticsFileVO> statisticsFileVOS = new ArrayList();
                for (UserManageVO item : users) {
                    List<LessonInfoVO> lessons = totalLessons.stream().filter(a -> a.getCompanyId().equals(item.getTargetId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(lessons)) {
                        continue;
                    }
                    for (LessonInfoVO lesson : lessons) {
                        String fileName = monthStr + item.getCompanyName() + lesson.getLessonCategoryName() + "《" + lesson.getLessonName() + "》安全培训课程档案";
                        String url = "/ShowInfo/Show/SafeTrainFiles?lessonid=" + lesson.getLessonId() + "&companyname=" + item.getCompanyName() + "&filename=" + fileName;
                        StatisticsFileVO vo = new StatisticsFileVO();
                        vo.setFileName(fileName);
                        vo.setUrl(url);
                        statisticsFileVOS.add(vo);

                        String fileName2 = monthStr + item.getCompanyName() + lesson.getLessonCategoryName() + "《" + lesson.getLessonName() + "》安全培训全部数据台账";
                        String url2 = "/ShowInfo/Show/Get_CompanySafeTrainFiles?LessonMonth=" + lastMonth + "&CompanyID=" + item.getTargetId() + "&DepartID=-999&LessonID=" + lesson.getLessonId() + "&CompanyName=" + item.getCompanyName() + "&filename=" + fileName2;
                        StatisticsFileVO vo2 = new StatisticsFileVO();
                        vo2.setFileName(fileName2);
                        vo2.setUrl(url2);
                        statisticsFileVOS.add(vo2);
                    }
                    if (CollectionUtils.isNotEmpty(statisticsFileVOS)) {
                        try {
                            String hzdata = "-";
                            String miniurl = "src/view/push/archives?companyId=" + item.getTargetId() + "&companyName=" + item.getCompanyName() + "&date=" + lastMonth;
                            String pushInfo = item.getPublicOpenId() + "|" + item.getUserName() + "，您好！" + item.getCompanyName() + monthStr + "培训台账已送达，请点击详情查看！|" + com.fzkj.common.utils.StringUtils.getStringInTwenty(item.getCompanyName()) + "|" + DateUtils.dateTimeNow("yyyy年MM月dd日") + "|" + hzdata + "|点击查看详情|小程序Appid|" + miniurl + "|";
                            WxMessage wxMessage = new WxMessage();
                            wxMessage.setCode("12");
                            wxMessage.setData(pushInfo);
                            wxMessage.setOpenId(item.getPublicOpenId());
                            wxService.sendWxMessage(wxMessage);
                            LogSendEmailInfo logSendEmailInfo = new LogSendEmailInfo();
                            logSendEmailInfo.setCompanyId(item.getTargetId());
                            logSendEmailInfo.setCompanyName(item.getCompanyName());
                            logSendEmailInfo.setLessonMonth(lastMonth);
                            logSendEmailInfo.setEmail(item.getPublicOpenId());
                            logSendEmailInfo.setIsSend(1);
                            logSendEmailInfo.setSendTime(LocalDateTime.now());
                            logSendEmailInfo.setArchiveUrl(JSON.toJSONString(statisticsFileVOS));
                            logSendEmailInfo.setSendAddress("");
                            logSendEmailInfoService.save(logSendEmailInfo);
                        } catch (Exception e) {
                            log.error("企业档案推送失败", e);
                        }
                    }

                }
            } finally {
                redisCache.releaseLock(MONTH_KEY, "true");
            }

        }
    }

    /**
     * 每周学习情况消息提醒 每周二、每周五早上10点钟执行
     *
     * @return
     */
    @Scheduled(cron = "0 0 10 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void weekStatistics() {
        if (!run){
            return;
        }
        Boolean lock = redisCache.getLock(WEEK_KEY, "true", 600);
        if (lock) {
            try {
                Integer[] weekDays = new Integer[]{1, 2, 3};
                Reminder reminder = reminderService.findReminderByTrainTypAndRemindType(1, 3);
                if (null != reminder && StringUtils.isNotEmpty(reminder.getVal())) {
                    String[] weekDayStr = reminder.getVal().split(",|，");
                    weekDays = new Integer[weekDayStr.length];
                    for (int i = 0; i < weekDayStr.length; i++) {
                        weekDays[i] = Integer.valueOf(weekDayStr[i]);
                    }
                }
                //查询所有需要推送的用户
                List<UserManageVO> users = userInfoService.getAllSendUserInfo();
                if (CollectionUtils.isEmpty(users)) {
                    log.info("每周学习情况消息提醒-不存在可推送的用户");
                    return;
                }
                Integer[] expireDays = new Integer[]{1, 2, 3};
                Reminder reminder2 = reminderService.findReminderByTrainTypAndRemindType(1, 1);
                if (null != reminder2 && StringUtils.isNotEmpty(reminder2.getVal())) {
                    //获取临期提醒天数
                    String[] expireDayStr = reminder2.getVal().split(",|，");
                    //expireDayStr转成Integer数组
                    expireDays = new Integer[expireDayStr.length];
                    for (int i = 0; i < expireDayStr.length; i++) {
                        expireDays[i] = Integer.valueOf(expireDayStr[i]);
                    }
                }
                for (UserManageVO item : users) {
                    LessonSafeTrainCompanyStatisticRequest request = new LessonSafeTrainCompanyStatisticRequest();
                    if (item.getFid() == 0 || item.getFid() == 543) {
                        request.setCompanyId(item.getTargetId());
                    } else {
                        request.setCompanyId(item.getFid());
                        request.setDepartId(item.getTargetId());
                    }
                    request.setLessonMonth(DateUtils.dateTimeNow("yyyy-MM"));
                    List<SafeTrainCompanyStatisticsVO> list = lessonSafeTrainPlanService.getSafeTrainCompanyStatistics2(request);
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (SafeTrainCompanyStatisticsVO stat : list) {
                            if (!"100%".equals(stat.getWcl())) {
                                boolean pushTime = false;
                                //计算当前日期和stat.getEtime的天数差
                                LocalDate currentDate = LocalDate.now();
                                LocalDate targetDate = LocalDate.parse(stat.getETime());
                                // 计算天数差
                                long daysDifference = ChronoUnit.DAYS.between(currentDate, targetDate);
                                for (Integer expireDay : expireDays) {
                                    if ((int) daysDifference == expireDay) {
                                        pushTime = true;
                                        break;
                                    }
                                }
                                if (Arrays.asList(weekDays).contains(DateUtils.getWeekDay()) || pushTime) {//配置日期或者临期
                                    String hzData = "总人数：" + stat.getTotalUserNum() + "人，参学人数：" + stat.getCxUserNum() + "人（" + stat.getCxl() + "），完成人数：" + stat.getWcUserNum() + "人（" + stat.getWcl() + "）";
                                    String miniurl = "src/pages/trainStatistics/index?companyId=" + stat.getCompanyId() + "&companyName=" + stat.getCompanyName();
                                    String pushInfo = item.getPublicOpenId() + "|《" + stat.getLessonName() + "》培训数据统计报告|" + com.fzkj.common.utils.StringUtils.getStringInTwenty(stat.getCompanyName()) + "|" + DateUtils.dateTimeNow("yyyy年MM月dd日") + "|" + com.fzkj.common.utils.StringUtils.getStringInTwenty(hzData) + "|点击查看详情|小程序Appid|" + miniurl + "|";
                                    WxMessage wxMessage = new WxMessage();
                                    wxMessage.setCode("12");
                                    wxMessage.setData(pushInfo);
                                    wxMessage.setOpenId(item.getPublicOpenId());
                                    wxService.sendWxMessage(wxMessage);
                                }
                            }
                        }
                    }
                }

            } finally {
                redisCache.releaseLock(WEEK_KEY, "true");
            }
        }
    }

    /**
     * 每晚1点自动同步用户公众号openId
     *
     * @return
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void syncUserOpenId() {
        if (!run){
            return;
        }
        wxService.getPublicOpenIdByUnionId("null");
    }

    /**
     * 每晚2点删除临时文件夹
     *
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void deleteTempDir() {
        if (!run){
            return;
        }
        File file = new File(uploadPath);
        if(file.exists() && file.isDirectory()){
            try {
                long size = getDirectorySize(file);
                if (size > 10L * 1024 * 1024 * 1024) {
                    file.deleteOnExit();
                } else {
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static long getDirectorySize(File file) throws IOException {
        if (!file.exists()) {
            throw new IllegalArgumentException("File does not exist.");
        }
        long size = 0;
        File[] files = file.listFiles();
        if (files != null) {
            for (File child : files) {
                if (child.isDirectory()) {
                    size += getDirectorySize(child);
                } else {
                    size += child.length();
                }
            }
        }
        return size;
    }

    /**
     * 每晚3点自动把已过期的学习记录迁移到历史库
     *
     * @return
     */
    @Scheduled(cron = "0 0 3 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void moveLearnRecordToHistory() {
        if (!run){
            return;
        }
        //获取已经过期的用户学习记录
        Boolean lock = redisCache.getLock(HISTORY_KEY, "true", 1200);
        if (lock) {
            try {
                userLessonRecordSafeTrainMongoService.moveToHistory();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                redisCache.releaseLock(HISTORY_KEY, "true");
            }
        }

    }

}