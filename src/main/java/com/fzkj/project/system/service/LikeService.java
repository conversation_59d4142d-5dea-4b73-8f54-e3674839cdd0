package com.fzkj.project.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.system.entity.Like;
import com.fzkj.project.system.vo.LikeVO;

public interface LikeService extends IService<Like> {

    Boolean edit_LikeShareCollectionAPP(LikeVO likeVO);

    Object getMyIntegralTotal();

    PageUtils getMyLikeCollection(String flag, PageEntity pageEntity);

}
