package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 课件信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "课件信息表Request")
public class CourseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "课件ID")
    private Long id;

    @ApiModelProperty(value = "企业ID")
    private Long companyId;

    @ApiModelProperty(value = "课件名称")
    private String courseName;

    @ApiModelProperty(value = "课件分类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "讲师编号")
    private String tearcherCode;

    @ApiModelProperty(value = "讲师名称")
    private String tearcherName;

    @ApiModelProperty(value = "课件介绍")
    private String summary;

    @ApiModelProperty(value = "课件内容")
    private String content;

    @ApiModelProperty(value = "音频内容 0:输入音频内容(默认) 1:上传音频内容")
    private Integer audioType;

    @ApiModelProperty(value = "视频内容 （上传音频内容 当audio_type=1时 改字段写入值）")
    private String videoContent;

    @ApiModelProperty(value = "课件封面图")
    private String imgUrl;

    @ApiModelProperty(value = "课件URL")
    private String fileUrl;

    @ApiModelProperty(value = "课件大小")
    private Long fileSize;

    @ApiModelProperty(value = "课件时长")
    private Long timeCount;

    @ApiModelProperty(value = "课件类型（1：视频，2：文件，3：图文，4：音频）")
    private Integer fileType;

    @ApiModelProperty(value = "文件ID")
    private String fileId;

    @ApiModelProperty(value = "课件所属来源 （1：平台 2：企业）")
    private Integer belongPlat;

    @ApiModelProperty(value = "是否显示给企业查看（1：显示 2：不显示）")
    private Integer isShow;

    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    @ApiModelProperty(value = "修改人姓名")
    private String reviseName;

    @ApiModelProperty(value = "来源（1：行业版）")
    private String source;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否有效 1是 0否 默认有效")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1（文件名称：上传文件的文件名称）")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2（学习人次）")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3 （上传音频内容 当audio_type=1时 改字段写上传的文件名）")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    private String reservedField5;

    @ApiModelProperty(value = "二级标题")
    private String secondTitle;

    @ApiModelProperty(value = "操作标识")
    private String flag;

}