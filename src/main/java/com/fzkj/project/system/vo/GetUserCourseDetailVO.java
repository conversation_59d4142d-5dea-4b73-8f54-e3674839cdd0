package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetUserCourseDetailVO {
    @ApiModelProperty(value = "课件ID")
    @JsonProperty("CourseID")
    private String courseId;

    @ApiModelProperty(value = "课件名称")
    @JsonProperty("CourseName")
    private String courseName;

    @ApiModelProperty(value = "时长")
    @JsonProperty("TimeCount")
    private String timeCount;

    @ApiModelProperty(value = "文件类型")
    @JsonProperty("FileType")
    private String fileType;

    @ApiModelProperty(value = "文件ID")
    @JsonProperty("FileID")
    private String fileId;

    @ApiModelProperty(value = "文件地址")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "讲师姓名")
    @JsonProperty("TearcherName")
    private String tearcherName;

    @ApiModelProperty(value = "技术职称")
    @JsonProperty("TechnicalTitle")
    private String technicalTitle;

    @ApiModelProperty(value = "评分")
    @JsonProperty("Grade")
    private String grade;

    @ApiModelProperty(value = "是否评分")
    @JsonProperty("IsGrade")
    private String isGrade;

    @ApiModelProperty(value = "学习签到状态")
    @JsonProperty("StudySignState")
    private String studySignState;

    @ApiModelProperty(value = "学习人脸识别状态")
    @JsonProperty("StudyFaceState")
    private String studyFaceState;

    @ApiModelProperty(value = "学习倒计时时间")
    @JsonProperty("StudyCountDownTime")
    private String studyCountDownTime;

    @ApiModelProperty(value = "学习签到时间")
    @JsonProperty("StudySignTime")
    private String studySignTime;

    @ApiModelProperty(value = "学习签到失败重复次数")
    @JsonProperty("StudyFailRepeatNum")
    private String studyFailRepeatNum;

    @ApiModelProperty(value = "课件人脸识别次数")
    @JsonProperty("CoursFaceNum")
    private String coursFaceNum;

    @ApiModelProperty(value = "学习次数")
    @JsonProperty("LearnNum")
    private String learnNum;

    @ApiModelProperty(value = "人脸识别图片")
    @JsonProperty("FaceDistinguishImg")
    private String faceDistinguishImg;

    @ApiModelProperty(value = "人脸识别字符串")
    @JsonProperty("FaceRecognitionString")
    private String faceRecognitionString;

    @ApiModelProperty(value = "是否完成")
    @JsonProperty("IsComplete")
    private String isComplete;

    @ApiModelProperty(value = "日志次数")
    @JsonProperty("LogTime")
    private int logTime;

    @ApiModelProperty(value = "图片URL")
    @JsonProperty("ImgUrl")
    private String imgUrl;

    @ApiModelProperty(value = "是否开启服务")
    @JsonProperty("IsOpenService")
    private String isOpenService;

    @ApiModelProperty(value = "内容")
    @JsonProperty("Content")
    private String content;

    @ApiModelProperty(value = "音频类型")
    @JsonProperty("AudioType")
    private String audioType;

    @ApiModelProperty(value = "视频内容")
    @JsonProperty("VideoContent")
    private String videoContent;

    @ApiModelProperty(value = "保留字段4")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "培训类型")
    @JsonProperty("TrainType")
    private String trainType;

}
