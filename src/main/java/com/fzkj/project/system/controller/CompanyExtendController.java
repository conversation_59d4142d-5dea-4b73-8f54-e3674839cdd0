package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.CompanyExtendService;
import com.fzkj.project.system.vo.CompanyExtendVO;
import com.fzkj.project.system.request.CompanyExtendRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 企业扩展表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "companyExtend")
public class CompanyExtendController extends BaseEntity {

    private final CompanyExtendService CompanyExtendServiceImpl;

    @PostMapping("list")
    @ApiOperation(value = "企业扩展表列表查询")
    public AjaxResult listCompanyExtend(@RequestBody CommonRequest commonRequest) {
        CompanyExtendRequest request = (CompanyExtendRequest) commonRequest.getRequest(CompanyExtendRequest.class);
        return AjaxResult.success(CompanyExtendServiceImpl.listCompanyExtend(request));
    }

    @PostMapping("listByPage")
    @ApiOperation(value = "企业扩展表分页查询")
    public AjaxResult listCompanyExtendByPage(@RequestBody CommonRequest commonRequest) {
        CompanyExtendRequest request = (CompanyExtendRequest) commonRequest.getRequest(CompanyExtendRequest.class);
        return AjaxResult.success(CompanyExtendServiceImpl.listByPage(request, commonRequest.getPageEntity()));
    }

    @GetMapping("{id}")
    @ApiOperation(value = "企业扩展表查询详情")
    public AjaxResult findCompanyExtend(@RequestBody CommonRequest commonRequest) {
        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
        return AjaxResult.success(CompanyExtendServiceImpl.findCompanyExtend(request.getId()));
    }

    @PostMapping
    @ApiOperation(value = "企业扩展表新增数据")
    public AjaxResult saveCompanyExtend(@RequestBody CommonRequest commonRequest) {
        CompanyExtendVO vo = (CompanyExtendVO) commonRequest.getRequest(CompanyExtendVO.class);
        return AjaxResult.success(CompanyExtendServiceImpl.saveCompanyExtend(vo));
    }

    @PostMapping("update")
    @ApiOperation(value = "企业扩展表修改数据")
    public AjaxResult updateCompanyExtend(@RequestBody CommonRequest commonRequest) {
        CompanyExtendVO vo = (CompanyExtendVO) commonRequest.getRequest(CompanyExtendVO.class);
        return AjaxResult.success(CompanyExtendServiceImpl.updateCompanyExtend(vo));
    }

    @PostMapping("delete")
    @ApiOperation(value = "企业扩展表删除数据")
    public AjaxResult removeCompanyExtend(@RequestBody CommonRequest commonRequest) {
        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
        return AjaxResult.success(CompanyExtendServiceImpl.removeCompanyExtend(request.getId()));
    }

}
