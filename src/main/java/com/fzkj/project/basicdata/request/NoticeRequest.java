package com.fzkj.project.basicdata.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 16:52
 * @Version: 1.0
 */
@Data
@ApiModel(value = "公告表Request")
public class NoticeRequest {

    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    /** 内容 */
    @ApiModelProperty(name = "内容")
    @JsonProperty("Title")
    private String title;

    @ApiModelProperty(name = "内容")
    @JsonProperty("Content")
    private String content;

    @ApiModelProperty(name = "类型")
    @JsonProperty("Type")
    private Integer type;


    @ApiModelProperty(name = "外链url")
    @JsonProperty("LinkUrl")
    private String linkUrl;


    @ApiModelProperty(name = "来源")
    @JsonProperty("Source")
    private String source;


    @ApiModelProperty(name = "开始时间")
    @JsonProperty("StartTime")
    private String startTime;


    @ApiModelProperty(name = "结束时间")
    @JsonProperty("EndTime")
    private String endTime;

    @ApiModelProperty(name = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(name = "创建人code")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(name = "创建时间")
    @JsonProperty("CreationTime")
    private String creationTime;

    @ApiModelProperty(name = "修改人code")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(name = "修改时间")
    @JsonProperty("ReviseTime")
    private String reviseTime;

    @ApiModelProperty(name = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(name = "")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(name = "创建人名称")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(name = "修改人名称")
    @JsonProperty("ReviseName")
    private String reviseName;

    @ApiModelProperty(name = "")
    @JsonProperty("Author")
    private String author;

    @ApiModelProperty(name = "")
    @JsonProperty("PutInPlatform")
    private Integer putInPlatform;

    @ApiModelProperty(name = "")
    @JsonProperty("PutInPlatformStr")
    private String putInPlatformStr;

    @ApiModelProperty(name = "")
    @JsonProperty("OrganizationID")
    private Long organizationId;

    @ApiModelProperty(name = "")
    @JsonProperty("IsPush")
    private Integer isPush;
    
    @JsonProperty("Flag")
    private String flag;
    @JsonProperty("IsExpire")
    private Integer isExpire;

    @JsonProperty("Platform")
    private Integer platform;
}
