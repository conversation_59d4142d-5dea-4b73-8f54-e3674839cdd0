package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.alibaba.fastjson2.JSON;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.project.system.entity.ExaminationPaperRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 考试信息表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value = "考试信息表VO")
public class ExamVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "考试名称")
    @NotNull(message = "考试名称不能为空")
    @JsonProperty("ExamName")
    private String examName;

    @ApiModelProperty(value = "题目数量")
    @NotNull(message = "题目数量不能为空")
    @JsonProperty("QuestionCount")
    private Integer questionCount;

    @ApiModelProperty(value = "考试时长")
    @NotNull(message = "考试时长不能为空")
    @JsonProperty("TimeCount")
    private Integer timeCount;

    @ApiModelProperty(value = "考试总分")
    @NotNull(message = "考试总分不能为空")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty(value = "及格分数")
    @NotNull(message = "及格分数不能为空")
    @JsonProperty("PassMark")
    private Integer passMark;

    @ApiModelProperty(value = "考试重复次数")
    @NotNull(message = "考试重复次数不能为空")
    @JsonProperty("ResitNumber")
    private Integer resitNumber;

    @ApiModelProperty(value = "分组排考类型")
    @NotNull(message = "分组排考类型不能为空")
    @JsonProperty("GroupRollType")
    private Integer groupRollType;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @NotNull(message = "创建人ID不能为空")
    @JsonProperty("CreatorID")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @NotNull(message = "修改人ID不能为空")
    @JsonProperty("ReviseID")
    private String reviseId;

    @ApiModelProperty(value = "修改时间")
    @NotNull(message = "修改时间不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效：0：无效，1：有效")
    @NotNull(message = "是否有效：0：无效，1：有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "组织ID")
    @NotNull(message = "组织ID不能为空")
    @JsonProperty("OrgID")
    private String orgId;

    @ApiModelProperty(value = "来源")
    @NotNull(message = "来源不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "考试签名状态：0：禁用，1：启用")
    @NotNull(message = "考试签名状态：0：禁用，1：启用不能为空")
    @JsonProperty("ExamSignState")
    private Integer examSignState;

    @ApiModelProperty(value = "考试人像识别状态：0：禁用，1：启用")
    @NotNull(message = "考试人像识别状态：0：禁用，1：启用不能为空")
    @JsonProperty("ExamFaceState")
    private Integer examFaceState;

    @ApiModelProperty(value = "考试倒计时时长")
    @NotNull(message = "考试倒计时时长不能为空")
    @JsonProperty("ExamCountDownTime")
    private Integer examCountDownTime;

    @ApiModelProperty(value = "考试识别失败的次数")
    @NotNull(message = "考试识别失败的次数不能为空")
    @JsonProperty("ExamFailRepeatNum")
    private Integer examFailRepeatNum;

    @ApiModelProperty(value = "是否提示答案：0：不提示 1：提示")
    @NotNull(message = "是否提示答案：0：不提示 1：提示不能为空")
    @JsonProperty("IsPromptAnswer")
    private Integer isPromptAnswer;

    @ApiModelProperty(value = "是否需要模拟考试：0：不需要 1：需要")
    @NotNull(message = "是否需要模拟考试：0：不需要 1：需要不能为空")
    @JsonProperty("IsMockExam")
    private Integer isMockExam;

    @ApiModelProperty(value = "是否需要习题练习：0：不需要 1：需要")
    @NotNull(message = "是否需要习题练习：0：不需要 1：需要不能为空")
    @JsonProperty("IsPractice")
    private Integer isPractice;

    @ApiModelProperty(value = "是否开启模拟考试人脸识别：0：不需要 1：需要")
    @NotNull(message = "是否开启模拟考试人脸识别：0：不需要 1：需要不能为空")
    @JsonProperty("IsMockExamFace")
    private Integer isMockExamFace;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("IsRelation")
    private Long IsRelation;

    @ApiModelProperty(value = "试卷类型列表")
    @JsonProperty("ExaminationPaperRuleList")
    private List<ExaminationPaperRuleVO> examinationPaperRuleList;

    @ApiModelProperty(value = "试题列表")
    @JsonProperty("ExamQuestionList")
    private List<ExamQuestionVO> examQuestionList;

    @JsonProperty("Flag")
    private String flag;

    @JsonProperty("GroupRollTypeStrsXML")
    private String groupRollTypeStrsXML;

    @JsonProperty("ExaminationPaperRules")
    private List<ExaminationPaperRule> examinationPaperRules;

    @JsonProperty("CompanyID")
    private Long companyId;

    @JsonProperty("ExamID")
    private Long examId;

    public List<ExaminationPaperRule> getExaminationPaperRules() {
        if (StringUtils.isEmpty(groupRollTypeStrsXML)) {
            return null;
        }
        return JSON.parseArray(groupRollTypeStrsXML, ExaminationPaperRule.class);
    }
}