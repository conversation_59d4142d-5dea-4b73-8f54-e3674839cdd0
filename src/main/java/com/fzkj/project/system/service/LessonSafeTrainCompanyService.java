package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.LessonSafeTrainCompany;
import com.fzkj.project.system.vo.LessonInfoVO;
import com.fzkj.project.system.vo.LessonSafeTrainCompanyVO;
import com.fzkj.project.system.request.LessonSafeTrainCompanyRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 给企业分发的安全培训课程关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface LessonSafeTrainCompanyService extends IService<LessonSafeTrainCompany> {

    List<LessonSafeTrainCompany> listLessonSafeTrainCompany(LessonSafeTrainCompanyRequest request);

    PageUtils listByPage(LessonSafeTrainCompanyRequest request, PageEntity pageEntity);

    LessonSafeTrainCompanyVO findLessonSafeTrainCompany(Long id);

    Long saveLessonSafeTrainCompany(LessonSafeTrainCompanyVO vo);

    boolean updateLessonSafeTrainCompany(LessonSafeTrainCompanyVO vo);

    boolean removeLessonSafeTrainCompany(Long id);

    List<LessonInfoVO> getLessonInfo(String month);
}
