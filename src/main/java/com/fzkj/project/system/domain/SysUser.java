package com.fzkj.project.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class SysUser {
    @TableId
    private Long userId;
    private String userName;
    private Date createTime;
    @JsonIgnore
    private String password;
    /**企业数据权限 1-所有 2-自己负责的企业 */
    @TableField(exist = false)
    private int permission;
    @TableField(exist = false)
    private Long enterpriseId;
    private String delFlag;
    private String status;

}
