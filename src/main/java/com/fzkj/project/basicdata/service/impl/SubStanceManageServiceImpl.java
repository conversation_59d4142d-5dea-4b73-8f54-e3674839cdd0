package com.fzkj.project.basicdata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.*;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.framework.security.service.TokenService;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.basicdata.entity.SubStanceLabel;
import com.fzkj.project.basicdata.entity.SubStanceManage;
import com.fzkj.project.basicdata.entity.SubStanceType;
import com.fzkj.project.basicdata.mapper.SubStanceLabelMapper;
import com.fzkj.project.basicdata.mapper.SubStanceManageMapper;
import com.fzkj.project.basicdata.mapper.SubStanceTypeMapper;
import com.fzkj.project.basicdata.request.SubStanceManageRequest;
import com.fzkj.project.basicdata.service.SubStanceManageService;
import com.fzkj.project.basicdata.service.SubStanceTypeService;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.mapper.CollectionMapper;
import com.fzkj.project.system.mapper.CommentMapper;
import com.fzkj.project.system.mapper.LikeMapper;
import com.fzkj.project.system.service.UserCompanyService;
import com.fzkj.project.system.vo.SubStanceManageVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 13:53
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class SubStanceManageServiceImpl extends ServiceImpl<SubStanceManageMapper, SubStanceManage> implements SubStanceManageService {

    @Resource
    UserCompanyService userCompanyService;

    @Resource
    SubStanceTypeService subStanceTypeService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SubStanceTypeMapper subStanceTypeMapper;

    @Autowired
    private SubStanceLabelMapper subStanceLabelMapper;

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private LikeMapper likeMapper;

    @Autowired
    CollectionMapper collectionMapper;

    @Override
    public PageUtils listByPage(SubStanceManage request, PageEntity pageEntity) {
        SubStanceManage entity = (SubStanceManage) DataTransfer.transfer(request, SubStanceManage.class);
        LambdaQueryWrapper<SubStanceManage> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtils.isEmpty(entity.getId())) {
            queryWrapper.eq(SubStanceManage::getIsValid, entity.getIsValid());
        }
        if (!ObjectUtils.isEmpty(entity.getId())){
            queryWrapper.eq(SubStanceManage::getId,entity.getId());
        }
        if (!ObjectUtils.isEmpty(entity.getSubStanceTitle())){
            queryWrapper.like(SubStanceManage::getSubStanceTitle,entity.getSubStanceTitle());
        }
        if (!ObjectUtils.isEmpty(entity.getFileType())){
            queryWrapper.eq(SubStanceManage::getFileType,entity.getFileType());
        }
        if (!ObjectUtils.isEmpty(entity.getReviseCode())){
            queryWrapper.eq(SubStanceManage::getReviseCode,entity.getReviseCode());
        }
        if (!ObjectUtils.isEmpty(entity.getIsLogin())){
            queryWrapper.eq(SubStanceManage::getIsLogin,entity.getIsLogin());
        }
        if (!ObjectUtils.isEmpty(entity.getSubStanceTypeId())){
            queryWrapper.eq(SubStanceManage::getSubStanceTypeId,entity.getSubStanceTypeId());
        }
        if (!ObjectUtils.isEmpty(entity.getSubStanceLabelIds())) {
            String subStanceLabelQuery = entity.getSubStanceLabelIds();
            String[] split = subStanceLabelQuery.split(",");
            Integer index = 1;
            for (String s : split) {
                if (split.length==1 && index == 1) {
                    queryWrapper.apply("sub_stance_label_query like '%,"+s+",%'");
                }else if (index== split.length){
                    queryWrapper.apply("sub_stance_label_query like '%,"+s+",%')");
                }else {
                    queryWrapper.apply("(sub_stance_label_query like '%,"+s+",%'").or();
                }
                index++;
            }
        }
        IPage<SubStanceManage> page = this.page(
                new Query<SubStanceManage>().getPage(pageEntity), queryWrapper);
        page.getRecords().forEach(item->{
            LambdaQueryWrapper<Comment> commentLambdaQueryWrapper = new LambdaQueryWrapper<>();
            commentLambdaQueryWrapper.eq(Comment::getFunType,11);
            commentLambdaQueryWrapper.eq(Comment::getTargetId,item.getId());
            commentLambdaQueryWrapper.eq(Comment::getStatus,1);
            commentLambdaQueryWrapper.eq(Comment::getIsValid,1);
            Integer count = commentMapper.selectCount(commentLambdaQueryWrapper);
            item.setCommentNum(count);
        });
        return new PageUtils(page);
    }

    @Override
    public Long insertSubStanceManage(SubStanceManage request) {
        SubStanceManage entity = (SubStanceManage) DataTransfer.transfer(request, SubStanceManage.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        entity.setCreatorName(userCompany.getUserName());
        entity.setCreatorCode(userCompany.getUserCode());
        entity.setCreationTime(DateUtils.dateTimeNew(new Date()));
        entity.setIsValid(1);
        entity.setReadNum(0);
        //写入标签
        Long subStanceTypeId = request.getSubStanceTypeId();
        SubStanceType byId = subStanceTypeService.getById(subStanceTypeId);
        String typeName = byId.getTypeName();
        entity.setSubStanceTypeName(typeName);
        this.save(entity);
        return request.getId();
    }

    @Override
    public Boolean updateSubStanceManage(SubStanceManage request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        SubStanceManage entity = (SubStanceManage) DataTransfer.transfer(request, SubStanceManage.class);
        entity.setReviseCode(userCompany.getUserCode());
        entity.setReviseName(userCompany.getUserName());
        entity.setReviseTime(DateUtils.dateTimeNew(new Date()));
        Long subStanceTypeId = request.getSubStanceTypeId();
        SubStanceType byId = subStanceTypeService.getById(subStanceTypeId);
        String typeName = byId.getTypeName();
        entity.setSubStanceTypeName(typeName);
        return this.saveOrUpdate(entity);
    }

    @Override
    public Boolean deleteSubStanceManage(Long id) {

        SubStanceManage byId = this.getById(id);
        if (byId.getIsValid().equals(0)){
            byId.setIsValid(1);
        }else {
            byId.setIsValid(0);
        }
        return this.saveOrUpdate(byId);
    }

    @Override
    public PageUtils getHomeContentMobile(SubStanceManageRequest request, PageEntity pageEntity) {
        IPage<SubStanceManage> page = null;
        if(StringUtils.isEmpty(request.getFlag()) || request.getFlag().equals("list")){
            //查询类型参数获取排序规则
            Integer homeSortRule = null;
            if(request.getTypeID() != null){
                SubStanceType subStanceType = subStanceTypeMapper.selectById(request.getTypeID());
                homeSortRule = subStanceType.getHomeSortRule();
            }
            LambdaQueryWrapper<SubStanceManage> queryWrapper = new LambdaQueryWrapper<>();
            if (!ObjectUtils.isEmpty(request.getKeywords())) {
                queryWrapper.like(SubStanceManage::getSubStanceTitle,request.getKeywords());
            }
            if(request.getIsLogin() != null && request.getIsLogin() == 0){
                queryWrapper.eq(SubStanceManage::getIsLogin,0);
            }
            if(request.getTypeID() != null){
                queryWrapper.eq(SubStanceManage::getSubStanceTypeId,request.getTypeID());
            }
            queryWrapper.eq(SubStanceManage::getIsValid,1);
            queryWrapper.eq(SubStanceManage::getPutinPlatform,256);
            queryWrapper.ne(SubStanceManage::getSubStanceTypeName,"banner");
            //1随机不做排序
            if(homeSortRule == null || homeSortRule == 0){
                //默认：序号
                queryWrapper.orderByAsc(SubStanceManage::getSort);
            }else if(homeSortRule == 2){
                //时间倒叙
                queryWrapper.orderByDesc(SubStanceManage::getCreationTime);
            }else if(homeSortRule == 3){
                queryWrapper.orderByDesc(SubStanceManage::getReadNum);
            }
            page = this.page(
                    new Query<SubStanceManage>().getPage(pageEntity), queryWrapper);
            //获取标签颜色和背景颜色
            page.getRecords().forEach(item ->{
                String[] labelIds = item.getSubStanceLabelIds().split(",");
                List<SubStanceLabel> subStanceLabels = subStanceLabelMapper.selectBatchIds(Arrays.asList(labelIds));
                String labelNames = "";
                for(SubStanceLabel subStanceLabel : subStanceLabels){
                    labelNames += subStanceLabel.getLabelName()+"|"+subStanceLabel.getColorCode()+",";
                }
                item.setSubStanceLabelNames(labelNames.substring(0,labelNames.length()-1));
            });
        }else if(request.getFlag().equals("byId")){
            LambdaQueryWrapper<SubStanceManage> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SubStanceManage::getId,request.getId());
            pageEntity = pageEntity == null? new PageEntity():pageEntity;
            pageEntity.setPageSize(1);
            pageEntity.setPageIndex(1);
            page = this.page(
                    new Query<SubStanceManage>().getPage(pageEntity), queryWrapper);
        }
        return new PageUtils(page);
    }

    @Override
    public Integer addSubStanceBrowsingRecord(SubStanceManageRequest request) {
        SubStanceManage subStanceManage = this.getById(request.getSubStanceID());
        subStanceManage.setReadNum(subStanceManage.getReadNum() + 1);
        return this.updateById(subStanceManage)?1:0;
    }

    @Override
    public List<SubStanceManage> getBanner() {
        LambdaQueryWrapper<SubStanceManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubStanceManage::getSubStanceTypeName,"banner");
        queryWrapper.eq(SubStanceManage::getIsValid,1);
        List<SubStanceManage> subStanceManageList = this.baseMapper.selectList(queryWrapper);
        return subStanceManageList;
    }

    @Override
    public List<SubStanceManageVO> getSubStanceAPP(Long id) {
        SubStanceManage subStanceManage = this.baseMapper.selectById(id);
        SubStanceManageVO subStanceManageVO =(SubStanceManageVO) DataTransfer.transfer(subStanceManage, SubStanceManageVO.class);
        //获取当前评论条数
        LambdaQueryWrapper<Comment> commentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        commentLambdaQueryWrapper.eq(Comment::getFunType,11);
        commentLambdaQueryWrapper.eq(Comment::getTargetId,id);
        commentLambdaQueryWrapper.eq(Comment::getStatus,1);
        commentLambdaQueryWrapper.eq(Comment::getIsValid,1);
        Integer commentNum = commentMapper.selectCount(commentLambdaQueryWrapper);
        //获取当前点赞次数
        LambdaQueryWrapper<Like> likeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        likeLambdaQueryWrapper.eq(Like::getFunType,11);
        likeLambdaQueryWrapper.eq(Like::getTargetId,id);
        likeLambdaQueryWrapper.eq(Like::getIsValid,1);
        Integer likeNum = likeMapper.selectCount(likeLambdaQueryWrapper);
        //获取当前收藏次数
        LambdaQueryWrapper<Collection> collectionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        collectionLambdaQueryWrapper.eq(Collection::getFunType,11);
        collectionLambdaQueryWrapper.eq(Collection::getTargetId,id);
        collectionLambdaQueryWrapper.eq(Collection::getIsValid,1);
        Integer collectionNum = collectionMapper.selectCount(collectionLambdaQueryWrapper);
        Integer isLike =0;
        Integer isCollection =0;
        try {
            UserInfo loginUser = SecurityUtils.getLoginUser().getUser();
            //自己是否点赞
            likeLambdaQueryWrapper.eq(Like::getUserCode, loginUser.getUserCode());
            isLike = likeMapper.selectCount(likeLambdaQueryWrapper);
            //自己是否收藏
            collectionLambdaQueryWrapper.eq(Collection::getUserCode, loginUser.getUserCode());
            isCollection = collectionMapper.selectCount(collectionLambdaQueryWrapper);
        } finally {
            subStanceManageVO.setCommentNum(commentNum);
            subStanceManageVO.setIsLike(isLike);
            subStanceManageVO.setLikeNum(likeNum);
            subStanceManageVO.setIsCollection(isCollection);
            subStanceManageVO.setCollectionNum(collectionNum);
            List<SubStanceManageVO> subStanceManageList = new ArrayList<>();
            //给标签颜色
            String[] labelIds = subStanceManageVO.getSubStanceLabelIds().split(",");
            List<SubStanceLabel> subStanceLabels = subStanceLabelMapper.selectBatchIds(Arrays.asList(labelIds));
            String labelNames = "";
            for(SubStanceLabel subStanceLabel : subStanceLabels){
                labelNames += subStanceLabel.getLabelName()+"|"+subStanceLabel.getColorCode()+",";
            }
            subStanceManageVO.setSubStanceLabelNames(labelNames.substring(0,labelNames.length()-1));
            subStanceManageList.add(subStanceManageVO);
            return subStanceManageList;
        }
    }
}
