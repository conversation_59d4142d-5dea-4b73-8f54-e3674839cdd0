package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.CompanyLessonSafeTrainSet;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetQueryRequest;
import com.fzkj.project.system.vo.CompanyLessonSafeTrainSetVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 企业安全培训课程设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface CompanyLessonSafeTrainSetMapper extends BaseMapper<CompanyLessonSafeTrainSet> {

    List<CompanyLessonSafeTrainSetVO> selectCompanyLessonSafeTrainSetList1(CompanyLessonSafeTrainSetQueryRequest request);

    List<CompanyLessonSafeTrainSetVO> selectCompanyLessonSafeTrainSetList3(CompanyLessonSafeTrainSetQueryRequest request);

    List<CompanyLessonSafeTrainSetVO> selectCompanyLessonSafeTrainSetList2(CompanyLessonSafeTrainSetQueryRequest request);

    List<CompanyLessonSafeTrainSetVO> selectCompanyLessonSafeTrainSetList4(CompanyLessonSafeTrainSetQueryRequest request);
}