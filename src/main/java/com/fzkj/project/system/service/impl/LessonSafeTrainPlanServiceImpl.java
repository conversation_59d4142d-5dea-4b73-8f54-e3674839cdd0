package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.mapper.*;
import com.fzkj.project.system.mongo.UserCourseRecordSafeTrain;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.*;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import com.fzkj.project.system.vo.*;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 公交培训计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class LessonSafeTrainPlanServiceImpl extends ServiceImpl<LessonSafeTrainPlanMapper, LessonSafeTrainPlan> implements LessonSafeTrainPlanService {
    private final static List completeStatus = Arrays.asList(1, 2, 4);
    private final static List noBeginStatus = Arrays.asList(-3, 3, 4);
    private final static List noCompleteStatus = Arrays.asList(0, 2);
    private final static List completedStatus = Arrays.asList(2, 4);
    private final CompanyService companyService;
    private final LessonSafeTrainPlanRelationshipService lessonSafeTrainPlanRelationshipService;
    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;
    private final UserCourseRecordSafeTrainMongoService userCourseRecordSafeTrainMongoService;
    private final LessonCourseSafeTrainMapper lessonCourseSafeTrainMapper;
    private final ExamUserRecordMapper examUserRecordMapper;
    private final LessonSafeTrainService lessonSafeTrainService;
    private final ExamMapper examMapper;
    private final UserCompanyMapper userCompanyMapper;
    private final UserInfoService userInfoService;
    private final LessonSafeTrainMapper lessonSafeTrainMapper;
    private final UserCourseRecordSafeTrainLogAllMapper userCourseRecordSafeTrainLogALLMapper;
    private final static Map<Integer, String> fileTypeMap = new HashMap<>();

    static {
        //1：视频，2：文件，3：图文，4：音频
        fileTypeMap.put(1, "视频");
        fileTypeMap.put(2, "文件");
        fileTypeMap.put(3, "图文");
        fileTypeMap.put(4, "音频");
    }


    @Override
    public List<LessonSafeTrainPlan> listLessonSafeTrainPlan(LessonSafeTrainPlanRequest request) {
        LessonSafeTrainPlan entity = (LessonSafeTrainPlan) DataTransfer.transfer(request, LessonSafeTrainPlan.class);
        LambdaQueryWrapper<LessonSafeTrainPlan> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(LessonSafeTrainPlanRequest request, PageEntity pageEntity) {
        LessonSafeTrainPlan entity = (LessonSafeTrainPlan) DataTransfer.transfer(request, LessonSafeTrainPlan.class);
        LambdaQueryWrapper<LessonSafeTrainPlan> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<LessonSafeTrainPlan> page = this.page(
                new Query<LessonSafeTrainPlan>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public LessonSafeTrainPlanVO findLessonSafeTrainPlan(Long id) {
        LessonSafeTrainPlan entity = this.getById(id);
        LessonSafeTrainPlanVO vo = (LessonSafeTrainPlanVO) DataTransfer.transfer(entity, LessonSafeTrainPlanVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveLessonSafeTrainPlan(LessonSafeTrainPlanVO vo) {
        LessonSafeTrainPlan entity = (LessonSafeTrainPlan) DataTransfer.transfer(vo, LessonSafeTrainPlan.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLessonSafeTrainPlan(LessonSafeTrainPlanVO vo) {
        LessonSafeTrainPlan entity = (LessonSafeTrainPlan) DataTransfer.transfer(vo, LessonSafeTrainPlan.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeLessonSafeTrainPlan(Long id) {
        return this.removeById(id);
    }

    @Override
    public Long editLessonSafeTrainPlan(LessonSafeTrainPlanVO request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LessonSafeTrainPlan entity = (LessonSafeTrainPlan) DataTransfer.transfer(request, LessonSafeTrainPlan.class);
        switch (request.getFlag()) {
            case "add":
                entity.setIsValid(1);
                entity.setCreatorCode(user.getUserCode());
                entity.setCreationTime(LocalDateTime.now());
                entity.setReviseCode(user.getUserCode());
                entity.setReviseTime(LocalDateTime.now());
                this.save(entity);
                return entity.getId();
            case "edit":
                entity.setIsValid(1);
                entity.setReviseCode(user.getUserCode());
                entity.setReviseTime(LocalDateTime.now());
                this.updateById(entity);
                return entity.getId();
            case "del":
                LambdaUpdateWrapper<LessonSafeTrainPlan> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(LessonSafeTrainPlan::getId, request.getId());
                updateWrapper.set(LessonSafeTrainPlan::getIsValid, 0);
                updateWrapper.set(LessonSafeTrainPlan::getReviseCode, user.getUserCode());
                updateWrapper.set(LessonSafeTrainPlan::getReviseTime, LocalDateTime.now());
                boolean del = this.update(updateWrapper);
                if (del) {
                    return request.getId();
                } else {
                    throw new CustomException("删除失败");
                }
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());

    }

    @Override
    public PageUtils getBusSafeTrainList(LessonSafeTrainPlanRequest request, PageEntity pageEntity) {
        String lessonMonth = request.getLessonMonth();
        if (StringUtils.isNotEmpty(lessonMonth) && lessonMonth.length() < 10) {
            request.setLessonMonth(lessonMonth + "-01");
        }
        if (null != request.getOrgId()) {
            request.setOrgIds(companyService.getSubDeptIds(request.getOrgId()));
        }
        if (null != pageEntity) {
            PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        }
        List<LessonSafeTrainPlanVO> list = getBaseMapper().getBusSafeTrainList(request);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> lessonIds = new ArrayList<>();
            Map<Long, List<Long>> planIdLessonIdMap = new HashMap<>();
            LambdaQueryWrapper<LessonSafeTrainPlanRelationship> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1);
            queryWrapper.in(LessonSafeTrainPlanRelationship::getPlanId, list.stream().map(LessonSafeTrainPlanVO::getId).collect(Collectors.toList()));
            List<LessonSafeTrainPlanRelationship> planRelationshipList = lessonSafeTrainPlanRelationshipService.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(planRelationshipList)) {
                lessonIds = planRelationshipList.stream().map(LessonSafeTrainPlanRelationship::getLessonId).distinct().collect(Collectors.toList());
                planIdLessonIdMap = planRelationshipList.stream().collect(Collectors.groupingBy(LessonSafeTrainPlanRelationship::getPlanId, Collectors.mapping(LessonSafeTrainPlanRelationship::getLessonId, Collectors.toList())));
                request.setLessonIds(lessonIds);
                List<UserLessonRecordLearnNumVO> userLessonRecordLearnNumVOS = userLessonRecordSafeTrainMongoService.getLearnNum(request);
                for (int i = 0; i < list.size(); i++) {
                    LessonSafeTrainPlanVO lessonSafeTrainPlanVO = list.get(i);
                    List<Long> tempLessonIds = planIdLessonIdMap.get(lessonSafeTrainPlanVO.getId());
                    if (CollectionUtils.isEmpty(tempLessonIds)) {
                        continue;
                    }
                    if (null == request.getLearnFlag()) {
                        lessonSafeTrainPlanVO.setTotalUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setCxUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setWcUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && (item.getStaticStatus() == 1 || item.getStaticStatus() == 2 || item.getStaticStatus() == 4)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (request.getLearnFlag() == 0) {
                        lessonSafeTrainPlanVO.setTotalUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && item.getStaticStatus() != 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setCxUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && item.getStaticStatus() != 1 && item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setWcUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && (item.getStaticStatus() == 2 || item.getStaticStatus() == 4)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (request.getLearnFlag() == 1) {
                        lessonSafeTrainPlanVO.setTotalUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setCxUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && (item.getStaticStatus() == 0 || item.getStaticStatus() == 1 || item.getStaticStatus() == 2)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setWcUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && item.getStaticStatus() == 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    }
                    DecimalFormat df = new DecimalFormat("#.##");
                    lessonSafeTrainPlanVO.setCxl(lessonSafeTrainPlanVO.getTotalUserNum() > 0 ? (df.format(lessonSafeTrainPlanVO.getCxUserNum() * 1.00 / lessonSafeTrainPlanVO.getTotalUserNum() * 100)) + "%" : "0%");
                    lessonSafeTrainPlanVO.setWcl(lessonSafeTrainPlanVO.getTotalUserNum() > 0 ? (df.format(lessonSafeTrainPlanVO.getWcUserNum() * 1.00 / lessonSafeTrainPlanVO.getTotalUserNum() * 100)) + "%" : "0%");
                }
            }

        } else {
            list = new ArrayList<>();
        }
        return new PageUtils(pageEntity, list);
    }

    @Override
    public PageUtils getBusSafeTrainOrgList(LessonSafeTrainPlanRequest request, PageEntity pageEntity) {
        List<Long> subDeptIds = companyService.getSubDeptIds(request.getOrgId());
        LambdaQueryWrapper<Company> companyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyLambdaQueryWrapper.in(Company::getId, subDeptIds);
        List<Company> companyList = companyService.list(companyLambdaQueryWrapper);
        //查询计划关联表
        LambdaQueryWrapper<LessonSafeTrainPlanRelationship> lessonSafeTrainPlanRelationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lessonSafeTrainPlanRelationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1)
                .eq(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanId());
        List<LessonSafeTrainPlanRelationship> lessonSafeTrainPlanRelationshipList = lessonSafeTrainPlanRelationshipService.list(lessonSafeTrainPlanRelationshipLambdaQueryWrapper);
        if (null != lessonSafeTrainPlanRelationshipList && lessonSafeTrainPlanRelationshipList.size() > 0) {
            List<Long> lessonIds = lessonSafeTrainPlanRelationshipList.stream().map(LessonSafeTrainPlanRelationship::getLessonId).collect(Collectors.toList());
            if (lessonIds.size() > 0) {
                request.setLessonIds(lessonIds);
                List<UserLessonRecordLearnNumVO> userLessonRecordLearnNumVOS = userLessonRecordSafeTrainMongoService.getLessonLearnNumByCompanyDepartId(request);
                DecimalFormat df = new DecimalFormat("#.##");
                if (CollectionUtils.isNotEmpty(userLessonRecordLearnNumVOS)) {
                    List<Company> tempCompanyList = companyList.stream().filter(request.getChildren() == 0 ? company -> company.getId().equals(request.getOrgId()) : company -> company.getParentId().equals(request.getOrgId())).collect(Collectors.toList());
                    List<BusSafeTrainOrgVO> result = new ArrayList<>();
                    for (int i = 0; i < tempCompanyList.size(); i++) {
                        Company company = tempCompanyList.get(i);
                        BusSafeTrainOrgVO vo = new BusSafeTrainOrgVO();
                        vo.setId(company.getId());
                        vo.setName(company.getCompanyName());
                        vo.setFid(company.getParentId());
                        //根据id查找所有子部门id
                        List<Long> tempDepartIds = companyService.getSubDeptIds(company.getId());
                        if (null == request.getLearnFlag()) {
                            vo.setSumNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId()))).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                            vo.setLearnNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                            vo.setCompleteNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && (item.getStaticStatus() == 1 || item.getStaticStatus() == 2 || item.getStaticStatus() == 4)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        } else if (request.getLearnFlag() == 0) {
                            vo.setSumNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && item.getStaticStatus() != 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                            vo.setLearnNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && item.getStaticStatus() != 1 && item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                            vo.setCompleteNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && (item.getStaticStatus() == 2 || item.getStaticStatus() == 4)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        } else if (request.getLearnFlag() == 1) {
                            vo.setSumNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId()))).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                            vo.setLearnNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && (item.getStaticStatus() == 0 || item.getStaticStatus() == 1 || item.getStaticStatus() == 2)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                            vo.setCompleteNumber(userLessonRecordLearnNumVOS.stream().filter(item -> (tempDepartIds.contains(item.getCompanyId()) || tempDepartIds.contains(item.getDepartId())) && item.getStaticStatus() == 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        }
                        vo.setCompleteRate(vo.getSumNumber() > 0 ? (df.format(vo.getCompleteNumber() * 1.00 / vo.getSumNumber() * 100)) + "%" : "0%");
                        vo.setLearnRate(vo.getSumNumber() > 0 ? (df.format(vo.getLearnNumber() * 1.00 / vo.getSumNumber() * 100)) + "%" : "0%");
                        vo.setIsNext(tempDepartIds.size() > 1 ? "1" : "0");
                        result.add(vo);
                    }
                    return new PageUtils(pageEntity, result);
                }
            }
        }
        return new PageUtils(pageEntity, new ArrayList());
    }

    @Override
    public PageUtils getBusSafeTrainUserList(LessonSafeTrainPlanRequest request, PageEntity pageEntity) {
        List<Long> subDeptIds = companyService.getSubDeptIds(request.getOrgId());
        if (null != request.getOrgId()) {
            request.setOrgIds(companyService.getSubDeptIds(request.getOrgId()));
        }
        //查询计划关联表
        LambdaQueryWrapper<LessonSafeTrainPlanRelationship> lessonSafeTrainPlanRelationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lessonSafeTrainPlanRelationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1)
                .eq(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanId());
        List<LessonSafeTrainPlanRelationship> lessonSafeTrainPlanRelationshipList = lessonSafeTrainPlanRelationshipService.list(lessonSafeTrainPlanRelationshipLambdaQueryWrapper);
        if (null != lessonSafeTrainPlanRelationshipList && lessonSafeTrainPlanRelationshipList.size() > 0) {
            List<Long> lessonIds = lessonSafeTrainPlanRelationshipList.stream().map(LessonSafeTrainPlanRelationship::getLessonId).collect(Collectors.toList());
            if (lessonIds.size() > 0) {
                request.setLessonIds(lessonIds);
                request.setDepartIds(subDeptIds);
                List<UserLessonRecordSafeTrainUserVO> list = userLessonRecordSafeTrainMongoService.getUserLessonRecordSafeTrainBus(request);
                int size = list.size();
                if (null != pageEntity) {
                    int startIndex = (pageEntity.getPageIndex() - 1) * pageEntity.getPageSize();
                    int endIndex = (pageEntity.getPageIndex()) * pageEntity.getPageSize();
                    if (size > 0 && startIndex < size) {
                        if (endIndex > size) {
                            endIndex = size;
                        }
                        list = list.subList(startIndex, endIndex);
                    }
                }
                PageUtils pageUtils = new PageUtils(pageEntity, list);
                pageUtils.setTotalCount(size);
                int tempLearnFlag = null != request.getLearnFlag() ? request.getLearnFlag() : -999;
                list.forEach(item -> {
                    item.setStaticStatus(tempLearnFlag == 1 && noBeginStatus.contains(item.getStaticStatus()) ? -3 : tempLearnFlag == 1 && noCompleteStatus.contains(item.getStaticStatus()) ? 0 : tempLearnFlag == 0 && completedStatus.contains(item.getStaticStatus()) ? 1 : item.getStaticStatus());
                    item.setCompleteTime(completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0 ? item.getReservedField4() : completeStatus.contains(item.getStaticStatus()) ? item.getCompleteTime() : "");
                    item.setTrainStatusStr(completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0 ? "已完成(" + item.getScore() + "分)" : completeStatus.contains(item.getStaticStatus()) ? "已完成" : item.getStaticStatus() == -3 ? "未开始" : null != item.getCompleteTime() ? "未考试" : "培训中");
                    item.setStatisticalGroupStr(item.getStatisticalGroup() == 1 ? "管理人员" : "普通人员");
                    item.setIsValidStr(item.getIsValid() == 1 ? "正常" : "已收回");
                });
                return pageUtils;
            }
        }
        return new PageUtils(pageEntity, new ArrayList());
    }

    @Override
    public SafeTrainDataExportVO getBusSafeTrainOrgDetial(LessonSafeTrainPlanRequest request, PageEntity pageEntity) {
        SafeTrainDataExportVO result = new SafeTrainDataExportVO();
        Integer learnFlag = request.getLearnFlag();
        List<Long> subDeptIds = companyService.getSubDeptIds(request.getOrgId());
        LambdaQueryWrapper<Company> companyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyLambdaQueryWrapper.in(Company::getId, subDeptIds);
        List<Company> companyList = companyService.list(companyLambdaQueryWrapper);
        //Map<Long, String> departMap = companyList.stream().collect(Collectors.toMap(Company::getId, Company::getCompanyName));
        if (null != request.getOrgId()) {
            request.setOrgIds(companyService.getSubDeptIds(request.getOrgId()));
        }
        //获取课程时长
        LambdaQueryWrapper<LessonSafeTrainPlanRelationship> lessonSafeTrainPlanRelationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lessonSafeTrainPlanRelationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1)
                .eq(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanId());
        List<LessonSafeTrainPlanRelationship> lessonSafeTrainPlanRelationshipList = lessonSafeTrainPlanRelationshipService.list(lessonSafeTrainPlanRelationshipLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(lessonSafeTrainPlanRelationshipList)) {
            List<Long> lessonIds = lessonSafeTrainPlanRelationshipList.stream().map(LessonSafeTrainPlanRelationship::getLessonId).collect(Collectors.toList());
            List<LessonTimeCountVO> lessonTimeCountVOS = getBaseMapper().selectPlanLessonTotalTime(request);
            request.setDepartIds(subDeptIds);
            String[] parentIds = companyService.getById(request.getOrgId()).getAncestors().split(",");
            List<Long> parents = Arrays.stream(parentIds).map(Long::valueOf).collect(Collectors.toList());
            parents.add(request.getOrgId());
            request.setParentIds(parents);
            List<CompanyPlanTimeVO> companyPlanTimeVOS = getBaseMapper().selectCompanyPlanTime(request);
            result.setTrainDates(companyPlanTimeVOS);
            if (CollectionUtils.isNotEmpty(companyPlanTimeVOS)) {
                result.setTrainCycle(DateUtils.formatDate(companyPlanTimeVOS.get(0).getSTime(), "yyyy-MM-dd", "yyyy年MM月dd日") + "至" + DateUtils.formatDate(companyPlanTimeVOS.get(0).getETime(), "yyyy-MM-dd", "yyyy年MM月dd日"));
            }
            request.setLessonIds(lessonIds);
            List<UserLessonRecordSafeTrainUserVO> userRecordList = userLessonRecordSafeTrainMongoService.getUserLessonRecordSafeTrainBus(request);
            if (CollectionUtils.isNotEmpty(userRecordList)) {
                int tempLearnFlag = null != learnFlag ? learnFlag : -1;
                userRecordList.forEach(item -> {
                    Integer staticStatus = tempLearnFlag == 1 && noBeginStatus.contains(item.getStaticStatus()) ? -3 : tempLearnFlag == 1 && noCompleteStatus.contains(item.getStaticStatus()) ? 0 : tempLearnFlag == 0 && completedStatus.contains(item.getStaticStatus()) ? 1 : item.getStaticStatus();
                    item.setReservedField3(completeStatus.contains(staticStatus) ? "已完成" : staticStatus == -3 ? "未开始" : "未完成");
                    item.setScore1(Double.valueOf(item.getScore() == null ? "0.0" : item.getScore()));
                    item.setScore(item.getScore() == null ? "--" : item.getScore());
                    item.setCompleteTime(completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0 ? item.getReservedField4() : completeStatus.contains(item.getStaticStatus()) ? item.getCompleteTime() : "");
                    item.setTrainTotalTime(lessonTimeCountVOS.stream().filter(e -> e.getLessonId().equals(item.getLessonId())).findFirst().map(LessonTimeCountVO::getTotalTimeCount).get());
                });
            }
            result.setPassMark("-");
            //查询部门列表 有子部门查询子部门，否则查询自己
            List<Company> departs = companyList.stream().filter(e -> e.getParentId().equals(request.getOrgId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(departs)) {
                departs = companyList.stream().filter(e -> e.getId().equals(request.getOrgId())).collect(Collectors.toList());
            }
            DecimalFormat df = new DecimalFormat("#.##");
            List<DepartTrainDataStaticsVO> vos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(departs)) {
                departs.forEach(item -> {
                    DepartTrainDataStaticsVO vo = new DepartTrainDataStaticsVO();
                    vo.setDepartId(item.getId());
                    vo.setDepartName(item.getCompanyName());
                    List<Long> subDepartIds = companyService.getSubDeptIds(item.getId());
                    List<UserLessonRecordSafeTrainUserVO> userInfoList = userRecordList.stream().filter(e -> subDepartIds.contains(e.getCompanyId()) || subDepartIds.contains(e.getDepartId())).collect(Collectors.toList());
                    if (learnFlag == null) {
                        vo.setTotalUserNum(userInfoList.size());
                        vo.setCxUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() != -3).count());
                        vo.setWcUserNum(userInfoList.stream().filter(e -> completeStatus.contains(e.getStaticStatus())).count());
                        userInfoList.sort(Comparator.comparing(UserLessonRecordSafeTrainUserVO::getReservedField3));
                        userInfoList.sort(Comparator.comparing(UserLessonRecordSafeTrainUserVO::getScore1));
                        vo.setUserInfoList(userInfoList);
                    } else if (learnFlag == 0) {
                        List tempUserList = userInfoList.stream().filter(e -> e.getStaticStatus() != 1).collect(Collectors.toList());
                        vo.setTotalUserNum(tempUserList.size());
                        vo.setCxUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() != 1 && e.getStaticStatus() != -3).count());
                        vo.setWcUserNum(userInfoList.stream().filter(e -> completedStatus.contains(e.getStaticStatus())).count());
                        tempUserList.sort(Comparator.comparing(UserLessonRecordSafeTrainUserVO::getReservedField3));
                        tempUserList.sort(Comparator.comparing(UserLessonRecordSafeTrainUserVO::getScore1));
                        vo.setUserInfoList(tempUserList);
                    } else if (learnFlag == 1) {
                        vo.setTotalUserNum(userInfoList.size());
                        vo.setCxUserNum(userInfoList.stream().filter(e -> Arrays.asList(0, 1, 2).contains(e.getStaticStatus())).count());
                        vo.setWcUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() == 1).count());
                        userInfoList.sort(Comparator.comparing(UserLessonRecordSafeTrainUserVO::getReservedField3));
                        userInfoList.sort(Comparator.comparing(UserLessonRecordSafeTrainUserVO::getScore1));
                        vo.setUserInfoList(userInfoList);
                    }
                    String cxl = (vo.getTotalUserNum() > 0 ? df.format((double) vo.getCxUserNum() / vo.getTotalUserNum() * 100) : "0") + "%";
                    String wcl = (vo.getTotalUserNum() > 0 ? df.format((double) vo.getWcUserNum() / vo.getTotalUserNum() * 100) : "0") + "%";
                    vo.setCxl(cxl);
                    vo.setWcl(wcl);
                    vos.add(vo);
                });
            }

            DepartTrainDataStaticsVO vo = new DepartTrainDataStaticsVO();
            vo.setDepartId(9999L);
            vo.setDepartName("合计");
            vo.setTotalUserNum(vos.stream().mapToLong(DepartTrainDataStaticsVO::getTotalUserNum).sum());
            vo.setCxUserNum(vos.stream().mapToLong(DepartTrainDataStaticsVO::getCxUserNum).sum());
            vo.setWcUserNum(vos.stream().mapToLong(DepartTrainDataStaticsVO::getWcUserNum).sum());
            String cxl = (vo.getTotalUserNum() > 0 ? df.format((double) vo.getCxUserNum() / vo.getTotalUserNum() * 100) : "0") + "%";
            String wcl = (vo.getTotalUserNum() > 0 ? df.format((double) vo.getWcUserNum() / vo.getTotalUserNum() * 100) : "0") + "%";
            vo.setCxl(cxl);
            vo.setWcl(wcl);
            vos.add(vo);
            List<UserLessonRecordSafeTrainUserVO> noCompleteUserList = userRecordList.stream().filter(e -> !Arrays.asList(1, 2, 4).contains(e.getStaticStatus())).collect(Collectors.toList());
            result.setPassMark("--");
            result.setStudyNoComplete(noCompleteUserList.size());
            result.setStudyNoCompleteName(noCompleteUserList.size() > 0 ? StringUtils.join(noCompleteUserList.stream().map(UserLessonRecordSafeTrainUserVO::getUserName).collect(Collectors.toList()), ",") : "");
            vos.sort(Comparator.comparing(DepartTrainDataStaticsVO::getDepartId));
            result.setDepartTrainDataList(vos);
        }
        return result;
    }

    @Override
    public PageUtils getSafeTrainCompanyStatistics(LessonSafeTrainCompanyStatisticRequest request, PageEntity pageEntity) {
        String month = null != request.getLessonMonth() ? request.getLessonMonth() : request.getLessonDate();
        String lessonMonth = month.length() < 10 ? month + "-01" : month;
        request.setLessonMonth(lessonMonth);
        Integer learnFlag = request.getLearnFlag();
        List<SafeTrainCompanyStatisticsVO> safeTrainCompanyStatisticsVOS = getBaseMapper().getSafeTrainCompanyStatistics(request);
        if (CollectionUtils.isNotEmpty(safeTrainCompanyStatisticsVOS)) {
            DecimalFormat df = new DecimalFormat("#.##");
            //查询学习记录
            request.setLessonIds(safeTrainCompanyStatisticsVOS.stream().map(SafeTrainCompanyStatisticsVO::getLessonId).collect(Collectors.toList()));
            if (request.getDepartId() != null) {
                request.setDepartIds(companyService.getSubDeptIds(request.getDepartId()));
            }
            List<UserLessonRecordLearnNumVO> userRecordList = userLessonRecordSafeTrainMongoService.getSafeTrainCompanyStatisticsUser(request);
            if (CollectionUtils.isNotEmpty(userRecordList)) {
                safeTrainCompanyStatisticsVOS.forEach(item -> {
                    List<UserLessonRecordLearnNumVO> userInfoList = userRecordList.stream().filter(e -> e.getLessonId().equals(item.getLessonId()) && e.getCompanyId().equals(item.getCompanyId())).collect(Collectors.toList());
                    if (learnFlag == null) {
                        item.setTotalUserNum(userInfoList.stream().mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setCxUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setWcUserNum(userInfoList.stream().filter(e -> completeStatus.contains(e.getStaticStatus())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (learnFlag == 0) {
                        item.setTotalUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() != 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setCxUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() != 1 && e.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setWcUserNum(userInfoList.stream().filter(e -> completedStatus.contains(e.getStaticStatus())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (learnFlag == 1) {
                        item.setTotalUserNum(userInfoList.size());
                        item.setCxUserNum(userInfoList.stream().filter(e -> Arrays.asList(0, 1, 2).contains(e.getStaticStatus())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setWcUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() == 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    }
                    item.setCxl((item.getTotalUserNum() > 0 ? df.format((double) item.getCxUserNum() / item.getTotalUserNum() * 100) : "0") + "%");
                    item.setWcl((item.getTotalUserNum() > 0 ? df.format((double) item.getWcUserNum() / item.getTotalUserNum() * 100) : "0") + "%");
                    item.setWclSort(item.getTotalUserNum() > 0 ? Double.valueOf(df.format((double) item.getWcUserNum() / item.getTotalUserNum() * 100)) : 0);
                });
            }
            if (null != learnFlag && learnFlag == 0) {
                safeTrainCompanyStatisticsVOS = safeTrainCompanyStatisticsVOS.stream().filter(e -> e.getTotalUserNum() > 0).collect(Collectors.toList());
            }
            safeTrainCompanyStatisticsVOS.forEach(item -> {
                if (learnFlag == null) {
                    item.setLedgerFileName(month.replace("-", "年") + "月" + item.getCompanyName() + "安全培训全部数据台账");
                } else if (learnFlag == 0) {
                    item.setLedgerFileName(month.replace("-", "年") + "月" + item.getCompanyName() + "安全培训补学数据台账");
                } else if (learnFlag == 1) {
                    item.setLedgerFileName(month.replace("-", "年") + "月" + item.getCompanyName() + "安全培训数据台账");
                }
                item.setLedgerUrl("/ShowInfo/Show/Get_CompanySafeTrainFiles?LessonMonth=" + lessonMonth.substring(0, 7) + "&CompanyID=" + item.getCompanyId() + "&DepartID=" + request.getDepartId() + "&LessonID=" + item.getLessonId() + "&CompanyName=" + item.getCompanyName() + "&LearnFlag=" + request.getLearnFlag());
                item.setFileName(lessonMonth.substring(0, 7).replace("-", "年") + "月" + item.getCompanyName() + item.getLessonCategoryName() + "安全培训课程档案");
                item.setFilesUrl("/ShowInfo/Show/SafeTrainFiles?lessonid=" + item.getLessonId() + "&companyname=" + item.getCompanyName());
                item.setFaceLederUrl("/ShowInfo/Show/Get_CompanySafeTrainFaceRecord?LessonMonth=" + lessonMonth.substring(0, 7) + "&CompanyID=" + item.getCompanyId() + "&DepartID=" + request.getDepartId() + "&LessonID=" + item.getLessonId() + "&CompanyName=" + item.getCompanyName() + "&LearnFlag=" + request.getLearnFlag());
                item.setFaceLederFileName(lessonMonth.substring(0, 7).replace("-", "年") + "月" + item.getCompanyName() + item.getLessonCategoryName() + "安全培训数据人像照片");
            });
            if (request.getSortType() == 1) {
                safeTrainCompanyStatisticsVOS.sort(Comparator.comparing(SafeTrainCompanyStatisticsVO::getWclSort).reversed());
            } else if (request.getSortType() == 2) {
                safeTrainCompanyStatisticsVOS.sort(Comparator.comparing(SafeTrainCompanyStatisticsVO::getTotalUserNum).reversed());
            }
        }
        if (null != pageEntity) {
            int size = safeTrainCompanyStatisticsVOS.size();
            int startIndex = (pageEntity.getPageIndex() - 1) * pageEntity.getPageSize();
            int endIndex = (pageEntity.getPageIndex()) * pageEntity.getPageSize();
            if (size > 0 && startIndex < size) {
                if (endIndex > size) {
                    endIndex = size;
                }
                safeTrainCompanyStatisticsVOS = safeTrainCompanyStatisticsVOS.subList(startIndex, endIndex);
            }
            PageUtils pageUtils = new PageUtils(pageEntity, safeTrainCompanyStatisticsVOS);
            pageUtils.setTotalCount(size);
            return pageUtils;
        }
        return new PageUtils(pageEntity, safeTrainCompanyStatisticsVOS);
    }

    @Override
    public List<SafeTrainCompanyStatisticsVO> getSafeTrainCompanyStatistics2(LessonSafeTrainCompanyStatisticRequest request) {
        String month = request.getLessonMonth();
        String lessonMonth = month.length() < 10 ? month + "-01" : month;
        request.setLessonMonth(lessonMonth);
        Integer learnFlag = request.getLearnFlag();
        List<SafeTrainCompanyStatisticsVO> safeTrainCompanyStatisticsVOS = getBaseMapper().getSafeTrainCompanyStatistics2(request);
        if (CollectionUtils.isNotEmpty(safeTrainCompanyStatisticsVOS)) {
            DecimalFormat df = new DecimalFormat("#.##");
            //查询学习记录
            request.setLessonIds(safeTrainCompanyStatisticsVOS.stream().map(SafeTrainCompanyStatisticsVO::getLessonId).collect(Collectors.toList()));
            if (request.getDepartId() != null) {
                request.setDepartIds(companyService.getSubDeptIds(request.getDepartId()));
            }
            List<UserLessonRecordLearnNumVO> userRecordList = userLessonRecordSafeTrainMongoService.getSafeTrainCompanyStatisticsUser(request);
            if (CollectionUtils.isNotEmpty(userRecordList)) {
                safeTrainCompanyStatisticsVOS.forEach(item -> {
                    List<UserLessonRecordLearnNumVO> userInfoList = userRecordList.stream().filter(e -> e.getLessonId().equals(item.getLessonId()) && e.getCompanyId().equals(item.getCompanyId())).collect(Collectors.toList());
                    if (learnFlag == null) {
                        item.setTotalUserNum(userInfoList.stream().mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setCxUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setWcUserNum(userInfoList.stream().filter(e -> completeStatus.contains(e.getStaticStatus())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (learnFlag == 0) {
                        item.setTotalUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() != 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setCxUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() != 1 && e.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setWcUserNum(userInfoList.stream().filter(e -> completedStatus.contains(e.getStaticStatus())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (learnFlag == 1) {
                        item.setTotalUserNum(userInfoList.size());
                        item.setCxUserNum(userInfoList.stream().filter(e -> Arrays.asList(0, 1, 2).contains(e.getStaticStatus())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        item.setWcUserNum(userInfoList.stream().filter(e -> e.getStaticStatus() == 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    }
                    item.setCxl((item.getTotalUserNum() > 0 ? df.format((double) item.getCxUserNum() / item.getTotalUserNum() * 100) : "0") + "%");
                    item.setWcl((item.getTotalUserNum() > 0 ? df.format((double) item.getWcUserNum() / item.getTotalUserNum() * 100) : "0") + "%");
                    item.setWclSort(item.getTotalUserNum() > 0 ? Double.valueOf(df.format((double) item.getWcUserNum() / item.getTotalUserNum() * 100)) : 0);
                });
                if (null != learnFlag && learnFlag == 0) {
                    safeTrainCompanyStatisticsVOS = safeTrainCompanyStatisticsVOS.stream().filter(e -> e.getTotalUserNum() > 0).collect(Collectors.toList());
                }
                safeTrainCompanyStatisticsVOS.forEach(item -> {
                    if (learnFlag == null) {
                        item.setLedgerFileName(month.replace("-", "年") + "月" + item.getCompanyName() + "安全培训全部数据台账");
                    } else if (learnFlag == 0) {
                        item.setLedgerFileName(month.replace("-", "年") + "月" + item.getCompanyName() + "安全培训补学数据台账");
                    } else if (learnFlag == 1) {
                        item.setLedgerFileName(month.replace("-", "年") + "月" + item.getCompanyName() + "安全培训数据台账");
                    }
                    item.setLedgerUrl("/ShowInfo/Show/Get_CompanySafeTrainFiles?LessonMonth=" + lessonMonth.substring(0, 7) + "&CompanyID=" + item.getCompanyId() + "&DepartID=" + request.getDepartId() + "&LessonID=" + item.getLessonId() + "&CompanyName=" + item.getCompanyName() + "&LearnFlag=" + request.getLearnFlag());
                    item.setFileName(lessonMonth.substring(0, 7).replace("-", "年") + "月" + item.getCompanyName() + item.getLessonCategoryName() + "安全培训课程档案");
                    item.setFilesUrl("/ShowInfo/Show/SafeTrainFiles?lessonid=" + item.getLessonId() + "&companyname=" + item.getCompanyName());
                    item.setFaceLederUrl("/ShowInfo/Show/Get_CompanySafeTrainFaceRecord?LessonMonth=" + lessonMonth.substring(0, 7) + "&CompanyID=" + item.getCompanyId() + "&DepartID=" + request.getDepartId() + "&LessonID=" + item.getLessonId() + "&CompanyName=" + item.getCompanyName() + "&LearnFlag=" + request.getLearnFlag());
                    item.setFaceLederFileName(lessonMonth.substring(0, 7).replace("-", "年") + "月" + item.getCompanyName() + item.getLessonCategoryName() + "安全培训数据人像照片");
                });

            }
        }
        return safeTrainCompanyStatisticsVOS;
    }

    @Override
    public SafeTrainDataExportVO getSafeTrainCompanyDetial(SafeTrainCompanyDetialRequest request) {
        SafeTrainDataExportVO result = new SafeTrainDataExportVO();
        Integer learnFlag = request.getLearnFlag();
        List<SafeTrainCompanyDetialUserVO> userRecordList = userLessonRecordSafeTrainMongoService.getSafeTrainCompanyDetialUser(request);
        if (CollectionUtils.isNotEmpty(userRecordList)) {
            userRecordList.forEach(userRecord -> {
                userRecord.setReservedField3(completeStatus.contains(userRecord.getStaticStatus()) ? "已完成" : userRecord.getStaticStatus() == -3 ? "未开始" : "未完成");
                userRecord.setScore("--");
                userRecord.setScore1(0);
                userRecord.setCompleteTime(userRecord.getExamId() > 0 ? "--" : userRecord.getCompleteTime());
                userRecord.setDepartId(userRecord.getDepartId() == 0 ? -999 : userRecord.getDepartId());
                userRecord.setReservedField5(userRecord.getExamId() > 0 ? userRecord.getReservedField5() : "无考试");
            });
            result.setPassMark("无考试");
            if (request.getDepartId() != null) {
                request.setDepartIds(companyService.getSubDeptIds(request.getDepartId()));
            }
            List<SafeTrainCompanyDetialUserExamRecordVO> safeTrainCompanyDetialUserExamRecordVOS = examUserRecordMapper.selectSafeTrainCompanyDetialUserExamRecord(request);
            List<CourseVO> courseVOS = lessonCourseSafeTrainMapper.selectCourseListByLessonId(request.getLessonId());
            LessonSafeTrain lessonSafeTrain = lessonSafeTrainService.getById(request.getLessonId());
            ExamVO examVO = examMapper.getExamByLessonId(request.getLessonId());
            List<UserCompanyVO> userCompanyList = userCompanyMapper.selectPlateNumberByCompanyId(request.getCompanyId());
            for (int i = 0; i < safeTrainCompanyDetialUserExamRecordVOS.size(); i++) {
                SafeTrainCompanyDetialUserExamRecordVO safeTrainCompanyDetialUserExamRecordVO = safeTrainCompanyDetialUserExamRecordVOS.get(i);
                for (int j = 0; j < userRecordList.size(); j++) {
                    SafeTrainCompanyDetialUserVO safeTrainCompanyDetialUserVO = userRecordList.get(j);
                    if (safeTrainCompanyDetialUserVO.getUserCode().equals(safeTrainCompanyDetialUserExamRecordVO.getUserId()) && safeTrainCompanyDetialUserExamRecordVO.getScore() >= safeTrainCompanyDetialUserExamRecordVO.getPassMark()) {
                        safeTrainCompanyDetialUserVO.setScore(safeTrainCompanyDetialUserExamRecordVO.getScore().toString());
                        safeTrainCompanyDetialUserVO.setScore1(safeTrainCompanyDetialUserExamRecordVO.getScore());
                        safeTrainCompanyDetialUserVO.setCompleteTime(safeTrainCompanyDetialUserExamRecordVO.getExamCompleteTime());
                        safeTrainCompanyDetialUserVO.setReservedField5(safeTrainCompanyDetialUserExamRecordVO.getSignUrl());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(courseVOS)) {
                for (CourseVO courseVO : courseVOS) {
                    courseVO.setFileTypeStr(fileTypeMap.get(courseVO.getFileType()));
                }
                result.setCourseList(courseVOS);
            }
            if (null != lessonSafeTrain && lessonSafeTrain.getIsValid() == 1) {
                String timestr = "";
                if (null != result.getCourseList()) {
                    timestr = "(" + DateUtils.formatTime(courseVOS.stream().mapToLong(CourseVO::getTimeCount).sum()) + ")";
                }
                result.setTotalTimeCount(lessonSafeTrain.getTotalTimeCount() + "学时" + timestr);
                result.setTrainStartTime(lessonSafeTrain.getSTime());
                result.setTrainEndTime(lessonSafeTrain.getETime());
                result.setTrainCycle(DateUtils.formatDate(result.getTrainStartTime(), "yyyy-MM-dd", "yyyy年MM月dd日") + "-" + DateUtils.formatDate(result.getTrainEndTime(), "yyyy-MM-dd", "yyyy年MM月dd日"));
                result.setLessonMonth(lessonSafeTrain.getLessonDate());
                result.setLessonCategoryName(lessonSafeTrain.getLessonCategoryName());
                result.setLessonName(lessonSafeTrain.getLessonName());
            }
            if (null != examVO) {
                result.setPassMark(examVO.getPassMark() + "分");
            }
            for (int i = 0; i < userCompanyList.size(); i++) {
                UserCompanyVO userCompanyVO = userCompanyList.get(i);
                userRecordList.forEach(item -> {
                    if (item.getUserCode().equals(userCompanyVO.getUserCode())) {
                        item.setPlateNumber(userCompanyVO.getPlateNumber());
                    }
                });
            }
        }
        //userRecordList根据departId创建map
        Map<Long, List<SafeTrainCompanyDetialUserVO>> userRecordMap = userRecordList.stream().collect(Collectors.groupingBy(SafeTrainCompanyDetialUserVO::getDepartId));
        //遍历userRecordMap
        DecimalFormat df = new DecimalFormat("#.##");
        List<DepartTrainDataStaticsVO> vos = new ArrayList<>();
        result.setDepartTrainDataList(vos);
        for (Map.Entry<Long, List<SafeTrainCompanyDetialUserVO>> entry : userRecordMap.entrySet()) {
            Long departId = entry.getKey();
            List<SafeTrainCompanyDetialUserVO> userRecordListByDepartId = entry.getValue();
            userRecordListByDepartId.forEach(item -> {
                if (StringUtils.isNotEmpty(item.getSignImg())) {
                    item.setSignImg(item.getSignImg() + "?x-oss-process=image/resize,h_35/quality,Q_80");
                }
                if (StringUtils.isNotEmpty(item.getReservedField5())) {
                    item.setReservedField5(item.getReservedField5() + "?x-oss-process=image/resize,h_35/quality,Q_80");
                }
            });
            //根据departId获取部门名称
            String departName = departId > 0 ? userRecordListByDepartId.get(0).getDepartName() : "--";
            //创建部门对象
            DepartTrainDataStaticsVO vo = new DepartTrainDataStaticsVO();
            vo.setDepartId(departId);
            vo.setDepartName(departName);
            if (null == learnFlag) {
                vo.setTotalUserNum(userRecordListByDepartId.size());
                vo.setCxUserNum(userRecordListByDepartId.stream().filter(e -> e.getStaticStatus() != -3).count());
                vo.setWcUserNum(userRecordListByDepartId.stream().filter(e -> completeStatus.contains(e.getStaticStatus())).count());
                vo.setUserInfoList(userRecordListByDepartId.stream().sorted(Comparator.comparing(SafeTrainCompanyDetialUserVO::getReservedField3).reversed()).sorted(Comparator.comparing(SafeTrainCompanyDetialUserVO::getScore1)).collect(Collectors.toList()));
            } else if (learnFlag == 0) {
                vo.setTotalUserNum(userRecordListByDepartId.stream().filter(e -> e.getStaticStatus() != 1).count());
                vo.setCxUserNum(userRecordListByDepartId.stream().filter(e -> e.getStaticStatus() != 1 && e.getStaticStatus() != -3).count());
                vo.setWcUserNum(userRecordListByDepartId.stream().filter(e -> completedStatus.contains(e.getStaticStatus())).count());
                vo.setUserInfoList(userRecordListByDepartId.stream().filter(e -> e.getStaticStatus() != 1).sorted(Comparator.comparing(SafeTrainCompanyDetialUserVO::getReservedField3).reversed()).sorted(Comparator.comparing(SafeTrainCompanyDetialUserVO::getScore1)).collect(Collectors.toList()));
            } else if (learnFlag == 1) {
                vo.setTotalUserNum(userRecordListByDepartId.size());
                vo.setCxUserNum(userRecordListByDepartId.stream().filter(e -> Arrays.asList(0, 1, 2).contains(e.getStaticStatus())).count());
                vo.setWcUserNum(userRecordListByDepartId.stream().filter(e -> 1 == e.getStaticStatus()).count());
                vo.setUserInfoList(userRecordListByDepartId.stream().sorted(Comparator.comparing(SafeTrainCompanyDetialUserVO::getReservedField3).reversed()).sorted(Comparator.comparing(SafeTrainCompanyDetialUserVO::getScore1)).collect(Collectors.toList()));
            }
            String cxl = (vo.getTotalUserNum() > 0 ? df.format((double) vo.getCxUserNum() / vo.getTotalUserNum() * 100) : "0") + "%";
            String wcl = (vo.getTotalUserNum() > 0 ? df.format((double) vo.getWcUserNum() / vo.getTotalUserNum() * 100) : "0") + "%";
            vo.setCxl(cxl);
            vo.setWcl(wcl);
            vos.add(vo);
        }
        DepartTrainDataStaticsVO vo = new DepartTrainDataStaticsVO();
        vo.setDepartId(9999L);
        vo.setDepartName("合计");
        vo.setTotalUserNum(vos.stream().mapToLong(DepartTrainDataStaticsVO::getTotalUserNum).sum());
        vo.setCxUserNum(vos.stream().mapToLong(DepartTrainDataStaticsVO::getCxUserNum).sum());
        vo.setWcUserNum(vos.stream().mapToLong(DepartTrainDataStaticsVO::getWcUserNum).sum());
        String cxl = (vo.getTotalUserNum() > 0 ? df.format((double) vo.getCxUserNum() / vo.getTotalUserNum() * 100) : "0") + "%";
        String wcl = (vo.getTotalUserNum() > 0 ? df.format((double) vo.getWcUserNum() / vo.getTotalUserNum() * 100) : "0") + "%";
        vo.setCxl(cxl);
        vo.setWcl(wcl);
        vos.add(vo);
        List<SafeTrainCompanyDetialUserVO> noCompleteUserList = userRecordList.stream().filter(e -> !Arrays.asList(1, 2, 4).contains(e.getStaticStatus())).collect(Collectors.toList());
        result.setStudyNoComplete(noCompleteUserList.size());
        result.setStudyNoCompleteName(noCompleteUserList.size() > 0 ? StringUtils.join(noCompleteUserList.stream().map(SafeTrainCompanyDetialUserVO::getUserName).collect(Collectors.toList()), ",") : "");
        vos.sort(Comparator.comparing(DepartTrainDataStaticsVO::getDepartId));
        return result;
    }

    @Override
    public PageUtils getSafeTrainUserRecordDetail(SafeTrainUserRecordDetailRequest request, PageEntity pageEntity) {
        String month = request.getLessonMonth();
        String lessonMonth = month.length() < 10 ? month + "-01" : month;
        LessonSafeTrainCompanyStatisticRequest lessonSafeTrainCompanyStatisticRequest = (LessonSafeTrainCompanyStatisticRequest) DataTransfer.transfer(request, LessonSafeTrainCompanyStatisticRequest.class);
        lessonSafeTrainCompanyStatisticRequest.setLessonMonth(lessonMonth);
        List<SafeTrainCompanyStatisticsVO> safeTrainCompanyStatisticsVOS = getBaseMapper().getSafeTrainCompanyStatistics(lessonSafeTrainCompanyStatisticRequest);
        if (CollectionUtils.isNotEmpty(safeTrainCompanyStatisticsVOS)) {
            request.setLessonIds(safeTrainCompanyStatisticsVOS.stream().map(SafeTrainCompanyStatisticsVO::getLessonId).collect(Collectors.toList()));
            request.setCompanyIds(safeTrainCompanyStatisticsVOS.stream().map(SafeTrainCompanyStatisticsVO::getCompanyId).collect(Collectors.toList()));
            //根据companyId和lessonId查询用户学习记录
            List<SafeTrainUserRecordDetailVO> userRecordList = userLessonRecordSafeTrainMongoService.getSafeTrainUserRecordDetail(request);
            int size = userRecordList.size();
            if (null != pageEntity) {
                int startIndex = (pageEntity.getPageIndex() - 1) * pageEntity.getPageSize();
                int endIndex = (pageEntity.getPageIndex()) * pageEntity.getPageSize();
                if (size > 0 && startIndex < size) {
                    if (endIndex > size) {
                        endIndex = size;
                    }
                    userRecordList = userRecordList.subList(startIndex, endIndex);
                }
            }
            PageUtils pageUtils = new PageUtils(pageEntity, userRecordList);
            pageUtils.setTotalCount(size);
            userRecordList.forEach(item -> {
                item.setCompleteTime(completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0 ? item.getReservedField4() : completeStatus.contains(item.getStaticStatus()) ? item.getCompleteTime() : "");
                item.setIsValidStr(item.getIsValid() == 1 ? "正常" : "已收回");
                item.setTrainStatusStr(completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0 ? "已完成(" + item.getScore() + "分)" : completeStatus.contains(item.getStaticStatus()) ? "已完成" : item.getStaticStatus() == -3 ? "未开始" : "未完成");
                item.setStatisticalGroupStr(item.getStatisticalGroup() == 1 ? "管理人员" : "普通人员");
            });
            return pageUtils;
        }
        return new PageUtils(pageEntity, new ArrayList());
    }

    @Override
    public UserSafeTrainFilesDetailVO getUserSafeTrainFilesDetail(UserSafeTrainFilesDetailRequest request) {
        UserSafeTrainFilesDetailVO result = new UserSafeTrainFilesDetailVO();
        LessonRecordVO lessonRecordVO = userLessonRecordSafeTrainMongoService.getUserSafeTrainFilesDetail(request);
        result.setLessonRecord(lessonRecordVO);
        if (null != lessonRecordVO) {
            if (lessonRecordVO.getExamId() > 0) {
                Map<String, Object> params = new HashMap<>();
                params.put("userId", request.getUserCode());
                params.put("lessonIds", Arrays.asList(request.getLessonId()));
                UserExamInfoVo userExamInfoVo = examMapper.getUserExamInfo(params);
                if (null != userExamInfoVo) {
                    //查询用户头像
                    AuthUserInfoVO authUserInfoVO = userInfoService.selectUserByUserCode(lessonRecordVO.getUserCode());
                    lessonRecordVO.setUserPhoto(authUserInfoVO.getUserPhoto());
                    lessonRecordVO.setScore(userExamInfoVo.getScore());
                    lessonRecordVO.setExamStateStr(userExamInfoVo.getScore() == -1 ? "未考试" : userExamInfoVo.getScore() >= userExamInfoVo.getPassMark() ? "合格" : "不合格");
                    lessonRecordVO.setExamCompleteTime(userExamInfoVo.getScore() >= userExamInfoVo.getPassMark() ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", userExamInfoVo.getCreationTime()) : "--");
                    lessonRecordVO.setExamSign(userExamInfoVo.getScore() >= userExamInfoVo.getPassMark() ? userExamInfoVo.getSignUrl() : "");
                    int resitNum = lessonRecordVO.getResitNumber() - userExamInfoVo.getExamCount() + 1;
                    lessonRecordVO.setResitNumber(Math.max(resitNum, 0));
                }
            }
            Map<String, Object> params = new HashMap<>();
            params.put("lessonId", request.getLessonId());
            UserCourseRecordSafeTrain queryRequest = new UserCourseRecordSafeTrain();
            queryRequest.setLessonId(request.getLessonId());
            queryRequest.setUserCode(request.getUserCode());
            List<UserCourseRecordSafeTrain> userCourse = userCourseRecordSafeTrainMongoService.getUserCourse(queryRequest);
            List<CourseRecordVO> courses = lessonCourseSafeTrainMapper.selectCourseRecord(params);
            if (CollectionUtils.isNotEmpty(userCourse)) {
                courses.forEach(item -> {
                    List<UserCourseRecordSafeTrain> courseRecordSafeTrains = userCourse.stream().filter(a -> a.getCourseId().equals(item.getCourseId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(courseRecordSafeTrains)) {
                        UserCourseRecordSafeTrain userCourseRecordSafeTrain = courseRecordSafeTrains.get(0);
                        item.setCourseIsComplete(String.valueOf(userCourseRecordSafeTrain.getCourseIsComplete()));
                        item.setFaceDistinguishImg(String.valueOf(userCourseRecordSafeTrain.getFaceDistinguishImg()));
                        item.setStartStudyTime(String.valueOf(userCourseRecordSafeTrain.getStartStudyTime()));
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(courses)) {
                courses.forEach(item -> {
                    if (null == item.getCourseIsComplete()) {
                        item.setCourseIsComplete("-3");
                    }
                    if (null == item.getStartStudyTime()) {
                        item.setStartStudyTime("");
                    }
                    if (null == item.getFaceDistinguishImg()) {
                        item.setFaceDistinguishImg("");
                    }
                });
                result.setCourseRecord(courses);
            }
        }
        return result;
    }

    @Override
    public PageUtils getUserCourseRecordLogAll(UserCourseRecordLogRequest request, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        LambdaQueryWrapper<UserCourseRecordSafeTrainLogAll> queryWrapper = Wrappers.lambdaQuery(UserCourseRecordSafeTrainLogAll.class)
                .eq(UserCourseRecordSafeTrainLogAll::getUserCode, request.getUserCode())
                .eq(UserCourseRecordSafeTrainLogAll::getLessonId, request.getLessonId())
                .eq(UserCourseRecordSafeTrainLogAll::getCourseId, request.getCourseId())
                .eq(UserCourseRecordSafeTrainLogAll::getIsValid, 1);
        return new PageUtils(pageEntity, userCourseRecordSafeTrainLogALLMapper.selectList(queryWrapper));
    }

    @Override
    public PageUtils getSafeTrainCompanyStatisticsList(GetSafeTrainCompanyStatisticsListRequest request, PageEntity pageEntity) {
        if (null == request.getPlanId()) {
            return getSafeTrainCompanyStatisticsListNoPlan(request, pageEntity);
        }
        List<GetSafeTrainCompanyStatisticsListVO> vos = new ArrayList();
        LambdaQueryWrapper<LessonSafeTrainPlanRelationship> relationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
        relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1);
        relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanId());
        List<LessonSafeTrainPlanRelationship> planRelationshipList = lessonSafeTrainPlanRelationshipService.list(relationshipLambdaQueryWrapper);
        Map<Long, List<Long>> planIdLessonIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(planRelationshipList)) {
            planIdLessonIdMap = planRelationshipList.stream().collect(Collectors.groupingBy(LessonSafeTrainPlanRelationship::getPlanId, Collectors.mapping(LessonSafeTrainPlanRelationship::getLessonId, Collectors.toList())));
        }
        LessonSafeTrainPlan plan = getById(request.getPlanId());
        List<Long> lessonIds = planIdLessonIdMap.get(plan.getId());
        if (CollectionUtils.isEmpty(lessonIds)) {
            return new PageUtils(pageEntity, new ArrayList<>());
        }
        request.setDepartIds(companyService.getSubDeptIds(request.getOrgId()));
        request.setLessonIds(lessonIds);
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getListLearnNumByRequest(request);
        List<CompanyVO> subDeparts = companyService.selectChildDepartNoRecursive(request.getOrgId());
        if (CollectionUtils.isEmpty(subDeparts)) {
            subDeparts = new ArrayList<>();
            Company company = companyService.getById(request.getOrgId());
            if (null != company) {
                CompanyVO companyVO = (CompanyVO) DataTransfer.transfer(company, CompanyVO.class);
                companyVO.setIsNext(0);
                subDeparts.add(companyVO);
            }
        }
        for (CompanyVO subDepart : subDeparts) {
            GetSafeTrainCompanyStatisticsListVO vo = new GetSafeTrainCompanyStatisticsListVO();
            vo.setOrgId(subDepart.getId());
            vo.setOrgName(subDepart.getCompanyName());
            vo.setIsNext(subDepart.getIsNext());
            vos.add(vo);
        }
        if (CollectionUtils.isNotEmpty(vos)) {
            DecimalFormat df = new DecimalFormat("#.##");
            for (GetSafeTrainCompanyStatisticsListVO vo : vos) {
                List<Long> subDeptIds = companyService.getSubDeptIds(vo.getOrgId());
                List<UserLessonRecordSafeTrain> safeTrains = userLessonRecordSafeTrains.stream().filter(item -> subDeptIds.contains(item.getDepartId())).collect(Collectors.toList());
                vo.setTotalUserNum(safeTrains.stream().mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
                vo.setWcUserNum(safeTrains.stream().filter(item -> completeStatus.contains(item.getStaticStatus())).mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
                vo.setWcl(vo.getTotalUserNum() > 0 ? (df.format(vo.getWcUserNum() * 1.00 / vo.getTotalUserNum() * 100)) + "%" : "0%");
                vo.setWclSort(0.0);
            }
            vos = vos.stream().filter(vo -> vo.getTotalUserNum() > 0).collect(Collectors.toList());
        }
        if (null != pageEntity) {
            int size = vos.size();
            int startIndex = (pageEntity.getPageIndex() - 1) * pageEntity.getPageSize();
            int endIndex = (pageEntity.getPageIndex()) * pageEntity.getPageSize();
            if (size > 0 && startIndex < size) {
                if (endIndex > size) {
                    endIndex = size;
                }
                vos = vos.subList(startIndex, endIndex);
            }
            PageUtils pageUtils = new PageUtils(pageEntity, vos);
            pageUtils.setTotalCount(size);
            return pageUtils;
        }
        return new PageUtils(pageEntity, vos);
    }

    private PageUtils getSafeTrainCompanyStatisticsListNoPlan(GetSafeTrainCompanyStatisticsListRequest request, PageEntity pageEntity) {
        List<GetSafeTrainCompanyStatisticsListVO> vos = new ArrayList();
        if (null == request.getLessonId()) {
            LambdaQueryWrapper<LessonSafeTrainPlan> planLambdaQueryWrapper = Wrappers.lambdaQuery(LessonSafeTrainPlan.class)
                    .eq(LessonSafeTrainPlan::getIsValid, 1)
                    .eq(LessonSafeTrainPlan::getMonthStr, request.getLessonDate()).orderByDesc(LessonSafeTrainPlan::getSort, LessonSafeTrainPlan::getCreationTime);
            List<LessonSafeTrainPlan> planList = this.list(planLambdaQueryWrapper);
            request.setPlanIds(planList.stream().map(LessonSafeTrainPlan::getId).collect(Collectors.toList()));
            LambdaQueryWrapper<LessonSafeTrainPlanRelationship> relationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
            relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1);
            relationshipLambdaQueryWrapper.in(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanIds());
            List<LessonSafeTrainPlanRelationship> planRelationshipList = lessonSafeTrainPlanRelationshipService.list(relationshipLambdaQueryWrapper);
            List<Long> lessonIds = planRelationshipList.stream().map(LessonSafeTrainPlanRelationship::getLessonId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lessonIds)) {
                return new PageUtils(pageEntity, new ArrayList<>());
            }
            request.setLessonIds(lessonIds);
        } else {
            request.setLessonIds(Arrays.asList(request.getLessonId()));
        }

        request.setDepartIds(companyService.getSubDeptIds(request.getOrgId()));
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getListLearnNumByRequest(request);
        List<CompanyVO> subDeparts = companyService.selectChildDepartNoRecursive(request.getOrgId());
        if (CollectionUtils.isEmpty(subDeparts)) {
            subDeparts = new ArrayList<>();
            Company company = companyService.getById(request.getOrgId());
            if (null != company) {
                CompanyVO companyVO = (CompanyVO) DataTransfer.transfer(company, CompanyVO.class);
                companyVO.setIsNext(0);
                subDeparts.add(companyVO);
            }
        }
        for (CompanyVO subDepart : subDeparts) {
            GetSafeTrainCompanyStatisticsListVO vo = new GetSafeTrainCompanyStatisticsListVO();
            vo.setOrgId(subDepart.getId());
            vo.setOrgName(subDepart.getCompanyName());
            vo.setIsNext(subDepart.getIsNext());
            vos.add(vo);
        }
        if (CollectionUtils.isNotEmpty(vos)) {
            DecimalFormat df = new DecimalFormat("#.##");
            for (GetSafeTrainCompanyStatisticsListVO vo : vos) {
                List<Long> subDeptIds = companyService.getSubDeptIds(vo.getOrgId());
                List<UserLessonRecordSafeTrain> safeTrains = userLessonRecordSafeTrains.stream().filter(item -> subDeptIds.contains(item.getDepartId())).collect(Collectors.toList());
                vo.setTotalUserNum(safeTrains.stream().mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
                vo.setWcUserNum(safeTrains.stream().filter(item -> completeStatus.contains(item.getStaticStatus())).mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
                vo.setWcl(vo.getTotalUserNum() > 0 ? (df.format(vo.getWcUserNum() * 1.00 / vo.getTotalUserNum() * 100)) + "%" : "0%");
                vo.setWclSort(0.0);
            }
            vos = vos.stream().filter(vo -> vo.getTotalUserNum() > 0).collect(Collectors.toList());
        }
        if (null != pageEntity) {
            int size = vos.size();
            int startIndex = (pageEntity.getPageIndex() - 1) * pageEntity.getPageSize();
            int endIndex = (pageEntity.getPageIndex()) * pageEntity.getPageSize();
            if (size > 0 && startIndex < size) {
                if (endIndex > size) {
                    endIndex = size;
                }
                vos = vos.subList(startIndex, endIndex);
            }
            PageUtils pageUtils = new PageUtils(pageEntity, vos);
            pageUtils.setTotalCount(size);
            return pageUtils;
        }
        return new PageUtils(pageEntity, vos);
    }

    @Override
    public GetSafeTrainCompanyStatisticsTotalVO getSafeTrainCompanyStatisticsTotal(GetSafeTrainCompanyStatisticsTotalRequest request) {
        if (null == request.getPlanId()) {
            return getSafeTrainCompanyStatisticsTotalNoPlan(request);
        }
        LambdaQueryWrapper<LessonSafeTrainPlanRelationship> relationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
        relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1);
        relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanId());
        List<LessonSafeTrainPlanRelationship> planRelationshipList = lessonSafeTrainPlanRelationshipService.list(relationshipLambdaQueryWrapper);
        Map<Long, List<Long>> planIdLessonIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(planRelationshipList)) {
            planIdLessonIdMap = planRelationshipList.stream().collect(Collectors.groupingBy(LessonSafeTrainPlanRelationship::getPlanId, Collectors.mapping(LessonSafeTrainPlanRelationship::getLessonId, Collectors.toList())));
        }
        LessonSafeTrainPlan plan = getById(request.getPlanId());
        GetSafeTrainCompanyStatisticsTotalVO vo = new GetSafeTrainCompanyStatisticsTotalVO();
        vo.setPlanId(plan.getId());
        vo.setPlanName(plan.getPlanName());
        Company company = companyService.getById(request.getOrgId());
        vo.setCompanyName(company.getCompanyName());
        vo.setLessonDate(request.getLessonDate());
        List<Long> lessonIds = planIdLessonIdMap.get(plan.getId());
        if (CollectionUtils.isEmpty(lessonIds)) {
            return vo;
        }
        request.setLessonIds(lessonIds);
        request.setDepartIds(companyService.getSubDeptIds(request.getOrgId()));
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getTotalLearnNumByRequest(request);
        vo.setTotalUserNum(userLessonRecordSafeTrains.stream().mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
        vo.setCxUserNum(userLessonRecordSafeTrains.stream().filter(item -> item.getStaticStatus() != -3).mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
        vo.setWcUserNum(userLessonRecordSafeTrains.stream().filter(item -> completeStatus.contains(item.getStaticStatus())).mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
        DecimalFormat df = new DecimalFormat("#.##");
        vo.setCxl(vo.getTotalUserNum() > 0 ? (df.format(vo.getCxUserNum() * 1.00 / vo.getTotalUserNum() * 100)) + "%" : "0%");
        vo.setWcl(vo.getTotalUserNum() > 0 ? (df.format(vo.getWcUserNum() * 1.00 / vo.getTotalUserNum() * 100)) + "%" : "0%");
        return vo;
    }

    private GetSafeTrainCompanyStatisticsTotalVO getSafeTrainCompanyStatisticsTotalNoPlan(GetSafeTrainCompanyStatisticsTotalRequest request) {
        GetSafeTrainCompanyStatisticsTotalVO vo = new GetSafeTrainCompanyStatisticsTotalVO();
        if (null == request.getLessonId()) {
            LambdaQueryWrapper<LessonSafeTrainPlan> planLambdaQueryWrapper = Wrappers.lambdaQuery(LessonSafeTrainPlan.class)
                    .eq(LessonSafeTrainPlan::getIsValid, 1)
                    .eq(LessonSafeTrainPlan::getMonthStr, request.getLessonDate()).orderByDesc(LessonSafeTrainPlan::getSort, LessonSafeTrainPlan::getCreationTime);
            List<LessonSafeTrainPlan> planList = this.list(planLambdaQueryWrapper);
            request.setPlanIds(planList.stream().map(LessonSafeTrainPlan::getId).collect(Collectors.toList()));
            LambdaQueryWrapper<LessonSafeTrainPlanRelationship> relationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
            relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1);
            relationshipLambdaQueryWrapper.in(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanIds());
            List<LessonSafeTrainPlanRelationship> planRelationshipList = lessonSafeTrainPlanRelationshipService.list(relationshipLambdaQueryWrapper);
            List<Long> lessonIds = planRelationshipList.stream().map(LessonSafeTrainPlanRelationship::getLessonId).collect(Collectors.toList());
            Company company = companyService.getById(request.getOrgId());
            vo.setCompanyName(company.getCompanyName());
            vo.setLessonDate(request.getLessonDate());
            if (CollectionUtils.isEmpty(lessonIds)) {
                return vo;
            }
            request.setLessonIds(lessonIds);
        } else {
            request.setLessonIds(Arrays.asList(request.getLessonId()));
        }
        request.setDepartIds(companyService.getSubDeptIds(request.getOrgId()));
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getTotalLearnNumByRequest(request);
        vo.setTotalUserNum(userLessonRecordSafeTrains.stream().mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
        vo.setCxUserNum(userLessonRecordSafeTrains.stream().filter(item -> item.getStaticStatus() != -3).mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
        vo.setWcUserNum(userLessonRecordSafeTrains.stream().filter(item -> completeStatus.contains(item.getStaticStatus())).mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
        DecimalFormat df = new DecimalFormat("#.##");
        vo.setCxl(vo.getTotalUserNum() > 0 ? (df.format(vo.getCxUserNum() * 1.00 / vo.getTotalUserNum() * 100)) + "%" : "0%");
        vo.setWcl(vo.getTotalUserNum() > 0 ? (df.format(vo.getWcUserNum() * 1.00 / vo.getTotalUserNum() * 100)) + "%" : "0%");
        return vo;
    }

    @Override
    public List<GetSafeTrainCompanyLessonVO> getSafeTrainCompanyLesson(GetSafeTrainCompanyLessonRequest request) {
        if (null != request.getOrgId()) {
            request.setDepartIds(companyService.getSubDeptIds(request.getOrgId()));
        }
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getSafeTrainCompanyLesson(request);
        List<GetSafeTrainCompanyLessonVO> vos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrains)) {
            userLessonRecordSafeTrains.forEach(userCourseRecordSafeTrain -> {
                GetSafeTrainCompanyLessonVO vo = new GetSafeTrainCompanyLessonVO();
                vo.setLessonId(userCourseRecordSafeTrain.getLessonId());
                vo.setLessonName(userCourseRecordSafeTrain.getLessonName());
                vos.add(vo);
            });
        }
        return vos;
    }

    @Override
    public PageUtils getSafeTrainStatisticsUserList(GetSafeTrainStatisticsUserListRequest request, PageEntity pageEntity) {
        if (null == request.getPlanId()) {
            return getSafeTrainStatisticsUserListNoPlan(request, pageEntity);
        }
        List<GetSafeTrainStatisticsUserListVO> vos = new ArrayList();
        LambdaQueryWrapper<LessonSafeTrainPlanRelationship> relationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
        relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1);
        relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanId());
        List<LessonSafeTrainPlanRelationship> planRelationshipList = lessonSafeTrainPlanRelationshipService.list(relationshipLambdaQueryWrapper);
        Map<Long, List<Long>> planIdLessonIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(planRelationshipList)) {
            planIdLessonIdMap = planRelationshipList.stream().collect(Collectors.groupingBy(LessonSafeTrainPlanRelationship::getPlanId, Collectors.mapping(LessonSafeTrainPlanRelationship::getLessonId, Collectors.toList())));
        }
        LessonSafeTrainPlan plan = getById(request.getPlanId());
        List<Long> lessonIds = planIdLessonIdMap.get(plan.getId());
        if (CollectionUtils.isEmpty(lessonIds)) {
            return new PageUtils(pageEntity, new ArrayList<>());
        }
        request.setLessonIds(lessonIds);
        if (null != request.getOrgId()) {
            request.setDepartIds(companyService.getSubDeptIds(request.getOrgId()));
        }
        PageUtils pageUtils = userLessonRecordSafeTrainMongoService.getSafeTrainStatisticsUserList(request, pageEntity);
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = (List<UserLessonRecordSafeTrain>) pageUtils.getList();
        for (UserLessonRecordSafeTrain item : userLessonRecordSafeTrains) {
            GetSafeTrainStatisticsUserListVO vo = new GetSafeTrainStatisticsUserListVO();
            vo.setStaticStatus(completeStatus.contains(item.getStaticStatus()) ? 1 : item.getStaticStatus() == 0 || item.getStaticStatus() == 3 ? 0 : -3);
            vo.setUserId(item.getUserId());
            vo.setUserCode(item.getUserCode());
            vo.setUserPhoto(item.getUserPhoto());
            vo.setUserName(item.getUserName());
            vo.setExamId(item.getExamId());
            vo.setDepartName(item.getDepartName());
            vo.setLessonCategoryName(item.getLessonCategoryName());
            vo.setScore(item.getScore());
            vo.setLessonId(item.getLessonId());
            vo.setLessonName(item.getLessonName());
            vo.setReservedField5((completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0) ? "已完成（" + item.getScore() + "分）" : completeStatus.contains(item.getStaticStatus()) ? "已完成" : item.getStaticStatus() == -3 ? "未开始" : "未完成");
            vos.add(vo);
        }
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> userCodes = vos.stream().map(GetSafeTrainStatisticsUserListVO::getUserCode).collect(Collectors.toList());
            LambdaQueryWrapper<UserCompany> userCompanyLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userCompanyLambdaQueryWrapper.in(UserCompany::getUserCode, userCodes);
            userCompanyLambdaQueryWrapper.isNotNull(UserCompany::getUserPhoto);
            List<UserCompany> userCompanyList = userCompanyMapper.selectList(userCompanyLambdaQueryWrapper);
            Map<String, String> userPhotoMap = userCompanyList.stream().collect(Collectors.toMap(UserCompany::getUserCode, UserCompany::getUserPhoto));
            vos.forEach(item -> {
                item.setUserPhoto(StringUtils.isNotEmpty(userPhotoMap.get(item.getUserCode())) ? userPhotoMap.get(item.getUserCode()) : item.getUserPhoto());
            });
        }
        pageUtils.setList(vos);
        return pageUtils;
    }

    private PageUtils getSafeTrainStatisticsUserListNoPlan(GetSafeTrainStatisticsUserListRequest request, PageEntity pageEntity) {
        List<GetSafeTrainStatisticsUserListVO> vos = new ArrayList();
        if (null == request.getLessonId()) {
            LambdaQueryWrapper<LessonSafeTrainPlan> planLambdaQueryWrapper = Wrappers.lambdaQuery(LessonSafeTrainPlan.class)
                    .eq(LessonSafeTrainPlan::getIsValid, 1)
                    .eq(LessonSafeTrainPlan::getMonthStr, request.getLessonDate()).orderByDesc(LessonSafeTrainPlan::getSort, LessonSafeTrainPlan::getCreationTime);
            List<LessonSafeTrainPlan> planList = this.list(planLambdaQueryWrapper);
            request.setPlanIds(planList.stream().map(LessonSafeTrainPlan::getId).collect(Collectors.toList()));
            LambdaQueryWrapper<LessonSafeTrainPlanRelationship> relationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
            relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1);
            relationshipLambdaQueryWrapper.in(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanIds());
            List<LessonSafeTrainPlanRelationship> planRelationshipList = lessonSafeTrainPlanRelationshipService.list(relationshipLambdaQueryWrapper);
            List<Long> lessonIds = planRelationshipList.stream().map(LessonSafeTrainPlanRelationship::getLessonId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lessonIds)) {
                return new PageUtils(pageEntity, new ArrayList<>());
            }
            request.setLessonIds(lessonIds);
        } else {
            request.setLessonIds(Arrays.asList(request.getLessonId()));
        }

        if (null != request.getOrgId()) {
            request.setDepartIds(companyService.getSubDeptIds(request.getOrgId()));
        }
        PageUtils pageUtils = userLessonRecordSafeTrainMongoService.getSafeTrainStatisticsUserList(request, pageEntity);
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = (List<UserLessonRecordSafeTrain>) pageUtils.getList();
        for (UserLessonRecordSafeTrain item : userLessonRecordSafeTrains) {
            GetSafeTrainStatisticsUserListVO vo = new GetSafeTrainStatisticsUserListVO();
            vo.setStaticStatus(completeStatus.contains(item.getStaticStatus()) ? 1 : item.getStaticStatus() == 0 || item.getStaticStatus() == 3 ? 0 : -3);
            vo.setUserId(item.getUserId());
            vo.setUserCode(item.getUserCode());
            vo.setUserPhoto(item.getUserPhoto());
            vo.setUserName(item.getUserName());
            vo.setExamId(item.getExamId());
            vo.setDepartName(item.getDepartName());
            vo.setLessonCategoryName(item.getLessonCategoryName());
            vo.setScore(item.getScore());
            vo.setLessonId(item.getLessonId());
            vo.setLessonName(item.getLessonName());
            vo.setReservedField5((completeStatus.contains(item.getStaticStatus()) && null != item.getExamId() && item.getExamId() > 0) ? "已完成（" + item.getScore() + "分）" : completeStatus.contains(item.getStaticStatus()) ? "已完成" : item.getStaticStatus() == -3 ? "未开始" : "未完成");
            vos.add(vo);
        }
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> userCodes = vos.stream().map(GetSafeTrainStatisticsUserListVO::getUserCode).collect(Collectors.toList());
            LambdaQueryWrapper<UserCompany> userCompanyLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userCompanyLambdaQueryWrapper.in(UserCompany::getUserCode, userCodes);
            userCompanyLambdaQueryWrapper.isNotNull(UserCompany::getUserPhoto);
            List<UserCompany> userCompanyList = userCompanyMapper.selectList(userCompanyLambdaQueryWrapper);
            Map<String, String> userPhotoMap = userCompanyList.stream().collect(Collectors.toMap(UserCompany::getUserCode, UserCompany::getUserPhoto));
            vos.forEach(item -> {
                item.setUserPhoto(StringUtils.isNotEmpty(userPhotoMap.get(item.getUserCode())) ? userPhotoMap.get(item.getUserCode()) : item.getUserPhoto());
            });
        }
        pageUtils.setList(vos);
        return pageUtils;
    }

    @Override
    public boolean addUserExamResitNumSafeTrain(AddUserExamResitNumSafeTrainRequest request) {
        if (request.getExamNum() <= 0) {
            throw new CustomException("增加补考次数必须大于0");
        }
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setUserId(Long.valueOf(request.getUserIds()));
        queryRequest.setIsValid(1);
        queryRequest.setLessonId(request.getLessonId());
        List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        if (CollectionUtils.isNotEmpty(userLessonRecordSafeTrainList)) {
            UserLessonRecordSafeTrain userLessonRecordSafeTrain = userLessonRecordSafeTrainList.get(0);
            userLessonRecordSafeTrain.setResitNumber(userLessonRecordSafeTrain.getResitNumber() + request.getExamNum());
            userLessonRecordSafeTrain.setReviseCode(user.getUserCode());
            userLessonRecordSafeTrain.setReviseTime(LocalDateTime.now());
            return userLessonRecordSafeTrainMongoService.updateUserLessonById(userLessonRecordSafeTrain);
        }
        return false;
    }

    @Override
    public List<GetSafeTrainCompanyStatisticsPlanVO> getSafeTrainCompanyStatisticsPlan(GetSafeTrainCompanyStatisticsPlanRequest request) {
        //根据年月查询培训计划，再根据培训计划查询对应课程
        List<GetSafeTrainCompanyStatisticsPlanVO> vos = new ArrayList<>();
        LambdaQueryWrapper<LessonSafeTrainPlan> planLambdaQueryWrapper = Wrappers.lambdaQuery(LessonSafeTrainPlan.class)
                .eq(LessonSafeTrainPlan::getIsValid, 1)
                .eq(LessonSafeTrainPlan::getMonthStr, request.getLessonDate()).orderByDesc(LessonSafeTrainPlan::getSort, LessonSafeTrainPlan::getCreationTime);
        List<LessonSafeTrainPlan> planList = this.list(planLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(planList)) {
            LambdaQueryWrapper<LessonSafeTrainPlanRelationship> relationshipLambdaQueryWrapper = new LambdaQueryWrapper<>();
            relationshipLambdaQueryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1);
            relationshipLambdaQueryWrapper.in(LessonSafeTrainPlanRelationship::getPlanId, planList.stream().map(LessonSafeTrainPlan::getId).collect(Collectors.toList()));
            List<LessonSafeTrainPlanRelationship> planRelationshipList = lessonSafeTrainPlanRelationshipService.list(relationshipLambdaQueryWrapper);
            Map<Long, List<Long>> planIdLessonIdMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(planRelationshipList)) {
                planIdLessonIdMap = planRelationshipList.stream().collect(Collectors.groupingBy(LessonSafeTrainPlanRelationship::getPlanId, Collectors.mapping(LessonSafeTrainPlanRelationship::getLessonId, Collectors.toList())));
            }
            DecimalFormat df = new DecimalFormat("#.##");
            for (LessonSafeTrainPlan plan : planList) {
                GetSafeTrainCompanyStatisticsPlanVO vo = new GetSafeTrainCompanyStatisticsPlanVO();
                vo.setPlanId(plan.getId());
                vo.setPlanName(plan.getPlanName());
                List<Long> lessonIds = planIdLessonIdMap.get(plan.getId());
                if (CollectionUtils.isEmpty(lessonIds)) {
                    return vos;
                }
                request.setLessonIds(lessonIds);
                request.setDepartIds(companyService.getSubDeptIds(request.getOrgId()));
                List<UserLessonRecordSafeTrain> userLessonRecordSafeTrains = userLessonRecordSafeTrainMongoService.getCompanyLearnNumByRequest(request);
                vo.setTotalUserNum(userLessonRecordSafeTrains.stream().mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
                vo.setCxUserNum(userLessonRecordSafeTrains.stream().filter(item -> item.getStaticStatus() != -3).mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
                vo.setWcUserNum(userLessonRecordSafeTrains.stream().filter(item -> completeStatus.contains(item.getStaticStatus())).mapToInt(UserLessonRecordSafeTrain::getLearnNum).sum());
                vo.setCxl(vo.getTotalUserNum() > 0 ? (df.format(vo.getCxUserNum() * 1.00 / vo.getTotalUserNum() * 100)) + "%" : "0%");
                vo.setWcl(vo.getTotalUserNum() > 0 ? (df.format(vo.getWcUserNum() * 1.00 / vo.getTotalUserNum() * 100)) + "%" : "0%");
                vos.add(vo);
            }
        }
        return vos.stream().filter(item -> item.getTotalUserNum() > 0).collect(Collectors.toList());
    }

    @Override
    public PageUtils getBusSafeTrainListC(LessonSafeTrainPlanRequest request, PageEntity pageEntity) {
        String lessonMonth = request.getLessonMonth();
        if (StringUtils.isNotEmpty(lessonMonth) && lessonMonth.length() < 10) {
            request.setLessonMonth(lessonMonth + "-01");
        }
        if (null != request.getOrgId()) {
            request.setOrgIds(companyService.getSubDeptIds(request.getOrgId()));
        }
        List<LessonSafeTrainPlanVO> list = getBaseMapper().getBusSafeTrainList(request);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> lessonIds = new ArrayList<>();
            Map<Long, List<Long>> planIdLessonIdMap = new HashMap<>();
            LambdaQueryWrapper<LessonSafeTrainPlanRelationship> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LessonSafeTrainPlanRelationship::getIsValid, 1);
            queryWrapper.in(LessonSafeTrainPlanRelationship::getPlanId, list.stream().map(LessonSafeTrainPlanVO::getId).collect(Collectors.toList()));
            List<LessonSafeTrainPlanRelationship> planRelationshipList = lessonSafeTrainPlanRelationshipService.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(planRelationshipList)) {
                lessonIds = planRelationshipList.stream().map(LessonSafeTrainPlanRelationship::getLessonId).distinct().collect(Collectors.toList());
                planIdLessonIdMap = planRelationshipList.stream().collect(Collectors.groupingBy(LessonSafeTrainPlanRelationship::getPlanId, Collectors.mapping(LessonSafeTrainPlanRelationship::getLessonId, Collectors.toList())));
                request.setLessonIds(lessonIds);
                List<UserLessonRecordLearnNumVO> userLessonRecordLearnNumVOS = userLessonRecordSafeTrainMongoService.getLearnNum(request);
                for (int i = 0; i < list.size(); i++) {
                    LessonSafeTrainPlanVO lessonSafeTrainPlanVO = list.get(i);
                    List<Long> tempLessonIds = planIdLessonIdMap.get(lessonSafeTrainPlanVO.getId());
                    if (CollectionUtils.isEmpty(tempLessonIds)) {
                        continue;
                    }
                    if (null == request.getLearnFlag()) {
                        lessonSafeTrainPlanVO.setTotalUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setCxUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setWcUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && (item.getStaticStatus() == 1 || item.getStaticStatus() == 2 || item.getStaticStatus() == 4)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (request.getLearnFlag() == 0) {
                        lessonSafeTrainPlanVO.setTotalUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && item.getStaticStatus() != 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setCxUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && item.getStaticStatus() != 1 && item.getStaticStatus() != -3).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setWcUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && (item.getStaticStatus() == 2 || item.getStaticStatus() == 4)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    } else if (request.getLearnFlag() == 1) {
                        lessonSafeTrainPlanVO.setTotalUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId())).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setCxUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && (item.getStaticStatus() == 0 || item.getStaticStatus() == 1 || item.getStaticStatus() == 2)).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                        lessonSafeTrainPlanVO.setWcUserNum(userLessonRecordLearnNumVOS.stream().filter(item -> tempLessonIds.contains(item.getLessonId()) && item.getStaticStatus() == 1).mapToInt(UserLessonRecordLearnNumVO::getLearnNum).sum());
                    }
                    DecimalFormat df = new DecimalFormat("#.##");
                    lessonSafeTrainPlanVO.setCxl(lessonSafeTrainPlanVO.getTotalUserNum() > 0 ? (df.format(lessonSafeTrainPlanVO.getCxUserNum() * 1.00 / lessonSafeTrainPlanVO.getTotalUserNum() * 100)) + "%" : "0%");
                    lessonSafeTrainPlanVO.setWcl(lessonSafeTrainPlanVO.getTotalUserNum() > 0 ? (df.format(lessonSafeTrainPlanVO.getWcUserNum() * 1.00 / lessonSafeTrainPlanVO.getTotalUserNum() * 100)) + "%" : "0%");
                }
            }
            list = list.stream().filter(item -> item.getTotalUserNum() > 0).collect(Collectors.toList());
            int size = list.size();
            if (null != pageEntity) {
                int startIndex = (pageEntity.getPageIndex() - 1) * pageEntity.getPageSize();
                int endIndex = (pageEntity.getPageIndex()) * pageEntity.getPageSize();
                if (size > 0 && startIndex < size) {
                    if (endIndex > size) {
                        endIndex = size;
                    }
                    list = list.subList(startIndex, endIndex);
                }
            }
            PageUtils pageUtils = new PageUtils(pageEntity, list);
            pageUtils.setTotalCount(size);
            return pageUtils;
        } else {
            list = new ArrayList<>();
        }
        return new PageUtils(pageEntity, list);
    }
}