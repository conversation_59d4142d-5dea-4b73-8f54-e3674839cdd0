package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.PlatformRole;
import com.fzkj.project.system.mapper.PlatformRoleMapper;
import com.fzkj.project.system.request.PlatformRoleRequest;
import com.fzkj.project.system.service.PlatformMenuService;
import com.fzkj.project.system.service.PlatformRoleService;
import com.fzkj.project.system.vo.PlatformRoleVO;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 平台角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class PlatformRoleServiceImpl extends ServiceImpl<PlatformRoleMapper, PlatformRole> implements PlatformRoleService {

    private final PlatformMenuService platformMenuService;
    @Override
    public PageUtils listPlatformRole(PlatformRoleRequest request, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<PlatformRoleVO> list = this.baseMapper.getPlatformRoleList(request.getRoleName(),request.getIsValid(),request.getOperPlatform(),request.getType());
        return new PageUtils(pageEntity, list);
    }

    @Override
    public List<PlatformRoleVO> listPlatformRoleSelect(PlatformRoleRequest request) {
        return this.baseMapper.getPlatformRoleListByCompanyIdAndOper(request.getOperPlatform(),request.getCompanyId());

    }


    @Override
    public PageUtils listByPage(PlatformRoleRequest request, PageEntity pageEntity) {
        PlatformRole entity = (PlatformRole) DataTransfer.transfer(request, PlatformRole.class);
        LambdaQueryWrapper<PlatformRole> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<PlatformRole> page = this.page(
                new Query<PlatformRole>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public PlatformRoleVO findPlatformRole(Long id) {
        PlatformRole entity = this.getById(id);
        PlatformRoleVO vo = (PlatformRoleVO) DataTransfer.transfer(entity, PlatformRoleVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePlatformRole(PlatformRoleVO vo) {
        String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
        PlatformRole entity = (PlatformRole) DataTransfer.transfer(vo, PlatformRole.class);
        entity.setIsValid(1);
        entity.setCreatorTime(LocalDateTime.now());
        entity.setIsShow(1);
        entity.setCreatorCode(userCode);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlatformRole(PlatformRoleVO vo) {
        String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
        PlatformRole entity = (PlatformRole) DataTransfer.transfer(vo, PlatformRole.class);
        entity.setReviseTime(LocalDateTime.now());
        entity.setReviseCode(userCode);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removePlatformRole(Long id) {
        PlatformRole platformRole = this.getById(id);
        platformRole.setIsValid(platformRole.getIsValid() == 1?0:1);
        return this.updateById(platformRole);
    }
}