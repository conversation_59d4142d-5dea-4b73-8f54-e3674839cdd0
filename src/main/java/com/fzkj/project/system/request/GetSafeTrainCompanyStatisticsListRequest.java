package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class GetSafeTrainCompanyStatisticsListRequest {

    @ApiModelProperty(value = "组织ID")
    @NotNull(message = "组织ID不能为空")
    @JsonProperty("OrgID")
    private Long orgId;

    @ApiModelProperty(value = "培训计划ID")
    @NotNull(message = "培训计划ID不能为空")
    @JsonProperty("PlanID")
    private Long planId;

    @JsonProperty("LessonID")
    private Long lessonId;

    private List<Long> planIds;

    private List<Long> lessonIds;

    private List<Long> departIds;

    @ApiModelProperty(value = "课程日期")
    @NotNull(message = "课程日期不能为空")
    @JsonProperty("LessonDate")
    private String lessonDate;
}
