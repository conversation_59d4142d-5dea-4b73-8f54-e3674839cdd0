package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.Exam;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.request.ExamRequest;
import com.fzkj.project.system.request.GetCompanySafeTrainFaceRecordRequest;
import com.fzkj.project.system.request.GetUserExamInfoRequest;
import com.fzkj.project.system.vo.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 考试信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Mapper
public interface ExamMapper extends BaseMapper<Exam> {

    List<ExamVO> selectExamInfo(ExamRequest request);

    List<ExaminationPaperRuleVO> selectExamPaperRule(ExamRequest request);

    List<ExamQuestionVO> selectExamQuestion(ExamRequest request);

    ExamVO getExamByLessonId(Long lessonId);

    UserExamInfoVo getUserExamInfo(Map<String, Object> params);

    List<GetUserExamInfoVO> selectUserExamInfo(GetUserExamInfoRequest request);

    List<ExamFaceRecordVO> selectExamFaceRecord(GetCompanySafeTrainFaceRecordRequest request);
}