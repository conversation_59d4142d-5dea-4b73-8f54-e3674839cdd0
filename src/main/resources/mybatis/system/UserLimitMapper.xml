<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.UserLimitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.UserLimit">
        <id column="id" property="id" />
        <result column="user_code" property="userCode" />
        <result column="target_id" property="targetId" />
        <result column="user_role_id" property="userRoleId" />
        <result column="oper_platform" property="operPlatform" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creator_time" property="creatorTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_code, target_id, user_role_id, oper_platform, source, sort, creator_code, creator_time, revise_code, revise_time, remark, is_valid
    </sql>

    <select id="selectUserLimitListByUserCode" resultType="com.fzkj.project.system.vo.UserLimitVO">
        SELECT
            * ,
            dc.company_name companyName,
            dct.company_name fName,
            pr.role_name roleName,
            prm.role_name roleNameMini
        FROM
            t_d_user_limit ul
                LEFT JOIN t_d_company dc ON ul.target_id = dc.id
                LEFT JOIN t_d_company dct ON dc.parent_id = dct.id
                LEFT JOIN t_d_platform_role pr ON ul.pc_role_id = pr.id
                LEFT JOIN t_d_platform_role prm ON ul.mobile_role_id = prm.id
        WHERE ul.user_code = #{userCode}
          and ul.target_id &lt;&gt;543
    </select>

    <select id="selectUserLimitListByCompanyId" resultType="com.fzkj.project.system.vo.UserLimitVO">
        SELECT
            ul.* ,
            dc.company_name companyName,
            pr.role_name roleName,
            prm.role_name roleNameMini,
            ui.id_card,
            ui.phone,
            uc.user_name
        FROM
            t_d_user_limit ul
                LEFT JOIN t_d_company dc ON ul.target_id = dc.id
                LEFT JOIN t_d_platform_role pr ON ul.pc_role_id = pr.id
                LEFT JOIN t_d_platform_role prm ON ul.mobile_role_id = prm.id
                LEFT JOIN t_d_user_info ui ON ul.user_code = ui.user_code
                LEFT JOIN t_d_user_company uc ON ul.user_code = uc.user_code
        WHERE ul.target_id = #{companyId}
    </select>

    <select id="selectLoginAuthTargetVO" resultType="com.fzkj.project.system.vo.AuthTargetVO">
        SELECT
            dc.id,
            dc.fid,
            dc.company_name,
            dcf.company_name fName,
            dc.company_name packageName,
            dc.area_code,
            dc.area_name
        FROM
            t_d_user_limit ul
        LEFT JOIN t_d_company dc ON ul.target_id = dc.id
        LEFT JOIN t_d_company dcf ON dc.fid = dcf.id
        WHERE ul.user_code = #{userCode}
          and ul.target_id &lt;&gt;543
        <if test="targetId != null"> AND ul.target_id in (${targetId})</if>
    </select>
    <select id="selectUserLimitCountByUserCode" resultType="java.lang.Integer">
        select count(*) from t_d_user_limit where user_code=#{userCode} and depart_id=#{departId}
    </select>

</mapper>