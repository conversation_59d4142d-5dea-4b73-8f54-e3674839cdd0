package com.fzkj.project.system.mongo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全培训课件分发学员学习记录
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@Document(collection = "_tableSafeTrainUserCourse")
@ApiModel(value = "安全培训课件分发学员学习记录实体")
public class UserCourseRecordSafeTrain implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @Id
    private ObjectId id;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课程分类")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课件ID")
    @JsonProperty("CourseID")
    private Long courseId;

    @ApiModelProperty(value = "课件名称")
    @JsonProperty("CourseName")
    private String courseName;

    @ApiModelProperty(value = "文件类型")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(value = "文件地址")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "课件完成状态：（-3,未开始，0学习中，1已完成，2过期学习，默认-3）")
    @JsonProperty("CourseIsComplete")
    private Integer courseIsComplete;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "部门ID")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "岗位ID")
    @JsonProperty("WorkID")
    private Long workId;

    @ApiModelProperty(value = "用户编号")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "是否统计")
    @JsonProperty("IsStatic")
    private Integer isStatic;

    @ApiModelProperty(value = "学习时长")
    @JsonProperty("StudyTimeCount")
    private Long studyTimeCount;

    @ApiModelProperty(value = "课件时长")
    @JsonProperty("TimeCount")
    private Long timeCount;

    @ApiModelProperty(value = "人脸识别弹框的时间（562,2256）")
    @JsonProperty("FaceRecognitionString")
    private String faceRecognitionString;

    @ApiModelProperty(value = "人脸识别图片地址")
    @JsonProperty("FaceDistinguishImg")
    private String faceDistinguishImg;

    @ApiModelProperty(value = "开始学习时间")
    @JsonProperty("StartStudyTime")
    private String startStudyTime;

    @ApiModelProperty(value = "完成学习时间")
    @JsonProperty("CompleteTime")
    private String completeTime;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "分发来源 1：售后服务端分发的， 0：用户自己购买分发的")
    @JsonProperty("DisSource")
    private Integer disSource;

    @ApiModelProperty(value = "参培课程（1：（通用课程） 2：（定制课程））")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "预留字段1")
    @JsonProperty("ReservedField1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    @JsonProperty("ReservedField2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    @JsonProperty("ReservedField4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    @JsonProperty("ReservedField5")
    private String reservedField5;

    @TableField(exist = false)
    @JsonIgnore
    private String userCodes;
    @TableField(exist = false)
    @JsonIgnore
    private List<Long> departIds;
}