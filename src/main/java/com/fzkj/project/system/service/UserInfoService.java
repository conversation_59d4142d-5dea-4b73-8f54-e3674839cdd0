package com.fzkj.project.system.service;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.request.AuthPortraitVerificationRequest;
import com.fzkj.project.system.request.GetUserMsgCenterRequest;
import com.fzkj.project.system.request.UpdateReadyStatusRequest;
import com.fzkj.project.system.vo.AuthUserInfoVO;
import com.fzkj.project.system.vo.UserFilesVO;
import com.fzkj.project.system.vo.UserInfoVO;
import com.fzkj.project.system.request.UserInfoRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.project.system.vo.UserManageVO;

import java.util.List;

/**
 * <p>
 * 用户登录信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface UserInfoService extends IService<UserInfo> {

    List<UserInfo> listUserInfo(UserInfoRequest request);

    PageUtils listByPage(UserInfoRequest request, PageEntity pageEntity);

    String exportUserInfo(UserInfoRequest request);

    PageUtils getUserSelect(UserInfoRequest request, PageEntity pageEntity);

    UserInfoVO findUserInfo(Long id);

    AjaxResult saveUserInfo(UserInfoVO vo);

    boolean updateUserInfo(UserInfoVO vo);

    Integer clearPhoto(String userCode);

    Integer editUserRecordSync(String userCode);

    Integer editJob(String userCode,String job);

    Integer editJoinTime(String userCode,String job);

    Integer editUserInfoRecordSync(UserInfoVO userInfoVO);

    PageUtils getUserOperLog(String userCode, PageEntity pageEntity);

    Integer restPassword(String userCode);

    Integer signurl(String userCode,String signUrl);

    Integer editphoto(String photoUrl);

    boolean removeUserInfo(Long id);

    UserInfo selectUserByUserName(String username);

    AuthUserInfoVO selectUserByUserCode(String userCode);

    AuthUserInfoVO selectUserByIdCard(String idCard);

    UserInfo selectUserByUnionId(String unionId, String openId);

    UserInfoVO selectUserLoginInfoByUserCode(String userCode);

    Integer companyEditUserInfo(UserInfoVO vo);

    Integer editPwd(UserInfoVO vo);

    AjaxResult importIsOut(UserInfoRequest request);

    AjaxResult Import(UserInfoRequest request);

    UserFilesVO getMyFiles(UserInfoVO vo);

    List<UserManageVO> getSendUserInfo(String month);

    PageUtils getUserMsgCenter(GetUserMsgCenterRequest request, PageEntity pageEntity);

    List<UserManageVO> getAllSendUserInfo();

    boolean updateReadyStatus(UpdateReadyStatusRequest request);

    boolean authFaceDetection(AuthPortraitVerificationRequest authPortraitVerificationRequest);
}
