package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.LessonSafeTrainPlan;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.vo.*;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 公交培训计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface LessonSafeTrainPlanService extends IService<LessonSafeTrainPlan> {

    List<LessonSafeTrainPlan> listLessonSafeTrainPlan(LessonSafeTrainPlanRequest request);

    PageUtils listByPage(LessonSafeTrainPlanRequest request, PageEntity pageEntity);

    LessonSafeTrainPlanVO findLessonSafeTrainPlan(Long id);

    Long saveLessonSafeTrainPlan(LessonSafeTrainPlanVO vo);

    boolean updateLessonSafeTrainPlan(LessonSafeTrainPlanVO vo);

    boolean removeLessonSafeTrainPlan(Long id);

    Long editLessonSafeTrainPlan(LessonSafeTrainPlanVO request);

    PageUtils getBusSafeTrainList(LessonSafeTrainPlanRequest request, PageEntity pageEntity);

    PageUtils getBusSafeTrainOrgList(LessonSafeTrainPlanRequest request, PageEntity pageEntity);

    PageUtils getBusSafeTrainUserList(LessonSafeTrainPlanRequest request, PageEntity pageEntity);

    SafeTrainDataExportVO getBusSafeTrainOrgDetial(LessonSafeTrainPlanRequest request, PageEntity pageEntity);

    PageUtils getSafeTrainCompanyStatistics(LessonSafeTrainCompanyStatisticRequest request, PageEntity pageEntity);

    List<SafeTrainCompanyStatisticsVO> getSafeTrainCompanyStatistics2(LessonSafeTrainCompanyStatisticRequest request);

    SafeTrainDataExportVO getSafeTrainCompanyDetial(SafeTrainCompanyDetialRequest request);

    PageUtils getSafeTrainUserRecordDetail(SafeTrainUserRecordDetailRequest request, PageEntity pageEntity);

    UserSafeTrainFilesDetailVO getUserSafeTrainFilesDetail(UserSafeTrainFilesDetailRequest request);

    PageUtils getUserCourseRecordLogAll(UserCourseRecordLogRequest request, PageEntity pageEntity);

    PageUtils getSafeTrainCompanyStatisticsList(GetSafeTrainCompanyStatisticsListRequest request, PageEntity pageEntity);

    GetSafeTrainCompanyStatisticsTotalVO getSafeTrainCompanyStatisticsTotal(GetSafeTrainCompanyStatisticsTotalRequest request);

    List<GetSafeTrainCompanyLessonVO> getSafeTrainCompanyLesson(GetSafeTrainCompanyLessonRequest request);

    PageUtils getSafeTrainStatisticsUserList(GetSafeTrainStatisticsUserListRequest request, PageEntity pageEntity);

    boolean addUserExamResitNumSafeTrain(AddUserExamResitNumSafeTrainRequest request);

    List<GetSafeTrainCompanyStatisticsPlanVO> getSafeTrainCompanyStatisticsPlan(GetSafeTrainCompanyStatisticsPlanRequest request);

    PageUtils getBusSafeTrainListC(LessonSafeTrainPlanRequest request, PageEntity pageEntity);

}
