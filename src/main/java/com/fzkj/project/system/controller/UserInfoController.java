package com.fzkj.project.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.aspectj.lang.enums.BusinessType;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.request.AuthPortraitVerificationRequest;
import com.fzkj.project.system.request.GetUserMsgCenterRequest;
import com.fzkj.project.system.request.UpdateReadyStatusRequest;
import com.fzkj.project.system.request.UserInfoRequest;
import com.fzkj.project.system.service.UserInfoService;
import com.fzkj.project.system.vo.UserInfoVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用户登录信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userPort")
public class UserInfoController extends BaseEntity {

    private final UserInfoService UserInfoServiceImpl;

//    @PostMapping("GetUser")
    @RequestMapping(value = "/GetUser", method = RequestMethod.POST)
    @ApiOperation(value = "用户管理分页查询")
    public AjaxResult listUserInfoByPage(@RequestBody CommonRequest commonRequest) {
        UserInfoRequest request = (UserInfoRequest) commonRequest.getRequest(UserInfoRequest.class);
        return AjaxResult.success(UserInfoServiceImpl.listByPage(request, commonRequest.getPageEntity()));
    }

    @PostMapping("ExportUserInfo")
    @ApiOperation(value = "用户管理查询分页")
    public AjaxResult exportUserInfo(@RequestBody CommonRequest commonRequest) {
        UserInfoRequest request = (UserInfoRequest) commonRequest.getRequest(UserInfoRequest.class);
        return AjaxResult.success(UserInfoServiceImpl.exportUserInfo(request));
    }

    @PostMapping("GetUserSelect")
    @ApiOperation(value = "用户下拉查询")
    public AjaxResult getUserSelect(@RequestBody CommonRequest commonRequest) {
        UserInfoRequest request = (UserInfoRequest) commonRequest.getRequest(UserInfoRequest.class);
        return AjaxResult.success(UserInfoServiceImpl.getUserSelect(request,commonRequest.getPageEntity()));
    }

    @PostMapping("EditUser")
    @ApiOperation(value = "用户登录信息表修改数据")
    @Log(title="用户信息修改",businessType= BusinessType.UPDATE)
    public AjaxResult updateUserInfo(@RequestBody CommonRequest commonRequest) {
        UserInfoVO vo = (UserInfoVO) commonRequest.getRequest(UserInfoVO.class);
        if(vo.getFlag().equals("clearphoto")){
            //清除头像
            return AjaxResult.success(UserInfoServiceImpl.clearPhoto(vo.getUserCode()));
        }else if(vo.getFlag().equals("resetpwd")){
            //重置密码
            return AjaxResult.success(UserInfoServiceImpl.restPassword(vo.getUserCode()));
        }else if(vo.getFlag().equals("signurl")){
            //更新保存签名
            return AjaxResult.success(UserInfoServiceImpl.signurl(vo.getUserCode(),vo.getSignUrl()));
        }else if(vo.getFlag().equals("editphoto")){
            //更新保存头像
            return AjaxResult.success(UserInfoServiceImpl.editphoto(vo.getUserPhoto()));
        }else if(vo.getFlag().equals("add")){
            return UserInfoServiceImpl.saveUserInfo(vo);
        }else{
            return AjaxResult.error("类型异常");
        }
    }

    @PostMapping("GetUserOperLog")
    @ApiOperation(value = "获取用户修改日志")
    public AjaxResult getUserOperLog(@RequestBody CommonRequest commonRequest) {
        UserInfoVO vo = (UserInfoVO) commonRequest.getRequest(UserInfoVO.class);
        return AjaxResult.success(UserInfoServiceImpl.getUserOperLog(vo.getUserCode(),commonRequest.getPageEntity()));
    }

    @PostMapping("EditUserRecordSync")
    @ApiOperation(value = "修改")
    @Log(title="用户信息修改",businessType= BusinessType.UPDATE)
    public AjaxResult editUserRecordSync(@RequestBody CommonRequest commonRequest) {
        UserInfoVO vo = (UserInfoVO) commonRequest.getRequest(UserInfoVO.class);
        if(vo.getFlag().equals("isout")){
            //离职
            return AjaxResult.success(UserInfoServiceImpl.editUserRecordSync(vo.getUserCode()));
        }else if(vo.getFlag().equals("editJob")){
            return AjaxResult.success(UserInfoServiceImpl.editJob(vo.getUserCode(),vo.getKeyWord()));
        }else if(vo.getFlag().equals("jointime")){
            return AjaxResult.success(UserInfoServiceImpl.editJoinTime(vo.getUserCode(),vo.getKeyWord()));
        }else{
            //基本信息修改
            return AjaxResult.success(UserInfoServiceImpl.editUserInfoRecordSync(vo));
        }
    }

    @PostMapping("CompanyEditUserInfo")
    @ApiOperation(value = "修改用户资料")
    public AjaxResult companyEditUserInfo(@RequestBody CommonRequest commonRequest) {
        UserInfoVO vo = (UserInfoVO) commonRequest.getRequest(UserInfoVO.class);
        return AjaxResult.success(UserInfoServiceImpl.companyEditUserInfo(vo));
    }

    @PostMapping("EditPwd")
    @ApiOperation(value = "修改用户密码")
    public AjaxResult editPwd(@RequestBody CommonRequest commonRequest) {
        UserInfoVO vo = (UserInfoVO) commonRequest.getRequest(UserInfoVO.class);
        Integer count = UserInfoServiceImpl.editPwd(vo);
        if(count == -1){
            return AjaxResult.error("旧密码错误！","旧密码错误");
        }else if(count == 0){
            return AjaxResult.error("旧密码错误！","旧密码错误");
        }else if(count == 1){
            return AjaxResult.success(count);
        }
        return AjaxResult.error("旧密码错误！","旧密码错误");
    }

    @PostMapping("ImportIsOut")
    @ApiOperation(value = "用户批量离职")
    public AjaxResult importIsOut(@RequestBody CommonRequest commonRequest) {
        UserInfoRequest request = (UserInfoRequest) commonRequest.getRequest(UserInfoRequest.class);
        return UserInfoServiceImpl.importIsOut(request);
    }

    @PostMapping("Import")
    @ApiOperation(value = "用户批量添加")
    public AjaxResult Import(@RequestBody CommonRequest commonRequest) {
        UserInfoRequest request = (UserInfoRequest) commonRequest.getRequest(UserInfoRequest.class);
        return UserInfoServiceImpl.Import(request);
    }

    @PostMapping("Get_UserMsgCenter")
    @ApiOperation(value = "小程序-消息中心")
    @ThirdPart
    public AjaxResult Get_UserMsgCenter(@RequestBody CommonRequest commonRequest) {
        GetUserMsgCenterRequest request = (GetUserMsgCenterRequest) commonRequest.getRequest(GetUserMsgCenterRequest.class);
        return AjaxResult.success(UserInfoServiceImpl.getUserMsgCenter(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Update_ReadyStatus")
    @ApiOperation(value = "小程序-消息中心")
    @ThirdPart
    public AjaxResult Update_ReadyStatus(@RequestBody CommonRequest commonRequest) {
        UpdateReadyStatusRequest request = (UpdateReadyStatusRequest) commonRequest.getRequest(UpdateReadyStatusRequest.class);
        return AjaxResult.success(UserInfoServiceImpl.updateReadyStatus(request));
    }

    @PostMapping("Get_MyFiles")
    @ApiOperation(value = "用户档案（小程序）")
    public AjaxResult getMyFiles(@RequestBody CommonRequest commonRequest) {
        UserInfoVO vo = (UserInfoVO) commonRequest.getRequest(UserInfoVO.class);
        return AjaxResult.success(UserInfoServiceImpl.getMyFiles(vo));
    }

    @PostMapping("Auth_FaceDetection")
    @ApiOperation(value = "修改头像活体检测")
    public AjaxResult authFaceDetection(@RequestBody CommonRequest commonRequest) {
        AuthPortraitVerificationRequest request = (AuthPortraitVerificationRequest) commonRequest.getRequest(AuthPortraitVerificationRequest.class);
        return AjaxResult.success(UserInfoServiceImpl.authFaceDetection(request));
    }


    @PostMapping("Auth_VerificationFace")
    @ThirdPart
    @ApiOperation(value = "修改头像活体检测")
    public AjaxResult authVerificationFace(@RequestBody CommonRequest commonRequest) {
        AuthPortraitVerificationRequest request = (AuthPortraitVerificationRequest) commonRequest.getRequest(AuthPortraitVerificationRequest.class);
        return AjaxResult.success(UserInfoServiceImpl.authFaceDetection(request));
    }
}
