package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.mapper.CommentMapper;
import com.fzkj.project.system.mapper.CommentReplyMapper;
import com.fzkj.project.system.mapper.UserInfoMapper;
import com.fzkj.project.system.service.CommentService;
import com.fzkj.project.system.vo.*;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 评论 服务实现类
 * </p>
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {

    private final UserInfoMapper userInfoMapper;

    private final CommentReplyMapper commentReplyMapper;

    @Override
    public PageUtils getComment(CommentVO commentVO, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<CommentVO> list = getBaseMapper().getCommentSubStanceList(commentVO.getFunType(),
                commentVO.getStatus(),
                commentVO.getIsValid(),
                commentVO.getTargetId(),
                commentVO.getUserName(),
                commentVO.getKeywords(),
                commentVO.getIsReply(),
                commentVO.getTeacherId());
        return new PageUtils(pageEntity, list);
    }

    @Override
    public String exportComment(CommentVO commentVO) {
        PageHelper.startPage(1, 9999999);
        List<CommentVO> list = getBaseMapper().getCommentSubStanceList(commentVO.getFunType(),
                commentVO.getStatus(),
                commentVO.getIsValid(),
                commentVO.getTargetId(),
                commentVO.getUserName(),
                commentVO.getKeywords(),
                commentVO.getIsReply(),
                commentVO.getTeacherId());
        //处理名称-省份证号
        list.forEach(item -> {
            item.setUserName(item.getUserName() + "-" + item.getIdCard());
        });
        try {
            ExcelUtil<CommentVO> util = new ExcelUtil<CommentVO>(CommentVO.class);
            //导出文件名判断
            String fileName = "";
            fileName += (commentVO.getFunType().equals("10") ? "课件评论-" : "内容评论-");
            fileName += (commentVO.getStatus() == 1 ? "有效" : "待审");
            fileName = util.encodingDateFilename(fileName + "评论列表导出");
            InputStream inputStream = util.exportExcelFile(list, fileName);
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "comment", fileName);
            return fileName + ".xlsx," + uploadFile.getUrl();
        } catch (Exception e) {
            log.error("评论导出上传异常");
            return "评论导出上传异常";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateComment(CommentVO commentVO) {
        if (commentVO.getFlag().equals("del") || commentVO.getFlag().equals("recovery")) {
            String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
            LambdaUpdateWrapper<Comment> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.in(Comment::getId, commentVO.getId().split(","));
            queryWrapper.set(Comment::getIsValid, commentVO.getFlag().equals("del") ? 0 : 1);
            queryWrapper.set(Comment::getReviseTime, LocalDateTime.now());
            queryWrapper.set(Comment::getReviseCode, userCode);
            this.update(queryWrapper);
        } else if (commentVO.getFlag().equals("auditreply") || commentVO.getFlag().equals("audit")) {
            String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
            Comment comment = this.getById(commentVO.getId());
            comment.setReviseTime(LocalDateTime.now());
            comment.setStatus(1);
            comment.setReviseCode(userCode);
            if (StringUtils.isNotEmpty(commentVO.getReplyContent())) {
                comment.setIsReply(1);
                //回复人信息查询
                UserInfoVO userInfoVO = userInfoMapper.selectUserLoginInfoByUserCode(userCode);
                //保存回复表
                CommentReply commentReply = new CommentReply();
                commentReply.setFunType(comment.getFunType());
                commentReply.setTargetId(comment.getId());
                commentReply.setUserName(userInfoVO.getUserName());
                commentReply.setReplyContent(commentVO.getReplyContent());
                commentReply.setCreatorCode(userCode);
                commentReply.setCreationTime(LocalDateTime.now());
                commentReply.setIsValid(1);
                commentReply.setType(1);
                commentReplyMapper.insert(commentReply);
            }
            this.updateById(comment);
        }
//        else if(commentVO.getFlag().equals("audit")){
//            String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
//            LambdaUpdateWrapper<Comment> queryWrapper = new LambdaUpdateWrapper<>();
//            queryWrapper.in(Comment::getId,commentVO.getId().split(","));
//            queryWrapper.set(Comment::getStatus,1);
//            queryWrapper.set(Comment::getReviseTime,LocalDateTime.now());
//            queryWrapper.set(Comment::getReviseCode,userCode);
//            this.update(queryWrapper);
//        }
        else if (commentVO.getFlag().equals("reply")) {
            String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
            Comment comment = this.getById(commentVO.getId());
            comment.setReviseTime(LocalDateTime.now());
            comment.setIsReply(1);
            comment.setReviseCode(userCode);
            this.updateById(comment);
            //回复人信息查询
            UserInfoVO userInfoVO = userInfoMapper.selectUserLoginInfoByUserCode(userCode);
            //保存回复表
            CommentReply commentReply = new CommentReply();
            commentReply.setFunType(comment.getFunType());
            commentReply.setTargetId(comment.getId());
            commentReply.setUserName(userInfoVO.getUserName());
            commentReply.setReplyContent(commentVO.getReplyContent());
            commentReply.setCreatorCode(userCode);
            commentReply.setCreationTime(LocalDateTime.now());
            commentReply.setIsValid(1);
            commentReply.setType(1);
            commentReplyMapper.insert(commentReply);
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult editComment(CommentVO commentVO, String funType) {
        if (commentVO.getFlag().equals("add")) {
            Comment entity = (Comment) DataTransfer.transfer(commentVO, Comment.class);
            entity.setFunType(funType);
            entity.setIsValid(1);
            entity.setStatus(0);
            entity.setSort(0);
            entity.setCreatorCode(SecurityUtils.getLoginUser().getUser().getUserCode());
            entity.setCreationTime(LocalDateTime.now());
            entity.setReviseTime(LocalDateTime.now());
            entity.setReviseCode(SecurityUtils.getLoginUser().getUser().getUserCode());
            entity.setIsReply(0);
            boolean save = save(entity);
            if (save && null != entity.getGrade() && Integer.valueOf(entity.getGrade()) > 0) {
                //返回平均分
                LambdaQueryWrapper<Comment> commentLambdaQueryWrapper = new LambdaQueryWrapper<>();
                commentLambdaQueryWrapper.eq(Comment::getTargetId, commentVO.getTargetId());
                commentLambdaQueryWrapper.eq(Comment::getFunType, entity.getFunType());
                commentLambdaQueryWrapper.eq(Comment::getIsValid, 1);
                commentLambdaQueryWrapper.gt(Comment::getGrade, 0);
                commentLambdaQueryWrapper.select(Comment::getGrade);
                List<Comment> comments = list(commentLambdaQueryWrapper);
                if (comments.size() > 0) {
                    double average = comments.stream().mapToInt(temp -> Integer.parseInt(temp.getGrade())).average().orElse(0);
                    return AjaxResult.success((int) Math.round(average));
                }
            }
            return AjaxResult.success("发表评论成功", 0);
        }
        return AjaxResult.error("请求类型异常", "1");
    }

    @Override
    public PageUtils getMyComment(CommentVO commentVO, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<GetMyCommentVO> list = getBaseMapper().getMyCommentSubStanceList(commentVO.getUserCode());
        list.forEach(item -> {
            //区分课件内容评论，分别赋值
            if (item.getFunType().equals("10")) {
                item.setTargetName(item.getContentTitle10());
            } else if (item.getFunType().equals("11")) {
                item.setTargetName(item.getContentTitle11());
            }
            //组装当前评论的回复
            List<CommentReply> commentReplies = new ArrayList<>();
            CommentReply commentReply = new CommentReply();
            commentReply.setReplyContent(item.getReplyContent());
            commentReplies.add(commentReply);
            item.setReplyInfoList(commentReplies);
        });
        return new PageUtils(pageEntity, list);
    }

    @Override
    public PageUtils getCommentAPP(CommentVO commentVO, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<GetMyCommentVO> list = getBaseMapper().getCommentAppList(commentVO.getTargetId());
        list.forEach(item -> {
            //组装当前评论的回复
            List<CommentReply> commentReplies = new ArrayList<>();
            CommentReply commentReply = new CommentReply();
            commentReply.setReplyContent(item.getReplyContent());
            commentReplies.add(commentReply);
            item.setReplyInfoList(commentReplies);
        });
        return new PageUtils(pageEntity, list);
    }
}