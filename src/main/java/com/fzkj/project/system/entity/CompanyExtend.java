package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业扩展表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_company_extend")
@ApiModel(value = "企业扩展表实体")
public class CompanyExtend implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("CompanyID")
    @ApiModelProperty(value = "企业id")
    private Long companyId;

    @JsonProperty("TaxpayerNumber")
    @ApiModelProperty(value = "纳税人识别号")
    private String taxpayerNumber;

    @JsonProperty("LicenseKey")
    @ApiModelProperty(value = "营业执照注册号")
    private String licenseKey;

    @JsonProperty("Liaison")
    @ApiModelProperty(value = "联系人")
    private String liaison;

    @JsonProperty("LiaisonPhone")
    @ApiModelProperty(value = "联系电话")
    private String liaisonPhone;

    @JsonProperty("IndustryType")
    @ApiModelProperty(value = "行业类型")
    private String industryType;

    @JsonProperty("Nature")
    @ApiModelProperty(value = "企业性质")
    private String nature;

    @JsonProperty("Scale")
    @ApiModelProperty(value = "企业规模")
    private String scale;

    @JsonProperty("BusinessScope")
    @ApiModelProperty(value = "经营范围")
    private String businessScope;

    @JsonProperty("RegisterSource")
    @ApiModelProperty(value = "注册来源")
    private String registerSource;

    @JsonProperty("IsEnable")
    @ApiModelProperty(value = "是否启用")
    private Integer isEnable;

    @JsonProperty("EnableSummary")
    @ApiModelProperty(value = "启用情况说明")
    private String enableSummary;

    @JsonProperty("OperPlatform")
    @ApiModelProperty(value = "操作平台")
    private Integer operPlatform;

    @JsonProperty("Source")
    @ApiModelProperty(value = "数据来源")
    private String source;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @JsonProperty("OpenSign")
    @ApiModelProperty(value = "启用标识")
    private Integer openSign;

    @JsonProperty("CompanyLabel")
    @ApiModelProperty(value = "公司标签")
    private String companyLabel;

    @JsonProperty("MerchantName")
    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    @JsonProperty("MerchantCode")
    @ApiModelProperty(value = "商户编码")
    private String merchantCode;

    @JsonProperty("TaxpayerUrl")
    @ApiModelProperty(value = "税务登记证")
    private String taxpayerUrl;

    @JsonProperty("TaxpayerFileName")
    @ApiModelProperty(value = "税务登记证文件名")
    private String taxpayerFileName;

    @JsonProperty("RegisterTime")
    @ApiModelProperty(value = "注册时间")
    private String registerTime;

    @JsonProperty("SiteArea")
    @ApiModelProperty(value = "公司地址")
    private String siteArea;

    @JsonProperty("RegisterCapital")
    @ApiModelProperty(value = "注册资本")
    private String registerCapital;

    @JsonProperty("EconomicType")
    @ApiModelProperty(value = "经济类型")
    private Integer economicType;

    @JsonProperty("IsOpenAlbum")
    @ApiModelProperty(value = "启用相册")
    private Integer isOpenAlbum;

    @JsonProperty("IsSafeStatus")
    @ApiModelProperty(value = "是否安全状态")
    private Integer isSafeStatus;

    @JsonProperty("UplusTime")
    @ApiModelProperty(value = "优铺时间")
    private String uplusTime;

    @JsonProperty("CompRange")
    @ApiModelProperty(value = "经营范围")
    private Integer compRange;

    @JsonProperty("IsAddress")
    @ApiModelProperty(value = "是否地址")
    private Integer isAddress;
}