package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 考试试题表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value="考试试题导入VO")
public class ExamQuestionImportVO implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "考试ID")
    @NotNull(message = "考试ID不能为空")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "试题导入文件路径")
    @NotNull(message = "试题导入文件路径")
    @JsonProperty("ExcelFilePath")
    private String excelFilePath;

    @JsonProperty("Flag")
    private String flag;

}