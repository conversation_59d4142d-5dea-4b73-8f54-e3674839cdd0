package com.fzkj.project.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.entity.CompanyWork;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 */
@Mapper
public interface CompanyWorkMapper extends BaseMapper<CompanyWork> {

    List<CompanyWork> selectCompanyWorkList(Long companyId);

    List<CompanyWork> selectCompanyWorkPage(Long companyId, String workName);

    Long selectCompanyDriver(Long id);
    Long selectCompanySystem(Long id);
}