package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 试题类型表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@TableName("t_d_exam_question_type")
@ApiModel(value = "试题类型表实体")
public class ExamQuestionType implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "试题类型ID")
    @TableId(type = IdType.AUTO)
    @JsonProperty("QuestionTypeId")
    private Long questionTypeId;

    @ApiModelProperty(value = "类型名称")
    @JsonProperty("TypeName")
    private String typeName;

    @ApiModelProperty(value = "创建者ID")
    @JsonProperty("CreatorID")
    private String creatorId;

    @ApiModelProperty(value = "创建者姓名")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "创建日期")
    @JsonProperty("CreateDate")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新日期")
    @JsonProperty("UpdateDate")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;


}