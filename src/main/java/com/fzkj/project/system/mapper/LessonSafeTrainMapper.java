package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.LessonSafeTrain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzkj.project.system.entity.lessonSafeTrainTd;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 安全培训课程 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface LessonSafeTrainMapper extends BaseMapper<LessonSafeTrain> {

    List<LessonSafeTrainVO> selectLessonSafeTrain(LessonSafeTrainRequest request);

    List<LessonSafeTrainCourseVO> getCourseByLessonSafeTrainId1(LessonSafeTrainCourseRequest request);

    List<LessonSafeTrainCourseVO> getCourseByLessonSafeTrainId2(LessonSafeTrainCourseRequest request);

    List<LessonSafeTrainCourseVO> getCourseByLessonSafeTrainId3(LessonSafeTrainCourseRequest request);

    List<LessonSafeTrain> selectLessonSafeTrainListBus(LessonSafeTrainRequest request);

    List<LessonSafeTrain> selectLessonSafeTrainListByPlanIdBus(LessonSafeTrainRequest request);

    List<Long> selectDistributeCompanyIds(LessonSafeTrainDisVO request);

    List<LessonSafeTrainVO> selectLessonSafeTrainListC(LessonSafeTrainRequest request);

    List<LessonSafeTrainVO> selectLessonSafeTrainListAPP(GetLessonSafeTrainListAPPRequest request);

    List<CourseVO> selectCourseByLessonSafeTrainId(Long lessonId);

    List<GetCourseAPPVO> selectCourseAPP(GetCourseAPPRequest request);

    List<GetCourseAPPVO> selectCourseAPPById(GetCourseAPPRequest request);

    List<LessonSafeTrainUserTemplateListVO> selectLessonSafeTrainUserTemplateList(GetUserSurveyRequest request);

    List<LessonSafeTrain> selectLessonSafeTrainByCompanyIDAndLessonName(Long companyId,String lessonName,String userCode);

    List<LessonSafeTrainVO> selectLessonSafeTrainListC1(LessonSafeTrainRequest request);

    List<LessonSafeTrainVO> selectLessonSafeTrainListCAll(LessonSafeTrainRequest request);

    List<Long> selectLessonSafeTrainIds(String sTime);

    List<Long> selectLessonSafeTrainIdsNoMonth(String sTime);

    Long selectIdByLessonName(String lessonName);

    List<lessonSafeTrainTd> selectAllByLessonId(@Param(value = "lessonId") Long lessonId);
}