package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.CoursewareResources;
import com.fzkj.project.system.vo.CoursewareResourcesVO;
import com.fzkj.project.system.request.CoursewareResourcesRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.CoursewareResourcesMapper;
import com.fzkj.project.system.service.CoursewareResourcesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 视频文件资源信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CoursewareResourcesServiceImpl extends ServiceImpl<CoursewareResourcesMapper, CoursewareResources> implements CoursewareResourcesService {

    @Override
    public List<CoursewareResources> listCoursewareResources(CoursewareResourcesRequest request) {
        CoursewareResources entity = (CoursewareResources) DataTransfer.transfer(request, CoursewareResources.class);
        LambdaQueryWrapper<CoursewareResources> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(CoursewareResourcesRequest request, PageEntity pageEntity) {
        String flag = request.getFlag();
        LambdaQueryWrapper<CoursewareResources> queryWrapper = new LambdaQueryWrapper<>();
        if("list".equals(flag)){
            queryWrapper.eq(CoursewareResources::getIsValid,1);
            if(null != request.getEncodeState()){
                queryWrapper.eq(CoursewareResources::getEncodeState,request.getEncodeState());
            }
            if(null != request.getKeywords()){
                queryWrapper.and(wrapper -> wrapper.like(CoursewareResources::getFileId,request.getKeywords()).or().like(CoursewareResources::getFileName, request.getKeywords()));
            }
            queryWrapper.orderByDesc(CoursewareResources::getSort,CoursewareResources::getId);
        } else if ("md5".equals(flag)) {
            queryWrapper.eq(CoursewareResources::getFileMd5,request.getKeywords());
        } else if ("byId".equals(flag)) {
            queryWrapper.eq(CoursewareResources::getId,request.getId()).or().like(CoursewareResources::getId, request.getResID());;
        }
        IPage<CoursewareResources> page = this.page(new Query<CoursewareResources>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public CoursewareResourcesVO findCoursewareResources(Long id) {
        CoursewareResources entity = this.getById(id);
        CoursewareResourcesVO vo = (CoursewareResourcesVO) DataTransfer.transfer(entity, CoursewareResourcesVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CoursewareResources saveCoursewareResources(CoursewareResourcesVO vo) {
        CoursewareResources entity = (CoursewareResources) DataTransfer.transfer(vo, CoursewareResources.class);
        this.save(entity);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCoursewareResources(CoursewareResourcesVO vo) {
        CoursewareResources entity = (CoursewareResources) DataTransfer.transfer(vo, CoursewareResources.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCoursewareResources(Long id) {
        return this.removeById(id);
    }
}