package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 企业安全培训课程设置表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="企业安全培训课程设置表VO")
public class CompanyLessonSafeTrainSetLogVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "企业安全培训课程设置表ID（T_D_CompanyLessonSafeTrainSet）")
    @NotNull(message = "企业安全培训课程设置表ID（T_D_CompanyLessonSafeTrainSet）不能为空")
    @JsonProperty("SafeTrainSetID")
    private Long safeTrainSetId;

    @ApiModelProperty(value = "操作名称（修改，创建，增减）")
    @NotNull(message = "操作名称（修改，创建，增减）不能为空")
    @JsonProperty("OperationName")
    private String operationName;

    @ApiModelProperty(value = "操作列名称")
    @NotNull(message = "操作列名称不能为空")
    @JsonProperty("ObjectName")
    private String objectName;

    @ApiModelProperty(value = "操作列值")
    @NotNull(message = "操作列值不能为空")
    @JsonProperty("ObjectValues")
    private String objectValues;

    @ApiModelProperty(value = "参培分类名称")
    @NotNull(message = "参培分类名称不能为空")
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty(value = "创建人ID")
    @NotNull(message = "创建人ID不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "创建时间不能为空")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "创建人名称")
    @NotNull(message = "创建人名称不能为空")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "备注信息")
    @NotNull(message = "备注信息不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否 默认有效")
    @NotNull(message = "是否有效 1：是， 0：否 默认有效不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

}