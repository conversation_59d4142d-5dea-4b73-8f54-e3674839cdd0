package com.fzkj.framework.security.service;

import com.fzkj.common.enums.UserStatus;
import com.fzkj.common.exception.BaseException;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.service.UserInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService
{
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private SysPermissionService permissionService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException
    {
        UserInfo user = userInfoService.selectUserByUserName(username);
        if (StringUtils.isNull(user))
        {
            log.info("登录用户：{} 不存在.", username);
            throw new UsernameNotFoundException("登录用户：" + username + " 不存在");
        }
        else if (UserStatus.DELETED.getCode() == user.getIsValid())
        {
            log.info("登录用户：{} 已被停用.", username);
            throw new BaseException("对不起，您的账号：" + username + " 已被停用");
        }
        return createLoginUser(user);
    }

    public UserDetails createLoginUser(UserInfo user)
    {
        return new LoginUser(user, permissionService.getMenuPermission(user));
    }
}
