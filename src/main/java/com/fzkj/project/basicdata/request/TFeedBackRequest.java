package com.fzkj.project.basicdata.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 16:31
 * @Version: 1.0
 */
@Data
@ApiModel(value = "常见问题Request")
public class TFeedBackRequest {
    /**  */
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(name = "用户名")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(name = "用户code")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(name = "1:问题反馈，2：意见箱，3：常见问题")
    @JsonProperty("BackType")
    private Integer backType;

    @ApiModelProperty(name = "问题内容")
    @JsonProperty("Content")
    private String content;

    @ApiModelProperty(name = "是否回复 0=：否，1：是")
    @JsonProperty("IsReply")
    private Integer isReply;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "回复时间")
    @JsonProperty("ReplyTime")
    private Date replyTime;

    @ApiModelProperty(name = "回复内容")
    @JsonProperty("ReplyContent")
    private String replyContent;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "新建时间")
    @JsonProperty("CreationTime")
    private Date creationTime;

    @ApiModelProperty(name = "是否有效0：无效，1：有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(name = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @JsonProperty("IsShow")
    private Integer isShow;

    @JsonProperty("Flag")
    private String flag;
}
