package com.fzkj.project.system.service;

import com.alibaba.fastjson2.JSONObject;
import com.fzkj.project.system.vo.WxLoginVO;
import com.fzkj.project.system.vo.WxMessage;

import java.util.HashMap;

public interface WxService {

    /**
     * 获取unionId和openId
     * @param code
     * @return
     */
    WxLoginVO wxLogin(String code);

    /**
     * 解密用户信息
     * @param encryptedData
     * @param code
     * @param iv
     * @return
     */
    JSONObject getUserInfo(String encryptedData, String code, String iv);

    /**
     * 生成分享二维码
     * @param request
     * @return
     */
    byte[] generateMiniProgramQRCode(HashMap request);

    /**
     * 根据扫描的二维码获取信息
     * @param sceneId
     * @return
     */
    Object getParamsBySceneId(String sceneId);

    /**
     * 发送微信消息
     * @param wxMessage 微信消息对象
     * @return 发送结果
     */
    boolean sendWxMessage(WxMessage wxMessage);

    /**
     * 根据unionId查询公众号openid
     * @return openId
     */
    String getPublicOpenIdByUnionId(String unionId);
}
