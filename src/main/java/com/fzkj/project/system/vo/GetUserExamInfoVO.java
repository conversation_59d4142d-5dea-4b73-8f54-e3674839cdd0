package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GetUserExamInfoVO {
    @ApiModelProperty("试卷ID")
    @JsonProperty("ExamID")
    private String examId;

    @ApiModelProperty("题目总数")
    @JsonProperty("QuestionCount")
    private int questionCount;

    @ApiModelProperty("总分")
    @JsonProperty("totalscore")
    private Integer totalScore;

    @ApiModelProperty("考试时长（分钟）")
    @JsonProperty("TimeCount")
    private Integer timeCount;

    @ApiModelProperty("允许补考次数")
    @JsonProperty("ResitNumber")
    private Integer resitNumber;

    @ApiModelProperty("及格分数线")
    @JsonProperty("PassMark")
    private Integer passMark;

    @ApiModelProperty("签到状态")
    @JsonProperty("ExamSignState")
    private Integer examSignState;

    @ApiModelProperty("人脸识别状态")
    @JsonProperty("ExamFaceState")
    private Integer examFaceState;

    @ApiModelProperty("倒计时时间（分钟）")
    @JsonProperty("ExamCountDownTime")
    private Integer examCountDownTime;

    @ApiModelProperty("考试失败后可重试次数")
    @JsonProperty("ExamFailRepeatNum")
    private Integer examFailRepeatNum;

    @ApiModelProperty("是否模拟人脸识别")
    @JsonProperty("IsMockExamFace")
    private Integer isMockExamFace;

    @ApiModelProperty("是否模拟考试")
    @JsonProperty("IsMockExam")
    private Integer isMockExam;

    @ApiModelProperty("是否练习模式")
    @JsonProperty("IsPractice")
    private Integer isPractice;

    @ApiModelProperty("课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty("已参加考试次数")
    @JsonProperty("ExamCount")
    private int examCount;

    @ApiModelProperty("得分")
    @JsonProperty("Score")
    private Integer score;

    @ApiModelProperty("剩余补考次数（系统记录）")
    @JsonProperty("syResitNumber")
    private int syResitNumber;

    @ApiModelProperty("照片URL")
    @JsonProperty("PhotoUrl")
    private String photoUrl;

    @ApiModelProperty("签到URL")
    @JsonProperty("SignUrl")
    private String signUrl;

    @ApiModelProperty("备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty("创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty("预留字段1")
    @JsonProperty("Column1")
    private Integer column1;

    @ApiModelProperty("用户累计答题次数")
    @JsonProperty("UserExamCount")
    private int userExamCount;

    @ApiModelProperty("准确率")
    @JsonProperty("Accuracy")
    private Double accuracy;

    @ApiModelProperty("已用时长（秒）")
    @JsonProperty("UseTimeCount")
    private Integer useTimeCount;

    @ApiModelProperty("身份证号")
    @JsonProperty("IDCard")
    private String idCard;

    @ApiModelProperty("用户名")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty("用户电话")
    @JsonProperty("UserPhone")
    private String userPhone;

    @ApiModelProperty("用户ID")
    @JsonProperty("UserID")
    private String userId;

    @ApiModelProperty("组织机构ID")
    @JsonProperty("OrganizationID")
    private String organizationId;
}