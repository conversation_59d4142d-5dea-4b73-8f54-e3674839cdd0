<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CourseCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.CourseCategory">
        <id column="id" property="id" />
        <result column="category_name" property="categoryName" />
        <result column="is_show" property="isShow" />
        <result column="creator_name" property="creatorName" />
        <result column="revise_name" property="reviseName" />
        <result column="source" property="source" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="reserved_field1" property="reservedField1" />
        <result column="reserved_field2" property="reservedField2" />
        <result column="reserved_field3" property="reservedField3" />
        <result column="reserved_field4" property="reservedField4" />
        <result column="reserved_field5" property="reservedField5" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, category_name, is_show, creator_name, revise_name, source, sort, creator_code, creation_time, revise_code, revise_time, remark, is_valid, reserved_field1, reserved_field2, reserved_field3, reserved_field4, reserved_field5
    </sql>
    <select id="selectCourseCategory" resultType="com.fzkj.project.system.vo.CourseCategoryVO">
        select tdcc.*,(select count(1) from t_d_course tdc where tdc.category_id =tdcc.id and tdc.is_valid = 1 and tdc.belong_plat=1) courseNum from t_d_course_category tdcc
        where 1 = 1
        <if test="isValid != null">
            and  tdcc.is_valid = #{isValid}
        </if>
        <if test="keywords != null and keywords != ''">
            and  tdcc.category_name like concat('%', #{keywords}, '%')
        </if>
        <if test="isShow != null">
            and  tdcc.is_show = #{isShow}
        </if>
        ORDER BY tdcc.sort asc
    </select>

</mapper>