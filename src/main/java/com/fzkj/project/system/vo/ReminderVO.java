package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 提醒管理表
 *
 * <AUTHOR>
 * @since 2024-03-04
 */
@Data
@ApiModel(value = "提醒管理表VO")
public class ReminderVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "提醒标题",example = "安全培训")
    @JsonProperty("Title")
    private String title;

    @ApiModelProperty(value = "提醒设置",example = "2,1,0")
    @JsonProperty("Val")
    private String val;

    @ApiModelProperty(value = "培训类型",example = "1",notes = "1:安全培训;目前只有安全培训，下拉框")
    @JsonProperty("TrainType")
    private Integer trainType;

    @ApiModelProperty(value = "提醒类型",example = "1",notes = "提醒类型：1-临期提醒；2-分发提醒；3-学习统计,下拉框,培训类型+提醒类型为唯一")
    @JsonProperty("RemindType")
    private Integer remindType;
}