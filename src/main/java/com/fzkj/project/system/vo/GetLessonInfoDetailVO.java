package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;

import java.time.LocalDateTime;

@Data
public class GetLessonInfoDetailVO {
    @ApiModelProperty(value = "id")
    @JsonProperty("Mongo_ObjId")
    private ObjectId id;

    @ApiModelProperty(value = "是否在线考试")
    @JsonProperty("IsOnlineExam")
    private Integer isOnlineExam;

    @ApiModelProperty(value = "培训时长")
    @JsonProperty("TrainTimeCount")
    private String trainTimeCount;

    @ApiModelProperty(value = "课程图片")
    @JsonProperty("LessonPic")
    private String lessonPic;

    @ApiModelProperty(value = "学习完成情况")
    @JsonProperty("StudyComplete")
    private String studyComplete;

    @ApiModelProperty(value = "考试ID")
    @JsonProperty("ExamID")
    private String examId;

    @ApiModelProperty(value = "考试完成状态")
    @JsonProperty("ExamComplete")
    private String examComplete;

    @ApiModelProperty(value = "公司ID")
    @JsonProperty("CompanyID")
    private String companyId;

    @ApiModelProperty(value = "部门ID")
    @JsonProperty("DepartID")
    private String departId;

    @ApiModelProperty(value = "学习状态")
    @JsonProperty("StudyFaceState")
    private String studyFaceState;

    @ApiModelProperty(value = "签到状态")
    @JsonProperty("StudySignState")
    private String studySignState;

    @ApiModelProperty(value = "签到图片URL")
    @JsonProperty("SignImg")
    private String signImg;

    @ApiModelProperty(value = "保留字段3")
    @JsonProperty("ReservedField3")
    private String reservedField3;

    @ApiModelProperty(value = "培训类型")
    @JsonProperty("TrainType")
    private String trainType;

    @ApiModelProperty(value = "培训类型名称")
    @JsonProperty("TrainTypeName")
    private String trainTypeName;

    @ApiModelProperty(value = "计划ID")
    @JsonProperty("PlanID")
    private String planId;

    @ApiModelProperty(value = "课节ID")
    @JsonProperty("LessonID")
    private String lessonId;

    @ApiModelProperty(value = "课节名称")
    @JsonProperty("LessonName")
    private String lessonName;

    @ApiModelProperty(value = "培训周期")
    @JsonProperty("TrainCycle")
    private String trainCycle;

    @ApiModelProperty(value = "学习次数")
    @JsonProperty("StudyNum")
    private String studyNum;

    @ApiModelProperty(value = "静态状态")
    @JsonProperty("StaticStatus")
    private Integer staticStatus;

    @ApiModelProperty(value = "是否已支付")
    @JsonProperty("IsPay")
    private String isPay;

    @ApiModelProperty(value = "补考次数")
    @JsonProperty("ResitNumber")
    private String resitNumber;

    @ApiModelProperty(value = "是否静态数据")
    @JsonProperty("IsStatic")
    private String isStatic;

    @ApiModelProperty(value = "是否完成")
    @JsonProperty("IsComplete")
    private String isComplete;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "静态状态1")
    @JsonProperty("StaticStatus1")
    private Integer staticStatus1;

    @ApiModelProperty(value = "形状或形式")
    @JsonProperty("Shape")
    private Integer shape;

    @ApiModelProperty(value = "考试状态")
    @JsonProperty("ExamStatus")
    private Integer examStatus;

    @ApiModelProperty(value = "是否完美完成")
    @JsonProperty("IsPerfect")
    private Integer isPerfect;

    @ApiModelProperty(value = "地区编码")
    @JsonProperty("AreaCode")
    private String areaCode;

    @ApiModelProperty(value = "发票状态")
    @JsonProperty("InvoiceStatus")
    private Integer invoiceStatus;

    @ApiModelProperty(value = "阶段")
    @JsonProperty("Stage")
    private Integer stage;

    @ApiModelProperty(value = "培训开始时间")
    @JsonProperty("TrainStartTime")
    private String trainStartTime;

    @ApiModelProperty(value = "培训结束时间")
    @JsonProperty("TrainEndTime")
    private String trainEndTime;

    @ApiModelProperty(value = "计划级别")
    @JsonProperty("PlanLevel")
    private Integer planLevel;

    @ApiModelProperty(value = "支付时间")
    @JsonProperty("PayDateTime")
    private String payDateTime;

    private Integer payStatus;
}
