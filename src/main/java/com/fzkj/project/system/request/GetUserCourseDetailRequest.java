package com.fzkj.project.system.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetUserCourseDetailRequest {
    @ApiModelProperty(value = "课程ID")
    private Long lessonId;

    @ApiModelProperty(value = "课程计划ID")
    private Long planId;

    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @ApiModelProperty(value = "课程类型")
    private String trainType;

    @ApiModelProperty(value = "课程编码")
    private Long courseId;
}
