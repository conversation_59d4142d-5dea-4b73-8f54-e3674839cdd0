package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户考试记录表（正式）
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value = "用户考试记录表（正式）Request")
public class ExamUserRecordRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "考试ID")
    private Long examId;

    @ApiModelProperty(value = "及格分")
    private Integer passMark;

    @ApiModelProperty(value = "得分")
    private Integer score;

    @ApiModelProperty(value = "总分")
    private Integer totalScore;

    @ApiModelProperty(value = "考试次数")
    private Integer resitNumber;

    @ApiModelProperty(value = "用时")
    private Integer useTimeCount;

    @ApiModelProperty(value = "正确率")
    private BigDecimal accuracy;

    @ApiModelProperty(value = "用户考试次数")
    private Integer userExamCount;

    @ApiModelProperty(value = "是否静态")
    private Integer isStatic;

    @ApiModelProperty(value = "错题数")
    private Integer errorCount;

    @ApiModelProperty(value = "课程ID")
    private Long lessonId;

    @ApiModelProperty(value = "人脸识别照片")
    private String photoUrl;

    @ApiModelProperty(value = "签字照片")
    private String signUrl;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "用户电话")
    private String userPhone;

    @ApiModelProperty(value = "部门ID")
    private Long departId;

    @JsonProperty("Flag")
    private String flag;
}