package com.fzkj.framework.aspectj;

import com.fzkj.common.utils.ServletUtils;
import com.fzkj.common.utils.StringUtils;
import com.fzkj.common.utils.spring.SpringUtils;
import com.fzkj.framework.aspectj.lang.annotation.DataScope;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.framework.security.service.TokenService;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.project.system.domain.SysUser;
import com.fzkj.project.system.entity.UserInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Set;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class DataScopeAspect
{
    /**
     * 所有企业
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自己负责的企业
     */
    public static final String DATA_SCOPE_DEPT = "2";


    // 配置织入点
    @Pointcut("@annotation(com.fzkj.framework.aspectj.lang.annotation.DataScope)")
    public void dataScopePointCut()
    {
    }

    @Before("dataScopePointCut()")
    public void doBefore(JoinPoint point) throws Throwable
    {
        handleDataScope(point);
    }

    protected void handleDataScope(final JoinPoint joinPoint)
    {
        // 获得注解
        DataScope controllerDataScope = getAnnotationLog(joinPoint);
        if (controllerDataScope == null)
        {
            return;
        }
        // 获取当前的用户
        LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
        UserInfo currentUser = loginUser.getUser();
        if (currentUser != null){
            dataScopeFilter(joinPoint, currentUser, controllerDataScope.deptAlias(),
                    controllerDataScope.userAlias());
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint 切点
     * @param user 用户
     * @param userAlias 别名
     */
    public static void dataScopeFilter(JoinPoint joinPoint, UserInfo user, String deptAlias, String userAlias) {
        StringBuilder sqlString = new StringBuilder();
        BaseEntity baseEntity = null;
        try{
            baseEntity = (BaseEntity) joinPoint.getArgs()[0];
            if(2 == user.getPermission()){
                if(StringUtils.isNotEmpty(deptAlias)){
                    sqlString.append(StringUtils.format(" and {}.company_id in ({}) ", deptAlias, user.getCompanyId()));
                }else{
                    sqlString.append(StringUtils.format(" and company_id = {} ", user.getCompanyId()));
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }

        if (!StringUtils.isEmpty(sqlString))
        {
            baseEntity.setDataScope(sqlString.toString());
        }else{
            //防止外部注入
            baseEntity.setDataScope("");
        }
    }

    /**
     * 集合的交集
     * @param a
     * @param b
     * @return java.lang.String
     */
    public static String getIntersection(List<Long> a,List<Long> b){
        if(a == null){
            a = Lists.newArrayList();
        }
        if(b == null){
            b = Lists.newArrayList();
        }
        Set<Long> s1 = Sets.newHashSet(a);
        Set<Long> s2 = Sets.newHashSet(b);
        Sets.SetView<Long> intersection = Sets.intersection(s1, s2);
        return Joiner.on(",").skipNulls().join(intersection);
    }

    /**
     * 是否存在注解，如果存在就获取
     */
    private DataScope getAnnotationLog(JoinPoint joinPoint)
    {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        if (method != null)
        {
            return method.getAnnotation(DataScope.class);
        }
        return null;
    }

}
