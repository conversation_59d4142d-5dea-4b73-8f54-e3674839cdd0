package com.fzkj.project.system.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetLessonSafeTrainListAPPRequest {

    @ApiModelProperty(value = "培训类型")
    private String trainType;

    @ApiModelProperty(value = "课程名称")
    private String lessonName;

    @ApiModelProperty(value = "操作标志")
    private String flag;

    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    @ApiModelProperty(value = "ID")
    private Long id;
}
