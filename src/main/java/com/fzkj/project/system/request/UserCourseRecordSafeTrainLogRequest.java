package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 安全培训课件分发学员学习记录日志
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "安全培训课件分发学员学习记录日志Request")
public class UserCourseRecordSafeTrainLogRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "课程ID")
    private Long lessonId;

    @ApiModelProperty(value = "课件ID")
    private Long courseId;

    @ApiModelProperty(value = "企业ID")
    private Long organizationId;

    @ApiModelProperty(value = "用户编号")
    private String userCode;

    @ApiModelProperty(value = "学习时长")
    private Long studyTimeCount;

    @JsonProperty("RecordType")
    private Integer recordType;

    @JsonProperty("StartLearningTime")
    private String startLearningTime;

    @JsonProperty("TrainType")
    private Integer trainType;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "创建人ID")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @ApiModelProperty(value = "预留字段1")
    private String reservedField1;

    @ApiModelProperty(value = "预留字段2")
    private String reservedField2;

    @ApiModelProperty(value = "预留字段3")
    private String reservedField3;

    @ApiModelProperty(value = "预留字段4")
    private String reservedField4;

    @ApiModelProperty(value = "预留字段5")
    private String reservedField5;

}