package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.ExamAnswerRecordSimulation;
import com.fzkj.project.system.vo.ExamAnswerRecordSimulationVO;
import com.fzkj.project.system.request.ExamAnswerRecordSimulationRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.ExamAnswerRecordSimulationMapper;
import com.fzkj.project.system.service.ExamAnswerRecordSimulationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户模拟考答题记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ExamAnswerRecordSimulationServiceImpl extends ServiceImpl<ExamAnswerRecordSimulationMapper, ExamAnswerRecordSimulation> implements ExamAnswerRecordSimulationService {

    @Override
    public List<ExamAnswerRecordSimulation> listExamAnswerRecordSimulation(ExamAnswerRecordSimulationRequest request) {
        ExamAnswerRecordSimulation entity = (ExamAnswerRecordSimulation) DataTransfer.transfer(request, ExamAnswerRecordSimulation.class);
        LambdaQueryWrapper<ExamAnswerRecordSimulation> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(ExamAnswerRecordSimulationRequest request, PageEntity pageEntity) {
        ExamAnswerRecordSimulation entity = (ExamAnswerRecordSimulation) DataTransfer.transfer(request, ExamAnswerRecordSimulation.class);
        LambdaQueryWrapper<ExamAnswerRecordSimulation> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<ExamAnswerRecordSimulation> page = this.page(
                new Query<ExamAnswerRecordSimulation>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public ExamAnswerRecordSimulationVO findExamAnswerRecordSimulation(Long id) {
        ExamAnswerRecordSimulation entity = this.getById(id);
        ExamAnswerRecordSimulationVO vo = (ExamAnswerRecordSimulationVO) DataTransfer.transfer(entity, ExamAnswerRecordSimulationVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveExamAnswerRecordSimulation(ExamAnswerRecordSimulationVO vo) {
        ExamAnswerRecordSimulation entity = (ExamAnswerRecordSimulation) DataTransfer.transfer(vo, ExamAnswerRecordSimulation.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamAnswerRecordSimulation(ExamAnswerRecordSimulationVO vo) {
        ExamAnswerRecordSimulation entity = (ExamAnswerRecordSimulation) DataTransfer.transfer(vo, ExamAnswerRecordSimulation.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeExamAnswerRecordSimulation(Long id) {
        return this.removeById(id);
    }
}