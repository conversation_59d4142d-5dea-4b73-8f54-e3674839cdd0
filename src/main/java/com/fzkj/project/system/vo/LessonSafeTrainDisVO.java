package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业安全培训课程设置表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="企业安全培训课程分发学员VO")
public class LessonSafeTrainDisVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "企业IDs")
    @JsonProperty("CompanyIDs")
    private String companyIds;

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "操作方式")
    @JsonProperty("HandMode")
    private Integer handMode;

    @JsonProperty("UserCodes")
    private String userCodes;

    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @JsonProperty("lessonSafeTrainCompanyIDs")
    private String lessonSafeTrainCompanyIds;

    @JsonProperty("Flag")
    private String flag;

    @JsonProperty("TotalTimeCount")
    private Integer totalTimeCount;

}