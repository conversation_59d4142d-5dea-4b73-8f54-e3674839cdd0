package com.fzkj.framework.security.handle;

import org.springframework.aop.support.AopUtils;
import org.springframework.core.MethodIntrospector;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.condition.ProducesRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.springframework.web.util.WebUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Set;

/**
 * 请求匹配不成功时，默认删除第一个path，再次匹配
 */
@Component
public class CustomRequestMatcher<T> extends RequestMappingHandlerMapping {

    @Override
    @Nullable
    protected HandlerMethod getHandlerInternal(HttpServletRequest request) throws Exception {
        request.removeAttribute(PRODUCIBLE_MEDIA_TYPES_ATTRIBUTE);
        try {
            if(request.getRequestURI().contains("favicon.ico")){
                return null;
            }
            HandlerMethod handlerInternal = super.getHandlerInternal(request);
            if (null == handlerInternal && !request.getRequestURI().contains("swagger") && !request.getRequestURI().contains("templates")) {
                String requestURI = request.getRequestURI();
                String contextPath = request.getContextPath();
                String path = requestURI.substring(contextPath.length());
                if (path.startsWith("/")) {
                    path = path.substring(1);
                }
                String[] pathSegments = path.split("/");
                if (pathSegments.length > 1) {
                    StringBuilder newPath = new StringBuilder();
                    for (int i = 1; i < pathSegments.length; i++) {
                        newPath.append("/").append(pathSegments[i]);
                    }
                    requestURI = contextPath + newPath;
                }
                request.setAttribute(WebUtils.INCLUDE_REQUEST_URI_ATTRIBUTE, requestURI);
                handlerInternal = super.getHandlerInternal(request);
                request.removeAttribute(WebUtils.INCLUDE_REQUEST_URI_ATTRIBUTE);
            }
            return handlerInternal;
        } catch (Exception e) {
            if (request.getRequestURI().contains("swagger")) {
                return null;
            } else {
                throw e;
            }
        } finally {
            ProducesRequestCondition.clearMediaTypesAttribute(request);
        }
    }

    @Override
    protected RequestMappingInfo getMappingForMethod(Method method, Class<?> handlerType) {
        return super.getMappingForMethod(method, handlerType);
    }

    @Override
    protected void detectHandlerMethods(Object handler) {
        Class<?> handlerType = (handler instanceof String ?
                obtainApplicationContext().getType((String) handler) : handler.getClass());

        if (handlerType != null) {
            Class<?> userType = ClassUtils.getUserClass(handlerType);
            Map<Method, RequestMappingInfo> methods = MethodIntrospector.selectMethods(userType,
                    (MethodIntrospector.MetadataLookup<RequestMappingInfo>) method -> {
                        try {
                            return super.getMappingForMethod(method, userType);
                        } catch (Throwable ex) {
                            throw new IllegalStateException("Invalid mapping on handler class [" +
                                    userType.getName() + "]: " + method, ex);
                        }
                    });
            methods.forEach((method, mapping) -> {
                Method invocableMethod = AopUtils.selectInvocableMethod(method, userType);
                Set<String> patterns = mapping.getPatternsCondition().getPatterns();
                String path = (String) patterns.toArray()[0];
                if (path.startsWith("/")) {
                    path = path.substring(1);
                }
                String[] pathSegments = path.split("/");
                String newPath = "";
                if (pathSegments.length > 1) {
                    for (int i = 1; i < pathSegments.length; i++) {
                        newPath = newPath + "/" + pathSegments[i];
                    }
                }
                if (!"".equals(newPath)) {
                    patterns.add(newPath);
                }
                registerHandlerMethod(handler, invocableMethod, mapping);
            });
        }
    }

    /**
     * 属性设置
     */
    @Override
    public void afterPropertiesSet() {
        // 提升当前 HandlerMapping 的在映射处理器列表中的顺序
        super.setOrder(0);
        super.afterPropertiesSet();
    }
}


