package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户操作日志表
 *
 */
@Data
@TableName("t_d_oper_user_log")
@ApiModel(value = "用户操作日志表")
public class OperUserLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonProperty("OperType")
    @ApiModelProperty(value = "1新增 0离职 ")
    private Integer operType;

    @JsonProperty("CompanyID")
    @ApiModelProperty(value = "公司Id")
    private Long companyId;

    @JsonProperty("DepartID")
    @ApiModelProperty(value = "部门id")
    private String departId;

    @JsonProperty("WorkID")
    @ApiModelProperty(value = "岗位ID")
    private Long workId;

    @JsonProperty("CompanyName")
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @JsonProperty("DepartName")
    @ApiModelProperty(value = "部门名称")
    private Integer departName;

    @JsonProperty("WorkName")
    @ApiModelProperty(value = "岗位名称")
    private String workName;

    @JsonProperty("UserName")
    @ApiModelProperty(value = "姓名")
    private String userName;

    @JsonProperty("Phone")
    @ApiModelProperty(value = "手机号")
    private String phone;

    @JsonProperty("IdCard")
    @ApiModelProperty(value = "生份证号")
    private String idCard;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatorTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @JsonProperty("OperName")
    @ApiModelProperty(value = "操作人")
    private String operName;
}