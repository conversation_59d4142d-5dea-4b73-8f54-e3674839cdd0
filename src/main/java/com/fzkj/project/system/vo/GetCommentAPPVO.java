package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.project.system.entity.CommentReply;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class GetCommentAPPVO {
    @JsonProperty("ID")
    @ApiModelProperty(value = "评论ID", example = "469336")
    private Long id;

    @JsonProperty("UserName")
    @ApiModelProperty(value = "用户名", example = "测试账号")
    private String userName;

    @JsonProperty("Grade")
    @ApiModelProperty(value = "评分", example = "0.00")
    private double grade;

    @JsonProperty("UserPhoto")
    @ApiModelProperty(value = "用户头像URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/mini/avatar/1665667965183.jpg")
    private String userPhoto;

    @JsonProperty("Content")
    @ApiModelProperty(value = "评论内容", example = "评论")
    private String content;

    @JsonProperty("IsReply")
    @ApiModelProperty(value = "是否回复", example = "0")
    private Integer isReply;

    @JsonProperty("CreationTime")
    @ApiModelProperty(value = "创建时间", example = "2024-04-01 10:12:43")
    private LocalDateTime creationTime;

    @JsonProperty("IsTeacherReply")
    @ApiModelProperty(value = "是否教师回复", example = "0")
    private Integer isTeacherReply;

    @JsonProperty("ReplyInfoList")
    @ApiModelProperty(value = "回复信息列表", dataType = "array", readOnly = true)
    private List<CommentReply> replyInfoList = new ArrayList<>();

}
