package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 视频文件资源信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_courseware_resources")
@ApiModel(value = "视频文件资源信息表实体")
public class CoursewareResources implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    private Long id;

    @JsonProperty("FileMd5")
    private String fileMd5;

    @ApiModelProperty(value = "文件ID")
    @JsonProperty("FileID")
    private String fileId;

    @ApiModelProperty(value = "文件名称")
    @JsonProperty("FileName")
    private String fileName;

    @ApiModelProperty(value = "文件地址")
    @JsonProperty("FileUrl")
    private String fileUrl;

    @ApiModelProperty(value = "文件转码地址")
    @JsonProperty("FileUrlHls")
    private String fileUrlHls;

    @ApiModelProperty(value = "文件时长")
    @JsonProperty("FileTime")
    private Integer fileTime;

    @ApiModelProperty(value = "文件大小")
    @JsonProperty("FileSize")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型")
    @JsonProperty("FileType")
    private Integer fileType;

    @ApiModelProperty(value = "转码状态 0：正常")
    @JsonProperty("EncodeState")
    private Integer encodeState;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人编码")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建人")
    @JsonProperty("CreatorName")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人编码")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改人")
    @JsonProperty("ReviseName")
    private String reviseName;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "来源")
    @JsonProperty("Source")
    private String source;
}