package com.fzkj.project.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.*;
import com.fzkj.common.utils.aliyun.FaceUtil;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.common.utils.security.Md5Utils;
import com.fzkj.framework.redis.RedisCache;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.monitor.domain.SysOperLog;
import com.fzkj.project.monitor.mapper.SysOperLogMapper;
import com.fzkj.project.system.entity.*;
import com.fzkj.project.system.mapper.*;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.LessonSafeTrainService;
import com.fzkj.project.system.service.UserCourseRecordSafeTrainMongoService;
import com.fzkj.project.system.service.UserInfoService;
import com.fzkj.project.system.service.UserLessonRecordSafeTrainMongoService;
import com.fzkj.project.system.vo.*;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.apache.catalina.User;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户登录信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    private final RedisCache redisCache;

    private final UserCompanyMapper userCompanyMapper;

    private final OperUserLogMapper operUserLogMapper;

    private final CompanyWorkMapper companyWorkMapper;

    private final CompanyMapper companyMapper;

    private final UserInfoMapper userInfoMapper;

    private final UserRoleMapper userRoleMapper;

    private final SysOperLogMapper sysOperLogMapper;

    private final UserLimitMapper userLimitMapper;

    private final LessonSafeTrainService lessonSafeTrainService;

    private final LessonSafeTrainMapper lessonSafeTrainMapper;

    private final PushCenterMsgMapper pushCenterMsgMapper;

    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;

    private final UserCourseRecordSafeTrainMongoService userCourseRecordSafeTrainMongoService;

    private final CompanyLessonSafeTrainSetMapper companyLessonSafeTrainSetMapper;

    private final LessonSafeTrainCompanyUserTemplateMapper lessonSafeTrainCompanyUserTemplateMapper;

    private final static Map<Integer, String> funTypeMap = new LinkedHashMap<>();

    static {
        //1：视频，2：文件，3：图文，4：音频
        funTypeMap.put(4, "课程提醒");
        funTypeMap.put(2, "活动通知");
        funTypeMap.put(1, "资讯通知");
        funTypeMap.put(3, "公告通知");
        funTypeMap.put(5, "到期提醒");
    }

    @Override
    public List<UserInfo> listUserInfo(UserInfoRequest request) {
        UserInfo entity = (UserInfo) DataTransfer.transfer(request, UserInfo.class);
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(UserInfoRequest request, PageEntity pageEntity) {
        if (!ObjectUtils.isEmpty(request.getCompanyId())) {
            CompanyVO companyListById = companyMapper.getCompanyListById(request.getCompanyId());
            if (!"0".equals(companyListById.getDeptType()) &&
                    !"1".equals(companyListById.getDeptType())) {
                request.setDepartId(request.getCompanyId());
                request.setCompanyId(null);
            }
        }
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<UserInfoVO> list = getBaseMapper().selectUserInfoPageList(request.getCompanyId(), request.getDepartId(), request.getKeyWord(),
                request.getIsOut(), request.getIsBind(),request.getWorkId());
        list.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getUnionId())) {
                item.setIsBind(1);
            } else {
                item.setIsBind(0);
            }
            item.setCompanyLabel("");
        });
        return new PageUtils(pageEntity, list);
    }

    @Override
    public String exportUserInfo(UserInfoRequest request) {
        PageHelper.startPage(1, 99999999);
        List<UserInfoVO> list = getBaseMapper().selectUserInfoPageList(request.getCompanyId(), request.getDepartId(), request.getKeyWord(),
                request.getIsOut(), request.getIsBind(),request.getWorkId());
        list.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getUnionId())) {
                item.setIsBind(1);
            } else {
                item.setIsBind(0);
            }
            item.setCompanyLabel("");
        });
        try {
            ExcelUtil<UserInfoVO> util = new ExcelUtil<UserInfoVO>(UserInfoVO.class);
            String fileName = util.encodingDateFilename("人员信息导出");
            InputStream inputStream = util.exportExcelFile(list, fileName);
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "user", fileName);
            return fileName + ".xlsx," + uploadFile.getUrl();
        } catch (Exception e) {
            log.error("用户信息导出上传异常");
            return "用户信息导出上传异常";
        }

    }

    @Override
    public PageUtils getUserSelect(UserInfoRequest request, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        List<UserInfoVO> userInfoVOS=new ArrayList<>();
        if (StringUtils.isNotEmpty(request.getKeyWord())) {
            userInfoVOS = baseMapper.getUserSelect(request.getKeyWord(), request.getCompanyId(),request.getDepartId(),request.getWorkId());
        } else {
            userInfoVOS = baseMapper.getUserSelect("", request.getCompanyId(),request.getDepartId(),request.getWorkId());
        }
        return new PageUtils(pageEntity, userInfoVOS);
    }

    public UserInfoVO findUserInfo(Long id) {
        UserInfo entity = this.getById(id);
        UserInfoVO vo = (UserInfoVO) DataTransfer.transfer(entity, UserInfoVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult
    saveUserInfo(UserInfoVO vo) {
        List<UserInfoVO> userInfoVOS = baseMapper.selectUserInfoAndCompanyInfoByPhoneOrIdCard(vo.getPhone(), vo.getIdCard());
        UserCompanyVO userInfoVO = userCompanyMapper.selectUserInfoByPhoneOrIdCard(vo.getPhone(), vo.getIdCard());
        if (!userInfoVOS.isEmpty() && ObjectUtils.isEmpty(userInfoVO)) {
            String errorCompanyName = "(" + userInfoVOS.get(0).getCompanyName() + "-" + userInfoVOS.get(0).getDepartName() + ")";
//            for(UserInfoVO userInfoVO : userInfoVOS){
//                errorCompanyName += userInfoVOS.get(0).getCompanyName()+"-"+userInfoVOS.get(0).getDepartName()+"/";
//            }
//            errorCompanyName = errorCompanyName.substring(0,errorCompanyName.length()-1) + ")";
            String errorSt = "新增失败，" + vo.getUserName() + "(" + vo.getIdCard() + "/" + vo.getPhone() + ")已在" + errorCompanyName + "中存在";
            return AjaxResult.error(errorSt, "");
        }
        if (!ObjectUtils.isEmpty(userInfoVO)){
            userCompanyMapper.updateCompany(userInfoVO.getUserCode(),Long.parseLong(vo.getCompanyId() + ""),vo.getDepartId());
            UserInfo userInfo = (UserInfo) DataTransfer.transfer(vo, UserInfo.class);
            return AjaxResult.success(userInfo);
        } else {
            String userCode = Md5Utils.hash(vo.getPhone()+vo.getUserName());
            String operUserCode = SecurityUtils.getLoginUser().getUser().getUserCode();
            UserInfo userInfo = (UserInfo) DataTransfer.transfer(vo, UserInfo.class);
            userInfo.setUserCode(userCode);
            if (!ObjectUtils.isEmpty(userInfo.getJobNo())){
                userInfo.setPassWord(SecurityUtils.encryptPassword(PinYinHeadUtils.getPinYinHeadChar(userInfo.getUserName()) + userInfo.getJobNo()));
            }else if (!ObjectUtils.isEmpty(userInfo.getPhone())){
                userInfo.setPassWord(SecurityUtils.encryptPassword(PinYinHeadUtils.getPinYinHeadChar(userInfo.getUserName()) + userInfo.getPhone()));
            }else {
                userInfo.setPassWord(SecurityUtils.encryptPassword("Gj202407"));
            }
            userInfo.setRemark(vo.getRemark());
            userInfo.setIsValid(1);
            userInfo.setCreatorCode(operUserCode);
            userInfo.setCreatorTime(LocalDateTime.now());
            baseMapper.insert(userInfo);

            UserCompany userCompany = new UserCompany();
            userCompany.setUserCode(userCode);
            userCompany.setCompanyId(Long.parseLong(vo.getCompanyId() + ""));
            userCompany.setDepartId(vo.getDepartId());
            userCompany.setWorkId(vo.getWorkId());
            userCompany.setUserName(vo.getUserName());
            userCompany.setIsEnable(1);
            userCompany.setIsOut(1);
            userCompany.setIsActivate(1);
            userCompany.setTrainTypeIdStr(vo.getTrainTypeIDStr());
            userCompany.setCreatorCode(operUserCode);
            userCompany.setCreatorTime(LocalDateTime.now());
            userCompany.setIsValid(1);
            if (StringUtils.isNotEmpty(vo.getJoinTime())) {
                userCompany.setJoinTime(vo.getJoinTime());
            }
            if (StringUtils.isNotEmpty(vo.getHomeAddress())) {
                userCompany.setHomeAddress(vo.getHomeAddress());
            }
            userCompanyMapper.insert(userCompany);
            Integer code = lessonSafeTrainCompanyUserTemplateMapper.selectCount(new QueryWrapper<LessonSafeTrainCompanyUserTemplate>()
                    .eq("user_code", userCode)
                    .eq("lesson_category_id",Long.parseLong(vo.getTrainTypeIDStr()+""))
                    .eq("company_id",Long.parseLong(vo.getCompanyId()+"")));
            //添加LessonSafeTrainCompanyUserTemplate表数据
            if(code<1 && StringUtils.isNotEmpty(vo.getTrainTypeIDStr()) && !vo.getTrainTypeIDStr().equals("0")){
                LambdaQueryWrapper<CompanyLessonSafeTrainSet> companyLessonSafeTrainSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
                companyLessonSafeTrainSetLambdaQueryWrapper.eq(CompanyLessonSafeTrainSet::getIsValid, 1);
                companyLessonSafeTrainSetLambdaQueryWrapper.eq(CompanyLessonSafeTrainSet::getCompanyId, vo.getCompanyId());
                companyLessonSafeTrainSetLambdaQueryWrapper.eq(CompanyLessonSafeTrainSet::getLessonCategoryId, vo.getTrainTypeIDStr());
                CompanyLessonSafeTrainSet companyLessonSafeTrainSet = companyLessonSafeTrainSetMapper.selectOne(companyLessonSafeTrainSetLambdaQueryWrapper);

                LessonSafeTrainCompanyUserTemplate lessonSafeTrainCompanyUserTemplate = new LessonSafeTrainCompanyUserTemplate();
                lessonSafeTrainCompanyUserTemplate.setCompanyLessonPayId(companyLessonSafeTrainSet.getId());
                lessonSafeTrainCompanyUserTemplate.setIsPayType(companyLessonSafeTrainSet.getIsPayType());
                lessonSafeTrainCompanyUserTemplate.setPayTypeName(companyLessonSafeTrainSet.getIsPayType() == 2 ? "学员免费" : companyLessonSafeTrainSet.getIsPayType() == 3 ? "学员付费" : "");
                lessonSafeTrainCompanyUserTemplate.setCompanyId(Long.parseLong(vo.getCompanyId() + ""));
                lessonSafeTrainCompanyUserTemplate.setCompanyName(companyLessonSafeTrainSet.getCompanyName());
                lessonSafeTrainCompanyUserTemplate.setUserCode(userCode);
                lessonSafeTrainCompanyUserTemplate.setUserName(vo.getUserName());
                lessonSafeTrainCompanyUserTemplate.setLessonCategoryFid(1L);
                lessonSafeTrainCompanyUserTemplate.setLessonCategoryId(Long.parseLong(vo.getTrainTypeIDStr() + ""));
                lessonSafeTrainCompanyUserTemplate.setLessonCategoryName(companyLessonSafeTrainSet.getLessonCategoryName());
                lessonSafeTrainCompanyUserTemplate.setHandMode(companyLessonSafeTrainSet.getHandMode());
                lessonSafeTrainCompanyUserTemplate.setIsTrusteeship(companyLessonSafeTrainSet.getIsTrusteeship());
                lessonSafeTrainCompanyUserTemplate.setSource(companyLessonSafeTrainSet.getSource());
                lessonSafeTrainCompanyUserTemplate.setSort(1);
                lessonSafeTrainCompanyUserTemplate.setCreatorCode(operUserCode);
                lessonSafeTrainCompanyUserTemplate.setCreationTime(LocalDateTime.now());
                lessonSafeTrainCompanyUserTemplate.setIsValid(1);
                lessonSafeTrainCompanyUserTemplate.setIsFree(1);
                lessonSafeTrainCompanyUserTemplate.setStatisticalGroup(vo.getStatisticalGroup());
                lessonSafeTrainCompanyUserTemplateMapper.insert(lessonSafeTrainCompanyUserTemplate);
            }
            return AjaxResult.success(userInfo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserInfo(UserInfoVO vo) {
        UserInfo entity = (UserInfo) DataTransfer.transfer(vo, UserInfo.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer clearPhoto(String userCode) {
        return baseMapper.clearPhotoByUserCode(userCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer editUserRecordSync(String userCode) {
        String operUserCode = SecurityUtils.getLoginUser().getUser().getUserCode();
        LambdaQueryWrapper<UserCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCompany::getUserCode, userCode);
        UserCompany userCompany = userCompanyMapper.selectOne(queryWrapper);
        Integer operType = userCompany.getIsOut() == 1 ? 0 : 1;
        userCompany.setIsOut(operType);
        userCompanyMapper.updateById(userCompany);
        //添加日志
        OperUserLog operUserLog = new OperUserLog();
        operUserLog.setCompanyId(userCompany.getCompanyId());
        operUserLog.setOperType(operType);
        operUserLog.setWorkId(userCompany.getWorkId());
        Company company = companyMapper.selectById(userCompany.getCompanyId());
        operUserLog.setCompanyName(company.getCompanyName());
        CompanyWork companyWork = companyWorkMapper.selectById(userCompany.getWorkId());
        operUserLog.setWorkName(companyWork.getWorkName());
        operUserLog.setUserName(userCompany.getUserName());
        UserInfoVO userInfoVO = userInfoMapper.selectUserLoginInfoByUserCode(userCode);
        operUserLog.setPhone(userInfoVO.getPhone());
        operUserLog.setIdCard(userInfoVO.getIdCard());
        UserInfoVO operUserInfo = userInfoMapper.selectUserLoginInfoByUserCode(operUserCode);
        operUserLog.setOperName(operUserInfo.getPhone());
        //删除当前离职人员所有菜单权限
        LambdaQueryWrapper<UserRole> userRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userRoleLambdaQueryWrapper.eq(UserRole::getUserCode, userCode);
        userRoleMapper.delete(userRoleLambdaQueryWrapper);
        //删除当前离职人员所有管理员数据
        LambdaQueryWrapper<UserLimit> userLimitLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLimitLambdaQueryWrapper.eq(UserLimit::getUserCode, userCode);
        userLimitMapper.delete(userLimitLambdaQueryWrapper);
        if (operType == 0) {
            SimpleDateFormat format=new SimpleDateFormat("yyyy-MM");
            String format1 = format.format(new Date());
            List<Long> lessonSafeTrainVOS = lessonSafeTrainMapper.selectLessonSafeTrainIds(format1+"-01");
            lessonSafeTrainVOS.forEach(a->{
                //回收所有课程
                LessonSafeTrainDisVO lessonSafeTrainDisVO=new LessonSafeTrainDisVO();
                lessonSafeTrainDisVO.setUserCodes(userCode);
                lessonSafeTrainDisVO.setLessonId(a);
                lessonSafeTrainService.delUserLessonRecordSafeTrain(lessonSafeTrainDisVO);
            });
            LambdaUpdateWrapper<LessonSafeTrainCompanyUserTemplate> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(LessonSafeTrainCompanyUserTemplate::getIsValid, 0);
            updateWrapper.set(LessonSafeTrainCompanyUserTemplate::getReviseTime, LocalDateTime.now());
            updateWrapper.eq(LessonSafeTrainCompanyUserTemplate::getIsValid, 1);
            updateWrapper.eq(LessonSafeTrainCompanyUserTemplate::getUserCode, userCode);
            lessonSafeTrainCompanyUserTemplateMapper.update(null, updateWrapper);
        }
        return operUserLogMapper.insert(operUserLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer editJob(String userCode, String job) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUserCode, userCode);
        UserInfo userInfo = baseMapper.selectOne(queryWrapper);
        UserCompany userCompany = userCompanyMapper.selectOne(new LambdaQueryWrapper<UserCompany>().eq(UserCompany::getUserCode, userCode));
        userInfo.setJobNo(job);
        if (!ObjectUtils.isEmpty(userInfo.getJobNo())){
            userInfo.setPassWord(SecurityUtils.encryptPassword(PinYinHeadUtils.getPinYinHeadChar(userCompany.getUserName()) + userInfo.getJobNo()));
        }else if (!ObjectUtils.isEmpty(userInfo.getPhone())){
            userInfo.setPassWord(SecurityUtils.encryptPassword(PinYinHeadUtils.getPinYinHeadChar(userCompany.getUserName()) + userInfo.getPhone()));
        }else {
            userInfo.setPassWord(SecurityUtils.encryptPassword("Gj202407"));
        }
        return baseMapper.updateById(userInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer editJoinTime(String userCode, String joinTime) {
        LambdaQueryWrapper<UserCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCompany::getUserCode, userCode);
        UserCompany userCompany = userCompanyMapper.selectOne(queryWrapper);
        userCompany.setJoinTime(joinTime);
        return userCompanyMapper.updateById(userCompany);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer editUserInfoRecordSync(UserInfoVO vo) {
        if (vo.getFlag().equals("editname")) {
            //修改姓名
            UserCompany userCompany = getUserCompanyByUserCode(vo.getUserCode());
            userCompany.setUserName(vo.getKeyWord());
            userCompanyMapper.updateById(userCompany);
            editUserNameSync(vo.getUserCode(), vo.getKeyWord());
        } else if (vo.getFlag().equals("editidcard")) {
            //修改身份证
            UserInfo userInfo = getUserInfoByUserCode(vo.getUserCode());
            userInfo.setIdCard(vo.getKeyWord());
            userInfoMapper.updateById(userInfo);
        } else if (vo.getFlag().equals("editphone")) {
            //修改手机号
            UserInfo userInfo = getUserInfoByUserCode(vo.getUserCode());
            userInfo.setPhone(vo.getKeyWord());
            userInfoMapper.updateById(userInfo);
        } else if (vo.getFlag().equals("editcompany")) {
            //修改企业相关信息
            String[] keyArray = vo.getKeyWord().split("\\|");   //企业/部门/岗位/参培分类/是否免费/统计分组
            Long companyId = Long.parseLong(keyArray[0]);
            Long departId = Long.parseLong(keyArray[1]);
            UserCompany userCompany = getUserCompanyByUserCode(vo.getUserCode());
            userCompany.setCompanyId(companyId);
            userCompany.setDepartId(departId);
            userCompany.setWorkId(Long.parseLong(keyArray[2]));
            //将当月课程调至新分公司
            Company company = companyMapper.selectById(departId);
            Company com = companyMapper.selectById(companyId);
            userLessonRecordSafeTrainMongoService.updateRecord(companyId, departId,company.getCompanyName(),
                    com.getCompanyName(),vo.getUserCode());
            userCourseRecordSafeTrainMongoService.updateCourseRecord(companyId, departId,
                    com.getCompanyName(),vo.getUserCode());
            //回收所有课程
            SimpleDateFormat format=new SimpleDateFormat("yyyy-MM");
            String format1 = format.format(new Date());
            List<Long> lessonSafeTrainVOS = lessonSafeTrainMapper.selectLessonSafeTrainIdsNoMonth(format1+"-01");
            lessonSafeTrainVOS.forEach(a->{
                //回收其他月份课程
                LessonSafeTrainDisVO lessonSafeTrainDisVO=new LessonSafeTrainDisVO();
                lessonSafeTrainDisVO.setUserCodes(vo.getUserCode());
                lessonSafeTrainDisVO.setLessonId(a);
                lessonSafeTrainService.delUserLessonRecordSafeTrain(lessonSafeTrainDisVO);
            });
            LambdaUpdateWrapper<LessonSafeTrainCompanyUserTemplate> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(LessonSafeTrainCompanyUserTemplate::getIsValid, 0);
            updateWrapper.eq(LessonSafeTrainCompanyUserTemplate::getIsValid, 1);
            updateWrapper.in(LessonSafeTrainCompanyUserTemplate::getUserCode, vo.getUserCode());
            lessonSafeTrainCompanyUserTemplateMapper.update(null, updateWrapper);
            userCompanyMapper.updateById(userCompany);
        } else if (vo.getFlag().equals("editwork")) {
            //修改岗位
            String[] keyArray = vo.getKeyWord().split("\\|");
            UserCompany userCompany = getUserCompanyByUserCode(vo.getUserCode());
            userCompany.setWorkId(Long.parseLong(keyArray[0]));
            userCompanyMapper.updateById(userCompany);
        } else if (vo.getFlag().equals("editjobno")) {
            //修改工号
            UserInfo userInfo = getUserInfoByUserCode(vo.getUserCode());
            userInfo.setJobNo(vo.getKeyWord());
            userInfoMapper.updateById(userInfo);
        } else if (vo.getFlag().equals("editjointime")) {
            //修改入职日期
            UserCompany userCompany = getUserCompanyByUserCode(vo.getUserCode());
            userCompany.setJoinTime(vo.getKeyWord());
            userCompanyMapper.updateById(userCompany);
        } else if (vo.getFlag().equals("editdepart")) {
            //修改部门
            String[] keyArray = vo.getKeyWord().split("\\|");
            UserCompany userCompany = getUserCompanyByUserCode(vo.getUserCode());
            userCompany.setDepartId(Long.parseLong(keyArray[0]));
            //将当月课程调至新分公司
            Company company = companyMapper.selectById(Long.parseLong(keyArray[0]));
            userLessonRecordSafeTrainMongoService.updateRecord(null,Long.parseLong(keyArray[0]),company.getCompanyName(),
                    null,vo.getUserCode());
            userCourseRecordSafeTrainMongoService.updateCourseRecord(null, Long.parseLong(keyArray[0]),
                    null,vo.getUserCode());
            userCompanyMapper.updateById(userCompany);
        } else if (vo.getFlag().equals("editnumber")) {
            //修改手机号
            UserInfo userInfo = getUserInfoByUserCode(vo.getUserCode());
            userInfo.setRemark(vo.getPlateNumber());
            userInfoMapper.updateById(userInfo);
        }
        return 1;
    }

    @Override
    public PageUtils getUserOperLog(String userCode, PageEntity pageEntity) {
        PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
        SysOperLog param = new SysOperLog();
        param.setOperatedUserCode(userCode);
        List<SysOperLog> list = sysOperLogMapper.selectOperLogList(param);
        List<UserOperLogVO> userOperLogVOS = new ArrayList<>();
        for (SysOperLog sysOperLog : list) {
            UserOperLogVO userOperLogVO = new UserOperLogVO();
            userOperLogVO.setId(sysOperLog.getId());
            userOperLogVO.setOperName(sysOperLog.getOperName());
            userOperLogVO.setCreatorTime(sysOperLog.getOperTime());
            try {
                userOperLogVO.setOperContent(JSONObject.parseObject(JSONObject.parseObject(sysOperLog.getOperParam()).getString("request")).getString("OperContent"));
            } catch (Exception e) {
                userOperLogVO.setOperContent("");
            }
            userOperLogVOS.add(userOperLogVO);
        }
        return new PageUtils(pageEntity, userOperLogVOS);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer restPassword(String userCode) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUserCode, userCode);
        UserInfo userInfo = this.getOne(queryWrapper);
        UserCompany userCompany = userCompanyMapper.selectOne(new LambdaQueryWrapper<UserCompany>().eq(UserCompany::getUserCode, userCode));
        if (!ObjectUtils.isEmpty(userInfo.getJobNo())){
            userInfo.setPassWord(SecurityUtils.encryptPassword(PinYinHeadUtils.getPinYinHeadChar(userCompany.getUserName()) + userInfo.getJobNo()));
        }else if (!ObjectUtils.isEmpty(userInfo.getPhone())){
            userInfo.setPassWord(SecurityUtils.encryptPassword(PinYinHeadUtils.getPinYinHeadChar(userCompany.getUserName()) + userInfo.getPhone()));
        }else {
            userInfo.setPassWord(SecurityUtils.encryptPassword("Gj202407"));
        }
        return this.updateById(userInfo) ? 1 : 0;
    }

    @Override
    public Integer signurl(String userCode, String signUrl) {
        LambdaQueryWrapper<UserCompany> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserCompany::getUserCode, userCode);
        UserCompany userCompany = userCompanyMapper.selectOne(lambdaQueryWrapper);
        userCompany.setSignUrl(signUrl);
        return userCompanyMapper.updateById(userCompany);
    }

    @Override
    public Integer editphoto(String photoUrl) {
        UserInfo loginUser = SecurityUtils.getLoginUser().getUser();
        LambdaQueryWrapper<UserCompany> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserCompany::getUserCode, loginUser.getUserCode());
        UserCompany userCompany = userCompanyMapper.selectOne(lambdaQueryWrapper);
        userCompany.setUserPhoto(photoUrl);
        return userCompanyMapper.updateById(userCompany);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserInfo(Long id) {
        return this.removeById(id);
    }

    @Override
    public UserInfo selectUserByUserName(String username) {
        return getBaseMapper().selectUserByUserName(username);
    }

    @Override
    public AuthUserInfoVO selectUserByUserCode(String userCode) {
        return getBaseMapper().selectUserByUserCode(userCode);
    }

    @Override
    public AuthUserInfoVO selectUserByIdCard(String idCard) {
        return getBaseMapper().selectUserByIdCard(idCard);
    }

    @Override
    public UserInfo selectUserByUnionId(String unionId, String openId) {
        return getBaseMapper().selectUserByUnionId(unionId, openId);
    }

    @Override
    public UserInfoVO selectUserLoginInfoByUserCode(String userCode) {
        return getBaseMapper().selectUserLoginInfoByUserCode(userCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer companyEditUserInfo(UserInfoVO vo) {
        LambdaQueryWrapper<UserCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCompany::getUserCode, vo.getUserCode());
        UserCompany userCompany = userCompanyMapper.selectOne(queryWrapper);
        userCompany.setUserName(vo.getUserName());
        userCompany.setUserPhoto(vo.getPhotoUrl());
        editUserNameSync(vo.getUserCode(), vo.getUserName());
        return userCompanyMapper.updateById(userCompany);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer editPwd(UserInfoVO vo) {
        String userCode = SecurityUtils.getLoginUser().getUser().getUserCode();
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUserCode, userCode);
        UserInfo userInfo = baseMapper.selectOne(queryWrapper);
        //判断旧密码是否正确
        if (SecurityUtils.matchesPassword(vo.getOldPwd(), userInfo.getPassWord())) {
            //正确
            userInfo.setPassWord(SecurityUtils.encryptPassword(vo.getNewPwd()));
            return this.updateById(userInfo) ? 1 : 0;
        } else {
            return -1;
        }

    }

    @Override
    public AjaxResult importIsOut(UserInfoRequest request) {
        List<UserBatchOutExcelVO> userBatchOutExcelVOS = null;
        try {
            InputStream inputStream = new URL(request.getExcelFilePath()).openStream();
            ExcelUtil<UserBatchOutExcelVO> util = new ExcelUtil<UserBatchOutExcelVO>(UserBatchOutExcelVO.class);
            userBatchOutExcelVOS = util.importExcel(inputStream);
        } catch (Exception e) {
            log.error("批量离职数据流操作异常：" + e.getMessage());
            return AjaxResult.error("请求异常！", "请求异常！");
        }
        //去重
        List<String> totalOut = new ArrayList<>();
        Integer successNum = 0;
        Integer errorNum = 0;
        List<UserBatchOutExcelResVO> userBatchOutExcelVORes = new ArrayList<>();
        //循环批量离职
        for (UserBatchOutExcelVO userBatchOutExcelVO : userBatchOutExcelVOS) {
            //判断是否已经执行
            if (totalOut.contains(userBatchOutExcelVO.getIdCard())) {
                continue;
            }
            totalOut.add(userBatchOutExcelVO.getIdCard());
            UserBatchOutExcelResVO res = new UserBatchOutExcelResVO();
            LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(UserInfo::getUserCode);
            queryWrapper.eq(UserInfo::getIdCard, userBatchOutExcelVO.getIdCard());
            UserInfo userInfo = baseMapper.selectOne(queryWrapper);
            //判断身份证是否存在
            if (userInfo != null && StringUtils.isNotEmpty(userInfo.getUserCode())) {
                Integer count = userCompanyMapper.userOut(userInfo.getUserCode());
                SimpleDateFormat format=new SimpleDateFormat("yyyy-MM");
                String format1 = format.format(new Date());
                List<Long> lessonSafeTrainVOS = lessonSafeTrainMapper.selectLessonSafeTrainIds(format1+"-01");
                lessonSafeTrainVOS.forEach(a->{
                    //回收所有课程
                    LessonSafeTrainDisVO lessonSafeTrainDisVO=new LessonSafeTrainDisVO();
                    lessonSafeTrainDisVO.setUserCodes(userInfo.getUserCode());
                    lessonSafeTrainDisVO.setLessonId(a);
                    lessonSafeTrainService.delUserLessonRecordSafeTrain(lessonSafeTrainDisVO);
                });
                //回收所有课程
//                lessonSafeTrainService.delUserLessonRecordSafeTrainAll(userInfo.getUserCode());
                LambdaUpdateWrapper<LessonSafeTrainCompanyUserTemplate> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(LessonSafeTrainCompanyUserTemplate::getIsValid, 0);
                updateWrapper.set(LessonSafeTrainCompanyUserTemplate::getReviseTime, LocalDateTime.now());
                updateWrapper.eq(LessonSafeTrainCompanyUserTemplate::getIsValid, 1);
                updateWrapper.eq(LessonSafeTrainCompanyUserTemplate::getUserCode, userInfo.getUserCode());
                lessonSafeTrainCompanyUserTemplateMapper.update(null, updateWrapper);
                if (count == 0) {
                    //离职异常，联系管理员
                    res.setOrder(userBatchOutExcelVO.getOrder());
                    res.setIdCard(userBatchOutExcelVO.getIdCard());
                    res.setRemark("离职异常，联系管理员");
                    userBatchOutExcelVORes.add(res);
                    errorNum += 1;
                } else {
                    successNum += 1;
                }
            } else {
                //身份证号不存在
                res.setOrder(userBatchOutExcelVO.getOrder());
                res.setIdCard(userBatchOutExcelVO.getIdCard());
                res.setRemark("身份证号不存在");
                userBatchOutExcelVORes.add(res);
                errorNum += 1;
            }
        }
        String message = "操作成功" + successNum + "人，操作失败" + errorNum + "人！";
        //判断当前导入是否有失败
        if (errorNum == 0) {
            return AjaxResult.success(message, "");
        } else {
            try {
                ExcelUtil<UserBatchOutExcelResVO> util = new ExcelUtil<UserBatchOutExcelResVO>(UserBatchOutExcelResVO.class);
                String fileName = util.encodingDateFilename("离职失败信息");
                InputStream inputStream = util.exportExcelFile(userBatchOutExcelVORes, fileName);
                UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "user", fileName);
                return AjaxResult.success(message, fileName + ".xlsx," + uploadFile.getUrl());
            } catch (Exception e) {
                log.error("用户批量离职错误报告上传异常：" + e.getMessage());
                return AjaxResult.success(message, "");
            }
        }
    }

    @Override
    public AjaxResult Import(UserInfoRequest request) {
        List<UserBatchAddExcelVO> userBatchAddExcelVOS = null;
        try {
            InputStream inputStream = new URL(request.getExcelFilePath()).openStream();
            ExcelUtil<UserBatchAddExcelVO> util = new ExcelUtil<UserBatchAddExcelVO>(UserBatchAddExcelVO.class);
            userBatchAddExcelVOS = util.importExcel(inputStream);
        } catch (Exception e) {
            log.error("批量添加数据流操作异常：" + e.getMessage());
            return AjaxResult.error("请求异常！", "请求异常！");
        }
        Integer successNum = 0;
        Integer errorNum = 0;
        List<UserBatchAddExcelResVO> userBatchAddExcelVOSRes = new ArrayList<>();
        for (UserBatchAddExcelVO userBatchAddExcelVO : userBatchAddExcelVOS) {
            UserInfoVO userInfoVO = (UserInfoVO) DataTransfer.transfer(userBatchAddExcelVO, UserInfoVO.class);
            userInfoVO.setCompanyId(Integer.parseInt(request.getCompanyId() + ""));
            userInfoVO.setDepartId(request.getDepartId());
            userInfoVO.setWorkId(request.getWorkId());
            userInfoVO.setTrainTypeIDStr(request.getTrainTypeIDStr());
            userInfoVO.setStatisticalGroup(request.getStatisticalGroup());
            //日期处理后添加
            if (userBatchAddExcelVO.getJoinTime() != null) {
                userInfoVO.setJoinTime(DateUtils.dateTime(userBatchAddExcelVO.getJoinTime()));
            }
            AjaxResult ajaxResult = saveUserInfo(userInfoVO);
            if (!ajaxResult.get("code").equals(1000)) {
                //添加失败
                UserBatchAddExcelResVO userBatchAddExcelResVO = (UserBatchAddExcelResVO) DataTransfer.transfer(userBatchAddExcelVO, UserBatchAddExcelResVO.class);
                userBatchAddExcelResVO.setError(ajaxResult.get("Message").toString());
                userBatchAddExcelVOSRes.add(userBatchAddExcelResVO);
                errorNum++;
            } else {
                successNum++;
            }
        }
        String message = "操作成功" + successNum + "人，操作失败" + errorNum + "人！";
        //判断当前导入是否有失败
        if (errorNum == 0) {
            return AjaxResult.success(message, "");
        } else {
            try {
                ExcelUtil<UserBatchAddExcelResVO> util = new ExcelUtil<UserBatchAddExcelResVO>(UserBatchAddExcelResVO.class);
                String fileName = util.encodingDateFilename("添加用户失败信息");
                InputStream inputStream = util.exportExcelFile(userBatchAddExcelVOSRes, fileName);
                UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(inputStream, "user", fileName);
                return AjaxResult.success(message, fileName + ".xlsx," + uploadFile.getUrl());
            } catch (Exception e) {
                log.error("用户批量添加错误报告上传异常：" + e.getMessage());
                return AjaxResult.success(message, "");
            }
        }
    }

    @Override
    public UserFilesVO getMyFiles(UserInfoVO vo) {
        UserFilesVO userFilesVO = new UserFilesVO();
        List<UserInfoVO> userInfoVO = baseMapper.selectUserInfoByUserCode(vo.getUserCode());
        userFilesVO.setUserinfo(userInfoVO);
        UserLessonRecordSafeTrain queryRequest = new UserLessonRecordSafeTrain();
        queryRequest.setIsValid(1);
        queryRequest.setUserCode(vo.getUserCode());
        List<UserLessonRecordSafeTrain> lessons = userLessonRecordSafeTrainMongoService.getUserLesson(queryRequest);
        userFilesVO.setUserlesson(lessons);
        return userFilesVO;
    }

    @Override
    public List<UserManageVO> getSendUserInfo(String month) {
        return getBaseMapper().selectSendUserInfo(month);
    }

    @Override
    public PageUtils getUserMsgCenter(GetUserMsgCenterRequest request, PageEntity pageEntity) {
        switch (request.getFlag()) {
            case "msgcenter":
                //获取用户ID
                LambdaQueryWrapper<PushCenterMsg> queryWrapper = Wrappers.lambdaQuery(PushCenterMsg.class)
                        .eq(PushCenterMsg::getUserId, request.getUserId()).eq(PushCenterMsg::getIsRead, 0).orderByDesc(PushCenterMsg::getId);
                List<PushCenterMsg> pushCenterMsgList = pushCenterMsgMapper.selectList(queryWrapper);
                List<GetUserMsgCenterTotalVO> result = new ArrayList<>();
                for (Map.Entry<Integer, String> entry : funTypeMap.entrySet()) {
                    Integer key = entry.getKey();
                    String value = entry.getValue();
                    List<PushCenterMsg> msgs = pushCenterMsgList.stream().filter(pushCenterMsg -> pushCenterMsg.getFunType().equals(key)).collect(Collectors.toList());
                    GetUserMsgCenterTotalVO vo = new GetUserMsgCenterTotalVO();
                    vo.setFunType(key);
                    vo.setFunTypeName(value);
                    vo.setUserId(request.getUserId());
                    vo.setIcon("");
                    vo.setTitle("");
                    if (CollectionUtils.isNotEmpty(msgs)) {
                        vo.setCreatorTime(msgs.get(0).getCreatorTime());
                        vo.setTitle(msgs.get(0).getTitle());
                        vo.setRedNum(msgs.size());
                    }
                    result.add(vo);
                }
                return new PageUtils(pageEntity, result);
            case "list":
                if (null != pageEntity) {
                    PageHelper.startPage(pageEntity.getPageIndex(), pageEntity.getPageSize());
                }
                LambdaQueryWrapper<PushCenterMsg> listQueryWrapper = Wrappers.lambdaQuery(PushCenterMsg.class)
                        .eq(PushCenterMsg::getUserId, request.getUserId())
                        .eq(PushCenterMsg::getFunType, request.getFunType());
                List<PushCenterMsg> list = pushCenterMsgMapper.selectList(listQueryWrapper);
                return new PageUtils(pageEntity, DataTransfer.transferList(list, GetUserMsgCenterListVO.class));
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    public List<UserManageVO> getAllSendUserInfo() {
        return getBaseMapper().selectAllSendUserInfo();
    }

    @Override
    public boolean updateReadyStatus(UpdateReadyStatusRequest request) {
        switch (request.getFlag()) {
            case "clearall":
                LambdaUpdateWrapper<PushCenterMsg> updateWrapper = Wrappers.lambdaUpdate(PushCenterMsg.class)
                        .set(PushCenterMsg::getIsRead, 1)
                        .eq(PushCenterMsg::getUserId, request.getId());
                return pushCenterMsgMapper.update(null, updateWrapper) > 0;
            case "clearone":
                LambdaUpdateWrapper<PushCenterMsg> updateWrapper2 = Wrappers.lambdaUpdate(PushCenterMsg.class)
                        .set(PushCenterMsg::getIsRead, 1)
                        .eq(PushCenterMsg::getId, request.getId());
                return pushCenterMsgMapper.update(null, updateWrapper2) > 0;
        }
        throw new CustomException("不支持的操作标识" + request.getFlag());
    }

    @Override
    public boolean authFaceDetection(AuthPortraitVerificationRequest authPortraitVerificationRequest) {
        return FaceUtil.detectLivingFace(authPortraitVerificationRequest.getComparisonPhotos());
    }

    public UserInfo getUserInfoByUserCode(String userCode) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUserCode, userCode);
        return userInfoMapper.selectOne(queryWrapper);
    }

    public UserCompany getUserCompanyByUserCode(String userCode) {
        LambdaQueryWrapper<UserCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCompany::getUserCode, userCode);
        return userCompanyMapper.selectOne(queryWrapper);
    }

    public void editUserNameSync(String userCode, String userName) {
        //t_d_lesson_safe_train_company_user_template
        LambdaUpdateWrapper<LessonSafeTrainCompanyUserTemplate> lessonSafeTrainCompanyUserTemplateLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lessonSafeTrainCompanyUserTemplateLambdaUpdateWrapper.eq(LessonSafeTrainCompanyUserTemplate::getUserCode, userCode);
        lessonSafeTrainCompanyUserTemplateLambdaUpdateWrapper.set(LessonSafeTrainCompanyUserTemplate::getUserName, userName);
        lessonSafeTrainCompanyUserTemplateMapper.update(null, lessonSafeTrainCompanyUserTemplateLambdaUpdateWrapper);
        //t_d_user_lesson_record_safe_train
        userLessonRecordSafeTrainMongoService.updateName(userCode, userName);
    }

}