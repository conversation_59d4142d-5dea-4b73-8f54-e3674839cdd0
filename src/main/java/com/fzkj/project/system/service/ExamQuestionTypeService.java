package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.ExamQuestionType;
import com.fzkj.project.system.vo.ExamQuestionTypeVO;
import com.fzkj.project.system.request.ExamQuestionTypeRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 试题类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ExamQuestionTypeService extends IService<ExamQuestionType> {

    List<ExamQuestionType> listExamQuestionType(ExamQuestionTypeRequest request);

    PageUtils listByPage(ExamQuestionTypeRequest request, PageEntity pageEntity);

    ExamQuestionTypeVO findExamQuestionType(Long id);

    Long saveExamQuestionType(ExamQuestionTypeVO vo);

    boolean updateExamQuestionType(ExamQuestionTypeVO vo);

    boolean removeExamQuestionType(Long id);
}
