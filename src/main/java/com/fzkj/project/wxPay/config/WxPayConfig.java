package com.fzkj.project.wxPay.config;

import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.io.FileNotFoundException;

@Component
public class WxPayConfig {
    private static String appid = "wx252535d88d3b0c09";//微信公众号或者小程序等的appid
    private static String secret = "67a441db1e1c04bede54befaa6dcb560";//微信公众号或者小程序等的密钥
    private static String mchid = "1416091102";//微信支付商户号
    private static String mchkey = "8bfdc3ea49191fbe9508b5d6f88b3d5f";//微信支付商户密钥
    private static String noticeUrl = "https://fkm.canow.com.cn:8002/edu/app/wx/notify";//回调地址

    public WxPayConfig() {
    }

    public static String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        WxPayConfig.appid = appid;
    }

    public static String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        WxPayConfig.secret = secret;
    }

    public static String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        WxPayConfig.mchid = mchid;
    }

    public static String getMchkey() {
        return mchkey;
    }

    public void setMchkey(String mchkey) {
        WxPayConfig.mchkey = mchkey;
    }

    public static String getNoticeUrl() {
        return noticeUrl;
    }

    public void setNoticeUrl(String noticeUrl) {
        WxPayConfig.noticeUrl = noticeUrl;
    }
}
