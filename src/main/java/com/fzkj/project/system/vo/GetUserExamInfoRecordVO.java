package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GetUserExamInfoRecordVO {
    @ApiModelProperty(value = "ID", example = "3190846")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "组织ID", example = "49")
    @JsonProperty("OrganizationID")
    private String organizationId;

    @ApiModelProperty(value = "用户ID", example = "b135339037ae410a87ad42c872c9353f")
    @JsonProperty("UserID")
    private String userId;

    @ApiModelProperty(value = "用户名", example = "测试账号")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "身份证号", example = "500228199306271018")
    @JsonProperty("IDCard")
    private String idCard;

    @ApiModelProperty(value = "考试ID", example = "25607")
    @JsonProperty("ExamID")
    private Long examId;

    @ApiModelProperty(value = "及格分数", example = "80")
    @JsonProperty("PassMark")
    private int passMark;

    @ApiModelProperty(value = "分数", example = "30")
    @JsonProperty("Score")
    private int score;

    @ApiModelProperty(value = "总分", example = "100")
    @JsonProperty("TotalScore")
    private int totalScore;

    @ApiModelProperty(value = "补考次数", example = "1")
    @JsonProperty("ResitNumber")
    private int resitNumber;

    @ApiModelProperty(value = "使用时间", example = "30")
    @JsonProperty("UseTimeCount")
    private int useTimeCount;

    @ApiModelProperty(value = "准确率", example = "0.00")
    @JsonProperty("Accuracy")
    private double accuracy;

    @ApiModelProperty(value = "考试次数", example = "3")
    @JsonProperty("UserExamCount")
    private int userExamCount;

    @ApiModelProperty(value = "是否静态", example = "1")
    @JsonProperty("IsStatic")
    private Integer isStatic;

    @ApiModelProperty(value = "错误次数", example = "2")
    @JsonProperty("ErrorCount")
    private int errorCount;

    @ApiModelProperty(value = "课程ID", example = "4412")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "照片URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2024-4/image/171195570410472447.jpg")
    @JsonProperty("PhotoUrl")
    private String photoUrl;

    @ApiModelProperty(value = "签名URL", example = "https://hy20-1252579960.cos.ap-chengdu.myqcloud.com/uesxcx/2023-11/image/169960956171731996.png")
    @JsonProperty("SignUrl")
    private String signUrl;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建时间", example = "2024-04-01T15:15:41.937")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效", example = "1")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "来源", example = "aqpx")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "用户手机号", example = "18988888888")
    @JsonProperty("UserPhone")
    private String userPhone;

    @ApiModelProperty(value = "部门ID", example = "50")
    @JsonProperty("DepartID")
    private Long departId;

}
