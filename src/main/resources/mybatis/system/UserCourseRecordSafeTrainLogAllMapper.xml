<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.UserCourseRecordSafeTrainLogAllMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.UserCourseRecordSafeTrainLogAll">
        <id column="id" property="id" />
        <result column="lesson_id" property="lessonId" />
        <result column="course_id" property="courseId" />
        <result column="organization_id" property="organizationId" />
        <result column="user_code" property="userCode" />
        <result column="study_time_count" property="studyTimeCount" />
        <result column="record_type" property="recordType" />
        <result column="record_type_str" property="recordTypeStr" />
        <result column="source" property="source" />
        <result column="creator_code" property="creatorCode" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_time" property="reviseTime" />
        <result column="is_valid" property="isValid" />
        <result column="reserved_field1" property="reservedField1" />
        <result column="reserved_field2" property="reservedField2" />
        <result column="reserved_field3" property="reservedField3" />
        <result column="reserved_field4" property="reservedField4" />
        <result column="reserved_field5" property="reservedField5" />
    </resultMap>

</mapper>