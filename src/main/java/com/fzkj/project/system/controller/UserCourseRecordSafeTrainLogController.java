package com.fzkj.project.system.controller;

import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.AddUserCourseLogRequest;
import com.fzkj.project.system.request.GetUserCourseStudyTimeRequest;
import com.fzkj.project.system.service.UserCourseRecordSafeTrainLogService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 安全培训课件分发学员学习记录日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
public class UserCourseRecordSafeTrainLogController extends BaseEntity {

    private final UserCourseRecordSafeTrainLogService UserCourseRecordSafeTrainLogServiceImpl;

    @PostMapping("Add_UserCourseLog")
    @ApiOperation(value = "小程序-新增课件学习日志")
    public AjaxResult Add_UserCourseLog(@RequestBody CommonRequest commonRequest) {
        AddUserCourseLogRequest request = (AddUserCourseLogRequest) commonRequest.getRequest(AddUserCourseLogRequest.class);
        return AjaxResult.success(UserCourseRecordSafeTrainLogServiceImpl.addUserCourseLog(request));
    }

    @PostMapping("Get_UserCourseStudyTime")
    @ApiOperation(value = "小程序-获取用户对应课件播放时间")
    public AjaxResult Get_UserCourseStudyTime(@RequestBody CommonRequest commonRequest) {
        GetUserCourseStudyTimeRequest request = (GetUserCourseStudyTimeRequest) commonRequest.getRequest(GetUserCourseStudyTimeRequest.class);
        return AjaxResult.success(UserCourseRecordSafeTrainLogServiceImpl.getUserCourseStudyTime(request));
    }
}
