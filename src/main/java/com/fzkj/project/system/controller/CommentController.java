package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.ExamRequest;
import com.fzkj.project.system.service.CommentService;
import com.fzkj.project.system.service.ExamService;
import com.fzkj.project.system.vo.CommentVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 评论Controller
 *
 */

@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "basicdata")
public class CommentController extends BaseEntity {

    private final CommentService commentService;

    @PostMapping("Get_Comment")
    @ApiOperation(value = "课件，内容评论分页查询")
    public AjaxResult getComment(@RequestBody CommonRequest commonRequest) {
        CommentVO request = (CommentVO) commonRequest.getRequest(CommentVO.class);
        return AjaxResult.success(commentService.getComment(request,commonRequest.getPageEntity()));
    }

    @PostMapping("ExportComment")
    @ApiOperation(value = "课件，内容评论导出")
    public AjaxResult exportComment(@RequestBody CommonRequest commonRequest) {
        CommentVO request = (CommentVO) commonRequest.getRequest(CommentVO.class);
        return AjaxResult.success(commentService.exportComment(request));
    }

    @PostMapping("Edit_CommentPC")
    @ApiOperation(value = "修改PC")
    public AjaxResult edit_CommentPC(@RequestBody CommonRequest commonRequest) {
        CommentVO request = (CommentVO) commonRequest.getRequest(CommentVO.class);
        return AjaxResult.success(commentService.updateComment(request));
    }

    @PostMapping("Edit_Comment")
    @ApiOperation(value = "修改小程序（课件评论edit）")
    public AjaxResult EditComment(@RequestBody CommonRequest commonRequest) {
        CommentVO request = (CommentVO) commonRequest.getRequest(CommentVO.class);
        return commentService.editComment(request,"10");
    }

    @PostMapping("Edit_CommentAPP")
    @ApiOperation(value = "发表评论小程序（内容评论）")
    public AjaxResult editCommentAPP(@RequestBody CommonRequest commonRequest) {
        CommentVO request = (CommentVO) commonRequest.getRequest(CommentVO.class);
        return commentService.editComment(request,"11");
    }

    @PostMapping("Get_MyComment")
    @ApiOperation(value = "课件，内容评论分页查询")
    public AjaxResult getMyComment(@RequestBody CommonRequest commonRequest) {
        CommentVO request = (CommentVO) commonRequest.getRequest(CommentVO.class);
        return AjaxResult.success(commentService.getMyComment(request,commonRequest.getPageEntity()));
    }

/*    @PostMapping("Get_CommentAPP")
    @ApiOperation(value = "课件或内容评论分页查询")
    public AjaxResult getCommentAPP(@RequestBody CommonRequest commonRequest) {
        CommentVO request = (CommentVO) commonRequest.getRequest(CommentVO.class);
        return AjaxResult.success(commentService.getCommentAPP(request,commonRequest.getPageEntity()));
    }*/
}
