package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.project.system.entity.LessonCourseSafeTrain;
import com.fzkj.project.system.entity.LessonSafeTrain;
import com.fzkj.project.system.entity.LessonSafeTrainPlanRelationship;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.vo.LessonSafeTrainPlanRelationshipVO;
import com.fzkj.project.system.request.LessonSafeTrainPlanRelationshipRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.LessonSafeTrainPlanRelationshipMapper;
import com.fzkj.project.system.service.LessonSafeTrainPlanRelationshipService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 给公交分发的培训课程关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class LessonSafeTrainPlanRelationshipServiceImpl extends ServiceImpl<LessonSafeTrainPlanRelationshipMapper, LessonSafeTrainPlanRelationship> implements LessonSafeTrainPlanRelationshipService {

    @Override
    public List<LessonSafeTrainPlanRelationship> listLessonSafeTrainPlanRelationship(LessonSafeTrainPlanRelationshipRequest request) {
        LessonSafeTrainPlanRelationship entity = (LessonSafeTrainPlanRelationship) DataTransfer.transfer(request, LessonSafeTrainPlanRelationship.class);
        LambdaQueryWrapper<LessonSafeTrainPlanRelationship> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(LessonSafeTrainPlanRelationshipRequest request, PageEntity pageEntity) {
        LessonSafeTrainPlanRelationship entity = (LessonSafeTrainPlanRelationship) DataTransfer.transfer(request, LessonSafeTrainPlanRelationship.class);
        LambdaQueryWrapper<LessonSafeTrainPlanRelationship> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<LessonSafeTrainPlanRelationship> page = this.page(
                new Query<LessonSafeTrainPlanRelationship>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public LessonSafeTrainPlanRelationshipVO findLessonSafeTrainPlanRelationship(Long id) {
        LessonSafeTrainPlanRelationship entity = this.getById(id);
        LessonSafeTrainPlanRelationshipVO vo = (LessonSafeTrainPlanRelationshipVO) DataTransfer.transfer(entity, LessonSafeTrainPlanRelationshipVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveLessonSafeTrainPlanRelationship(LessonSafeTrainPlanRelationshipVO vo) {
        LessonSafeTrainPlanRelationship entity = (LessonSafeTrainPlanRelationship) DataTransfer.transfer(vo, LessonSafeTrainPlanRelationship.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLessonSafeTrainPlanRelationship(LessonSafeTrainPlanRelationshipVO vo) {
        LessonSafeTrainPlanRelationship entity = (LessonSafeTrainPlanRelationship) DataTransfer.transfer(vo, LessonSafeTrainPlanRelationship.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeLessonSafeTrainPlanRelationship(Long id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editLessonSafeTrainPlanRelationship(LessonSafeTrainPlanRelationshipVO request) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LessonSafeTrainPlanRelationship entity = (LessonSafeTrainPlanRelationship) DataTransfer.transfer(request, LessonSafeTrainPlanRelationship.class);
        switch (request.getFlag()) {
            case "edit":
                String lessonIDs = request.getLessonIds();
                if (StringUtils.isEmpty(lessonIDs)) {
                    throw new CustomException("请添加课件");
                }
                String[] lessonIds = lessonIDs.split(",");
                Integer sort = request.getSort();
                if (null == sort) {
                    sort = 0;
                }
                for (int i = 0; i < lessonIds.length; i++) {
                    String lessonIdStr = lessonIds[i];
                    if (StringUtils.isEmpty(lessonIdStr)) {
                        continue;
                    }
                    Long lessonId = Long.valueOf(lessonIdStr);
                    entity.setId(null);
                    entity.setLessonId(lessonId);
                    entity.setSort(sort);
                    entity.setIsValid(1);
                    entity.setCreatorCode(user.getUserCode());
                    entity.setCreationTime(LocalDateTime.now());
                    entity.setReviseCode(user.getUserCode());
                    entity.setReviseTime(LocalDateTime.now());
                    this.save(entity);
                    sort++;
                }
                return true;
            case "del":
                LambdaQueryWrapper<LessonSafeTrainPlanRelationship> delWrapper = new LambdaQueryWrapper<>();
                delWrapper.eq(LessonSafeTrainPlanRelationship::getPlanId, request.getPlanId());
                delWrapper.in(LessonSafeTrainPlanRelationship::getLessonId, request.getLessonIds().split(","));
                return this.remove(delWrapper);
        }
        throw new CustomException("不支持的操作标识"+request.getFlag());
    }

}