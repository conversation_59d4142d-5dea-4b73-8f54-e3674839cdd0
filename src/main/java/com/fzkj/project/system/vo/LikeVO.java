package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "用户点赞信息表VO")
public class LikeVO implements Serializable {

    @JsonProperty("ID")
    private Long id;

    @JsonProperty("FunType")
    private Integer funType;

    @JsonProperty("UserCode")
    private String userCode;

    @JsonProperty("IDCard")
    private String idCard;

    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("Phone")
    private String phone;

    @JsonProperty("UserPhoto")
    private String userPhoto;

    @JsonProperty("TargetID")
    private Long targetId;

    @JsonProperty("Sort")
    private Integer sort;

    @JsonProperty("CreatorCode")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("Source")
    private Integer source;

    @JsonProperty("Flag")
    private String flag;

    @JsonProperty("TargetName")
    private String targetName;

    @JsonProperty("ImgUrl")
    private String imgUrl;

    @JsonProperty("ReleaseTime")
    private String releaseTime;

    @JsonProperty("CategoryName")
    private String categoryName;
}