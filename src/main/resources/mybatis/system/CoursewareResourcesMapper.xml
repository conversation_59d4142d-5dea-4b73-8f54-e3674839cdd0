<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.CoursewareResourcesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.CoursewareResources">
        <id column="id" property="id" />
        <result column="file_md5" property="fileMd5" />
        <result column="file_id" property="fileId" />
        <result column="file_name" property="fileName" />
        <result column="file_url" property="fileUrl" />
        <result column="file_url_hls" property="fileUrlHls" />
        <result column="file_time" property="fileTime" />
        <result column="file_size" property="fileSize" />
        <result column="file_type" property="fileType" />
        <result column="encode_state" property="encodeState" />
        <result column="sort" property="sort" />
        <result column="creator_code" property="creatorCode" />
        <result column="creator_name" property="creatorName" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_code" property="reviseCode" />
        <result column="revise_name" property="reviseName" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="source" property="source" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_md5, file_id, file_name, file_url, file_url_hls, file_time, file_size, file_type, encode_state, sort, creator_code, creator_name, creation_time, revise_code, revise_name, revise_time, remark, is_valid, source
    </sql>

</mapper>