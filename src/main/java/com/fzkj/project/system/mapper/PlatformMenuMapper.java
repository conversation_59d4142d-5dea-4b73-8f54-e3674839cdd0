package com.fzkj.project.system.mapper;

import com.fzkj.project.system.entity.PlatformMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 平台菜单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Mapper
public interface PlatformMenuMapper extends BaseMapper<PlatformMenu> {

    List<String> platformMenuList(@Param(value = "userCode") String userCode,
                                  @Param(value = "platform") Integer platform,
                                  @Param(value = "targetId") String targetId);

}