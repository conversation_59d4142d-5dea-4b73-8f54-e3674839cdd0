package com.fzkj.project.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fzkj.common.constant.ApiConstants;
import com.fzkj.common.utils.OssRsaUtils;
import com.fzkj.common.utils.PlatHttpUtil;
import com.fzkj.common.utils.RsaUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.domain.SsoUserInfo;
import com.fzkj.project.system.domain.SysUser;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.request.GetUserAuthInfoRequest;
import com.fzkj.project.system.request.LoginRequest;
import com.fzkj.project.system.request.SsoLoginRequest;
import com.fzkj.project.system.request.WxLoginRequest;
import com.fzkj.project.system.service.*;
import com.fzkj.project.system.vo.AuthUserInfoVO;
import com.fzkj.project.system.vo.GetUserAuthInfoResponse;
import com.fzkj.project.system.vo.LoginResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "登录管理")
@RequiredArgsConstructor
@ThirdPart
//@RequestMapping("/organduser")
public class LoginController {

    private final ILoginService loginService;

    private final UserInfoService userInfoService;

    private final WxService wxService;

    private final ISmsService smsService;

    private final PlatformMenuService platformMenuService;

    private final UserLimitService userLimitService;

    //登录
    @PostMapping("GetUserLogin")
    @ApiOperation(value = "登录")
    public AjaxResult GetUserLogin(@RequestBody CommonRequest request) {
        LoginRequest loginVO = (LoginRequest) request.getRequest(LoginRequest.class);
        // 生成令牌
        LoginResultVO result = loginService.login(loginVO.getAccount(), loginVO.getPwd(), request.getPlatform(), null, null);
        if (null != result) {
            if ("error".equals(result.getToken())){
                return AjaxResult.error("暂无权限，请联系管理员！");
            }
            if ("isOut".equals(result.getToken())){
                return AjaxResult.error("您已离职，暂无权限登录！");
            }
            return AjaxResult.success(result);
        } else {
            return AjaxResult.error("登录失败,账号或密码错误");
        }
    }

    //获取用户信息
    @PostMapping("GetUserAuthInfo")
    @ThirdPart(false)
    @ApiOperation(value = "获取用户信息")
    public AjaxResult GetUserAuthInfo(@RequestBody CommonRequest request) {
        GetUserAuthInfoRequest userAuthInfoRequest = (GetUserAuthInfoRequest) request.getRequest(GetUserAuthInfoRequest.class);
        AuthUserInfoVO vo = userInfoService.selectUserByUserCode(userAuthInfoRequest.getUserCode());
        GetUserAuthInfoResponse response = new GetUserAuthInfoResponse();
        response.setUserInfo(Arrays.asList(vo));
        response.setMenu(platformMenuService.loginPlatformMenu(userAuthInfoRequest.getOperPlatform(), userAuthInfoRequest.getTargetID()));
        response.setTargetInfo(userLimitService.loginAuthTargetVO(userAuthInfoRequest.getTargetID()));
        return AjaxResult.success(response);
    }

    //获取用户OPENID UNIONID
    @PostMapping("getSmallwxuser")
    public AjaxResult getSmallwxuser(@RequestBody CommonRequest request) {
        String code = request.getData();
        return AjaxResult.success(wxService.wxLogin(code));
    }

    //获取用户OPENID UNIONID
    @PostMapping("getSmallUserUnionid")
    public AjaxResult getSmallUserUnionid(@RequestBody CommonRequest request) {
        HashMap parameter = (HashMap) request.getRequest(HashMap.class);
        String code = parameter.get("code").toString();
        String encryptedData = parameter.get("userinfo").toString();
        String iv = parameter.get("iv").toString();
        JSONObject userInfo = wxService.getUserInfo(encryptedData, code, iv);
        return AjaxResult.success(userInfo);
    }

    //使用 UNIONID单点登录
    @PostMapping("GetUnionIDLogin_New")
    public AjaxResult GetUnionIDLogin_New(@RequestBody CommonRequest request) {
        WxLoginRequest parameter = (WxLoginRequest) request.getRequest(WxLoginRequest.class);
        String flag = parameter.getFlag();
        LoginResultVO result = new LoginResultVO();
        if ("unionid".equals(flag)) {
            String unionId = parameter.getUnionID();
            String openId = parameter.getOpenID();
            result = loginService.loginByUnionId(unionId, openId, request.getPlatform());
            if (null != result) {
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("微信未绑定，登录失败", result);
            }
        }
        return AjaxResult.error("微信未绑定，登录失败", result);
    }

    //使用用户密码或手机验证码
    @PostMapping("GetUserLogin_New")
    public AjaxResult GetUserLogin_New(@RequestBody CommonRequest request) {
        WxLoginRequest parameter = (WxLoginRequest) request.getRequest(WxLoginRequest.class);
        String flag = parameter.getFlag();
        LoginResultVO result = new LoginResultVO();
        if ("login".equals(flag)) {
            String account = parameter.getAccount();
            String pwd = parameter.getPwd();
            result = loginService.login(account, pwd, request.getPlatform(), parameter.getUnionID(), parameter.getOpenID());
            if (null != result) {
                if ("error".equals(result.getToken())){
                    return AjaxResult.error("暂无权限，请联系管理员！");
                }
                if ("isOut".equals(result.getToken())){
                    return AjaxResult.error("您已离职，暂无权限登录！");
                }
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("登录失败,账号或密码错误", result);
            }
        } else if ("phone".equals(flag)) {
            String account = parameter.getAccount();
            String authCode = parameter.getAuthCode();
            boolean verifyResult = smsService.verify(account, "1", authCode);
            if (!verifyResult) {
                return AjaxResult.error("手机号未注册,或验证码有误,登录失败", result);
            }
            result = loginService.loginByAuthCode(account, request.getPlatform());
            if (null != result) {
                smsService.removeVerificationCode(account, "1");
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("手机号未注册,或验证码有误,登录失败", result);
            }
        }
        return AjaxResult.error(null);
    }

    @PostMapping("CodeLogin")
    public AjaxResult codeLogin(@RequestBody CommonRequest request) {
        SsoUserInfo userSso = loginService.getUserSso(request.getData());
        String data = userSso.getData();
        String s = null;
        try {
            s = OssRsaUtils.decryptByPublicKey(data);
        } catch (Exception e) {
            return AjaxResult.error("解密失败");
        }
        String username = JSONObject.parseObject(s).get("account").toString();
        LoginResultVO result = new LoginResultVO();
        result = loginService.loginBySSO(username, request.getPlatform());
        if (null != result) {
            return AjaxResult.success(result);
        } else {
            return AjaxResult.error("登录失败", result);
        }
    }

    /**
     * 绑定sso
     * @return 结果
     */
    @PostMapping("sso/register")
    public AjaxResult ssoRegister() {
        UserInfo sysUser =SecurityUtils.getLoginUser().getUser();
        AjaxResult ajax = AjaxResult.success();
        String s ="";
        Map<String, Object> map = new HashMap<>();
        map.put("systemId", ApiConstants.SSO_SYSTEM_KEY);
        map.put("account", sysUser.getUserName());
        map.put("phoneNo", sysUser.getPhone());
        try {
            s = OssRsaUtils.encryptByPublicKey(JSON.toJSONString(map));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error("加密失败");
        }
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("user",s);
        paramsMap.put("systemId",ApiConstants.SSO_SYSTEM_KEY);
        String result = PlatHttpUtil.sendPostParams(ApiConstants.SSO_GET_USER_KEY_URL,
                JSON.toJSONString(paramsMap), null, "UTF-8", null);
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(result);
        String code = jsonObject.get("code").toString();
        if ("200".equals(code)){
            String userKey = com.alibaba.fastjson.JSONObject.parseObject(jsonObject.get("data").toString()).get("userKey").toString();
            ajax.put("url", ApiConstants.SSO_BIND_URL+ApiConstants.SSO_SYSTEM_KEY+"&userKey="+userKey);
            return ajax;
        }else {
            return AjaxResult.error(jsonObject.get("msg").toString());
        }
    }

    /**
     * 绑定sso
     * @return 结果
     */
    @PostMapping("sso/login")
    public AjaxResult ssoLogin() {
        UserInfo sysUser = SecurityUtils.getLoginUser().getUser();
        AjaxResult ajax = AjaxResult.success();
        String s ="";
        Map<String, Object> map = new HashMap<>();
        map.put("systemId", ApiConstants.SSO_SYSTEM_KEY);
        map.put("account", sysUser.getUserName());
        map.put("phoneNo", sysUser.getPhone());
        try {
            s = OssRsaUtils.encryptByPublicKey(JSON.toJSONString(map));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error("加密失败");
        }
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("user",s);
        paramsMap.put("systemId",ApiConstants.SSO_SYSTEM_KEY);
        String result = PlatHttpUtil.sendPostParams(ApiConstants.SSO_GET_USER_KEY_URL,
                JSON.toJSONString(paramsMap), null, "UTF-8", null);
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(result);
        String resilt = jsonObject.get("code").toString();
        if ("200".equals(resilt)){
            String userKey = com.alibaba.fastjson.JSONObject.parseObject(jsonObject.get("data").toString()).get("userKey").toString();
            ajax.put("url", ApiConstants.SSO_BIND_URL+ApiConstants.SSO_SYSTEM_KEY+"&userKey="+userKey);
            return ajax;
        }else {
            return AjaxResult.error(jsonObject.get("msg").toString());
        }
    }
    //获取手机验证码
    @PostMapping("GetAuthCode")
    public AjaxResult GetAuthCode(@RequestBody CommonRequest request) {
        WxLoginRequest parameter = (WxLoginRequest) request.getRequest(WxLoginRequest.class);
        String phone = parameter.getPhone();
        String smsType = parameter.getSMS_TYPE();
        String verificationCode = smsService.sendVerificationCode(phone, smsType);
        if (StringUtils.isEmpty(verificationCode)) {
            return AjaxResult.error("短信发送失败", verificationCode);
        }
        return AjaxResult.success("操作成功", verificationCode);
    }

    //获取手机验证码
    @PostMapping("UseAuthCode")
    public AjaxResult UseAuthCode(@RequestBody CommonRequest request) {
        WxLoginRequest parameter = (WxLoginRequest) request.getRequest(WxLoginRequest.class);
        String phone = parameter.getPhone();
        String smsType = parameter.getSMS_TYPE();
        String authCode = parameter.getAuthCode();
        boolean verifyResult = smsService.verify(phone, smsType, authCode);
        if (verifyResult) {
            return AjaxResult.success("验证成功", null);
        }
        return AjaxResult.error("验证码错误", null);
    }

    //获取用户登录状态
    @PostMapping("GetRemoteLoginStatus")
    public AjaxResult GetRemoteLoginStatus(@RequestBody CommonRequest request) {
        String parameter = (String) request.getRequest(String.class);
        String[] info = parameter.split("\\|");
        boolean verifyResult = loginService.verifySessionId(info[0], info[1]);
//        int platform = request.getPlatform();
//        if (platform==3){
//            return AjaxResult.success("登录状态正常", null);
//        }
        if (verifyResult) {
            return AjaxResult.success("登录状态正常", null);
        }
        return AjaxResult.error(401, "为了你的网络账户安全，请重新登录账号", null);
    }

    //用户登出
    @PostMapping("UserExit")
    public AjaxResult UserExit(@RequestBody CommonRequest request) {
        loginService.userExit(request);
        return AjaxResult.success("操作成功", null);
    }

    /**
     * 获取停机维护信息
     *
     * @return
     */
    @GetMapping("Get_StopInfo")
    public AjaxResult Get_StopInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put("IsStop", 0);
        //result.put("StopDescribe","今日停机维护");
        return AjaxResult.success("操作成功", result);
    }

    /**
     * 获取答题活动
     *
     * @return
     */
    @PostMapping("GetActivityAPP")
    public AjaxResult GetActivityAPP() {
        //0-无活动 1-答题红包
        return AjaxResult.success("操作成功", 0);
    }
}
