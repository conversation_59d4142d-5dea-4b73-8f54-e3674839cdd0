package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 提醒管理表
 *
 * <AUTHOR>
 * @since 2024-03-04
 */
@Data
@ApiModel(value = "提醒管理表Request")
public class ReminderRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "培训类型")
    private String title;

    @ApiModelProperty(value = "内容")
    private String val;

    @ApiModelProperty(value = "培训类型")
    private Integer trainType;

    @ApiModelProperty(value = "提醒类型")
    private Integer remindType;


}