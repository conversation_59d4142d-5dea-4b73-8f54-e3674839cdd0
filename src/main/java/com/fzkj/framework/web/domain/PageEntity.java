package com.fzkj.framework.web.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * 通用基础字段，需要此通用字段的实体可继承此类
 */
@Data
public class PageEntity implements Serializable {

    private static final long serialVersionUID = 6314329260886250542L;
    /**
     * 当前页面数据量
     */
    @TableField(exist = false)
    @JsonIgnore
    private int PageSize = 10;
    /**
     * 当前页码
     */
    @TableField(exist = false)
    @JsonIgnore
    private int PageIndex = 1;
    /**
     * 排序字段
     */
    @TableField(exist = false)
    @JsonIgnore
    private String sidx;
    /**
     * 排序规则，asc升序，desc降序
     */
    @TableField(exist = false)
    @JsonIgnore
    private String order;
}
