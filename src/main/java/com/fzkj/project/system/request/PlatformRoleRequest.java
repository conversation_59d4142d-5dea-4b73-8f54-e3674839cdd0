package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 平台角色
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "平台角色Request")
public class PlatformRoleRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("CompanyID")
    private Long companyId;

    @JsonProperty("RoleName")
    private String roleName;

    @JsonProperty("OperPlatform")
    private Integer operPlatform;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("Sort")
    private Integer sort;

    @JsonProperty("CreatorCode")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @JsonProperty("ReviseCode")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("IsShow")
    private Integer isShow;

    @ApiModelProperty(value = "Type: 0-通用角色 1-定制角色")
    @JsonProperty("Type")
    private Integer type;

    @JsonProperty("Flag")
    private String flag;

}