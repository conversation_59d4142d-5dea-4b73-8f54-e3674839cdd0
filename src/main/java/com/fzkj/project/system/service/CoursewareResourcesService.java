package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.CoursewareResources;
import com.fzkj.project.system.vo.CoursewareResourcesVO;
import com.fzkj.project.system.request.CoursewareResourcesRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 视频文件资源信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CoursewareResourcesService extends IService<CoursewareResources> {

    List<CoursewareResources> listCoursewareResources(CoursewareResourcesRequest request);

    PageUtils listByPage(CoursewareResourcesRequest request, PageEntity pageEntity);

    CoursewareResourcesVO findCoursewareResources(Long id);

    CoursewareResources saveCoursewareResources(CoursewareResourcesVO vo);

    boolean updateCoursewareResources(CoursewareResourcesVO vo);

    boolean removeCoursewareResources(Long id);
}
