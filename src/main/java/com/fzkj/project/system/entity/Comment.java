package com.fzkj.project.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论表
 *
 */
@Data
@TableName("t_d_comment")
@ApiModel(value = "评论表实体")
public class Comment implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @JsonProperty("FunType")
    @ApiModelProperty(value = "功能模块（10：课件，11：内容）")
    private String funType;

    @JsonProperty("UserCode")
    @ApiModelProperty(value = "用户编号")
    private String userCode;

    @JsonProperty("IdCard")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "用户姓名")
    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("Phone")
    @ApiModelProperty(value = "手机号")
    private String phone;

    @JsonProperty("UserPhoto")
    @ApiModelProperty(value = "用户头像")
    private String userPhoto;

    @JsonProperty("Content")
    @ApiModelProperty(value = "评论内容")
    private String content;

    @JsonProperty("Grade")
    @ApiModelProperty(value = "评分")
    private String grade;

    @JsonProperty("TargetID")
    @ApiModelProperty(value = "目标id(如课件ID、内容ID)")
    private Long targetId;

    @JsonProperty("Status")
    @ApiModelProperty(value = "状态（1：审核通过，0：未审核，默认为0）")
    private Integer status;

    @JsonProperty("Sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonProperty("CreatorCode")
    @ApiModelProperty(value = "创建人编码")
    private String creatorCode;

    @JsonProperty("CreatorTime")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @JsonProperty("ReviseCode")
    @ApiModelProperty(value = "修改人编码")
    private String reviseCode;

    @JsonProperty("ReviseTime")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @JsonProperty("Remark")
    @ApiModelProperty(value = "备注信息")
    private String remark;

    @JsonProperty("IsValid")
    @ApiModelProperty(value = "是否有效 1是 0否 默认有效")
    private Integer isValid;

    @JsonProperty("IsReply")
    @ApiModelProperty(value = "是否回复（1：已回复，0：未回复）")
    private Integer isReply;

    @JsonProperty("ReplyContent")
    @ApiModelProperty(value = "回复内容")
    private String replyContent;

    @JsonProperty("Source")
    @ApiModelProperty(value = "数据来源")
    private String source;

    @JsonProperty("ReplyUserCode")
    private String replyUserCode;

    @JsonProperty("ReplyTime")
    private LocalDateTime replyTime;

}