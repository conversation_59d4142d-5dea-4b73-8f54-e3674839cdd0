package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 安全培训课程
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "安全培训课程查看已分发企业Request")
public class LessonSafeTrainQueryCompanyRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程ID")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "地区代码")
    @JsonProperty("AreaCode")
    private Long areaCode;

    @ApiModelProperty(value = "是否托管")
    @JsonProperty("IsTrusteeship")
    private Integer isTrusteeship;

}

