package com.fzkj.common.utils;


import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;


/**
 * @Author: haowen
 * @Version: 1.0
 * @Date: 2022/9/1 17:53
 */
public class OssRsaUtils {

//    public static String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL6YQPNgtso0H2ts668aDB0FSk9oevJEZqvEFCmmQ7OWP6i56nwwyrVeDs0GSMs5Xa+efgTkBW9fRdU6YGDxbl41DW5LUcIbZ+J7wGlcaCXWlaP8dgisDZ39TvHG8w7xQyEAZ8x1oas+sZ1lOv3mnMv+r5Ulk5ypICC7H9gR27XXAgMBAAECgYEAgKuhDa15OcE+hPOfmTLogp8Tr9BFUFFFTyisxAFvK0p/55S77OOztgWt0FQxyKEN0oYZZYva73lOV8l1T30umFiKV55s7nOFeH/C+x9J8CuyBxceFyR/brU75dQkB95rO2uhg21ZMyEIPv+QNBuQy8d1hgnUCSmUw2VRAHTuSAECQQDmlDnpjQ/TP4YvIGXj3/a0hQfhp/5oUrN868RZKBD3HURF4DUJnzvIb7NuHr8zHMUifu6abRPvJJ9P6u5C7m8BAkEA05uG3TGzjVwce647DO13kMZeoGkYKMxfGVtQVJP0Z4KjLNWX8Atpo8k9K6EqPi0O1+yHKdzZJNQzYxSptdh81wJAWQxZqUbfG7hmvACJGQ4/msvdlVppuCRoSRBMjhoZIzZcTZgfI44pamkpJJgfQ0ATKhVXVLBXiH3eQOD8D0FCAQJAHwdtTT4egBoDqzcvPYQMxlPwSEFg44qkX0l+jAhuDEehuc9QSkjCItw9dgpZ8WgWBx+N6luSE85yApNjF2E5XQJAE6f2z8NyiiRNnq3IvdE1k2+kIU9I1ZEzWtF/I8KFlr7EJ+mZjJ3eam2rMpUw2fJnF4U7ILDiIPJ8QHwjTnkl1A==";
    public static final String jgPrivateKey =
        "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAJdWyTTcY4qzc7RVKjLRUjmSHho3bhKdd5hwbODMi1exRjivyNzkj3+Xck4RoEuiXASJrcTmlmUhj5APEyNYtev4rkERRqzWklr4DH4NC1GgqDfup9734R3EP0/TkpzTHKQpSkfgPRBZupCnTm6GIyryNHMypXeuwh+V0qFjkEexAgMBAAECgYEAgwSWQpY98xkypT9Jvh7rgX3XY7jzKVh77lP+XouWYr/NXEwKp/mTpdFWGvI1hOU1ppQPBTTfpQPuBriWR/aH3zcdD4ekK6Uq//ra9AJH8GvLyGRhRtVkjbJ63kAPDsuXNpE7/BZY3zvt1I3O2HkiHlR6NaTU9o7+0ltYjuJKJaUCQQD+b1gAKY/WK6kRx6ZiN/zVk3a+Lmt75tkj4d3kaZ34/QQfjZQ5Ct29+9jRCasjKJFm1SIzLpUuD7h47DeDK+5HAkEAmEUZGVO2V4oWNRuzxLTz9Pl5VZLvJua2Y3V36bsS3VO5nDx8akZM/cA5E8HJSLK1DmLd6LB5iy5g8tyYJfQ+RwJBAMoo3dsDKu4YsrUV1PWlVpa8HFTvvSctPMpt5EhgQsxa/LO1YlgQci9Js/Qjon7E9QKnxgy0roNyqjgqN9FZTnECQA2Rmrr3CriiRXhWD57nwRWDZZyKi2UcCgSy4wyg7v1qCJAzk6KkwRebmXp5OkaQJjIx/TRPBYWxjFIX9jbrnOECQQDLcrwCCsAMJT6LZhr96+vzCybm4hwI3/Kz0GJ90mExz3gav0up53QK2ez3Tj7p8tFEVmzuwKO5/mV3T++qgK7w";

    private static final String privateKey="MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCi+oEzfInb0qHp/FnPzEenzEIh/L6NfMTbERmCO4O5PKkI5ZV+WE0HtyrLEmkZWCKaLfic+hR8P7FVYwGg/0si30mkAHXzAiMK2NwlU0I0pBnTS7a0f6h7lGp1QHczfsgdQE2GtmEvUy1WGqLm6Jyb/9bQIYvhLsnAQXAeUa4Ay4kQ7O2S2NWbj1VoiJhNW27qMf7E9IRfLlMgtobaDoRfQw76dEgS8nqNJymUAIEnfoQ8v//sz2WzI7yBVG5GLKQvV4XKBiUef4IEmf2OJOTFMrDbXlRf/opD2hYleswrp6nXq/gaCJzyni0WyDWfQ+7ZPybDha5zbVl0Of6C4jyPAgMBAAECggEAOXSWl9APya835YVRJDCgEkewCMtRYg/aVmBw3bidd3ArJzfIvAHlJhMswup6orSvfis2uiBvmF3b5n5A1MEre9+71lVTP5hBQlGxSndPn786W4MVpgxbGTX5UdyoKT9IzQceTZ9+I3DvSquZQk4+58AJutYYugoElgJ7xDSHYz/MJQnEYuLL6AxFpthilLGnIiBwG9sU5WTKHjxC9vm2iDSTQFV9u0grjFDhYwAPPftdV9PUnZoiIOnjFX54VtIKIBoWkVACIDpwD+OAaxbhPetLc3dnS7yPGa6z8KH27acjnxiCoGalCtCP3St6U1vPv1ul/DaI4rtsnhCLTHv6AQKBgQDtsG3Dsd6K6OUrY6dOwJVa+YBNNm9yB3N90h3Xpr/00T4Dr9bh4gudCEBlozyNi2OyiZVfpnozoI+TDLfd8SfgNFO8HAzWM5CBLdGLoiG5oWIx04hWvNR/Z2KMAVusX4wTqZ2zbekp1F1zA1fwp05LYvl+KhRQS4kvk/uT69hycwKBgQCviKzMfI373PH9fGRfvjB+zSc1f4D8WqLntg9zxiLMhQhJHBG4Q/u1lIrHntf1wkfmwu6Q1cU+Ls+vfXVoYmCWv8E7XtTtOxrKHuYMWmFZAO13mS2gtH7XjnxaSAuJ/tUwtCBVD6eLY90mxuclEfPUzQQA+kd40r8eP0zZmoDadQKBgCWd+oEyMUpCuaI4T05DZF/VhNyzdMfTLhAHpPlwRoj0ZKAowBVXtQbRO3/aacbs/IZRB/bvXf+1tYNMS/0wIWN2ZJosxktQNdp76eHzcuryA5xnIfi4Qdk3/3yw6lVJed3SxkaTaodYOMHvEYfgsp9ztpClEDn0+m0xOmuas9LJAoGAHCfAD8yquYtnBVG+h9zpymz9rQcWmWOwXZpGQDu3uTZdbdyiuvuslCOPXxWLblALWGhL+89OI/s+NNfazTN6arO3Ot6rKKIiWek63SBiHPSZImXfe7d34Ptp1tFXiHrnlzhNN1ebeDA8eMvOp5OJmMiJq955OFgVb7SXjw/JO0ECgYAbXSCQLF+3ZiLPILK4+xwH+Y1B90Hx3t1w7aU3IWIE0TVsiKjxqPk8cpPZhByYMI2ATbz/F5NfMsyzGWaGvZvRyKa3hHjMOYKZurh10/b757rB44RaAAYwy4md/ZOaPe/LhRyWK0i5yy7X6Z43M/m2sUc7f94x0nWz5jusdbw2vg==";

    public static final String publicKeyString=
            "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAovqBM3yJ29Kh6fxZz8xHp8xCIfy+jXzE2xEZgjuDuTypCOWVflhNB7cqyxJpGVgimi34nPoUfD+xVWMBoP9LIt9JpAB18wIjCtjcJVNCNKQZ00u2tH+oe5RqdUB3M37IHUBNhrZhL1MtVhqi5uicm//W0CGL4S7JwEFwHlGuAMuJEOztktjVm49VaIiYTVtu6jH+xPSEXy5TILaG2g6EX0MO+nRIEvJ6jScplACBJ36EPL//7M9lsyO8gVRuRiykL1eFygYlHn+CBJn9jiTkxTKw215UX/6KQ9oWJXrMK6ep16v4Ggic8p4tFsg1n0Pu2T8mw4Wuc21ZdDn+guI8jwIDAQAB";
    /**
     * 私钥解密
     *
     * @param text             待解密的文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String text) throws Exception {
        return decryptByPrivateKey(privateKey, text);
    }

    /**
     * 公钥解密
     *
     * @param publicKeyString 公钥
     * @param text            待解密的信息
     * @return 解密后的文本
     */
    public static String decryptByPublicKey(String publicKeyString, String text) throws Exception {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }
    public static String decryptByPublicKey(String text) throws Exception {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }
    /**
     * 私钥加密
     *
     * @param privateKeyString 私钥
     * @param text             待加密的信息
     * @return 加密后的文本
     */
    public static String encryptByPrivateKey(String privateKeyString, String text) throws Exception {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }
    public static String encryptByPrivateKey(String text) throws Exception {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }
    /**
     * 私钥解密
     *
     * @param privateKeyString 私钥
     * @param text             待解密的文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String privateKeyString, String text) throws Exception {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec5 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec5);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 公钥加密
     *
     * @param publicKeyString 公钥
     * @param text            待加密的文本
     * @return 加密后的文本
     */
    public static String encryptByPublicKey(String publicKeyString, String text) throws Exception {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }
    public static String encryptByPublicKey(String text) throws Exception {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }
    /**
     * 构建RSA密钥对
     *
     * @return 生成后的公私钥信息
     */
    public static RsaKeyPair generateKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(1024);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
        String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
        String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
        return new RsaKeyPair(publicKeyString, privateKeyString);
    }

    /**
     * RSA密钥对对象
     */
    public static class RsaKeyPair {
        private final String publicKey;
        private final String privateKey;

        public RsaKeyPair(String publicKey, String privateKey) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey() {
            return publicKey;
        }

        public String getPrivateKey() {
            return privateKey;
        }
    }
}
