package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetUserMsgCenterRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "用户编码")
    @JsonProperty("UserCode")
    private Long userId;

    @ApiModelProperty(value = "功能类型")
    @JsonProperty("FunType")
    private Integer funType;

    @ApiModelProperty(value = "操作标识")
    @JsonProperty("Flag")
    private String flag;


}
