package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.CompanyLessonSafeTrainSetLog;
import com.fzkj.project.system.vo.CompanyLessonSafeTrainSetLogVO;
import com.fzkj.project.system.request.CompanyLessonSafeTrainSetLogRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 企业安全培训课程设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface CompanyLessonSafeTrainSetLogService extends IService<CompanyLessonSafeTrainSetLog> {

    List<CompanyLessonSafeTrainSetLog> listCompanyLessonSafeTrainSetLog(CompanyLessonSafeTrainSetLogRequest request);

    PageUtils listByPage(CompanyLessonSafeTrainSetLogRequest request, PageEntity pageEntity);

    CompanyLessonSafeTrainSetLogVO findCompanyLessonSafeTrainSetLog(Long id);

    Long saveCompanyLessonSafeTrainSetLog(CompanyLessonSafeTrainSetLogVO vo);

    boolean updateCompanyLessonSafeTrainSetLog(CompanyLessonSafeTrainSetLogVO vo);

    boolean removeCompanyLessonSafeTrainSetLog(Long id);
}
