package com.fzkj.project.monitor.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import com.fzkj.framework.aspectj.lang.annotation.Excel.ColumnType;
import com.fzkj.framework.web.domain.BaseEntity;
import lombok.Data;

/**
 * 操作日志记录表 oper_log
 *
 * <AUTHOR>
 */
@Data
@TableName("t_d_user_oper_log")
public class SysOperLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志主键 */
    @Excel(name = "日志编号", cellType = ColumnType.NUMERIC)
    private Long operId;

    /** 操作模块 */
    @Excel(name = "系统模块")
    private String title;

    /** 业务类型（0其它 1新增 2修改 3删除） */
    @Excel(name = "操作类型", readConverterExp = "0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据")
    private Integer businessType;

    /** 业务类型数组 */
    private Integer[] businessTypes;

    /** 请求方法 */
    @Excel(name = "请求方式")
    private String method;

    /** 请求方式 */
    @Excel(name = "请求方式")
    private String requestMethod;

    /** 操作人员 */
    @Excel(name = "操作人员")
    private String operName;

    /** 被操作人员 */
    @Excel(name = "被操作人员")
    private String operatedUserCode;

    /** 操作类别（0其它 1后台用户 2手机端用户） */
    //@Excel(name = "操作类别", readConverterExp = "0=其它,1=后台用户,2=手机端用户")
    private Integer operatorType;


    /** 部门名称 */
    //@Excel(name = "部门名称")
    private String deptName;

    /** 请求url */
    @Excel(name = "主机")
    private String operUrl;

    /** 操作地址 */
    @Excel(name = "操作IP")
    private String operIp;

    private String operLocation;

    /** 请求参数 */
    //@Excel(name = "请求参数")
    private String operParam;

    /** 返回参数 */
    //@Excel(name = "返回参数")
    private String jsonResult;

    /** 操作状态（0正常 1异常） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=异常")
    private Integer status;

    /** 错误消息 */
    //@Excel(name = "错误消息")
    private String errorMsg;

    /** 操作时间 */
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 平台:3-小程序,6-管理端,7-企业端*/
    private Integer platform;

}
