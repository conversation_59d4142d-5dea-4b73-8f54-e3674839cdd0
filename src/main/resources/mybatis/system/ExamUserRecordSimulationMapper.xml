<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.ExamUserRecordSimulationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.ExamUserRecordSimulation">
        <id column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="id_card" property="idCard" />
        <result column="exam_id" property="examId" />
        <result column="pass_mark" property="passMark" />
        <result column="score" property="score" />
        <result column="total_score" property="totalScore" />
        <result column="resit_number" property="resitNumber" />
        <result column="use_time_count" property="useTimeCount" />
        <result column="accuracy" property="accuracy" />
        <result column="user_exam_count" property="userExamCount" />
        <result column="is_static" property="isStatic" />
        <result column="error_count" property="errorCount" />
        <result column="lesson_id" property="lessonId" />
        <result column="photo_url" property="photoUrl" />
        <result column="sign_url" property="signUrl" />
        <result column="sort" property="sort" />
        <result column="creation_time" property="creationTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
        <result column="source" property="source" />
        <result column="user_phone" property="userPhone" />
        <result column="depart_id" property="departId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, organization_id, user_id, user_name, id_card, exam_id, pass_mark, score, total_score, resit_number, use_time_count, accuracy, user_exam_count, is_static, error_count, lesson_id, photo_url, sign_url, sort, creation_time, remark, is_valid, source, user_phone, depart_id
    </sql>

</mapper>