package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.PlatformRole;
import com.fzkj.project.system.vo.PlatformRoleVO;
import com.fzkj.project.system.request.PlatformRoleRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 平台角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface PlatformRoleService extends IService<PlatformRole> {

    PageUtils listPlatformRole(PlatformRoleRequest request, PageEntity pageEntity);

    List<PlatformRoleVO> listPlatformRoleSelect(PlatformRoleRequest request);

    PageUtils listByPage(PlatformRoleRequest request, PageEntity pageEntity);

    PlatformRoleVO findPlatformRole(Long id);

    Long savePlatformRole(PlatformRoleVO vo);

    boolean updatePlatformRole(PlatformRoleVO vo);

    boolean removePlatformRole(Long id);
}
