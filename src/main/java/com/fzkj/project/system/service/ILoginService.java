package com.fzkj.project.system.service;

import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.domain.SsoUserInfo;
import com.fzkj.project.system.vo.LoginResultVO;
import com.fzkj.project.system.vo.UserInfoVO;

public interface ILoginService {
    LoginResultVO login(String username, String password, int platform, String unionId, String openId);

    LoginResultVO loginByUnionId(String unionId, String openId, int platform);

    LoginResultVO loginByAuthCode(String account, int platform);

    LoginResultVO loginBySSO(String account, int platform);

    SsoUserInfo getUserSso(String account);

    UserInfoVO getUserInfo(String userCode);

    boolean verifySessionId(String userCode, String sessionId);

    void userExit(CommonRequest request);
}
