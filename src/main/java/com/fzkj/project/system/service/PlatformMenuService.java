package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.PlatformMenu;
import com.fzkj.project.system.vo.AuthMenuVO;
import com.fzkj.project.system.vo.PlatformMenuVO;
import com.fzkj.project.system.request.PlatformMenuRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 平台菜单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface PlatformMenuService extends IService<PlatformMenu> {

    List<PlatformMenu> listPlatformMenuAll(Integer request);

    List<PlatformMenu> listPlatformMenu(Integer request);

    List<?> loginPlatformMenu(String request,String targetId);

    PageUtils listByPage(PlatformMenuRequest request, PageEntity pageEntity);

    PlatformMenuVO findPlatformMenu(Long id);

    Long savePlatformMenu(PlatformMenuVO vo);

    boolean updatePlatformMenu(PlatformMenuVO vo);

    Boolean removePlatformMenu(Long id);

    List<PlatformMenu> buildMenuTree(List<PlatformMenu> menus);
}
