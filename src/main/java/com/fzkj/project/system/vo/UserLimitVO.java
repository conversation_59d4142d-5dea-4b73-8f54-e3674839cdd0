package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户管理机构表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="用户管理机构表VO")
public class UserLimitVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("TargetID")
    private Long targetId;

    @JsonProperty("TargetType")
    private String targetType;

    @JsonProperty("PcRoleID")
    private String pcRoleId;

    @JsonProperty("MobileRoleID")
    private String mobileRoleId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("OperPlatform")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorTime")
    private String creatorTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @JsonProperty("Flag")
    private String flag;

    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("FName")
    private String fName;

    @JsonProperty("RoleName")
    private String roleName;

    @JsonProperty("RoleNameMini")
    private String roleNameMini;

    @JsonProperty("IdCard")
    private String idCard;

    @JsonProperty("Phone")
    private String phone;

    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("isMy")
    private Integer isMy;
}