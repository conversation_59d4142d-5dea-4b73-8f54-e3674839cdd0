package com.fzkj.project.system.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value="用户信息VO")
public class UserCompanyVO implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("DepartID")
    private Long departId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("WorkID")
    private Long workId;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("UserName")
    private String userName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("UserPhoto")
    private String userPhoto;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sex")
    private Integer sex;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("BirthDay")
    private LocalDate birthDay;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("HomeAddress")
    private String homeAddress;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsEnable")
    private Integer isEnable;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsOut")
    private Integer isOut;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsActivate")
    private Integer isActivate;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("PlateNumber")
    private String plateNumber;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("SignUrl")
    private String signUrl;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("TrainTypeIdStr")
    private String trainTypeIdStr;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("AccountCode")
    private String accountCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("OperPlatform")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CreatorTime")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("Identity")
    private Integer identity;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IsValid")
    private Integer isValid;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IndustryType")
    private String industryType;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("JoinTime")
    private String joinTime;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("NoAccidentCertificate")
    private String noAccidentCertificate;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("NoAccidentFileName")
    private String noAccidentFileName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("XattrsVal")
    private String xattrsVal;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("EduLevel")
    private String eduLevel;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("IDCardJson")
    private String idCardJson;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("SosName")
    private String sosName;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("SosPhone")
    private String sosPhone;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("LearnType")
    private String learnType;

    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空")
    @JsonProperty("CompanyLabel")
    private String companyLabel;

}