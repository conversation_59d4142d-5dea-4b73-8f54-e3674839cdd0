package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SafeTrainCompanyStatisticsVO implements Serializable{
    
    @ApiModelProperty("课程ID")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty("课程名称")
    @JsonProperty("LessonName")
    @Excel(name = "课程名称",order = 2)
    private String lessonName;

    @ApiModelProperty("课程分类ID")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty("课程分类名称")
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty("企业ID")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty("企业名称")
    @JsonProperty("CompanyName")
    @Excel(name = "企业名称",order = 1)
    private String companyName;

    @ApiModelProperty("区域名称")
    @JsonProperty("AreaName")
    private String areaName;

    @ApiModelProperty("总分发人数")
    @JsonProperty("TotalUserNum")
    @Excel(name = "总人数",order = 4)
    private int totalUserNum;

    @ApiModelProperty("总分发人数1")
    @JsonProperty("TotalUserNum1")
    private long totalUserNum1;

    @ApiModelProperty("参学人数数量")
    @JsonProperty("CXUserNum")
    @Excel(name = "参学人数",order = 5)
    private long cxUserNum;

    @ApiModelProperty("完成人数")
    @JsonProperty("WCUserNum")
    @Excel(name = "完成人数",order = 6)
    private long wcUserNum;

    @ApiModelProperty(value = "参学率")
    @JsonProperty("CXL")
    @Excel(name = "参学率",order = 7)
    private String cxl = "0%";

    @ApiModelProperty(value = "完成率")
    @JsonProperty("WCL")
    @Excel(name = "完成率",order = 8)
    private String wcl = "0%";

    @ApiModelProperty(value = "完成率排序")
    @JsonProperty("WCLSort")
    private double wclSort = 0.0;

    @ApiModelProperty(value = "档案名称")
    @JsonProperty("FileName")
    private String fileName;

    @ApiModelProperty(value = "台账名称")
    @JsonProperty("LedgerFileName")
    private String ledgerFileName;

    @ApiModelProperty(value = "人像照片名称")
    @JsonProperty("FaceLederFileName")
    private String faceLederFileName;

    @ApiModelProperty(value = "档案地址")
    @JsonProperty("FilesUrl")
    private String filesUrl;

    @ApiModelProperty(value = "台账地址")
    @JsonProperty("LedgerUrl")
    private String ledgerUrl;

    @ApiModelProperty(value = "人像照片地址")
    @JsonProperty("FaceLederUrl")
    private String faceLederUrl;

    @ApiModelProperty(value = "是否标记")
    @JsonProperty("MarkeUserName")
    private String markeUserName;

    @ApiModelProperty(value = "是否手动")
    @JsonProperty("HandMode")
    private int handMode;

    @ApiModelProperty("用户结束学习时间")
    @JsonProperty("ETime")
    private String eTime;

}
