package com.fzkj.project.system.controller;

import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.request.UserRelationWorkRequest;
import com.fzkj.project.system.service.UserOperLogService;
import com.fzkj.project.system.service.UserRelationWorkService;
import com.fzkj.project.system.vo.UserOperLogVO;
import com.fzkj.project.system.vo.UserRelationWorkVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
@RestController
@RequiredArgsConstructor
//@RequestMapping(value = "userPort")
public class UserOperLogController extends BaseEntity {

    private final UserOperLogService userOperLogService;

//    @PostMapping("list")
//    @ApiOperation(value = "用户关联岗位列表查询")
//    public AjaxResult listUserRelationWork(@RequestBody CommonRequest commonRequest) {
//        UserRelationWorkRequest request = (UserRelationWorkRequest) commonRequest.getRequest(UserRelationWorkRequest.class);
//        return AjaxResult.success(UserRelationWorkServiceImpl.listUserRelationWork(request));
//    }

    @PostMapping("GetOperUserLog")
    @ApiOperation(value = "用户操作日志分页查询")
    public AjaxResult getOperUserLog(@RequestBody CommonRequest commonRequest) {
        UserOperLogVO request = (UserOperLogVO) commonRequest.getRequest(UserOperLogVO.class);
        return AjaxResult.success(userOperLogService.getOperUserLog(request, commonRequest.getPageEntity()));
    }
//
//    @GetMapping("{id}")
//    @ApiOperation(value = "用户关联岗位查询详情")
//    public AjaxResult findUserRelationWork(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(UserRelationWorkServiceImpl.findUserRelationWork(request.getId()));
//    }
//
//    @PostMapping
//    @ApiOperation(value = "用户关联岗位新增数据")
//    public AjaxResult saveUserRelationWork(@RequestBody CommonRequest commonRequest) {
//        UserRelationWorkVO vo = (UserRelationWorkVO) commonRequest.getRequest(UserRelationWorkVO.class);
//        return AjaxResult.success(UserRelationWorkServiceImpl.saveUserRelationWork(vo));
//    }
//
//    @PostMapping("update")
//    @ApiOperation(value = "用户关联岗位修改数据")
//    public AjaxResult updateUserRelationWork(@RequestBody CommonRequest commonRequest) {
//        UserRelationWorkVO vo = (UserRelationWorkVO) commonRequest.getRequest(UserRelationWorkVO.class);
//        return AjaxResult.success(UserRelationWorkServiceImpl.updateUserRelationWork(vo));
//    }
//
//    @PostMapping("delete")
//    @ApiOperation(value = "用户关联岗位删除数据")
//    public AjaxResult removeUserRelationWork(@RequestBody CommonRequest commonRequest) {
//        IdRequest request = (IdRequest) commonRequest.getRequest(IdRequest.class);
//        return AjaxResult.success(UserRelationWorkServiceImpl.removeUserRelationWork(request.getId()));
//    }

}
