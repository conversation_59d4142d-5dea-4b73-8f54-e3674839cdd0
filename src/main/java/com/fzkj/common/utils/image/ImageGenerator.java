package com.fzkj.common.utils.image;

import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.file.FileUploadUtils;
import com.fzkj.common.utils.oss.UploadFile;
import com.fzkj.project.system.vo.LearnCertificatePicVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.LineBreakMeasurer;
import java.awt.font.TextAttribute;
import java.awt.font.TextLayout;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.text.AttributedString;
import java.util.Base64;
import java.util.UUID;

@Slf4j
public class ImageGenerator {

    private static final int MAX_WIDTH = 260; // 最大文本宽度
    private static final int MAX_CONTENT_WIDTH = 560; // 最大文本宽度

    public static String generateImage(LearnCertificatePicVO vo) {
        try {
            InputStream templateInputStream = ImageGenerator.class.getClassLoader().getResourceAsStream("templates/cert.png");
            BufferedImage templateImage = ImageIO.read(templateInputStream);
            // 创建一个新的BufferedImage对象，用于存储添加内容后的图片
            BufferedImage image = new BufferedImage(templateImage.getWidth(), templateImage.getHeight(), BufferedImage.TYPE_INT_ARGB);

            // 获取Graphics2D对象以便绘制
            Graphics2D g2d = image.createGraphics();
            // 在新图片上绘制模板图片
            g2d.drawImage(templateImage, 0, 0, null);
            float alpha = 1f;
            AlphaComposite alphaComposite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha);
            g2d.setComposite(alphaComposite);
            // 加载自定义字体

            try {
                Font customFont = Font.createFont(Font.TRUETYPE_FONT, ImageGenerator.class.getClassLoader().getResourceAsStream("fonts/simsun.ttf"));
                g2d.setFont(customFont.deriveFont(Font.BOLD, 24f)); // 设置字体大小和样式
            } catch (IOException | FontFormatException e) {
                // 如果字体加载失败，可以使用默认字体
                g2d.setFont(new Font("simsun", Font.BOLD, 24));
            }
            g2d.setColor(Color.BLACK);
            // 绘制左上角用户头像
            if (StringUtils.isNotEmpty(vo.getUserPhoto())) {
                URL avatarUrl = new URL(vo.getUserPhoto());
                InputStream inputStream = avatarUrl.openStream();
                BufferedImage avatarImage = ImageIO.read(inputStream);
                g2d.drawImage(avatarImage, 57, 183, 155, 200, null);
            } else {
                g2d.setColor(Color.WHITE);
                g2d.fillRect(57, 183, 155, 200);
                g2d.setColor(Color.BLACK);
                g2d.drawRect(57, 183, 155, 200);
                g2d.setColor(Color.BLACK);
                drawCenteredText(g2d, "默认头像", 57, 183, 155, 200);
            }
            // 计算文本的宽度和高度
            FontMetrics fontMetrics = g2d.getFontMetrics(g2d.getFont());
            // 绘制文本
            int y = 188;
            drawText(g2d, "姓名：", 235, y, fontMetrics);
            y = drawText(g2d, vo.getUserName(), 363, y, fontMetrics);
            drawText(g2d, "身份证号：", 235, y, fontMetrics);
            y = drawText(g2d, vo.getIdCard(), 363, y, fontMetrics);
            drawText(g2d, "所属行业：", 235, y, fontMetrics);
            y = drawText(g2d, vo.getIndustryType(), 363, y, fontMetrics);
            drawText(g2d, "企业名称：", 235, y, fontMetrics);
            drawText(g2d, vo.getCompanyName(), 363, y, fontMetrics);
            //正文
            String content = String.format("%s，于%s至%s通过在线教育平台参加《%s》，已完成教育计划制定的全部课程（%s学时），%s予以结业，特发此证。",
                    vo.getUserName(), vo.getStartStudyTime(), vo.getCompleteTime(), vo.getLessonName(), vo.getTrainTimeCount(), vo.getScore());
            drawText(g2d, content, 50, 437, fontMetrics, MAX_CONTENT_WIDTH, 15);
            //签名
            drawText(g2d, "学习签名：", 50, 702, fontMetrics);
            if (StringUtils.isNotEmpty(vo.getSignImg())) {
                g2d.drawImage(ImageIO.read(new URL(vo.getSignImg()).openStream()), 170, 692, 80, 60, null);
            }
            drawText(g2d, "培训机构:（盖章）", 50, 772, fontMetrics);
            //盖章
            InputStream stampStream = ImageGenerator.class.getClassLoader().getResourceAsStream("templates/stamp.png");
            BufferedImage stampImage = ImageIO.read(stampStream);
            if (null != stampImage) {
                g2d.drawImage(stampImage, 55, 713, 160, 110, null);
            }
            drawText(g2d, vo.getCompleteTime(), 50, 806, fontMetrics);
            //微信二维码
            if (StringUtils.isNotEmpty(vo.getCode())) {
                BufferedImage qrCode = decodeBase64ToImage(vo.getCode());
                if (null != qrCode) {
                    g2d.drawImage(qrCode, 465, 702, 100, 100, null);
                }
                drawText(g2d, "扫描二维码查验", 434, 805, fontMetrics);
            }
            // 释放Graphics2D对象使用的资源
            g2d.dispose();
            String filename = generatePNGName();
            byte[] imageBytes = bufferedImageToBytes(image, "png");
            UploadFile uploadFile = FileUploadUtils.uploadOssInputStream(new ByteArrayInputStream(imageBytes), "Upload", filename);
            return uploadFile.getUrl();
        } catch (Exception e) {
            log.error("生成图片失败" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 编码文件名+时间
     */
    public static String generatePNGName() {
        return UUID.randomUUID().toString().replace("-", "") + "renders" + DateUtils.dateTimesNow() + ".png";
    }

    private static byte[] bufferedImageToBytes(BufferedImage image, String formatName) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, formatName, outputStream);
        return outputStream.toByteArray();
    }

    private static int drawText(Graphics2D g2d, String text, int x, int y, FontMetrics fontMetrics) throws Exception {
        return drawText(g2d, text, x, y, fontMetrics, MAX_WIDTH, 20);
    }

    private static int drawText(Graphics2D g2d, String text, int x, int y, FontMetrics fontMetrics, int maxWidth, int height) throws Exception {
        int lineHeight = fontMetrics.getHeight();
        if (StringUtils.isEmpty(text)) {
            return y + lineHeight + height;
        }
        int maxLineWidth = maxWidth;
        AttributedString attributedString = new AttributedString(text);
        attributedString.addAttribute(TextAttribute.FONT, g2d.getFont());
        LineBreakMeasurer lineBreakMeasurer = new LineBreakMeasurer(attributedString.getIterator(), g2d.getFontRenderContext());
        int currentY = y;
        while (lineBreakMeasurer.getPosition() < text.length()) {
            TextLayout layout = lineBreakMeasurer.nextLayout(maxLineWidth);
            currentY += layout.getAscent();
            float layoutX = x;

            if (layout.isLeftToRight()) {
                // 修改为靠左对齐
                layoutX += 0;
            } else {
                layoutX -= (maxLineWidth - layout.getAdvance()) / 2;
            }

            layout.draw(g2d, layoutX, currentY);
            currentY += layout.getDescent() + layout.getLeading() + height;
        }
        return currentY;
    }

    private static BufferedImage decodeBase64ToImage(String base64Image) {
        try {
            // 将Base64编码的字符串解码为字节数组
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);

            // 使用字节数组创建一个ByteArrayInputStream对象
            ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes);

            // 使用ImageIO.read()方法从ByteArrayInputStream对象中读取图像
            return ImageIO.read(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private static void drawCenteredText(Graphics2D g2d, String text, int x, int y, int width, int height) {
        FontMetrics fontMetrics = g2d.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(text);
        int textHeight = fontMetrics.getHeight();
        int textX = x + (width - textWidth) / 2;
        int textY = y + (height - textHeight) / 2 + fontMetrics.getAscent();
        g2d.drawString(text, textX, textY);
    }
}
