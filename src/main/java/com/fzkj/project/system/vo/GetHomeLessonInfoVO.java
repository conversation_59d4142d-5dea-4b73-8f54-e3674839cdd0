package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;

import java.time.LocalDateTime;

@Data
public class GetHomeLessonInfoVO {

    @ApiModelProperty(value = "id")
    @JsonProperty("Mongo_ObjId")
    private ObjectId id;

    @ApiModelProperty(value = "培训类型", example = "gqpx")
    @JsonProperty("TrainType")
    private String trainType;

    @ApiModelProperty(value = "培训类型名称", example = "岗前培训")
    @JsonProperty("TrainTypeName")
    private String trainTypeName;

    @ApiModelProperty(value = "计划ID", example = "137")
    @JsonProperty("PlanID")
    private Long planId;

    @ApiModelProperty(value = "课程ID", example = "1140")
    @JsonProperty("LessonID")
    private Long lessonId;

    @ApiModelProperty(value = "课程名称", example = "【岗前培训】普通货运岗前培训-公司级（中贸）")
    @JsonProperty("LessonName")
    private String lessonName;

    @ApiModelProperty(value = "培训周期")
    @JsonProperty("TrainCycle")
    private String trainCycle;

    @ApiModelProperty(value = "学习次数", example = "0")
    @JsonProperty("StudyNum")
    private int studyNum;

    @ApiModelProperty(value = "培训状态", example = "-3")
    @JsonProperty("StaticStatus")
    private int staticStatus;

    @ApiModelProperty(value = "是否支付", example = "1")
    @JsonProperty("IsPay")
    private int isPay;

    @ApiModelProperty(value = "重考次数")
    @JsonProperty("ResitNumber")
    private Integer resitNumber;

    @ApiModelProperty(value = "是否统计")
    @JsonProperty("IsStatic")
    private Integer isStatic;

    @ApiModelProperty(value = "是否完成")
    @JsonProperty("IsComplete")
    private Integer isComplete;

    @ApiModelProperty(value = "创建时间", example = "2023-08-28 11:18:20")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "培训状态1", example = "-3")
    @JsonProperty("StaticStatus1")
    private Integer staticStatus1;

    @ApiModelProperty(value = "形状", example = "2")
    @JsonProperty("Shape")
    private Integer shape;

    @ApiModelProperty(value = "考试状态", example = "0")
    @JsonProperty("ExamStatus")
    private Integer examStatus;

    @ApiModelProperty(value = "是否完美", example = "0")
    @JsonProperty("IsPerfect")
    private Integer isPerfect;

    @ApiModelProperty(value = "区域代码")
    @JsonProperty("AreaCode")
    private String areaCode;

    @ApiModelProperty(value = "发票状态", example = "0")
    @JsonProperty("InvoiceStatus")
    private int invoiceStatus;

    @ApiModelProperty(value = "阶段", example = "1")
    @JsonProperty("Stage")
    private int stage;

    @ApiModelProperty(value = "培训开始时间")
    @JsonProperty("TrainStartTime")
    private String trainStartTime;

    @ApiModelProperty(value = "培训结束时间")
    @JsonProperty("TrainEndTime")
    private String trainEndTime;

    @ApiModelProperty(value = "计划级别", example = "2")
    @JsonProperty("PlanLevel")
    private int planLevel;

    @ApiModelProperty(value = "支付日期时间", example = "0001-01-01T00:00:00")
    @JsonProperty("PayDateTime")
    private String payDateTime;
}
