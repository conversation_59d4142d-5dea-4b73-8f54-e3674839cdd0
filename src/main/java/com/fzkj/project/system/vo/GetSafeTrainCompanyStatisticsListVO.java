package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetSafeTrainCompanyStatisticsListVO {

    @ApiModelProperty(value = "组织ID")
    @JsonProperty("OrgID")
    private Long orgId;

    @ApiModelProperty(value = "组织名称")
    @JsonProperty("OrgName")
    private String orgName;

    @ApiModelProperty(value = "是否下级")
    @JsonProperty("IsNext")
    private Integer isNext;

    @ApiModelProperty(value = "总人数")
    @JsonProperty("TotalUserNum")
    private Integer totalUserNum;

    @ApiModelProperty(value = "已完成人数")
    @JsonProperty("WCUserNum")
    private Integer wcUserNum;

    @ApiModelProperty(value = "完成率")
    @JsonProperty("WCL")
    private String wcl;

    @ApiModelProperty(value = "完成率排序")
    @JsonProperty("WCLSort")
    private double wclSort;

}
