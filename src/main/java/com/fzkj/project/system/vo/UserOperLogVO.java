package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fzkj.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel(value="日志表VO")
public class UserOperLogVO implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "")
    @JsonProperty("OperContent")
    private String operContent;

    @ApiModelProperty(value = "")
    @JsonProperty("CreatorTime")
    private Date creatorTime;

    @JsonProperty("OperID")
    private Long operId;

    /** 操作模块 */
    @JsonProperty("Title")
    private String title;

    /** 业务类型（0其它 1新增 2修改 3删除） */
    @JsonProperty("BusinessType")
    private Integer businessType;

    /** 业务类型数组 */
    @JsonProperty("BusinessTypes")
    private Integer[] businessTypes;

    /** 请求方法 */
    @JsonProperty("Method")
    private String method;

    /** 请求方式 */
    @JsonProperty("RequestMethod")
    private String requestMethod;

    /** 操作人员 */
    @JsonProperty("OperName")
    private String operName;

    /** 被操作人员 */
    @JsonProperty("OperatedUserCode")
    private String operatedUserCode;

    /** 操作类别（0其它 1后台用户 2手机端用户） */
    @JsonProperty("OperatorType")
    private Integer operatorType;


    /** 部门名称 */
    @JsonProperty("DeptName")
    private String deptName;

    /** 请求url */
    @JsonProperty("OperUrl")
    private String operUrl;

    /** 操作地址 */
    @JsonProperty("OperIp")
    private String operIp;

    @JsonProperty("OperLocation")
    private String operLocation;

    /** 请求参数 */
    @JsonProperty("OperParam")
    private String operParam;

    /** 返回参数 */
    @JsonProperty("JsonResult")
    private String jsonResult;

    /** 操作状态（0正常 1异常） */
    @JsonProperty("Status")
    private Integer status;

    /** 错误消息 */
    @JsonProperty("ErrorMsg")
    private String errorMsg;

    /** 操作时间 */
    @JsonProperty("OperTime")
    private Date operTime;

    /** 平台:3-小程序,6-管理端,7-企业端*/
    @JsonProperty("Platform")
    private int platform;

    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("CompanyID")
    private Long companyId;

    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("IdCard")
    private String idCard;

    @JsonProperty("Phone")
    private String phone;

    @JsonProperty("KeyWord")
    private String keyWord;

    @JsonProperty("StartTime")
    private String startTime;

    @JsonProperty("EndTime")
    private String endTime;
}