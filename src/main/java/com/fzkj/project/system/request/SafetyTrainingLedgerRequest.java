package com.fzkj.project.system.request;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fzkj.common.utils.ImagUrlUtil;
import com.fzkj.common.utils.StaticStatusConverter;
import com.fzkj.framework.interceptor.annotation.EnumFiledConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ContentRowHeight(30)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, borderLeft = BorderStyleEnum.THIN, borderTop = BorderStyleEnum.THIN, borderRight = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
public class SafetyTrainingLedgerRequest implements Serializable {

    @ApiModelProperty("用户ID")
    @ExcelIgnore
    private String userCode;


    @ApiModelProperty("部门ID")
    @ExcelIgnore
    private Long departId;

    @ApiModelProperty("课程ID")
    @ExcelIgnore
    private Long lessonId;

    @ApiModelProperty("部门名称")
    @ExcelIgnore
    private String departName;

    @ApiModelProperty("序号")
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private Integer num;

    @ApiModelProperty("用户名称")
    @ExcelProperty(value = "姓名",index = 1)
    @ColumnWidth(10)
    private String userName;

    @ApiModelProperty("工作名称")
    @ExcelProperty(value = "岗位",index = 2)
    @ColumnWidth(12)
    private String workName;

    @ApiModelProperty("用户身份证号")
    @ExcelProperty(value = "身份证号",index = 3)
    @ColumnWidth(21)
    private String idCard;

    @ApiModelProperty("用户静态状态")
    @ExcelProperty(value = "培训状态",index = 4,converter = StaticStatusConverter.class)
    @ColumnWidth(21)
    @EnumFiledConvert(enumMap = "-3-未完成,0-未完成,1-正常开始正常已完成,2-正常开始过期完成,3-过期开始培训中,4-过期开始过期完成")
    @NotNull(message = "课程统计状态（-3：未参加；0：培训中；1：已完成；2：过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）不能为空")
    private Integer staticStatus;

    @ApiModelProperty("用户成绩")
    @ColumnWidth(13)
    @ExcelProperty(value = "考试成绩",index = 5)
    private Integer score;

    @ApiModelProperty("用户完成时间")
    @ExcelProperty(value = "培训完成时间",index = 6)
    @ColumnWidth(20)
    private String completeTime;

    @ApiModelProperty("用户签名图片")
    @ExcelProperty(value = "学习签名",index = 7,converter = ImagUrlUtil.class)//,converter = ImagUrlUtil.class
    @ColumnWidth(18)
    private String signImg;

    @ApiModelProperty("用户考试签名")
    @ExcelProperty(value = "考试签名",index = 8,converter = ImagUrlUtil.class)
    @ColumnWidth(18)
    private String examSign;

    @ExcelIgnore
    private Integer sort;
}
