package com.fzkj.project.system.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试试题表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@ApiModel(value = "考试试题表Request")
public class ExamQuestionRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "考试ID")
    private Long examId;

    @ApiModelProperty(value = "试题类型")
    private Integer subjectType;

    @ApiModelProperty(value = "试题名称")
    private String subjectName;

    @ApiModelProperty(value = "试题类型ID")
    private Integer questionTypeId;

    @ApiModelProperty(value = "试题类型名称")
    private String questionTypeName;

    @ApiModelProperty(value = "分数")
    private Integer score;

    @ApiModelProperty(value = "试题选项")
    private String questionOption;

    @ApiModelProperty(value = "正确答案")
    private String rightOption;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改者ID")
    private String reviseId;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否有效")
    private Integer isValid;

    @ApiModelProperty(value = "图片")
    private String img;

    @ApiModelProperty(value = "试题解析")
    private String problemAnalysis;

    @JsonProperty("Flag")
    private String flag;
}