package com.fzkj.project.system.service.impl;

import com.fzkj.project.system.entity.CompanyLicence;
import com.fzkj.project.system.vo.CompanyLicenceVO;
import com.fzkj.project.system.request.CompanyLicenceRequest;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.system.mapper.CompanyLicenceMapper;
import com.fzkj.project.system.service.CompanyLicenceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 企业证照 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class CompanyLicenceServiceImpl extends ServiceImpl<CompanyLicenceMapper, CompanyLicence> implements CompanyLicenceService {

    @Override
    public List<CompanyLicence> listCompanyLicence(CompanyLicenceRequest request) {
        CompanyLicence entity = (CompanyLicence) DataTransfer.transfer(request, CompanyLicence.class);
        LambdaQueryWrapper<CompanyLicence> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public PageUtils listByPage(CompanyLicenceRequest request, PageEntity pageEntity) {
        CompanyLicence entity = (CompanyLicence) DataTransfer.transfer(request, CompanyLicence.class);
        LambdaQueryWrapper<CompanyLicence> queryWrapper = new LambdaQueryWrapper<>(entity);
        IPage<CompanyLicence> page = this.page(
                new Query<CompanyLicence>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public CompanyLicenceVO findCompanyLicence(Long id) {
        CompanyLicence entity = this.getById(id);
        CompanyLicenceVO vo = (CompanyLicenceVO) DataTransfer.transfer(entity, CompanyLicenceVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCompanyLicence(CompanyLicenceVO vo) {
        CompanyLicence entity = (CompanyLicence) DataTransfer.transfer(vo, CompanyLicence.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCompanyLicence(CompanyLicenceVO vo) {
        CompanyLicence entity = (CompanyLicence) DataTransfer.transfer(vo, CompanyLicence.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCompanyLicence(Long id) {
        return this.removeById(id);
    }
}