package com.fzkj.project.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.basicdata.mapper.OrderMapper;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.entity.lessonSafeTrainTd;
import com.fzkj.project.system.mapper.*;
import com.fzkj.project.system.mongo.UserCourseRecordSafeTrain;
import com.fzkj.project.system.mongo.UserLessonRecordSafeTrain;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.UserLessonRecordSafeTrainMongoService;
import com.fzkj.project.system.vo.*;
import com.fzkj.project.wxPay.dto.Order;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.fzkj.common.utils.MongoQueryUtil.*;

@Service
@RequiredArgsConstructor
public class UserLessonRecordSafeTrainMongoServiceImpl implements UserLessonRecordSafeTrainMongoService {
    public static final String TABLE_SAFE_TRAIN_USER_LESSON = "_tableSafeTrainUserLesson";
    public static final String TABLE_SAFE_TRAIN_USER_COURSE = "_tableSafeTrainUserCourse";
    @Autowired
    @Qualifier("mongoTemplateMaster")
    private MongoTemplate mongoTemplateMaster;

    @Autowired
    @Qualifier("mongoTemplateHistory")
    private MongoTemplate mongoTemplateHistory;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private UserCompanyMapper userCompanyMapper;

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private LessonSafeTrainMapper lessonSafeTrainMapper;

    @Autowired
    private LessonSafeTrainCompanyUserTemplateMapper lessonSafeTrainCompanyUserTemplateMapper;

    private final static List completeStatus = Arrays.asList(1, 2, 4);

    @Override
    public List<UserLessonRecordLearnNumVO> getLearnNum(LessonSafeTrainPlanRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1)
                        .and("lessonDate").is(request.getLessonMonth())),
                getConditionalStage("statisticalGroup", request.getStatisticalGroup()),
                getConditionalStage("lessonId", request.getLessonIds()),
                getConditionalStageForOr(request.getOrgIds(), "departId", "companyId"),
                Aggregation.group("lessonId", "staticStatus").count().as("learnNum"),
                Aggregation.project("lessonId", "staticStatus", "learnNum").and("id").previousOperation()
        );
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public List<UserLessonRecordLearnNumVO> getLessonLearnNumByCompanyDepartId(LessonSafeTrainPlanRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1)),
                getConditionalStage("statisticalGroup", request.getStatisticalGroup()),
                getConditionalStage("lessonId", request.getLessonIds()),
                Aggregation.group("companyId", "departId", "staticStatus").count().as("learnNum"),
                Aggregation.project("companyId", "departId", "staticStatus", "learnNum").and("id").previousOperation()
        );
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public List<UserLessonRecordSafeTrainUserVO> getUserLessonRecordSafeTrainBus(LessonSafeTrainPlanRequest request) {
        List<Integer> staticStatusList = new ArrayList<>();
        //课程统计状态（-3：未参加；0：正常培训中；1：正常开始正常已完成；2：正常开始过期完成；3:过期开始培训中，4：过期开始过期完成;默认-3）
        if (null != request.getStaticStatus()) {
            if (request.getStaticStatus() == 0) {
                if (null == request.getLearnFlag() ||request.getLearnFlag() != 1) {
                    staticStatusList = Arrays.asList(0, 3);
                }else {
                    staticStatusList = Arrays.asList(0, 2);
                }
            } else if (request.getStaticStatus() == 1) {
                if (null == request.getLearnFlag()) {
                    staticStatusList = Arrays.asList(1, 2, 4);
                } else if (request.getLearnFlag() == 1) {
                    staticStatusList = Arrays.asList(1);
                } else if (request.getLearnFlag() == 0) {
                    staticStatusList = Arrays.asList(2, 4);
                }
            } else if (request.getStaticStatus() == -3) {
                if (null == request.getLearnFlag() ||request.getLearnFlag() != 1) {
                    staticStatusList = Arrays.asList(request.getStaticStatus());
                }else {
                    staticStatusList = Arrays.asList(-3, 3, 4);
                }
            }
        }
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1)),
                getConditionalStage("lessonId", request.getLessonIds()),
                getConditionalStageForOr(request.getOrgIds(), "departId", "companyId"),
                getConditionalStage("statisticalGroup", request.getStatisticalGroup()),
                getConditionalStage("staticStatus", staticStatusList),
                getConditionalStageOrLike(request.getKeyWords(), "userName", "idCard", "phone", "jobNo")
        );
        List<UserLessonRecordSafeTrainUserVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        List<UserLessonRecordSafeTrainUserVO> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        List list = mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
        list.sort(Comparator.comparing(UserLessonRecordSafeTrainUserVO::getCreationTime).reversed());
        return list;
    }

    @Override
    public List getUserLessonRecordSafeTrain1(String lessonName, Long companyId) {
        Long lessonId = lessonSafeTrainMapper.selectIdByLessonName(lessonName);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("lessonId").is(lessonId)
                        .and("departId").is(companyId).and("isValid").is(1)
                )
            );
            List<SafetyTrainingLedgerRequest> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, SafetyTrainingLedgerRequest.class).getMappedResults();
            List<SafetyTrainingLedgerRequest> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, SafetyTrainingLedgerRequest.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }


    @Override
    public List<UserLessonRecordLearnNumVO> getSafeTrainCompanyStatisticsUser(LessonSafeTrainCompanyStatisticRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1).and("lessonDate").is(request.getLessonMonth())),
                getConditionalStage("lessonId", request.getLessonIds()),
                getConditionalStage("companyId", request.getCompanyId()),
                getConditionalStage("departId", request.getDepartIds()),
                getConditionalStage("statisticalGroup", request.getStatisticalGroup()),
                Aggregation.group("companyId", "lessonId", "staticStatus").count().as("learnNum"),
                Aggregation.project("companyId", "lessonId", "staticStatus", "learnNum").and("id").previousOperation()
        );
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public List<SafeTrainCompanyDetialUserVO> getSafeTrainCompanyDetialUser(SafeTrainCompanyDetialRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1)),
                getConditionalStage("lessonId", request.getLessonId()),
                getConditionalStage("companyId", request.getCompanyId()),
                getConditionalStage("departId", request.getDepartId())
        );
        List<SafeTrainCompanyDetialUserVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, SafeTrainCompanyDetialUserVO.class).getMappedResults();
        List<SafeTrainCompanyDetialUserVO> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, SafeTrainCompanyDetialUserVO.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public List<SafeTrainUserRecordDetailVO> getSafeTrainUserRecordDetail(SafeTrainUserRecordDetailRequest request) {
        List<Integer> staticStatusList = new ArrayList();
        if (null != request.getStaticStatus()) {
            if (request.getStaticStatus() == 0) {
                staticStatusList = Arrays.asList(0, 3);
            } else if (request.getStaticStatus() == 1) {
                staticStatusList = Arrays.asList(1, 2, 4);
            } else if (request.getStaticStatus() == -3) {
                staticStatusList = Arrays.asList(-3);
            } else if (request.getStaticStatus() == 3) {
                staticStatusList = Arrays.asList(-3, 0, 3);
            }
        }
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isStatic").is(1)),
                getConditionalStage("isValid", request.getIsValid()),
                getConditionalStage("companyId", request.getCompanyIds()),
                getConditionalStage("companyId", request.getCompanyId()),
                getConditionalStage("lessonId", request.getLessonIds()),
                getConditionalStage("staticStatus", staticStatusList),
                getConditionalStage("statisticalGroup", request.getStatisticalGroup()),
                getConditionalStageOrLike(request.getKeywords(), "userName", "idCard", "phone", "jobNo")
        );
        List<SafeTrainUserRecordDetailVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, SafeTrainUserRecordDetailVO.class).getMappedResults();
        List<SafeTrainUserRecordDetailVO> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, SafeTrainUserRecordDetailVO.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public LessonRecordVO getUserSafeTrainFilesDetail(UserSafeTrainFilesDetailRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isStatic").is(1)),
                getConditionalStage("lessonId", request.getLessonId()),
                getConditionalStage("userCode", request.getUserCode())
        );
        List<LessonRecordVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, LessonRecordVO.class).getMappedResults();
        if (CollectionUtils.isEmpty(tableSafeTrainUserLesson)) {
            tableSafeTrainUserLesson = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, LessonRecordVO.class).getMappedResults();
        }
        LessonRecordVO recordVO = tableSafeTrainUserLesson.get(0);
        AuthUserInfoVO authUserInfoVO = userInfoMapper.selectUserByUserCode(request.getUserCode());
        if (null != authUserInfoVO) {
            recordVO.setUserPhoto(authUserInfoVO.getUserPhoto());
        }
        return recordVO;
    }

    @Override
    public List<UserLessonRecordSafeTrain> getListLearnNumByRequest(GetSafeTrainCompanyStatisticsListRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1)),
                getConditionalStage("lessonId", request.getLessonIds()),
                getConditionalStage("statisticalGroup", 0),
                getConditionalStage("departId", request.getDepartIds()),
                Aggregation.group("departId", "staticStatus").count().as("learnNum"),
                Aggregation.project("departId", "staticStatus", "learnNum").and("id").previousOperation()
        );
        List<UserLessonRecordSafeTrain> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public List<UserLessonRecordSafeTrain> getTotalLearnNumByRequest(GetSafeTrainCompanyStatisticsTotalRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1)),
                getConditionalStage("statisticalGroup", 0),
                getConditionalStage("lessonId", request.getLessonIds()),
                getConditionalStageOr(null != request.getOrgId(), Criteria.where("departId").in(request.getDepartIds()), Criteria.where("companyId").is(request.getOrgId())),
                Aggregation.group("staticStatus").count().as("learnNum"),
                /*Aggregation.project("staticStatus", "learnNum").and("id").previousOperation()*/
                Aggregation.project().and("_id").as("staticStatus") // 将_id字段的值赋给staticStatus
                        .andInclude("learnNum") // 确保包含learnNum字段
                        .andExclude("_id") // 排除默认的_id字段
        );
        List<UserLessonRecordSafeTrain> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public List<UserLessonRecordSafeTrain> getCompanyLearnNumByRequest(GetSafeTrainCompanyStatisticsPlanRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1)),
                getConditionalStage("statisticalGroup", 0),
                getConditionalStage("lessonId", request.getLessonIds()),
                getConditionalStageOr(null != request.getOrgId(), Criteria.where("departId").in(request.getDepartIds()), Criteria.where("companyId").is(request.getOrgId())),
                Aggregation.group("staticStatus").count().as("learnNum"),
                Aggregation.project().and("_id").as("staticStatus") // 将_id字段的值赋给staticStatus
                        .andInclude("learnNum") // 确保包含learnNum字段
                        .andExclude("_id") // 排除默认的_id字段
        );
        List<UserLessonRecordSafeTrain> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public List<UserLessonRecordLearnNumVO> getLearnNumByLessonId(LessonSafeTrainPlanRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isStatic").is(1)),
                getConditionalStage("lessonId", request.getLessonIds()),
                Aggregation.group("companyId", "staticStatus").count().as("learnNum"),
                Aggregation.project("companyId", "staticStatus", "learnNum").and("id").previousOperation()
        );
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public List<UserLessonRecordSafeTrainVO> getLessonDisedUserList(LessonSafeTrainQueryUserRequest request) {
        List<Integer> staticStatusList = new ArrayList();
        if (null != request.getStaticStatus()) {
            if (request.getStaticStatus() == 0) {
                staticStatusList = Arrays.asList(0, 3);
            } else if (request.getStaticStatus() == 1) {
                staticStatusList = Arrays.asList(1, 2, 4);
            } else if (request.getStaticStatus() == -3) {
                staticStatusList = Arrays.asList(-3);
            } else if (request.getStaticStatus() == 3) {
                staticStatusList = Arrays.asList(-3, 0, 3);
            }
        }
        List<Long> parents  = null;
        if(null != request.getDepartId()){
            String[] parentIds = companyMapper.selectById(request.getDepartId()).getAncestors().split(",");
            parents= Arrays.stream(parentIds).map(Long::valueOf).collect(Collectors.toList());
            parents.add(request.getDepartId());
        }
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isStatic").is(1)),
                getConditionalStage("isValid", request.getIsValid()),
                getConditionalStage("staticStatus", staticStatusList),
                getConditionalStage("companyId", request.getCompanyId()),
                getConditionalStage("departId", parents),
                getConditionalStage("workId", request.getWorkId()),
                getConditionalStage("lessonId", request.getLessonId()),
                getConditionalStageOrLike(request.getKeyWords(), "userName", "idCard", "phone", "jobNo"),
                getConditionalStage(StringUtils.isNotEmpty(request.getStartDate()), Criteria.where("completeTime").gte(request.getStartDate())),
                getConditionalStage(StringUtils.isNotEmpty(request.getEndDate()), Criteria.where("completeTime").lte(request.getEndDate() + " 23:59:59")
                ));
        List<UserLessonRecordSafeTrainVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainVO.class).getMappedResults();
        List<UserLessonRecordSafeTrainVO> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainVO.class).getMappedResults();
        List<UserLessonRecordSafeTrainVO> list = mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
        return list;
    }

    @Override
    public List<LessonSafeTrainQueryUserVO> getLessonNoDisUserList(LessonSafeTrainQueryUserRequest request) {
        //查询参培学员
        List<LessonSafeTrainQueryUserVO> list = lessonSafeTrainCompanyUserTemplateMapper.selectUsers(request);
        //筛选已分发学员
        Aggregation aggregation = Aggregation.newAggregation(
                getConditionalStage("companyId", request.getCompanyId()),
                getConditionalStage("lessonId", request.getLessonId()),
                Aggregation.project("userCode")
        );
        List<UserLessonRecordSafeTrainUserVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        if (CollectionUtils.isEmpty(tableSafeTrainUserLesson)) {
            tableSafeTrainUserLesson = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        }
        if (CollectionUtils.isEmpty(tableSafeTrainUserLesson)) {
            return list;
        }
        List<String> userCodes = tableSafeTrainUserLesson.stream().map(UserLessonRecordSafeTrainUserVO::getUserCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().filter(item -> !userCodes.contains(item.getUserCode())).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<UserLessonRecordLearnNumVO> getLessonLearnNumByLessonId(LessonSafeTrainRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isStatic").is(1)),
                getConditionalStage("lessonId", request.getLessonIds()),
                getConditionalStage("companyId", request.getCompanyId()),
                getConditionalStage("departId", request.getDepartId()),
                getConditionalStage(null != request.getIsDel() && 1 == request.getIsDel(), "isValid", 0),
                getConditionalStage(null == request.getIsDel() || 0 == request.getIsDel(), "isValid", 1),
                Aggregation.group("lessonId", "staticStatus").count().as("learnNum"),
                Aggregation.project("lessonId", "staticStatus", "learnNum").and("id").previousOperation()
        );
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        List<UserLessonRecordLearnNumVO> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordLearnNumVO.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public List<UserLessonRecordSafeTrain> getLessonSafeTrainUnDisUser(LessonSafeTrainDisVO request) {
        //查询参培学员
        List<UserLessonRecordSafeTrain> list = lessonSafeTrainCompanyUserTemplateMapper.selectLessonSafeTrainUnDisUser(request);
        //筛选已分发学员
        Aggregation aggregation = Aggregation.newAggregation(
                getConditionalStage("companyId", request.getCompanyIds()),
                getConditionalStage("lessonId", request.getLessonId()),
                Aggregation.project("userCode")
        );
        List<UserLessonRecordSafeTrainUserVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        if (CollectionUtils.isEmpty(tableSafeTrainUserLesson)) {
            tableSafeTrainUserLesson = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        }
        if (CollectionUtils.isEmpty(tableSafeTrainUserLesson)) {
            return list;
        }
        List<String> userCodes = tableSafeTrainUserLesson.stream().map(UserLessonRecordSafeTrainUserVO::getUserCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().filter(item -> !userCodes.contains(item.getUserCode())).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public int getLessonNoDisUserNum(LessonNoDisUserNumRequest request) {
        //查询参培学员
        List<UserLessonRecordSafeTrain> list = lessonSafeTrainCompanyUserTemplateMapper.selectLessonNoDisUserNum(request);
        //筛选已分发学员
        Aggregation aggregation = Aggregation.newAggregation(
                getConditionalStage("companyId", request.getCompanyId()),
                getConditionalStage("lessonId", request.getLessonId()),
                Aggregation.project("userCode")
        );
        List<UserLessonRecordSafeTrainUserVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        if (CollectionUtils.isEmpty(tableSafeTrainUserLesson)) {
            tableSafeTrainUserLesson = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        }
        if (CollectionUtils.isEmpty(tableSafeTrainUserLesson)) {
            return list.size();
        }
        List<String> userCodes = tableSafeTrainUserLesson.stream().map(UserLessonRecordSafeTrainUserVO::getUserCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().filter(item -> !userCodes.contains(item.getUserCode())).collect(Collectors.toList()).size();
        }
        return list.size();
    }

    @Override
    public List<UserLessonRecordSafeTrain> getUserLesson(UserLessonRecordSafeTrain request) {
        Integer statusStatus = null;
        if (!ObjectUtils.isEmpty(request.getStaticStatus())) {
            statusStatus = request.getStaticStatus();
            request.setStaticStatus(null);
        }
        List<AggregationOperation> other = Arrays.asList(
                getConditionalStage(null != request.getUserCodes(), "userCode", request.getUserCodes()),
                getConditionalStage(null != request.getLessonIds(), "lessonId", request.getLessonIds()),
                getConditionalStage(null != request.getCompanyIds(), "companyId", request.getCompanyIds()),
                getConditionalStage(null != request.getDepartIds(), "departId", request.getDepartIds()),
                getConditionalStage(null != request.getStaticStatus(), Criteria.where("statusStatus").ne(statusStatus)));
        Aggregation aggregation = buildAggregationFromRequest(request, other);
        if (null != request.getLessonId()) {
            List<UserLessonRecordSafeTrain> userLesson1 = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
            if (CollectionUtils.isNotEmpty(userLesson1)) {
                return userLesson1;
            } else {
                return mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
            }
        } else {
            return mergeList(mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults(),
                    mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults());
        }
    }

    @Override
    public boolean removeUserLessonById(List<ObjectId> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(ids));
        mongoTemplateMaster.remove(query, UserLessonRecordSafeTrain.class);
        mongoTemplateHistory.remove(query, UserLessonRecordSafeTrain.class);
        return true;
    }

    @Override
    public boolean updateUserLessonById(UserLessonRecordSafeTrain request) {
        // 创建查询条件
        Query query = new Query();
        query.addCriteria(Criteria.where("id").is(request.getId()));
        // 根据 ID 查询记录
        UserLessonRecordSafeTrain userLesson = mongoTemplateMaster.findOne(query, UserLessonRecordSafeTrain.class);
        if (userLesson == null) {
            mongoTemplateHistory.save(request);
        } else {
            mongoTemplateMaster.save(request);
        }
        return true;
    }

    @Override
    public boolean delUserLesson(List<ObjectId> userLessonIds) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        LocalDateTime now = LocalDateTime.now();
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(userLessonIds));
        Update update = new Update();
        update.set("isValid", 0);
        update.set("reviseCode", user.getUserCode());
        update.set("reviseTime", now);
        // 执行批量更新操作
        mongoTemplateMaster.updateMulti(query, update, UserLessonRecordSafeTrain.class);
        mongoTemplateHistory.updateMulti(query, update, UserLessonRecordSafeTrain.class);
        return true;
    }

    @Override
    public int getLessonLearnNum(GetLessonLearnNumRequest request) {
        Query query = new Query();
        query.addCriteria(Criteria.where("isValid").is(1).and("isStatic").is(1).and("isComplete").ne(-3).and("lessonId").is(request.getLessonId()));
        // 查询记录条数
        long count = mongoTemplateMaster.count(query, UserLessonRecordSafeTrain.class);
        if (count > 0) {
            return (int) count;
        }
        return (int) mongoTemplateHistory.count(query, UserLessonRecordSafeTrain.class);
    }

    @Override
    public int getLearnRedNum(GetLearnRedNumRequest request) {
        Query query = new Query();
        query.addCriteria(Criteria.where("isValid").is(1)
                .and("userCode").is(request.getUserCode())
                .and("isComplete").is(-3)
                .and("trainStartTime")
                .lte(DateUtils.parseDateToStr("yyyy-MM-dd", new Date()))
                .and("trainEndTime").gte(DateUtils.parseDateToStr("yyyy-MM-dd", new Date())));
        long count = mongoTemplateMaster.count(query, UserLessonRecordSafeTrain.class);
        if (count > 0) {
            return (int) count;
        }
        return (int) mongoTemplateMaster.count(query, UserLessonRecordSafeTrain.class);
    }

    @Override
    public List<UserLessonRecordSafeTrain> getCompleteUserLessonRecord(String userId, Long lessonId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("isValid").is(1)
                .and("userCode").is(userId)
                .and("isComplete").in(1, 2, 4)
                .and("lessonId").is(lessonId));
        List<UserLessonRecordSafeTrain> userLesson1 = mongoTemplateMaster.find(query, UserLessonRecordSafeTrain.class);
        if (CollectionUtils.isNotEmpty(userLesson1)) {
            return userLesson1;
        }
        return mongoTemplateHistory.find(query, UserLessonRecordSafeTrain.class);

    }

    @Override
    public List<UserLessonRecordSafeTrain> getSafeTrainCompanyLesson(GetSafeTrainCompanyLessonRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1)),
                getConditionalStageLike("lessonDate", request.getLessonDate()),
                getConditionalStageOr(null != request.getOrgId(), Criteria.where("departId").in(request.getDepartIds()), Criteria.where("companyId").is(request.getOrgId())),
                Aggregation.group("lessonId", "lessonName").count().as("learnNum"),
                Aggregation.project("lessonId", "lessonName").and("id").previousOperation()
        );
        List<UserLessonRecordSafeTrain> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> tableSafeTrainUserLessonPre = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        return mergeList(tableSafeTrainUserLesson, tableSafeTrainUserLessonPre);
    }

    @Override
    public PageUtils getSafeTrainStatisticsUserList(GetSafeTrainStatisticsUserListRequest request, PageEntity pageEntity) {
        List<Integer> staticStatusList = new ArrayList();
        if (null != request.getStaticStatus()) {
            if (request.getStaticStatus() == 0) {
                staticStatusList = Arrays.asList(0, 3);
            } else if (request.getStaticStatus() == 1) {
                staticStatusList = Arrays.asList(1, 2, 4);
            } else if (request.getStaticStatus() == -3) {
                staticStatusList = Arrays.asList(-3);
            }
        }
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)
                        .and("isStatic").is(1)),
                getConditionalStage("statisticalGroup", 0),
                getConditionalStage("lessonId", request.getLessonId()),
                getConditionalStage("lessonId", request.getLessonIds()),
                getConditionalStage("staticStatus", staticStatusList),
                getConditionalStageOrLike(request.getKeywords(), "userName", "idCard", "phone", "jobNo"),
                getConditionalStageOr(null != request.getOrgId(), Criteria.where("departId").in(request.getDepartIds()), Criteria.where("companyId").is(request.getOrgId()))
        );
        List<UserLessonRecordSafeTrain> userLesson1 = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> userLesson2 = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> result = mergeList(userLesson1, userLesson2);
        return PageUtils.page(pageEntity, result);
    }

    @Override
    public List<UserLessonRecordSafeTrain> getHomeLessonInfo(GetHomeLessonInfoRequest request) {
        String now = DateUtils.parseDateToStr("yyyy-MM", new Date());
        String date = DateUtils.parseDateToStr("yyyy-MM-dd", new Date());
        request.setNow(now);
        request.setDate(date);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)),
                getConditionalStage("userCode", request.getUserCode()),
                getConditionalStageLike("lessonDate", request.getNow()),
                getConditionalStage(true, Criteria.where("trainStartTime").lte(request.getDate()))
        );
        List<UserLessonRecordSafeTrain> userLesson1 = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> userLesson2 = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> result = mergeList(userLesson1, userLesson2);
        result.forEach(item -> {
            item.setReservedField5(completeStatus.contains(item.getStaticStatus()) ? "1" : item.getStaticStatus() == 0 || item.getStaticStatus() == 3 ? "0" : "-3");
        });
        List<UserLessonRecordSafeTrain> sortedResult = result.stream()
                .sorted(Comparator.comparing(UserLessonRecordSafeTrain::getReservedField5)
                        .thenComparing(UserLessonRecordSafeTrain::getCreationTime).reversed())
                .collect(Collectors.toList());
        return sortedResult;
    }

    @Override
    public List<UserLessonRecordSafeTrain> getLearningUserLesson(EditUserLessonRecordSignRequest request) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)),
                getConditionalStage("userCode", request.getUserCode()),
                getConditionalStage("isComplete", Arrays.asList(0, 3)),
                getConditionalStage("lessonId", request.getLessonId())
        );
        List<UserLessonRecordSafeTrain> userLessons = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        if (CollectionUtils.isNotEmpty(userLessons)) {
            return userLessons;
        }
        return mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
    }

    @Override
    public List<UserLessonRecordSafeTrain> getMyLessonInfo(GetMyLessonInfoRequest request) {
        String date = DateUtils.parseDateToStr("yyyy-MM-dd", new Date());
        request.setDate(date);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1)),
                getConditionalStage("userCode", request.getUserCode()),
                getConditionalStage(true, Criteria.where("trainStartTime").lte(request.getDate()))
        );
        List<UserLessonRecordSafeTrain> userLesson1 = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> userLesson2 = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> result = mergeList(userLesson1, userLesson2);
        result.forEach(item -> {
            item.setReservedField5(completeStatus.contains(item.getStaticStatus()) ? "1" : item.getStaticStatus() == 0 || item.getStaticStatus() == 3 ? "0" : "-3");
        });
        List<UserLessonRecordSafeTrain> sortedResult = result.stream()
                .sorted(Comparator.comparing(UserLessonRecordSafeTrain::getReservedField5)
                        .thenComparing(UserLessonRecordSafeTrain::getCreationTime).reversed())
                .collect(Collectors.toList());
        return sortedResult;
    }

    @Override
    public List<UserLessonRecordSafeTrain> getUserSurvey(GetUserSurveyRequest request) {
        String year = String.valueOf(LocalDate.now().getYear());
        Aggregation aggregation = Aggregation.newAggregation(
                getConditionalStage("userCode", request.getUserCode()),
                getConditionalStage(true, Criteria.where("lessonDate").gte(year + "-01-01")),
                getConditionalStage(true, Criteria.where("lessonDate").lte(year + "-12-01"))
        );
        List<UserLessonRecordSafeTrain> userLesson1 = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> userLesson2 = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> result = mergeList(userLesson1, userLesson2);
        result.sort(Comparator.comparing(UserLessonRecordSafeTrain::getCreationTime).reversed());
        return result;
    }

    @Override
    public PageUtils getUserLessonRecordByUserCode(GetUserSurveyRequest request, PageEntity pageEntity) {
        Aggregation aggregation = Aggregation.newAggregation(
                getConditionalStage("userCode", request.getUserCode()),
                getConditionalStage("staticStatus", request.getStaticStatus()),
                getConditionalStage("isValid", request.getIsValid()),
                getConditionalStage(request.getStartMonth(), Criteria.where("lessonDate").gte(request.getStartMonth() + "-01")),
                getConditionalStage(request.getEndMonth(), Criteria.where("lessonDate").lte(request.getEndMonth() + "-01"))
        );
        List<UserLessonRecordSafeTrain> userLesson1 = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> userLesson2 = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        List<UserLessonRecordSafeTrain> result = mergeList(userLesson1, userLesson2);
        return PageUtils.page(pageEntity, result);
    }

    @Override
    public boolean recoveryUserLesson(String userCode, List<ObjectId> ids) {
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(ids));
        Update update = new Update();
        update.set("isValid", 1);
        update.set("reviseCode", userCode);
        update.set("reviseTime", LocalDateTime.now());
        // 执行批量更新操作
        mongoTemplateMaster.updateMulti(query, update, UserLessonRecordSafeTrain.class);
        mongoTemplateHistory.updateMulti(query, update, UserLessonRecordSafeTrain.class);
        return true;
    }

    @Override
    public int getUserLessonCount(Long companyId, Long lessonCategoryId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("companyId").is(companyId)
                .and("lessonCategoryId").is(lessonCategoryId)
                .and("isComplete").ne(-3));
        return (int) (mongoTemplateMaster.count(query, UserLessonRecordSafeTrain.class) + mongoTemplateHistory.count(query, UserLessonRecordSafeTrain.class));
    }

    @Override
    public PageUtils getPayUserList(UserLessonRecordSafeTrainVO request, PageEntity pageEntity) {
        List<UserInfoVO> userInfoVOS = userCompanyMapper.selectPayUserList();
        Aggregation aggregation = Aggregation.newAggregation(getConditionalStage("lessonId", request.getLessonId()),
                Aggregation.project("id", "userCode")
        );
        List<UserLessonRecordSafeTrainUserVO> tableSafeTrainUserLesson = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        if (CollectionUtils.isEmpty(tableSafeTrainUserLesson)) {
            tableSafeTrainUserLesson = mongoTemplateHistory.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        }
        List<String> ids = tableSafeTrainUserLesson.stream().map(UserLessonRecordSafeTrainUserVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return PageUtils.page(pageEntity, userInfoVOS);
        }
        LambdaQueryWrapper<Order> queryWrapper = Wrappers.lambdaQuery(Order.class).in(Order::getDataId, ids).eq(Order::getStatus, 1).select(Order::getDataId);
        List<Order> orders = orderMapper.selectList(queryWrapper);
        List<String> payIds = orders.stream().map(Order::getDataId).collect(Collectors.toList());
        List<String> userCodes = tableSafeTrainUserLesson.stream().filter(item -> payIds.contains(item.getId())).map(UserLessonRecordSafeTrainUserVO::getUserCode).collect(Collectors.toList());
        List<UserInfoVO> result = userInfoVOS.stream().filter(item -> userCodes.contains(item.getUserCode())).collect(Collectors.toList());
        return PageUtils.page(pageEntity, result);
    }

    @Override
    public boolean updateName(String userCode, String userName) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userCode").is(userCode));
        Update update = new Update();
        update.set("userName", userName);
        // 执行批量更新操作
        mongoTemplateMaster.updateMulti(query, update, UserLessonRecordSafeTrain.class);
        mongoTemplateHistory.updateMulti(query, update, UserLessonRecordSafeTrain.class);
        return true;
    }

    @Override
    public void saveBatch(List<UserLessonRecordSafeTrain> userLessonRecordSafeTrainList) {
        mongoTemplateMaster.insertAll(userLessonRecordSafeTrainList);
    }

    @Override
    public void updateIsPayById(int payStatus, String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("id").is(new ObjectId(id)));
        Update update = new Update();
        update.set("payStatus", payStatus);
        // 执行批量更新操作
        mongoTemplateMaster.updateMulti(query, update, UserLessonRecordSafeTrain.class);
        mongoTemplateHistory.updateMulti(query, update, UserLessonRecordSafeTrain.class);
    }

    @Override
    public void updateIsPayById(boolean isHistory, int payStatus, String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("id").is(new ObjectId(id)));
        Update update = new Update();
        update.set("payStatus", payStatus);
        // 执行批量更新操作
        if (isHistory) {
            mongoTemplateHistory.updateMulti(query, update, UserLessonRecordSafeTrain.class);
        } else {
            mongoTemplateMaster.updateMulti(query, update, UserLessonRecordSafeTrain.class);
        }
    }

    @Override
    public void moveToHistory() {
        Aggregation aggregation = Aggregation.newAggregation(
                getConditionalStage(true, Criteria.where("trainEndTime").lt(DateUtils.dateTime(new Date())))
        );
        List<UserLessonRecordSafeTrain> list = mongoTemplateMaster.aggregate(aggregation, TABLE_SAFE_TRAIN_USER_LESSON, UserLessonRecordSafeTrain.class).getMappedResults();
        if (CollectionUtils.isNotEmpty(list)) {
            List<UserLessonRecordSafeTrain> lessonHistory = DataTransfer.transferList(list, UserLessonRecordSafeTrain.class);
            mongoTemplateHistory.insertAll(lessonHistory);
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(list.stream().map(UserLessonRecordSafeTrain::getId).collect(Collectors.toList())));
            mongoTemplateMaster.remove(query, UserLessonRecordSafeTrain.class);
            Set<Long> lessonIds = lessonHistory.stream().map(UserLessonRecordSafeTrain::getLessonId).collect(Collectors.toSet());
            //获取已经过期的用户学习记录
            Aggregation aggregation2 = Aggregation.newAggregation(
                    getConditionalStage(true, Criteria.where("lessonId").in(lessonIds))
            );
            List<UserCourseRecordSafeTrain> courseList = mongoTemplateMaster.aggregate(aggregation2, TABLE_SAFE_TRAIN_USER_COURSE, UserCourseRecordSafeTrain.class).getMappedResults();
            if (CollectionUtils.isNotEmpty(courseList)) {
                mongoTemplateHistory.insertAll(courseList);
                Query query2 = new Query();
                query2.addCriteria(Criteria.where("id").in(courseList.stream().map(UserCourseRecordSafeTrain::getId).collect(Collectors.toList())));
                mongoTemplateMaster.remove(query2, UserCourseRecordSafeTrain.class);
            }
        }
    }

    @Override
    public List<UserLessonRecordSafeTrain> getExpireLesson(String expireDays) {
        Query query = new Query();
        Criteria criteria = Criteria.where("staticStatus").ne(1).and("isStatic").is(1);
        query.addCriteria(criteria);
        Date now  = new Date();
        List<Integer> expireDaysList = Arrays.stream(expireDays.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        List<Criteria> criteriaList = expireDaysList.stream()
                .map(expireDay -> {
                    return Criteria.where("trainEndTime")
                            .is(DateUtils.parseDateToStr("yyyy-MM-dd",DateUtils.addDays(now, expireDay)));
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(criteriaList)) {
            Criteria orOperator = new Criteria().orOperator(criteriaList.toArray(new Criteria[0]));
            query.addCriteria(orOperator);
        }

        return mongoTemplateMaster.find(query, UserLessonRecordSafeTrain.class);
    }

    @Override
    public int getUserLessonLearningCount(Long lessonId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("lessonId").is(lessonId));
        return (int) (mongoTemplateMaster.count(query, UserLessonRecordSafeTrain.class) + mongoTemplateHistory.count(query, UserLessonRecordSafeTrain.class));
    }

    @Override
    public List<Long> getUserLearningLessonIds(String userCode) {
        Query query = new Query();
        // 根据userCode查询lessonIds
        query.addCriteria(Criteria.where("userCode").is(userCode));
        List<UserLessonRecordSafeTrain> records = mongoTemplateMaster.find(query, UserLessonRecordSafeTrain.class);
        List<UserLessonRecordSafeTrain> recordHistory = mongoTemplateHistory.find(query, UserLessonRecordSafeTrain.class);
        List<UserLessonRecordSafeTrain> list = mergeList(records, recordHistory);
        return list.stream()
                .map(UserLessonRecordSafeTrain::getLessonId)
                .collect(Collectors.toList());
    }

    @Override
    public void updateRecord(Long companyId, Long departId, String departName, String companyName, String userCode) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userCode").is(userCode));
        Update update = new Update();
        update.set("departId", departId);
        update.set("departName", departName);
        if(null != companyId){
            update.set("companyId", companyId);
            update.set("companyName", companyName);
        }
        mongoTemplateMaster.updateMulti(query, update, UserLessonRecordSafeTrain.class);
    }

    @Override
    public List<lessonSafeTrainTd> selectSignUrlById(String lessonName) {
        Long lessonId = lessonSafeTrainMapper.selectIdByLessonName(lessonName);
        List<lessonSafeTrainTd> list = lessonSafeTrainMapper.selectAllByLessonId(lessonId);
        return list;
    }
}
