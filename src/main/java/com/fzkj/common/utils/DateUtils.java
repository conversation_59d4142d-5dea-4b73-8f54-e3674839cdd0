package com.fzkj.common.utils;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static final String YYYY = "yyyy";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String FORMAT_MONTH_DAY_CN = "MM月dd日";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String FORMAT_DATE_WITHOUT_LINK = "yyyyMMdd";

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String YYYYMMDDHHMMSSS = "yyyyMMddHHmmssS";

    public static final String YYYYMMDD = "yyyyMMdd";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static final String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimesNow() {
        return dateTimeNow(YYYYMMDDHHMMSSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String dateTimeNew(final Date date) {
        return parseDateToStr(YYYY_MM_DD_HH_MM_SS, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final String parseDateToStr(final String format, final LocalDateTime date) {
        return date.format(DateTimeFormatter.ofPattern(format));
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取指定月份的最后一条，输入为2023-01，输出为2023-01-31
     *
     * @param yearMonthStr
     * @return
     */
    public static String getLastDayOfMonthAsString(String yearMonthStr) {
        // 解析输入的字符串为YearMonth对象
        YearMonth yearMonth = YearMonth.parse(yearMonthStr);

        // 获取该月份的最后一天
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

        // 转换为字符串，格式为yyyy-MM-dd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return lastDayOfMonth.format(formatter);
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    public static Date getDayByIndex(int day) {
        Date now = new Date();
        LocalDate localDate = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate yesterday = localDate.plusDays(day);
        Date newDate = java.sql.Date.valueOf(yesterday);
        return newDate;
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 当月有多少天
     *
     * @param date
     * @return int
     */
    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 日期是当月的第几天
     *
     * @param date
     * @return int
     */
    public static int getDayInMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 把秒换成xx时xx分xx秒
     *
     * @param seconds
     * @return
     */
    public static String formatTime(long seconds) {
        int h = (int) (seconds / 3600);
        int m = (int) (seconds % 3600) / 60;
        int s = (int) (seconds % 60);
        String result = "";
        if (h != 0) {
            result += h + "时";
        }
        if (m != 0 || h != 0) {
            result += m + "分";
        }
        result += s + "秒";
        return result;


    }

    /**
     * 获取月份
     *
     * @param
     * @return int
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }
    /**
     * 获取月份
     *
     * @param
     * @return int
     */
    public static String getMonthNow(Date date) {
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM");
        return format.format(date);
    }
    /**
     * Description: 按指定格式格式化日期
     *
     * @param date * @param format
     * @return:
     */
    public static String formatDate(LocalDate date, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return date.format(formatter);
    }

    public static String formatDate(String date, String orgFormat, String dstFormat) {
        SimpleDateFormat orgDateFormat = new SimpleDateFormat(orgFormat);
        SimpleDateFormat dstDateFormat = new SimpleDateFormat(dstFormat);
        try {
            return dstDateFormat.format(orgDateFormat.parse(date));
        } catch (ParseException e) {
            if (date.length() == 10) {
                orgFormat = "yyyy-MM-dd";
            } else if (date.length() == 19) {
                orgFormat = "yyyy-MM-dd HH:mm:ss";
                if (date.contains("T")) {
                    orgFormat = "yyyy-MM-ddTHH:mm:ss";
                }
            }
            orgDateFormat = new SimpleDateFormat(orgFormat);
            try {
                return dstDateFormat.format(orgDateFormat.parse(date));
            } catch (ParseException ex) {
                return "";
            }
        }
    }

    public static LocalDate parseDate(String date, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return LocalDate.parse(date, formatter);
    }

    public static List<String> getDates(String startDate, String endDate, String format) {
        List<String> list = Lists.newArrayList();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(format);
        LocalDate tmp = LocalDate.parse(startDate, dateFormatter);
        LocalDate end = LocalDate.parse(endDate, dateFormatter);
        while (tmp.isBefore(end)) {
            list.add(formatDate(tmp, format));
            tmp = tmp.plusDays(1);
        }
        list.add(formatDate(end, format));
        return list;
    }

    public static List<Pair<Date, Date>> get24HourRegine(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        List<Pair<Date, Date>> list = Lists.newArrayList();
        Date pre = calendar.getTime();
        for (int i = 1; i <= 96; i++) {
            calendar.add(Calendar.MINUTE, i * 15);
            Date now = new Date(calendar.getTime().getTime());
            Pair<Date, Date> pair = Pair.of(new Date(pre.getTime()), now);
            list.add(pair);
            pre = now;
        }
        return list;
    }

    public static List<String> get24HourList() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        List<String> list = Lists.newArrayList();
        SimpleDateFormat df = new SimpleDateFormat("HH:mm");
        for (int i = 1; i <= 96; i++) {
            calendar.add(Calendar.MINUTE, 15);
            Date now = new Date(calendar.getTime().getTime());
            list.add(df.format(now));
        }
        return list;
    }

    /**
     * 获取上月日期
     ****/
    public static String getLastMonth() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar lastDate = Calendar.getInstance();
        //设为当前月的1号
        lastDate.set(Calendar.DATE, 1);
        //加一个月，变为下月的1号
        lastDate.add(Calendar.MONTH, -1);
        str = sdf.format(lastDate.getTime());
        return str;
    }

    /**
     * 获取上年日期
     ****/
    public static String getLastYear() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        Calendar lastDate = Calendar.getInstance();
        //设为当前月的1号
        lastDate.set(Calendar.DATE, 1);
        //加一个月，变为下月的1号
        lastDate.add(Calendar.YEAR, -1);
        str = sdf.format(lastDate.getTime());
        return str;
    }

    /**
     * 获取本周一日期
     **/
    public static Date getFirstDayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(date);
            cal.set(Calendar.DAY_OF_WEEK, 2);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cal.getTime();
    }

    /**
     * 指定日期前几天日期
     **/
    public static String getBeforeDay(String date, int day) {
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = format.parse(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - day);
        return new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime());
    }

    /**
     * 获取当前季度第一天
     */
    public static String getQuarterFirstDay() {
        String month = new SimpleDateFormat("M").format(new Date());
        String year = new SimpleDateFormat("yyyy").format(new Date());
        if (Integer.parseInt(month) <= 3) {
            return year + "-01-01";
        }
        if (Integer.parseInt(month) <= 6) {
            return year + "-04-01";
        }
        if (Integer.parseInt(month) <= 9) {
            return year + "-07-01";
        }
        if (Integer.parseInt(month) <= 12) {
            return year + "-10-01";
        }
        return null;
    }

    /**
     * 获取当前季度最后一天
     */
    public static String getQuarterLastDay() {
        String month = new SimpleDateFormat("M").format(new Date());
        String year = new SimpleDateFormat("yyyy").format(new Date());
        if (Integer.parseInt(month) <= 3) {
            return year + "-03-31";
        }
        if (Integer.parseInt(month) <= 6) {
            return year + "-06-30";
        }
        if (Integer.parseInt(month) <= 9) {
            return year + "-09-30";
        }
        if (Integer.parseInt(month) <= 12) {
            return year + "-12-31";
        }
        return null;
    }

    /**
     * 获取今年第几周
     *
     * @param date
     * @return
     */
    public static Integer getWeeks(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(sdf.parse(date));
        } catch (Exception e) {
            e.printStackTrace();
        }
        //设置星期一为一周开始的第一天
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        //获得当前日期属于今年的第几周
        return calendar.get(Calendar.WEEK_OF_YEAR);
    }

    public static String getCurrentTime() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    /**
     * 上月第一天
     *
     * @return
     */
    public static String getPreviousMonthFirstDay() {
        Calendar lastDate = Calendar.getInstance();
        //设为当前月的1号
        lastDate.set(Calendar.DATE, 1);
        //减一个月，变为下月的1号
        lastDate.add(Calendar.MONTH, -1);
        return new SimpleDateFormat("yyyy-MM-dd").format(lastDate.getTime());
    }

    /**
     * 上月最后一天
     *
     * @return
     */
    public static String getPreviousMonthLastDay() {
        Calendar lastDate = Calendar.getInstance();
        //设为当前月的1号
        lastDate.set(Calendar.DATE, 1);
        //减一个月，变为下月的1号
        lastDate.add(Calendar.DAY_OF_MONTH, -1);
        return new SimpleDateFormat("yyyy-MM-dd").format(lastDate.getTime());
    }

    /**
     * 获取最近几个月的月份
     *
     * @param num
     * @return
     */
    public static List<String> getMonths(Integer num) {
        List<String> months = new ArrayList<>();
        for (int i = num - 1; i >= 0; i--) {
            //减一个月，变为下月的1号
            Calendar lastDate = Calendar.getInstance();
            lastDate.add(Calendar.MONTH, -i);
            months.add(new SimpleDateFormat("yyyy-MM").format(lastDate.getTime()));
        }
        return months;
    }


    /**
     * 获取当前星期数，星期一则为1，星期2则为2
     *
     * @return
     */
    public static Integer getWeekDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.SUNDAY);
        return calendar.get(Calendar.DAY_OF_WEEK);
    }
}
