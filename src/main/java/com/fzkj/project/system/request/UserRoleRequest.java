package com.fzkj.project.system.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户关联角色
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "用户关联角色Request")
public class UserRoleRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private Long targetId;

    @ApiModelProperty(value = "")
    private Long roleId;

    @ApiModelProperty(value = "")
    private String userCode;

    @ApiModelProperty(value = "")
    private Integer operPlatform;

    @ApiModelProperty(value = "")
    private String source;

    @ApiModelProperty(value = "")
    private Integer sort;

    @ApiModelProperty(value = "")
    private String creatorCode;

    @ApiModelProperty(value = "")
    private LocalDateTime creatorTime;

    @ApiModelProperty(value = "")
    private String reviseCode;

    @ApiModelProperty(value = "")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty(value = "")
    private Integer isValid;

}