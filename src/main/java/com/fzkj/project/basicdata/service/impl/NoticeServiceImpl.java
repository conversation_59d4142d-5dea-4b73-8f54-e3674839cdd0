package com.fzkj.project.basicdata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.ServletUtils;
import com.fzkj.framework.security.LoginUser;
import com.fzkj.framework.security.service.TokenService;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.framework.web.domain.Query;
import com.fzkj.project.basicdata.entity.NoticeEntity;
import com.fzkj.project.basicdata.mapper.NoticeMapper;
import com.fzkj.project.basicdata.request.NoticeRequest;
import com.fzkj.project.basicdata.service.NoticeService;
import com.fzkj.project.system.entity.UserCompany;
import com.fzkj.project.system.request.GetNoticeAPPRequest;
import com.fzkj.project.system.service.UserCompanyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 13:53
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class NoticeServiceImpl extends ServiceImpl<NoticeMapper, NoticeEntity> implements NoticeService {

    @Resource
    UserCompanyService userCompanyService;

    @Autowired
    private TokenService tokenService;

    @Override
    public PageUtils listByPage(NoticeRequest request, PageEntity pageEntity) {
        NoticeEntity entity = (NoticeEntity) DataTransfer.transfer(request, NoticeEntity.class);
        LambdaQueryWrapper<NoticeEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtils.isEmpty(entity.getId())) {
            queryWrapper.eq(NoticeEntity::getIsValid, entity.getIsValid());
        }
        if (!ObjectUtils.isEmpty(entity.getTitle())){
            queryWrapper.like(NoticeEntity::getTitle,entity.getTitle());
        }
        if (!ObjectUtils.isEmpty(entity.getTitle())){
            queryWrapper.like(NoticeEntity::getTitle,entity.getTitle());
        }
        if (!ObjectUtils.isEmpty(entity.getId())){
            queryWrapper.eq(NoticeEntity::getId,entity.getId());
        }
        if (!ObjectUtils.isEmpty(entity.getIsExpire())){
            if (entity.getIsExpire().equals(0)){
                queryWrapper.gt(NoticeEntity::getEndTime,new Date());
            }else if (entity.getIsExpire().equals(1)){
                queryWrapper.lt(NoticeEntity::getEndTime,new Date());
            }
        }
        if (!ObjectUtils.isEmpty(entity.getPlatform()) &&
                !entity.getPlatform().equals(-999)){
            queryWrapper.eq(NoticeEntity::getPlatform,entity.getPlatform());
        }
        IPage<NoticeEntity> page = this.page(
                new Query<NoticeEntity>().getPage(pageEntity), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public Long insertNotice(NoticeRequest request) {
        NoticeEntity entity = (NoticeEntity) DataTransfer.transfer(request, NoticeEntity.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        entity.setCreatorName(userCompany.getUserName());
        entity.setCreatorCode(userCompany.getUserCode());
        entity.setCreationTime(DateUtils.dateTimeNew(new Date()));
        entity.setIsValid(1);
        this.save(entity);
        return request.getId();
    }

    @Override
    public Boolean updateNotice(NoticeRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        UserCompany userCompany = userCompanyService.selectUserCompany(loginUser.getUser());
        NoticeEntity entity = (NoticeEntity) DataTransfer.transfer(request, NoticeEntity.class);
        entity.setReviseCode(userCompany.getUserCode());
        entity.setReviseName(userCompany.getUserName());
        entity.setReviseTime(DateUtils.dateTimeNew(new Date()));
        return this.saveOrUpdate(entity);
    }

    @Override
    public Boolean deleteNotice(Long id) {

        NoticeEntity byId = this.getById(id);
        if (byId.getIsValid().equals(0)){
            byId.setIsValid(1);
        }else {
            byId.setIsValid(0);
        }
        return this.saveOrUpdate(byId);
    }

    @Override
    public List<NoticeEntity> getNoticeAPP(GetNoticeAPPRequest request) {
        return getBaseMapper().selectNoticeAPP(request);
    }
}
