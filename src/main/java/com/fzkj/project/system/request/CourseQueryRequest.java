package com.fzkj.project.system.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 课件信息表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(value = "课件信息表查询Request")
public class CourseQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课件主键ID")
    private String id;

    @ApiModelProperty(value = "请求标识")
    private String flag;

    @ApiModelProperty(value = "企业ID")
    private Long companyId;

    @ApiModelProperty(value = "关键字")
    private String keywords;

    @ApiModelProperty(value = "分类ID")
    private Long categoryId;

    @ApiModelProperty("讲师编码")
    private Long tearcherCode;

    @ApiModelProperty("文件类型")
    private Long fileType;

    @ApiModelProperty("是否展示")
    private Long isShow;

    @ApiModelProperty("排序")
    private Long sort;

    @ApiModelProperty("转码状态")
    private Long encodeState;

    @ApiModelProperty("是否有效")
    private Long isValid;

    @ApiModelProperty("所属平台")
    private Long belongPlat;


}