<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzkj.project.system.mapper.ExamAnswerRecordSimulationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fzkj.project.system.entity.ExamAnswerRecordSimulation">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="organization_id" property="organizationId" />
        <result column="user_id" property="userId" />
        <result column="exam_id" property="examId" />
        <result column="question_id" property="questionId" />
        <result column="sub_option" property="subOption" />
        <result column="right_option" property="rightOption" />
        <result column="score" property="score" />
        <result column="serial_number" property="serialNumber" />
        <result column="sort" property="sort" />
        <result column="creator_id" property="creatorId" />
        <result column="creation_time" property="creationTime" />
        <result column="revise_id" property="reviseId" />
        <result column="revise_time" property="reviseTime" />
        <result column="remark" property="remark" />
        <result column="is_valid" property="isValid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, organization_id, user_id, exam_id, question_id, sub_option, right_option, score, serial_number, sort, creator_id, creation_time, revise_id, revise_time, remark, is_valid
    </sql>

</mapper>