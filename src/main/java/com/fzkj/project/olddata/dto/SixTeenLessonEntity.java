package com.fzkj.project.olddata.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("t_f_lesson")
public class SixTeenLessonEntity {
    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField("LessonName")
    @JsonProperty("CompanyName")
    private String lessonName;

    @TableField("CompanyID")
    @JsonProperty("CompanyID")
    private String companyId;

    @TableField("LessonYear")
    @JsonProperty("fId")
    private String lessonYear;

    @TableField("LessonMonth")
    @JsonProperty("fId")
    private String lessonMonth;

    @TableField("IsValid")
    @JsonProperty("fId")
    private Integer isValid;
}
