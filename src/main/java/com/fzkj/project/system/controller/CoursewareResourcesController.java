package com.fzkj.project.system.controller;

import com.alibaba.fastjson2.JSON;
import com.aliyuncs.http.CallBack;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fzkj.common.utils.DataTransfer;
import com.fzkj.common.utils.SecurityUtils;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.entity.CoursewareResources;
import com.fzkj.project.system.entity.UserInfo;
import com.fzkj.project.system.request.CallBackRequest;
import com.fzkj.project.system.request.IdRequest;
import com.fzkj.project.system.service.CoursewareResourcesService;
import com.fzkj.project.system.vo.CoursewareResourcesVO;
import com.fzkj.project.system.request.CoursewareResourcesRequest;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.time.LocalDateTime;

/**
 * <p>
 * 视频文件资源信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "lessonApi")
@ThirdPart
@Slf4j
@Log
public class CoursewareResourcesController extends BaseEntity {

    private final CoursewareResourcesService CoursewareResourcesServiceImpl;


    @PostMapping("Get_CoursewareRes")
    @ApiOperation(value = "视频文件资源信息表分页查询")
    public AjaxResult Get_CoursewareRes(@RequestBody CommonRequest commonRequest) {
        CoursewareResourcesRequest request = (CoursewareResourcesRequest) commonRequest.getRequest(CoursewareResourcesRequest.class);
        return AjaxResult.success(CoursewareResourcesServiceImpl.listByPage(request, commonRequest.getPageEntity()));
    }

    @PostMapping("callback")
    @ApiOperation(value = "视频转码回调")
    public AjaxResult callback(@RequestBody CallBackRequest request) {
        log.info("视频转码回调：{}", JSON.toJSONString(request));
        if ("success".equals(request.getStatus()) && null != request.getVideoId()) {
            LambdaUpdateWrapper<CoursewareResources> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CoursewareResources::getEncodeState, 0);
            updateWrapper.set(CoursewareResources::getReviseTime, LocalDateTime.now());
            updateWrapper.eq(CoursewareResources::getFileId, request.getVideoId());
            CoursewareResourcesServiceImpl.update(updateWrapper);
            return AjaxResult.success("回调成功");
        } else if ("fail".equals(request.getStatus()) && null != request.getVideoId()) {
            LambdaUpdateWrapper<CoursewareResources> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CoursewareResources::getEncodeState, 1);
            updateWrapper.set(CoursewareResources::getReviseTime, LocalDateTime.now());
            updateWrapper.eq(CoursewareResources::getFileId, request.getVideoId());
            CoursewareResourcesServiceImpl.update(updateWrapper);
            return AjaxResult.success("回调成功");
        }
        return AjaxResult.error("回调失败");
    }

    @PostMapping("Edit_CoursewareRes")
    @ApiOperation(value = "视频文件资源信息编辑")
    public AjaxResult Edit_CoursewareRes(@RequestBody CommonRequest commonRequest) {
        UserInfo user = SecurityUtils.getLoginUser().getUser();
        CoursewareResourcesRequest request = (CoursewareResourcesRequest) commonRequest.getRequest(CoursewareResourcesRequest.class);
        String flag = request.getFlag();
        if ("add".equals(flag) || (null == flag)) {
            //查询是否存在相同md5文件
            LambdaQueryWrapper<CoursewareResources> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CoursewareResources::getFileMd5, request.getFileMd5());
            queryWrapper.eq(CoursewareResources::getIsValid, 1);
            CoursewareResources coursewareResources = CoursewareResourcesServiceImpl.getOne(queryWrapper);
            if (null != coursewareResources) {
                return AjaxResult.success(coursewareResources.getId());
            } else {
                CoursewareResources entity = (CoursewareResources) DataTransfer.transfer(request, CoursewareResources.class);
                if (entity.getFileType() == 1) {
                    entity.setEncodeState(1);
                } else {
                    entity.setEncodeState(0);
                }
                entity.setCreationTime(LocalDateTime.now());
                entity.setCreatorCode(user.getUserCode());
                entity.setCreatorName(user.getUserName());
                entity.setReviseCode(user.getUserCode());
                entity.setReviseName(user.getUserName());
                entity.setReviseTime(LocalDateTime.now());
                CoursewareResourcesServiceImpl.saveOrUpdate(entity);
                return AjaxResult.success(entity.getId());
            }
        } else if ("edit".equals(flag)) {
            LambdaUpdateWrapper<CoursewareResources> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CoursewareResources::getEncodeState, 0);
            updateWrapper.set(CoursewareResources::getFileUrlHls, request.getFileUrlHls());
            updateWrapper.set(CoursewareResources::getReviseTime, LocalDateTime.now());
            updateWrapper.set(CoursewareResources::getReviseCode, user.getUserCode());
            updateWrapper.set(CoursewareResources::getReviseName, user.getUserName());
            updateWrapper.eq(CoursewareResources::getFileId, request.getFileId());
            boolean update = CoursewareResourcesServiceImpl.update(updateWrapper);
            if (update) {
                return AjaxResult.success(1000, "回调成功");
            }
            return AjaxResult.success(1000, "回调失败");
        } else if ("del".equals(flag)) {
            LambdaUpdateWrapper<CoursewareResources> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CoursewareResources::getReviseCode, user.getReviseCode());
            updateWrapper.set(CoursewareResources::getReviseName, user.getUserName());
            updateWrapper.set(CoursewareResources::getReviseTime, LocalDateTime.now());
            updateWrapper.eq(CoursewareResources::getRemark, request.getRemark());
            updateWrapper.eq(CoursewareResources::getIsValid, 0);
            boolean update = CoursewareResourcesServiceImpl.update(updateWrapper);
            if (update) {
                return AjaxResult.success(1000, "删除成功");
            }
            return AjaxResult.success(1000, "删除失败");
        }
        return AjaxResult.success();
    }

}
