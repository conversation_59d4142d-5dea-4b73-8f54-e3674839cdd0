package com.fzkj.framework.web.domain;

import com.alibaba.fastjson.JSON;
import com.fzkj.common.utils.JsonUtil;
import lombok.Data;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

@Data
public class CommonRequest {
    private static Log logger = LogFactory.getLog(CommonRequest.class);
    private int platform;
    private String source;
    private String data;
    private Object request;
    private PageEntity pageEntity;

    public Object getRequest(Class clazz) {
        if (clazz.equals(String.class)) {
            return this.data;
        }
        if (clazz.equals(Integer.class)) {
            return Integer.valueOf(this.data);
        }
        if (clazz.equals(Long.class)) {
            return Long.valueOf(this.data);
        }
        if (data.startsWith("{")) {
            try {
                // 数据转换
                this.request = JsonUtil.objectMapper().readValue(this.data, clazz);
            } catch (Exception e) {
                logger.error("数据转换失败", e);
            }
        }
        if (null == this.request) {
            try {
                this.request = clazz.newInstance();
            } catch (Exception e) {
                logger.error("数据转换失败", e);
            }
        }
        return this.request;
    }

    public void setData(String data) {
        this.data = data;
        if (data.startsWith("{")) {
            try {
                this.setPageEntity(JSON.parseObject(this.data, PageEntity.class));
            } catch (Exception e) {
                logger.error("数据转换失败", e);
            }
        }
        if (null == this.pageEntity) {
            this.pageEntity = new PageEntity();
        }
    }

    public int getPageIndex() {
        if(null == pageEntity){
            return 1;
        }
        return this.pageEntity.getPageIndex();
    }

    public int getPageSize() {
        if(null == pageEntity){
            return 10;
        }
        return this.pageEntity.getPageSize();
    }
}
