package com.fzkj.project.basicdata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.basicdata.entity.SubStanceLabel;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 13:52
 * @Version: 1.0
 */
public interface SubStanceLabelService extends IService<SubStanceLabel> {
    PageUtils listByPage(SubStanceLabel request, PageEntity pageEntity);

    /**
     * 新增内容管理
     *
     * @param subStanceLabel 内容管理
     * @return 结果
     */
    public Long insertSubStanceLabel(SubStanceLabel subStanceLabel);

    /**
     * 修改内容管理
     *
     * @param subStanceLabel 内容管理
     * @return 结果
     */
    public Boolean updateSubStanceLabel(SubStanceLabel subStanceLabel);

    /**
     * 删除内容管理
     *
     * @param id 内容管理主键
     * @return 结果
     */
    public Boolean deleteSubStanceLabel(Long id);

    String exportSubstanceLabel(SubStanceLabel request,PageEntity pageEntity);

}
