package com.fzkj.project.system.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetCourseAPPRequest {

    @ApiModelProperty(value = "查询关键字", example = "11")
    @JsonProperty("Keywords")
    private String keywords;

    @ApiModelProperty(value = "课程分类ID", example = "1")
    @JsonProperty("CategoryID")
    private Long categoryId;

    @ApiModelProperty(value = "课程标签ID", example = "1")
    @JsonProperty("LabelID")
    private Long labelId;

    @ApiModelProperty(value = "教师ID", example = "1")
    @JsonProperty("TearcherID")
    private Long teacherId;

    @ApiModelProperty(value = "课件ID", example = "1")
    @JsonProperty("ID")
    private Long id;

    @ApiModelProperty(value = "查询方式", example = "byId")
    @JsonProperty("Flag")
    private String flag;

    @ApiModelProperty(value = "用户编码", example = "b135339037ae410a87ad42c872c9353f")
    @JsonProperty("UserCode")
    private String userCode;

    @ApiModelProperty(value = "讲师编码", example = "b135339037ae410a87ad42c872c9353f")
    @JsonProperty("TearcherCode")
    private String tearcherCode;


}
