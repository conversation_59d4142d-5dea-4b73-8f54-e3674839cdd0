package com.fzkj.project.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CourseStatisticsVO {

    @ApiModelProperty("课程数量")
    @JsonProperty("CourseNum")
    private int courseNum;

    @ApiModelProperty("总时长")
    @JsonProperty("TotalTimeCount")
    private double totalTimeCount;

    @ApiModelProperty("企业可使用数量")
    @JsonProperty("CompanyUseNum")
    private int companyUseNum;

    @ApiModelProperty("讲师总数")
    @JsonProperty("TearcherNum")
    private int tearcherNum;

    @ApiModelProperty("评论总数")
    @JsonProperty("CommentNum")
    private int commentNum;

    @ApiModelProperty("学习人数")
    @JsonProperty("LearnNum")
    private int learnNum;

}
