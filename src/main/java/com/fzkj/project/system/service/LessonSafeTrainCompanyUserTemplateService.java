package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.LessonSafeTrainCompanyUserTemplate;
import com.fzkj.project.system.vo.LessonSafeTrainCompanyUserTemplateVO;
import com.fzkj.project.system.request.LessonSafeTrainCompanyUserTemplateRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 企业下的用户安全培训课程用户模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface LessonSafeTrainCompanyUserTemplateService extends IService<LessonSafeTrainCompanyUserTemplate> {

    PageUtils listLessonSafeTrainCompanyUserTemplate(LessonSafeTrainCompanyUserTemplateRequest request, PageEntity pageEntity);

    PageUtils listByPage(LessonSafeTrainCompanyUserTemplateRequest request, PageEntity pageEntity);

    LessonSafeTrainCompanyUserTemplateVO findLessonSafeTrainCompanyUserTemplate(Long id);

    Long saveLessonSafeTrainCompanyUserTemplate(LessonSafeTrainCompanyUserTemplateVO vo);

    boolean updateLessonSafeTrainCompanyUserTemplate(LessonSafeTrainCompanyUserTemplateVO vo);

    boolean removeLessonSafeTrainCompanyUserTemplate(Long id);

    boolean editLessonSafeTrainCompanyUserTemplate(LessonSafeTrainCompanyUserTemplateVO request);
}
