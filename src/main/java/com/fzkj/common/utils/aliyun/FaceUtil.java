package com.fzkj.common.utils.aliyun;

import com.aliyun.facebody20191230.Client;
import com.aliyun.facebody20191230.models.*;
import com.aliyun.tea.TeaModel;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.fzkj.common.utils.oss.OSSConst;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.Arrays;

@Slf4j
public class FaceUtil {

    public static Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "facebody.cn-shanghai.aliyuncs.com";
        return new Client(config);
    }

    public static boolean compareFace(String comparisonPhotos, String originalPhotos) {
        try {
            Client client = createClient(OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
            CompareFaceAdvanceRequest compareFaceRequest = new CompareFaceAdvanceRequest()
                    .setImageURLAObject(new URL(comparisonPhotos).openConnection().getInputStream())
                    .setImageURLBObject(new URL(originalPhotos).openConnection().getInputStream());
            RuntimeOptions runtime = new RuntimeOptions();
            CompareFaceResponse compareFaceResponse = client.compareFaceAdvance(compareFaceRequest, runtime);
            Float confidence = compareFaceResponse.getBody().getData().getConfidence();
            return confidence > 61;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("人脸识别失败");
        }
        return false;
    }

    public static boolean detectLivingFace(String imageUrl) {
        try {
            DetectLivingFaceAdvanceRequest request = new DetectLivingFaceAdvanceRequest();
            DetectLivingFaceAdvanceRequest.DetectLivingFaceAdvanceRequestTasks task = new DetectLivingFaceAdvanceRequest.DetectLivingFaceAdvanceRequestTasks();
            task.setImageURLObject(new URL(imageUrl).openConnection().getInputStream());
            request.setTasks(Arrays.asList(task));
            Client client = createClient(OSSConst.ACCESS_KEY_ID, OSSConst.ACCESS_KEY_SECRET);
            DetectLivingFaceResponse response = client.detectLivingFaceAdvance(request, new RuntimeOptions());
            return "normal".equals(response.getBody().getData().getElements().get(0).getResults().get(0).getLabel());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("活体检测失败");
        }
        return false;
    }

}
