package com.fzkj.project.system.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业安全培训课程设置表
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@TableName("t_d_company_lesson_safe_train_set")
@ApiModel(value = "企业安全培训课程设置表实体")
public class CompanyLessonSafeTrainSet implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @JsonProperty("ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "企业ID（T_D_Company）")
    @JsonProperty("CompanyID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("CompanyName")
    private String companyName;

    @ApiModelProperty(value = "课程分类")
    @JsonProperty("LessonCategoryID")
    private Long lessonCategoryId;

    @ApiModelProperty(value = "课程分类名称")
    @JsonProperty("LessonCategoryName")
    private String lessonCategoryName;

    @ApiModelProperty(value = "参培课程（1：（通用课程）自动分发 2：（定制课程）手动分发）")
    @JsonProperty("HandMode")
    private Integer handMode;

    @ApiModelProperty(value = "付费方式（课程支付类型）：1：余额支付（去掉）；2：包干（学员免费）；3:员工支付（学员付费）")
    @JsonProperty("IsPayType")
    private Integer isPayType;

    @ApiModelProperty(value = "员工支付价格（企业课程）")
    @JsonProperty("StaffPrice")
    private BigDecimal staffPrice;

    @ApiModelProperty(value = "课程时长（包含关联的所有课件时间）")
    @JsonProperty("TotalTimeCount")
    private Integer totalTimeCount;

    @ApiModelProperty(value = "是否托管：1：开启0：不开启")
    @JsonProperty("IsTrusteeship")
    private Integer isTrusteeship;

    @ApiModelProperty(value = "参培人数")
    @JsonProperty("PersonNum")
    private Integer personNum;

    @ApiModelProperty(value = "有效期开始时间")
    @JsonProperty("STime")
    private String sTime;

    @ApiModelProperty(value = "有效期结束时间")
    @JsonProperty("ETime")
    private String eTime;

    @JsonProperty("Source")
    private String source;

    @ApiModelProperty(value = "排序")
    @JsonProperty("Sort")
    private Integer sort;

    @ApiModelProperty(value = "创建人ID")
    @JsonProperty("CreatorCode")
    private String creatorCode;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("CreationTime")
    private LocalDateTime creationTime;

    @ApiModelProperty(value = "修改人ID")
    @JsonProperty("ReviseCode")
    private String reviseCode;

    @ApiModelProperty(value = "修改时间")
    @JsonProperty("ReviseTime")
    private LocalDateTime reviseTime;

    @ApiModelProperty(value = "备注信息")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty(value = "是否有效 1：是， 0：否 默认有效")
    @JsonProperty("IsValid")
    private Integer isValid;
}