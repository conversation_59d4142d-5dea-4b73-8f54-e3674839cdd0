package com.fzkj.project.basicdata.entity;

import com.fzkj.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2024-04-01 15:39
 * @Version: 1.0
 */
@Data
public class SubStanceTypeExportVo {
    @Excel(name = "分类名称")
    public String name;
    @Excel(name = "分类标签")
    public String type;
    @Excel(name = "文章数量")
    public Integer num;
    @Excel(name = "排序编号")
    private Integer code;
    @Excel(name = "排序规则")
    private String sort;
    @Excel(name = "首页展示数量")
    private String homeNum;
    @Excel(name = "首页是否展示")
    private String isShowHome;
    @Excel(name = "换一换按钮")
    private String isShowChange;
    @Excel(name = "栏目状态")
    private String isShow;
}
