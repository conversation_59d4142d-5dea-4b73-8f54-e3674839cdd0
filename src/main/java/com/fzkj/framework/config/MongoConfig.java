package com.fzkj.framework.config;

import com.fzkj.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.CustomConversions;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableMongoRepositories(basePackages = "com.fzkj.project.system.mongo")
public class MongoConfig {

    @Value("${spring.data.mongodb.master.uri}")
    private String masterUri;

    @Value("${spring.data.mongodb.history.uri}")
    private String historyUri;

    @Bean
    @Primary
    public MongoDatabaseFactory mongoDatabaseFactoryMaster() {
        return new SimpleMongoClientDatabaseFactory(masterUri);
    }

    @Bean(name = "mongoTemplateMaster")
    @Primary
    public MongoTemplate mongoTemplateMaster() {
        return new MongoTemplate(mongoDatabaseFactoryMaster());
    }

    @Bean
    public MongoDatabaseFactory mongoDatabaseFactoryHistory() {
        return new SimpleMongoClientDatabaseFactory(historyUri);
    }

    @Bean(name = "mongoTemplateHistory")
    public MongoTemplate mongoTemplateHistory() {
        return new MongoTemplate(mongoDatabaseFactoryHistory());
    }

    @Bean
    public CustomConversions customConversions() {
        List<Converter<?, ?>> converterList = new ArrayList<Converter<?, ?>>();
        converterList.add(new DateToStringConverter());
        converterList.add(new StringToDateConverter());
        return new CustomConversions(converterList);
    }

    public static class DateToStringConverter implements Converter<LocalDateTime, String> {
        @Override
        public String convert(LocalDateTime source) {
            return DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", source);
        }
    }

    public static class StringToDateConverter implements Converter<String, LocalDateTime> {
        @Override
        public LocalDateTime convert(String source) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(source, formatter);
        }
    }
}