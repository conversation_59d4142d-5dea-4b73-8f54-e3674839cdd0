package com.fzkj.project.system.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.WriteContext;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.fzkj.common.exception.CustomException;
import com.fzkj.common.utils.DateUtils;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.common.utils.poi.ExcelCellData;
import com.fzkj.common.utils.poi.ExcelUtil;
import com.fzkj.common.utils.poi.SheetVO;
import com.fzkj.framework.annotation.ListNoRows;
import com.fzkj.framework.annotation.ThirdPart;
import com.fzkj.framework.aspectj.lang.annotation.Log;
import com.fzkj.framework.web.domain.AjaxResult;
import com.fzkj.framework.web.domain.BaseEntity;
import com.fzkj.framework.web.domain.CommonRequest;
import com.fzkj.project.system.entity.Company;
import com.fzkj.project.system.entity.LessonSafeTrain;
import com.fzkj.project.system.entity.lessonSafeTrainTd;
import com.fzkj.project.system.mapper.ExamUserRecordMapper;
import com.fzkj.project.system.request.*;
import com.fzkj.project.system.service.*;
import com.fzkj.project.system.service.impl.CompanyServiceImpl;
import com.fzkj.project.system.service.impl.UserLessonRecordSafeTrainMongoServiceImpl;
import com.fzkj.project.system.vo.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.AsyncContext;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 公交培训计划 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequiredArgsConstructor
@ThirdPart
@RequestMapping(value = "lessonSafeTrainPlan")
@Log
public class LessonSafeTrainPlanController extends BaseEntity {

    private final LessonSafeTrainPlanService LessonSafeTrainPlanServiceImpl;
    private final UserLessonRecordSafeTrainMongoService userLessonRecordSafeTrainMongoService;
    private final CompanyService companyService;

    private final LessonSafeTrainService lessonSafeTrainService;


//    private final  CompanyServiceImpl companyService;

    @PostMapping("Get_BusSafeTrainList")
    @ApiOperation(value = "公交培训计划-列表查询")
    public AjaxResult Get_BusSafeTrainList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getBusSafeTrainList(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_BusSafeTrainList_C")
    @ApiOperation(value = "公交培训计划-列表查询")
    public AjaxResult Get_BusSafeTrainList_C(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getBusSafeTrainListC(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Export_BusSafeTrainList")
    @ApiOperation(value = "公交培训计划-列表导出")
    public AjaxResult Export_BusSafeTrainList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        PageUtils pageUtils = LessonSafeTrainPlanServiceImpl.getBusSafeTrainList(request, null);
        List list = pageUtils.getList();
        ExcelUtil<LessonSafeTrainPlanVO> util = new ExcelUtil<>(LessonSafeTrainPlanVO.class);
        return util.exportExcel(list, request.getLessonMonth().substring(0, 7).replace("-", "年") + "月安全培训计划报表", request.getLessonMonth().substring(0, 7).replace("-", "年") + "月安全培训计划报表", true);
    }

    @PostMapping("Get_BusSafeTrainOrgList")
    @ApiOperation(value = "公交培训计划-详情查询")
    public AjaxResult Get_BusSafeTrainOrgList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getBusSafeTrainOrgList(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_BusSafeTrainUserList")
    @ApiOperation(value = "公交培训计划-详情查询-查看学员")
    public AjaxResult Get_BusSafeTrainUserList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getBusSafeTrainUserList(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Export_SafeTrainCompanyDetial3")
    public void ExportSafetyTrainingLedger(HttpServletResponse response, @RequestBody CommonRequest commonRequest){
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        Company company = companyService.getById(request.getOrgId());
        try (ServletOutputStream outputStream = response.getOutputStream()){
            //部门信息
            String lessonName = request.getLessonName();
            String companyName = company.getCompanyName();
            Long companyId = company.getId();
            // 设置响应头，确保文件名正确
            StringBuffer name = new StringBuffer(companyName);
            String s = name.append("安全培训数据人员明细.xlsx").toString();
            String fileName = URLEncoder.encode(s, "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName  + "\"");
            //获取当前下级公司或部门的用户
            List<SafetyTrainingLedgerRequest> userList = userLessonRecordSafeTrainMongoService.getUserLessonRecordSafeTrain1(lessonName,companyId);
            //获取用户考试图片
            List<lessonSafeTrainTd> userExamSignUrlList = userLessonRecordSafeTrainMongoService.selectSignUrlById(lessonName);
            //将用户考试签字URl集合转换为Map
            Map<String, String> userIdToLessonIdMap = userExamSignUrlList.stream()
                    .collect(Collectors.toMap(lesson -> lesson.getUserId(), lesson -> lesson.getSignUrl()));
            int i=0;
            //用户数据绑定考试签字URL
            for (SafetyTrainingLedgerRequest user : userList){
                userList.get(i).setNum(i+1);
                if (!ObjectUtils.isEmpty(user.getSignImg()) && !ObjectUtils.isEmpty(userIdToLessonIdMap.get(user.getUserCode()))){
                    user.setSort(1);
                }
                if (ObjectUtils.isEmpty(user.getSignImg()) || ObjectUtils.isEmpty(userIdToLessonIdMap.get(user.getUserCode()))){
                    user.setSort(2);
                }
                if (ObjectUtils.isEmpty(user.getSignImg()) && ObjectUtils.isEmpty(userIdToLessonIdMap.get(user.getUserCode()))){
                    user.setSort(3);
                }
                if (!ObjectUtils.isEmpty(userIdToLessonIdMap.get(user.getUserCode()))){
                    user.setExamSign(userIdToLessonIdMap.get(user.getUserCode()));
                }
                i++;
            }
            List<SafetyTrainingLedgerRequest> collect = userList.stream().sorted(Comparator.comparing(SafetyTrainingLedgerRequest::getSort)).collect(Collectors.toList());
            System.out.println("处理完成准备写入excel");
            // 6. 创建工作表
            EasyExcel.write(response.getOutputStream(), SafetyTrainingLedgerRequest.class).sheet(companyName).doWrite(collect);
        }catch (IOException e){
            System.out.println("导出Excel文件时发生错误");
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("导出失败：" + e.getMessage());
        }
    }

    @PostMapping("Export_SafetyTrainingLedger")
    @ApiModelProperty(value = "公交培训计划-导出安全培训台账")
    public void Export_SafetyTrainingLedger(HttpServletResponse response, @RequestBody CommonRequest commonRequest){
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        CompanyRequest companyRequest = new CompanyRequest();
        companyRequest.setId(request.getOrgId());
        //获取公司下级部门
        List<Company> companyList = companyService.getSubDeps(companyRequest);
        String lessonName = request.getLessonName();
        try (ServletOutputStream outputStream = response.getOutputStream()){
            // 设置文件下载头
            String fileName = URLEncoder.encode(request.getLessonName()+"-安全培训数据人员明细", "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"); // 设置为Excel MIME类型
            response.setCharacterEncoding("UTF-8"); // 设置字符编码为UTF-8
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + fileName + ".xlsx");
            // 构建多工作表
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
            for (Company company : companyList){
                Long companyId = company.getId();
                String companyName = company.getCompanyName();
                //获取当前下级公司或部门的用户
                List<SafetyTrainingLedgerRequest> userList = userLessonRecordSafeTrainMongoService.getUserLessonRecordSafeTrain1(lessonName,companyId);
                //获取用户考试图片
                List<lessonSafeTrainTd> userExamSignUrlList = userLessonRecordSafeTrainMongoService.selectSignUrlById(lessonName);
                //将用户考试签字URl集合转换为Map
                Map<String, String> userIdToLessonIdMap = userExamSignUrlList.stream()
                        .collect(Collectors.toMap(lesson -> lesson.getUserId(), lesson -> lesson.getSignUrl()));
                //用户数据绑定考试签字URL
                int i=0;
                for (SafetyTrainingLedgerRequest user : userList){

                    if (ObjectUtils.isEmpty(user)){
                        continue;
                    }
                    userList.get(i).setNum(i+1);
                    if (!ObjectUtils.isEmpty(user.getSignImg()) && !ObjectUtils.isEmpty(userIdToLessonIdMap.get(user.getUserCode()))){
                        user.setSort(1);
                    }
                    if (ObjectUtils.isEmpty(user.getSignImg()) || ObjectUtils.isEmpty(userIdToLessonIdMap.get(user.getUserCode()))){
                        user.setSort(2);
                    }
                    if (ObjectUtils.isEmpty(user.getSignImg()) && ObjectUtils.isEmpty(userIdToLessonIdMap.get(user.getUserCode()))){
                        user.setSort(3);
                    }
                    if (!ObjectUtils.isEmpty(userIdToLessonIdMap.get(user.getUserCode()))){
                        user.setExamSign(userIdToLessonIdMap.get(user.getUserCode()));
                    }
                    i++;
                }
                List<SafetyTrainingLedgerRequest> collect = userList.stream().sorted(Comparator.comparing(SafetyTrainingLedgerRequest::getSort)).collect(Collectors.toList());
                System.out.println("处理完成准备写入excel");
                // 6. 创建工作表
                WriteSheet writeSheet = EasyExcel.writerSheet(companyName).head(SafetyTrainingLedgerRequest.class).build();
                excelWriter.write(collect, writeSheet);
            }
            //结束写入
            System.out.println("结束写入");
            excelWriter.finish();
            excelWriter.close();
        }catch (IOException e){
            System.out.println("导出Excel文件时发生错误");
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("导出失败：" + e.getMessage());
        }
    }

    @PostMapping("Get_UserSafeTrainFilesDetail")
    @ApiOperation(value = "公交培训计划-详情查询-查看学员详情")
    public AjaxResult Get_UserSafeTrainFilesDetail(@RequestBody CommonRequest commonRequest) {
        UserSafeTrainFilesDetailRequest request = (UserSafeTrainFilesDetailRequest) commonRequest.getRequest(UserSafeTrainFilesDetailRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getUserSafeTrainFilesDetail(request));
    }

    @PostMapping("Get_UserCourseRecordLog_All")
    @ApiOperation(value = "公交培训计划-详情查询-查看学员学习日志")
    public AjaxResult Get_UserCourseRecordLog_All(@RequestBody CommonRequest commonRequest) {
        UserCourseRecordLogRequest request = (UserCourseRecordLogRequest) commonRequest.getRequest(UserCourseRecordLogRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getUserCourseRecordLogAll(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Export_BusSafeTrainUserList")
    @ApiOperation(value = "公交培训计划-详情查询-导出学员")
    public AjaxResult Export_BusSafeTrainUserList(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        PageUtils pageUtils = LessonSafeTrainPlanServiceImpl.getBusSafeTrainUserList(request, null);
        List list = pageUtils.getList();
        ExcelUtil<UserLessonRecordSafeTrainUserVO> util = new ExcelUtil<>(UserLessonRecordSafeTrainUserVO.class);
        return util.exportExcel(list, request.getPlanName() + request.getOrgName() + "学员名单", request.getPlanName() + request.getOrgName() + "学员名单", true);
    }

    @PostMapping("Get_SafeTrainCompanyStatistics")
    @ApiOperation(value = "培训统计-企业统计")
    public AjaxResult Get_SafeTrainCompanyStatistics(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainCompanyStatisticRequest request = (LessonSafeTrainCompanyStatisticRequest) commonRequest.getRequest(LessonSafeTrainCompanyStatisticRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getSafeTrainCompanyStatistics(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_SafeTrainCompanyStatisticsList")
    @ApiOperation(value = "小程序-培训统计-部门列表")
    public AjaxResult Get_SafeTrainCompanyStatisticsList(@RequestBody CommonRequest commonRequest) {
        GetSafeTrainCompanyStatisticsListRequest request = (GetSafeTrainCompanyStatisticsListRequest) commonRequest.getRequest(GetSafeTrainCompanyStatisticsListRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getSafeTrainCompanyStatisticsList(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Get_SafeTrainCompanyStatisticsPlan")
    @ApiOperation(value = "小程序-培训统计-根据年月查询培训计划列表")
    public AjaxResult Get_SafeTrainCompanyStatisticsPlan(@RequestBody CommonRequest commonRequest) {
        GetSafeTrainCompanyStatisticsPlanRequest request = (GetSafeTrainCompanyStatisticsPlanRequest) commonRequest.getRequest(GetSafeTrainCompanyStatisticsPlanRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getSafeTrainCompanyStatisticsPlan(request));
    }

    @PostMapping("Get_SafeTrainCompanyStatisticsTotal")
    @ApiOperation(value = "小程序-培训统计-数据合计")
    public AjaxResult Get_SafeTrainCompanyStatisticsTotal(@RequestBody CommonRequest commonRequest) {
        GetSafeTrainCompanyStatisticsTotalRequest request = (GetSafeTrainCompanyStatisticsTotalRequest) commonRequest.getRequest(GetSafeTrainCompanyStatisticsTotalRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getSafeTrainCompanyStatisticsTotal(request));
    }

    @PostMapping("Get_SafeTrainCompanyLesson")
    @ApiOperation(value = "小程序-培训统计-课程列表")
    @ListNoRows
    public AjaxResult Get_SafeTrainCompanyLesson(@RequestBody CommonRequest commonRequest) {
        GetSafeTrainCompanyLessonRequest request = (GetSafeTrainCompanyLessonRequest) commonRequest.getRequest(GetSafeTrainCompanyLessonRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getSafeTrainCompanyLesson(request));
    }

    @PostMapping("Get_SafeTrainStatisticsUserList")
    @ApiOperation(value = "小程序-培训统计-学员列表")
    @ListNoRows
    public AjaxResult Get_SafeTrainStatisticsUserList(@RequestBody CommonRequest commonRequest) {
        GetSafeTrainStatisticsUserListRequest request = (GetSafeTrainStatisticsUserListRequest) commonRequest.getRequest(GetSafeTrainStatisticsUserListRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.getSafeTrainStatisticsUserList(request, commonRequest.getPageEntity()));
    }

    @PostMapping("Export_SafeTrainCompanyStatistics")
    @ApiOperation(value = "公交培训统计-企业统计下载")
    public AjaxResult Export_SafeTrainCompanyStatistics(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainCompanyStatisticRequest request = (LessonSafeTrainCompanyStatisticRequest) commonRequest.getRequest(LessonSafeTrainCompanyStatisticRequest.class);
        PageUtils pageUtils = LessonSafeTrainPlanServiceImpl.getSafeTrainCompanyStatistics(request, commonRequest.getPageEntity());
        List list = pageUtils.getList();
        ExcelUtil<SafeTrainCompanyStatisticsVO> util = new ExcelUtil<>(SafeTrainCompanyStatisticsVO.class);
        return util.exportExcel(list, request.getLessonMonth().substring(0, 7).replace("-", "年") + "月安全培训企业统计报表", request.getLessonMonth().substring(0, 7).replace("-", "年") + "月安全培训企业统计报表", true);
    }

    @PostMapping("Export_SafeTrainCompanyDetial")
    @ApiOperation(value = "公交培训统计-企业统计下载")
    public AjaxResult Export_SafeTrainCompanyDetial(@RequestBody CommonRequest commonRequest) {
        SafeTrainCompanyDetialRequest request = (SafeTrainCompanyDetialRequest) commonRequest.getRequest(SafeTrainCompanyDetialRequest.class);
        LessonSafeTrain lessonSafeTrain = lessonSafeTrainService.getById(request.getLessonId());
        SafeTrainDataExportVO vo = LessonSafeTrainPlanServiceImpl.getSafeTrainCompanyDetial(request);
        ExcelUtil<SafeTrainCompanyDetialVO> util = new ExcelUtil<>(SafeTrainCompanyDetialVO.class);
        String title = "安全培训全部数据报表";
        if (request.getLearnFlag() != null && request.getLearnFlag() == 1) {
            title = "安全培训数据报表";
        } else if (request.getLearnFlag() != null && request.getLearnFlag() == 2) {
            title = "安全培训补学数据报表";
        }
        String filename = request.getLessonMonth().substring(0, 7).replace("-", "年") + "月" + request.getCompanyName() + lessonSafeTrain.getLessonCategoryName() + title;
        List<SheetVO> vos = new ArrayList<>();
        SheetVO sheet1 = new SheetVO();
        sheet1.setSheetName("总统计");
        vos.add(sheet1);
        List<List<ExcelCellData>> records = new ArrayList<>();
        List<ExcelCellData> row1 = new ArrayList<>();
        row1.add(new ExcelCellData(request.getLessonMonth().substring(0, 7).replace("-", "年") + "月" + request.getCompanyName() + "安全培训数据", "formatHeader", 6));
        records.add(row1);
        List<ExcelCellData> row2 = new ArrayList<>();
        row2.add(new ExcelCellData("企业名称", "formatTitle"));
        row2.add(new ExcelCellData(request.getCompanyName(),"formatLeftVal", 5));
        records.add(row2);
        List<ExcelCellData> row3 = new ArrayList<>();
        row3.add(new ExcelCellData("部门名称", "formatTitle", (short) 30));
        row3.add(new ExcelCellData("总人数", "formatTitle", (short) 15));
        row3.add(new ExcelCellData("参学人数", "formatTitle", (short) 15));
        row3.add(new ExcelCellData("完成人数", "formatTitle", (short) 15));
        row3.add(new ExcelCellData("参学率", "formatTitle", (short) 15));
        row3.add(new ExcelCellData("完成率", "formatTitle", (short) 15));
        records.add(row3);
        List<DepartTrainDataStaticsVO> departTrainDataList = vo.getDepartTrainDataList();
        for (DepartTrainDataStaticsVO item : departTrainDataList) {
            List<ExcelCellData> departRow = new ArrayList<>();
            departRow.add(new ExcelCellData(item.getDepartName()));
            departRow.add(new ExcelCellData(String.valueOf(item.getTotalUserNum())));
            departRow.add(new ExcelCellData(String.valueOf(item.getCxUserNum())));
            departRow.add(new ExcelCellData(String.valueOf(item.getWcUserNum())));
            departRow.add(new ExcelCellData(String.valueOf(item.getCxl())));
            departRow.add(new ExcelCellData(String.valueOf(item.getWcl())));
            records.add(departRow);
        }
        List<ExcelCellData> lineRow = new ArrayList<>();
        lineRow.add(new ExcelCellData("-------------------------------------------------------", "formatVal", 6));
        records.add(lineRow);
        List<ExcelCellData> row4 = new ArrayList<>();
        row4.add(new ExcelCellData("学习未完成人数", "formatTitle", 2));
        row4.add(new ExcelCellData("姓名", "formatTitle", 4));
        records.add(row4);
        List<ExcelCellData> row5 = new ArrayList<>();
        row5.add(new ExcelCellData("学习未完成" + vo.getStudyNoComplete() + "人", "formatVal", 2, (short) 0, (short) 2000));
        row5.add(new ExcelCellData(vo.getStudyNoCompleteName(), 4));
        records.add(row5);
        sheet1.setExcelCells(records);
        //生成子部门数据
        for (DepartTrainDataStaticsVO item : departTrainDataList) {
            if ("合计".equals(item.getDepartName())) {
                continue;
            }
            SheetVO sheet = new SheetVO();
            sheet.setSheetName(item.getDepartName());
            vos.add(sheet);
            List<List<ExcelCellData>> departRecords = new ArrayList<>();
            List<ExcelCellData> departRow1 = new ArrayList<>();
            departRow1.add(new ExcelCellData(request.getLessonMonth().substring(0, 7).replace("-", "年") + "月" + request.getCompanyName() + "安全培训数据人员明细", "formatHeader", 7));
            departRecords.add(departRow1);
            List<ExcelCellData> departRow2 = new ArrayList<>();
            departRow2.add(new ExcelCellData("参学部门", "formatTitle", 2, (short) 25));
            departRow2.add(new ExcelCellData(item.getDepartName(), 5));
            departRecords.add(departRow2);
            List<ExcelCellData> departRow3 = new ArrayList<>();
            departRow3.add(new ExcelCellData("培训日期", "formatTitle", 2, (short) 25));
            departRow3.add(new ExcelCellData(vo.getTrainCycle(), 5));
            departRecords.add(departRow3);
            List<ExcelCellData> departRow4 = new ArrayList<>();
            departRow4.add(new ExcelCellData("培训内容", "formatTitle", 2, (short) 25));
            departRow4.add(new ExcelCellData(vo.getLessonName(), 5));
            departRecords.add(departRow4);
            List<ExcelCellData> departRow5 = new ArrayList<>();
            departRow5.add(new ExcelCellData("及格分数", "formatTitle", 2, (short) 25));
            departRow5.add(new ExcelCellData(vo.getPassMark(), 5));
            departRecords.add(departRow5);
            List<ExcelCellData> departRow6 = new ArrayList<>();
            departRow6.add(new ExcelCellData("序号", "formatTitle", (short) 8));
            departRow6.add(new ExcelCellData("姓名", "formatTitle", (short) 15));
            departRow6.add(new ExcelCellData("岗位", "formatTitle", (short) 15));
            departRow6.add(new ExcelCellData("身份证号", "formatTitle", (short) 25));
            departRow6.add(new ExcelCellData("培训状态", "formatTitle", (short) 15));
            departRow6.add(new ExcelCellData("考试成绩", "formatTitle", (short) 15));
            departRow6.add(new ExcelCellData("培训完成时间", "formatTitle", (short) 20));
            departRecords.add(departRow6);
            List<SafeTrainCompanyDetialUserVO> userInfoList = item.getUserInfoList();
            if (CollectionUtils.isNotEmpty(userInfoList)) {
                for (int i = 0; i < userInfoList.size(); i++) {
                    SafeTrainCompanyDetialUserVO user = userInfoList.get(i);
                    List<ExcelCellData> userRow = new ArrayList<>();
                    userRow.add(new ExcelCellData(String.valueOf(i + 1)));
                    userRow.add(new ExcelCellData(user.getUserName()));
                    userRow.add(new ExcelCellData(user.getWorkName()));
                    userRow.add(new ExcelCellData(user.getIdCard()));
                    userRow.add(new ExcelCellData(user.getReservedField3()));
                    userRow.add(new ExcelCellData(user.getScore()));
                    userRow.add(new ExcelCellData(user.getCompleteTime()));
                    departRecords.add(userRow);
                }
            }
            sheet.setExcelCells(departRecords);
        }
        return util.exportCustomExcel(vos, filename);
    }

    @PostMapping("Export_BusSafeTrainOrgDetial")
    @ApiOperation(value = "公交培训计划-详情查询-下载报表")
    public AjaxResult Export_BusSafeTrainOrgDetial(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        SafeTrainDataExportVO vo = LessonSafeTrainPlanServiceImpl.getBusSafeTrainOrgDetial(request, null);
        ExcelUtil<UserLessonRecordSafeTrainUserVO> util = new ExcelUtil<>(UserLessonRecordSafeTrainUserVO.class);
        String title = "安全培训全部数据报表";
        if (request.getLearnFlag() != null && request.getLearnFlag() == 1) {
            title = "安全培训数据报表";
        } else if (request.getLearnFlag() != null && request.getLearnFlag() == 2) {
            title = "安全培训补学数据报表";
        }
        String filename = request.getLessonMonth().substring(0, 7).replace("-", "年") + "月" + request.getOrgName() + title;
        List<SheetVO> vos = new ArrayList<>();
        SheetVO sheet1 = new SheetVO();
        sheet1.setSheetName("总统计");
        vos.add(sheet1);
        List<List<ExcelCellData>> records = new ArrayList<>();
        List<ExcelCellData> row1 = new ArrayList<>();
        row1.add(new ExcelCellData(request.getLessonMonth().substring(0, 7).replace("-", "年") + "月" + request.getOrgName() + "安全培训数据", "formatHeader", 6));
        records.add(row1);
        List<ExcelCellData> row2 = new ArrayList<>();
        row2.add(new ExcelCellData("培训时间", "formatTitle"));
        row2.add(new ExcelCellData(vo.getTrainCycle(), "formatLeftVal", 5));
        records.add(row2);
        List<ExcelCellData> row3 = new ArrayList<>();
        row3.add(new ExcelCellData("部门名称", "formatTitle", (short) 30));
        row3.add(new ExcelCellData("总人数", "formatTitle", (short) 15));
        row3.add(new ExcelCellData("参学人数", "formatTitle", (short) 15));
        row3.add(new ExcelCellData("完成人数", "formatTitle", (short) 15));
        row3.add(new ExcelCellData("参学率", "formatTitle", (short) 15));
        row3.add(new ExcelCellData("考试合格率", "formatTitle", (short) 15));
        records.add(row3);
        List<DepartTrainDataStaticsVO> departTrainDataList = vo.getDepartTrainDataList();
        for (DepartTrainDataStaticsVO item : departTrainDataList) {
            List<ExcelCellData> departRow = new ArrayList<>();
            departRow.add(new ExcelCellData(item.getDepartName()));
            departRow.add(new ExcelCellData(String.valueOf(item.getTotalUserNum())));
            departRow.add(new ExcelCellData(String.valueOf(item.getCxUserNum())));
            departRow.add(new ExcelCellData(String.valueOf(item.getWcUserNum())));
            departRow.add(new ExcelCellData(String.valueOf(item.getCxl())));
            departRow.add(new ExcelCellData(String.valueOf(item.getWcl())));
            records.add(departRow);
        }
        List<ExcelCellData> lineRow = new ArrayList<>();
        lineRow.add(new ExcelCellData("-------------------------------------------------------", "formatVal", 6));
        records.add(lineRow);
        List<ExcelCellData> row4 = new ArrayList<>();
        row4.add(new ExcelCellData("学习未完成人数", "formatTitle", 2));
        row4.add(new ExcelCellData("姓名", "formatTitle", 4));
        records.add(row4);
        List<ExcelCellData> row5 = new ArrayList<>();
        row5.add(new ExcelCellData("学习未完成" + vo.getStudyNoComplete() + "人", "formatVal", 2, (short) 0, (short) 2000));
        row5.add(new ExcelCellData(vo.getStudyNoCompleteName(), 4));
        records.add(row5);
        sheet1.setExcelCells(records);
        //生成子部门数据
        Map<Long, String> trainDateMap = vo.getTrainDates().stream().collect(Collectors.toMap(CompanyPlanTimeVO::getCompanyId, (item) ->
                DateUtils.formatDate(item.getSTime(), "yyyy-MM-dd", "yyyy年MM月dd日") + "至" + DateUtils.formatDate(item.getETime(), "yyyy-MM-dd", "yyyy年MM月dd日")));
        for (DepartTrainDataStaticsVO item : departTrainDataList) {
            if ("合计".equals(item.getDepartName())) {
                continue;
            }
            SheetVO sheet = new SheetVO();
            sheet.setSheetName(item.getDepartName());
            vos.add(sheet);
            List<List<ExcelCellData>> departRecords = new ArrayList<>();
            List<ExcelCellData> departRow1 = new ArrayList<>();
            departRow1.add(new ExcelCellData(request.getLessonMonth().substring(0, 7).replace("-", "年") + "月" + request.getOrgName() + "安全培训数据人员明细", "formatHeader", 9));
            departRecords.add(departRow1);
            List<ExcelCellData> departRow2 = new ArrayList<>();
            departRow2.add(new ExcelCellData("培训时间", "formatTitle", 2));
            departRow2.add(new ExcelCellData(trainDateMap.containsKey(item.getDepartId()) ? trainDateMap.get(item.getDepartId()) : vo.getTrainCycle(), "formatLeftVal",7));
            departRecords.add(departRow2);
            List<ExcelCellData> departRow6 = new ArrayList<>();
            departRow6.add(new ExcelCellData("序号", "formatTitle", (short) 8));
            departRow6.add(new ExcelCellData("姓名", "formatTitle", (short) 15));
            departRow6.add(new ExcelCellData("部门名称", "formatTitle", (short) 30));
            departRow6.add(new ExcelCellData("岗位", "formatTitle", (short) 15));
            departRow6.add(new ExcelCellData("身份证号", "formatTitle", (short) 25));
            departRow6.add(new ExcelCellData("培训状态", "formatTitle", (short) 15));
            departRow6.add(new ExcelCellData("考试成绩", "formatTitle", (short) 15));
            departRow6.add(new ExcelCellData("培训时长", "formatTitle", (short) 13));
            departRow6.add(new ExcelCellData("培训完成时间", "formatTitle", (short) 20));
            departRecords.add(departRow6);
            List<UserLessonRecordSafeTrainUserVO> userInfoList = item.getUserInfoList();
            if (CollectionUtils.isNotEmpty(userInfoList)) {
                for (int i = 0; i < userInfoList.size(); i++) {
                    UserLessonRecordSafeTrainUserVO user = userInfoList.get(i);
                    List<ExcelCellData> userRow = new ArrayList<>();
                    userRow.add(new ExcelCellData(String.valueOf(i + 1)));
                    userRow.add(new ExcelCellData(user.getUserName()));
                    userRow.add(new ExcelCellData(user.getDepartName()));
                    userRow.add(new ExcelCellData(user.getWorkName()));
                    userRow.add(new ExcelCellData(user.getIdCard()));
                    userRow.add(new ExcelCellData(user.getReservedField3()));
                    userRow.add(new ExcelCellData(user.getScore()));
                    userRow.add(new ExcelCellData(user.getTrainTotalTime()));
                    userRow.add(new ExcelCellData(user.getCompleteTime()));
                    departRecords.add(userRow);
                }
            }
            sheet.setExcelCells(departRecords);
        }
        return util.exportFormatExcel(vos, filename);
    }
    @PostMapping("Export_SafetyTrainingLedger2")
    @ApiModelProperty(value = "公交培训计划-导出安全培训台账")
    public AjaxResult Export_SafetyTrainingLedger2(@RequestBody CommonRequest commonRequest){
        LessonSafeTrainPlanRequest request = (LessonSafeTrainPlanRequest) commonRequest.getRequest(LessonSafeTrainPlanRequest.class);
        CompanyRequest companyRequest = new CompanyRequest();
        companyRequest.setId(request.getOrgId());
        String lessonName = request.getLessonName();
        ExcelUtil<SafetyTrainingLedgerRequest> util = new ExcelUtil<>(SafetyTrainingLedgerRequest.class);
        List<SheetVO> vos = new ArrayList<>();
        List<List<ExcelCellData>> departRecords = new ArrayList<>();
        String filename = "安全培训数据人员明细";
        //获取公司下级部门
        List<Company> companyList = companyService.getSubDeps(companyRequest);
            for (Company company : companyList) {
                Long companyId = company.getId();
                String companyName = company.getCompanyName();
                System.out.println("处理"+companyName+"中");
                SheetVO sheet = new SheetVO();
                sheet.setSheetName(companyName);
                vos.add(sheet);
                List<ExcelCellData> departRow1 = new ArrayList<>();
                departRow1.add(new ExcelCellData("序号", "formatTitle", (short) 8));
                departRow1.add(new ExcelCellData("姓名", "formatTitle", (short) 15));
                departRow1.add(new ExcelCellData("岗位", "formatTitle", (short) 12));
                departRow1.add(new ExcelCellData("身份证号", "formatTitle", (short) 21));
                departRow1.add(new ExcelCellData("培训状态", "formatTitle", (short) 25));
                departRow1.add(new ExcelCellData("考试成绩", "formatTitle", (short) 15));
                departRow1.add(new ExcelCellData("培训完成时间", "formatTitle", (short) 20));
                departRow1.add(new ExcelCellData("学习签名", "formatTitle", (short) 18));
                departRow1.add(new ExcelCellData("考试签名", "formatTitle", (short) 18));
                departRecords.add(departRow1);
                //获取当前下级公司或部门的用户
                List<SafetyTrainingLedgerRequest> userList = userLessonRecordSafeTrainMongoService.getUserLessonRecordSafeTrain1(lessonName, companyId);
                System.out.println("该部门有"+userList.size()+"人");
                //获取用户考试图片
                List<lessonSafeTrainTd> userExamSignUrlList = userLessonRecordSafeTrainMongoService.selectSignUrlById(lessonName);
                //将用户考试签字URl集合转换为Map
                Map<String, String> userIdToLessonIdMap = userExamSignUrlList.stream()
                        .collect(Collectors.toMap(lesson -> lesson.getUserId(), lesson -> lesson.getSignUrl()));
                //用户数据绑定考试签字URL
                if (CollectionUtils.isNotEmpty(userList)){
                    for (int i = 0; i < userList.size(); i++) {
                        SafetyTrainingLedgerRequest user = userList.get(i);
                        List<ExcelCellData> userRow = new ArrayList<>();
                        userRow.add(new ExcelCellData(String.valueOf(i + 1)));
                        userRow.add(new ExcelCellData(user.getUserName()));
                        userRow.add(new ExcelCellData(user.getWorkName()));
                        userRow.add(new ExcelCellData(user.getIdCard()));
                        userRow.add(new ExcelCellData(user.getStaticStatus().toString()));
                        userRow.add(new ExcelCellData(user.getScore().toString()));
                        userRow.add(new ExcelCellData(user.getCompleteTime()));
                        userRow.add(new ExcelCellData(user.getSignImg()));
                        if(!ObjectUtils.isEmpty(userIdToLessonIdMap.get(user.getUserCode()))){
                            userRow.add(new ExcelCellData(userIdToLessonIdMap.get(user.getUserCode())));
                        }
                        departRecords.add(userRow);
                    }
                }
                sheet.setExcelCells(departRecords);
            }
        return util.exportFormatExcel2(vos, filename);
    }
    @PostMapping("Edit_LessonSafeTrainPlan")
    @ApiOperation(value = "公交培训计划-编辑")
    public AjaxResult Edit_LessonSafeTrainPlan(@RequestBody CommonRequest commonRequest) {
        LessonSafeTrainPlanVO request = (LessonSafeTrainPlanVO) commonRequest.getRequest(LessonSafeTrainPlanVO.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.editLessonSafeTrainPlan(request));
    }

    @PostMapping("Get_LessonNoDisUserNum")
    @ApiOperation(value = "培训统计-查询待分发人员数")
    public AjaxResult Get_LessonNoDisUserNum(@RequestBody CommonRequest commonRequest) {
        LessonNoDisUserNumRequest request = (LessonNoDisUserNumRequest) commonRequest.getRequest(LessonNoDisUserNumRequest.class);
        Map map = new HashMap<>();
        map.put("NoDisNum", userLessonRecordSafeTrainMongoService.getLessonNoDisUserNum(request));
        return AjaxResult.success(map);
    }

    @PostMapping("Get_SafeTrainUserRecordDetail")
    @ApiOperation(value = "培训统计-人员明细")
    public AjaxResult Get_SafeTrainUserRecordDetail(@RequestBody CommonRequest commonRequest) {
        SafeTrainUserRecordDetailRequest request = (SafeTrainUserRecordDetailRequest) commonRequest.getRequest(SafeTrainUserRecordDetailRequest.class);
        PageUtils pageUtils = LessonSafeTrainPlanServiceImpl.getSafeTrainUserRecordDetail(request, commonRequest.getPageEntity());
        return AjaxResult.success(pageUtils);
    }

    @PostMapping("Export_SafeTrainUserRecordDetail")
    @ApiOperation(value = "培训统计-人员明细下载")
    public AjaxResult Export_SafeTrainUserRecordDetail(@RequestBody CommonRequest commonRequest) {
        SafeTrainUserRecordDetailRequest request = (SafeTrainUserRecordDetailRequest) commonRequest.getRequest(SafeTrainUserRecordDetailRequest.class);
        PageUtils pageUtils = LessonSafeTrainPlanServiceImpl.getSafeTrainUserRecordDetail(request, null);
        List list = pageUtils.getList();
        ExcelUtil<SafeTrainUserRecordDetailVO> util = new ExcelUtil<>(SafeTrainUserRecordDetailVO.class);
        String filename = request.getLessonMonth().substring(0, 7).replace("-", "年") + "月安全培训人员学习数据明细表";
        return util.exportExcel(list, filename, filename, true);
    }

    @PostMapping("Add_UserExamResitNumSafeTrain")
    @ApiOperation(value = "培训统计-针对考试不合格学员设置补考次数")
    public AjaxResult Add_UserExamResitNumSafeTrain(@RequestBody CommonRequest commonRequest) {
        AddUserExamResitNumSafeTrainRequest request = (AddUserExamResitNumSafeTrainRequest) commonRequest.getRequest(AddUserExamResitNumSafeTrainRequest.class);
        return AjaxResult.success(LessonSafeTrainPlanServiceImpl.addUserExamResitNumSafeTrain(request));
    }

}
