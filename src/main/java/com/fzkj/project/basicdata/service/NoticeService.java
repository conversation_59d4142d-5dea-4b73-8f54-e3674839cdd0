package com.fzkj.project.basicdata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fzkj.common.utils.PageUtils;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.project.basicdata.entity.NoticeEntity;
import com.fzkj.project.basicdata.request.NoticeRequest;
import com.fzkj.project.system.request.GetNoticeAPPRequest;

import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2024-03-13 13:52
 * @Version: 1.0
 */
public interface NoticeService extends IService<NoticeEntity> {
    PageUtils listByPage(NoticeRequest request, PageEntity pageEntity);

    /**
     * 新增公告
     *
     * @param tFeedBack 常见问题
     * @return 结果
     */
    public Long insertNotice(NoticeRequest tFeedBack);

    /**
     * 修改公告
     *
     * @param tFeedBack 常见问题
     * @return 结果
     */
    public Boolean updateNotice(NoticeRequest tFeedBack);

    /**
     * 删除公告
     *
     * @param id 公告主键
     * @return 结果
     */
    public Boolean deleteNotice(Long id);

    List<NoticeEntity> getNoticeAPP(GetNoticeAPPRequest request);
}
