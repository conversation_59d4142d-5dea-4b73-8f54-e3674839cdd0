package com.fzkj.project.system.service;

import com.fzkj.project.system.entity.UserRole;
import com.fzkj.project.system.vo.UserRoleVO;
import com.fzkj.project.system.request.UserRoleRequest;
import com.fzkj.framework.web.domain.PageEntity;
import com.fzkj.common.utils.PageUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户关联角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface UserRoleService extends IService<UserRole> {

    List<UserRole> listUserRole(UserRoleRequest request);

    PageUtils listByPage(UserRoleVO request, PageEntity pageEntity);

    UserRoleVO findUserRole(Long id);

    Long saveUserRole(UserRoleVO vo);

    Integer updateUserRole(UserRoleVO vo);

    boolean removeUserRole(Long id);
}
